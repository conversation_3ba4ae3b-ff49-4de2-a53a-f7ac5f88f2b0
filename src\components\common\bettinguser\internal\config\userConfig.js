/*
 * @Desc: 此文件待确认后再处理
 * @Date: 2020-01-27 15:42:23
 * @Author:Nice
 * @FilePath: /src/components/common/bettinguser/internal/config/userConfig.js
 * 
 */
import { i18n } from 'src/boot/i18n';
const position = 'left'
let table_title = i18n.t('internal.merchant.bettinguser.userConfig')
export const tablecolumns_config = [
  {//序号
    title: table_title[0],
    width: 80,
    dataIndex: "_index",
    key: "_index",
    fixed: "left",
    align: "center"
  },
  {//IP地址
    title: table_title[1],
    width: 140,
    dataIndex: "ip",
    key: "ip",
    fixed: "left",
    align: position
  },
  {//地区
    title: table_title[2],
    dataIndex: "ipAddress",
    key: "1",
    width: 150,
    align: position
  },
  {//注单数量
    title: table_title[3],
    dataIndex: "betNumber",
    key: "2",
    align: position
  },
  {//登录次数
    title: table_title[4],
    dataIndex: "loginNumber",
    key: "3",
    width: 100,
    align: position
  },
  {//最后登录时间
    title: table_title[5],
    dataIndex: "endTime",
    key: "4",
    width: 150,
    align: "center"
  },
];

