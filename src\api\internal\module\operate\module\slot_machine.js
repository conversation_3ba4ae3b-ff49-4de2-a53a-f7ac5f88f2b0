/*
 * @FilePath: d:/projects/new-merchant-management/src/api/internal/module/operate/module/slot_machine.js
 * @Description: 老虎机接口
 */

import axios from "src/api/common/axioswarpper.js";


let prefix_1 = process.env.API_PREFIX_4;

// 老虎机列表
export const get_slot_machine_list = () => {
  let prefix = prefix_1;
  let url = `/slotMachine/slotMachineList`;
  return axios.get(`${prefix}${url}`);
}

// 老虎机状态开关
export const modify_slot_machine_state = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/modifySlotMachineState`;
  return axios.post(`${prefix}${url}`, params, {
    type: 1
  });
}

// 编辑接口
export const edit_slot_machine = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/editSlotMachineCfg`;
  return axios.post(`${prefix}${url}`, params);
}

// 新增接口
export const add_slot_machine = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/addSlotMachineCfg`;
  return axios.post(`${prefix}${url}`, params);
}

// 排序接口
export const sort_slot_machine = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/draggableSort`;
  return axios.post(`${prefix}${url}`, params, {
    type: 1,
  });
}

// 查询老虎机彩金配置
export const query_slot_machine_jackpot = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/querySlotJackpotCfg`;
  return axios.get(`${prefix}${url}`, {
    params: {
      ...params
    }
  });
}


// 编辑老虎机彩金配置
export const edit_slot_machine_jackpot = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/editSlotJackpotCfg`;
  return axios.post(`${prefix}${url}`, params);
}

// 查询老虎机道具配置
export const query_slot_machine_props = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/queryMachinePropsCfg`;
  return axios.get(`${prefix}${url}`, {
    params: {
      ...params
    }
  });
}

// 保存老虎机道具配置
export const save_slot_machine_props = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/saveSlotMachineProps`;
  return axios.post(`${prefix}${url}`, params);
}
