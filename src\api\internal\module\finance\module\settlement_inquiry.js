
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_4;
let prefix2 = process.env.API_PREFIX_17
let prefix3 = process.env.API_PREFIX_3;



// 结算查询-修改是否赔偿
export const update_batchSetCompensateStatus = (
    params,
    url = "/order/orderTimeSettle/batchSetCompensateStatus"
  ) => axios.post(`${prefix3}${url}`, params);


//结算查询-提交审核接口（新接口）POST
export const submitSettleAudit = (
  params,
  url = "/order/orderTimeSettle/submitSettleAudit"
  ) => axios.post(`${prefix3}${url}`, params);

  

  
//结算查询-修改系统参数（新接口）POST
export const setSystemParamConfig = (
  params,
  url = "/systemSwitch/setSystemParamConfig"
  ) => axios.post(`${prefix}${url}`, params);
  
//结算查询-查询系统参数
export const get_getSystemParamConfig = (
    params,
    url = "/systemSwitch/getSystemParamConfig"
    ) => axios.post(`${prefix}${url}`, params);



//结算查询-二次结算汇总商户列表（新接口）POST
export const getSummaryMerchantList = (
  params,
  url = "/order/orderTimeSettle/getSummaryMerchantList"
  ) => axios.post(`${prefix3}${url}`, params);

  
//结算查询-二次结算汇总列表（新接口）POST
export const getSummaryList = (
  params,
  url = "/order/orderTimeSettle/getSummaryList"
  ) => axios.post(`${prefix3}${url}`, params);

  
//驳回接口（新接口）POST
export const rejectAudit = (
  params,
  url = "/order/orderTimeSettle/rejectAudit"
  ) => axios.post(`${prefix3}${url}`, params);
  

  
  

  
  
//查看驳回原因接口（新接口）POST
export const getRejectReasonList = (
  params,
  url = "/order/orderTimeSettle/getRejectReasonList"
  ) => axios.post(`${prefix3}${url}`, params);



  
//获取未读记录数
export const getRecordCounts = (
  params,
  url = "/order/orderTimeSettle/getRecordCounts"
  ) => axios.post(`${prefix3}${url}`, params);

  
//重置记录数
export const resetRecordCounts = (
  params,
  url = "/order/orderTimeSettle/resetRecordCounts"
  ) => axios.post(`${prefix3}${url}`, params);