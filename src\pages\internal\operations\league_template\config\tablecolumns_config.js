/*
 * @Author: 
 * @Date: 2025-07-25 11:43:51
 * @Description:运营管理/联赛模板-config
 * @FilePath: src/pages/internal/operations/league_template/config/activity_list.js
 */
import {
  i18n
} from "src/boot/i18n";
const POSITION = 'left'
export const tablecolumns_config = [
  {//序号
    title: i18n.t("internal.league_template.sort"),
    key: "_index",
    dataIndex: "_index",
    align: "center",
    width:50,
  },
  { // 联赛模板
    title: i18n.t('internal.activity_manage.config.t1'),
    width: 120,
    key: "activityTitle",
    align: "center",
    dataIndex: "activityTitle",
    scopedSlots: {
      customRender: "activityTitle"
    }
  },
  { // 活动类型
    title: i18n.t('internal.activity_manage.config.t2'),
    width: 120,
    key: "activityType",
    align: "center",
    dataIndex: "activityType",
    scopedSlots: {
      customRender: "activityType"
    }
  },
  { // 展示设备
    title: i18n.t('internal.activity_manage.config.t3'),
    width: 100,
    key: "deviceType",
    align: "center",
    dataIndex: "deviceType",
    scopedSlots: {
      customRender: "deviceType"
    }
  },
  { // 活动时间
    title: i18n.t('internal.activity_manage.config.t4'),
    width: 100,
    key: "activityTime",
    align: "center",
    dataIndex: "activityTime",
    scopedSlots: {
      customRender: "activityTime"
    }
  },
  { // 播放方式
    title: i18n.t('internal.activity_manage.config.t5'),
    width: 120,
    key: "playType",
    align: "center",
    dataIndex: "playType",
    scopedSlots: {
      customRender: "playType"
    }
  },
  { // 烟花款式
    title: i18n.t('internal.activity_manage.config.t6'),
    width: 120,
    key: "fireworksType",
    ellipsis: true,
    align: "center",
    dataIndex: "fireworksType",
    scopedSlots: {
      customRender: "fireworksType"
    }
  },
  { // 活动状态
    title: i18n.t('internal.activity_manage.config.t7'),
    width: 100,
    key: "activityStatus",
    ellipsis: true,
    dataIndex: "activityStatus",
    align: "center",
    scopedSlots: {
      customRender: "activityStatus"
    }
  },
  { // logo
    title: i18n.t('internal.activity_manage.config.t8'),
    width: 90,
    key: "logo",
    ellipsis: true,
    align: "center",
    dataIndex: "logo",
    scopedSlots: {
      customRender: "logo"
    }
  },
  { // 文案说明
    title: i18n.t('internal.activity_manage.config.t9'),
    width: 120,
    key: "copyDesc",
    ellipsis: true,
    dataIndex: "copyDesc",
    align: "center",
    scopedSlots: {
      customRender: "copyDesc"
    }
  },
  { // 最后操作时间
    title: i18n.t('internal.activity_manage.config.t10'),
    width: 100,
    key: "updateTime",
    ellipsis: true,
    dataIndex: "updateTime",
    align: "Center",
    scopedSlots: {
      customRender: "updateTime"
    }
  },
  { // 最后操作人
    title: i18n.t('internal.activity_manage.config.t11'),
    key: "updateBy",
    width: 80,
    align: "Center",
    dataIndex: "updateBy",
    scopedSlots: {
      customRender: "updateBy"
    },

  },
  { // 操作
    title: i18n.t('internal.activity_manage.config.t12'),
    key: "operation",
    width: 240,
    align: "Center",
    fixed: 'right',
    dataIndex: "operation",
    scopedSlots: {
      customRender: "operation"
    },

  }
];

