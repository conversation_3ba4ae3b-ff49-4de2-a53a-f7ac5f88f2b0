/*
 * @Author: Nice
 * @Date: 2020-05-29 17:29
 * @Description: 注单查询的复用的mixin
 * @FilePath: /src/components/common/bet_slip/internal/mixin/index.js
 */

import { i18n } from 'src/boot/i18n';

const  add_empty_value=(obj)=>{
  for(let i in obj ){
    if(!obj[i].hasOwnProperty('value')){
      obj[i]['value']=''
    }
  }
  return obj
}
export default {
  data() {
    return {
      filterType: i18n.t('internal.data.betslip.store.filterType'),
      filterTypeReserve: i18n.t('internal.data.betslip.store.filterTypeReserve'),
      settleTypeName: add_empty_value( i18n.t('internal.data.betslip.store.settleTypeName')),
      matchTypeList:  add_empty_value(i18n.t('internal.data.betslip.store.matchTypeList')) ,
      loseOrWinStatus:  i18n.t('internal.data.betslip.store.loseOrWinStatus'),
      settleStatusName:  i18n.t('internal.data.betslip.store.settleStatusName'),
      // 设备信息
      deviceTypeName: i18n.t('internal.data.betslip.store.deviceTypeName'),
      // 注单类型
      matchTypeName:  i18n.t('internal.data.betslip.store.matchTypeName'),
      // 结算状态
      settleStatusNameObj: i18n.t('internal.data.betslip.store.settleStatusNameObj'),
      filterstatues: i18n.t('internal.data.betslip.store.filterstatues'),
      //展开条件
      expand_conditions:{
        1: i18n.t("internal.syndicate.text14") ,
        2: i18n.t("internal.syndicate.text15") ,
        3: i18n.t("internal.syndicate.text16") 
      }

    };
  },
 
};

