/*
 * @Desc: 
 * @Date: 2020-01-27 15:42:23
 * @Author:Nice
 * @FilePath: /src/components/common/bettinguser/internal/config/config.js
 * @Description: 商户中心/投注用户管理(列表配置)
 */
import { i18n } from 'src/boot/i18n';
const POSITION = 'left';
let table_title = i18n.t('internal.merchant.bettinguser.config')
export const tablecolumns_config =({route_name})=>{

  let arr= [
    {//序号
      title: table_title[0], 
      width: 80,
      dataIndex: "_index",
      key: "_index",
      fixed: "left",
      align: "center"
    },
    {//用户id
      title: table_title[1], 
      width: 200,
      dataIndex: "userId",
      key: "userId",
      fixed: "left",
      align: POSITION,
      scopedSlots:{customRender:"userId"},
    },
    {//用户名
      title: table_title[2], 
      width: 200,
      dataIndex: "userName",
      key: "userName",
      fixed: "left",
      align: POSITION,
      scopedSlots:{customRender:"userName"},
    },
    route_name=='white_betting_users'?  {//来源
        title: table_title[36], 
        width: 200,
        dataIndex: "allowListSource",
        key: "allowListSource",
        fixed: "left",
        align: POSITION,
        scopedSlots:{customRender:"allowListSource"},
      }:'',
      route_name=='white_betting_users'? {}: {
        //用户VIP等级 
        title: table_title['userVipLevel'],
        width: 160,
        dataIndex: "userVipLevel",
        key: "userVipLevel",
        align: POSITION,
        scopedSlots:{customRender:"userVipLevel"},
      },  
     route_name=='white_betting_users'? {}
     :{// 启&禁用
      title: table_title[29], 
      width: 100,
      dataIndex: "disabled",
      key: "disabled",
      align: POSITION,
      scopedSlots:{customRender:"disabled"},
    },
    route_name=='white_betting_users'?{}: {
      //合买开关
      title:   i18n.t('internal.merchant.bettinguser.config.text8'),
      dataIndex: "userPurchase",
      key: "userPurchase",
      width: 100,
      align: "left",
      scopedSlots: { customRender: "userPurchase" }

  },
    
    route_name=='white_betting_users'? {}:{
      //额度转入
      title: table_title[51], 
      dataIndex: "userTransferIn",
      key: "userTransferIn",
      width: 100,
      align: "left",
      scopedSlots: { customRender: "userTransferIn" }

  },
  route_name=='white_betting_users'?{}: {
      //额度转出
      title: table_title[52], 
      dataIndex: "userTransferOut",
      key: "userTransferOut",
      width: 100,
      align: "left",
      scopedSlots: { customRender: "userTransferOut" }

  },
    {//所属商户
      title: table_title[3], 
      dataIndex: "merchantName",
      key: "merchantName",
      
      width: 180,
      align: POSITION,
      scopedSlots:{customRender:"merchantName"},
    },
    {//线路vip
      title: table_title[27], 
      dataIndex: "isvip",
      key: "isvip",
      width: 180,
      align: POSITION,
      scopedSlots:{customRender:"isvip"},
    },
    {//可用余额
      title: table_title[4], 
      dataIndex: "amount",
      key: "amount",
      width: 180,
      align: POSITION,
      sorter:  route_name == 'white_betting_users'? false : true,
      scopedSlots: { customRender: "amount" },
    },
    {//累计投注额
      title: table_title[5], 
      dataIndex: "betAmount",
      key: "betAmount",
      width: 200,
      align: POSITION,
      sorter: route_name == 'white_betting_users'? false : true,
      scopedSlots: { customRender: "betAmount" },
    },
    {//累计盈利
      title: table_title[6], 
      dataIndex: "profit",
      key: "profit",
      width: 180,
      sorter: route_name == 'white_betting_users'? false : true,
      align: POSITION,
      scopedSlots: { customRender: "profit" },
    },
    {//用户币种
      title: table_title[7], 
      dataIndex: "currencyCode",
      key: "currencyCode",
      width: 120,
      align: POSITION,
      scopedSlots: { customRender: "currencyCode" },
    },
    {//注单数量
      title: table_title[8], 
      dataIndex: "betNum",
      key: "betNum",
      width: 150,
      align: POSITION,
      sorter: route_name == 'white_betting_users'? false : true,
      scopedSlots: { customRender: "betNum" },
    },
    {//最后投注时间
      title: table_title[9], 
      dataIndex: "lastBetStr",
      key: "lastBetStr",
      width: 160,
      align: POSITION,
      
    },
    route_name =='betting_user' ?{//注册时间
      title: table_title[46], 
      dataIndex: "createTime",
      key: "createTime",
      width: 160,
      align: POSITION,
      scopedSlots: { customRender: 'createTime' }, 
      
    } : '',
    {//在线状态
      title: table_title[10], 
      dataIndex: "enabled",
      key: "enabled",
      width: 110,
      align: "center",
      scopedSlots: { customRender: 'enabled' }, 
      filters: table_title[13],
      onFilter: (value, record) => record.enabled == value ,
      filterMultiple: false,
    },
    {//最后登陆时间
      title: table_title[11], 
      dataIndex: "lastLoginStr",
      key: "lastLoginStr",
      width: 120,
      // sorter: (a, b) => new Date(a.createTime).getTime() - new Date(b.createTime).getTime(),
      // sortDirections: ['descend', 'ascend'],
      align: POSITION,
      scopedSlots: { customRender: "lastLoginStr" },
    },
    {//体育赔率分组
     title:table_title[47], 
     dataIndex: "marketLevel",
     key: "marketLevel",
     width: 180,
     align: POSITION,
     scopedSlots: { customRender: "marketLevel" },
   },
    {// 电竞赔率分组
     title:table_title[48], 
     dataIndex: "esMarketLevel",
     key: "esMarketLevel",
     width: 180,
     align: POSITION,
     scopedSlots: { customRender: "esMarketLevel" },
   },
    {//投注延时
      title:table_title[18], 
      dataIndex: "specialBettingLimitDelayTime",
      key: "specialBettingLimitDelayTime",
      width: 150,
      align: POSITION,
      scopedSlots: { customRender: "specialBettingLimitDelayTime" },
    },
    {//投注特殊限额
      title: table_title[14], 
      dataIndex: "specialBettingLimitType",
      key: "specialBettingLimitType",
      width: 180,
      align: POSITION,
      scopedSlots: { customRender: "specialBettingLimitType" },
    },
    {//特殊限额设置时间
      title: table_title[20], 
      dataIndex: "specialBettingLimitTime",
      key: "specialBettingLimitTime",
      width: 200,
      align: POSITION,
      scopedSlots: { customRender: "specialBettingLimitTime" },
    },
    {//VIP升级时间  （显示格式 yyyy-mm-ss hh:ms:ss，非VIP用户空着即可）
      title: table_title[16], 
      width: 200,
      dataIndex: "vipUpdateTime",
      key: "vipUpdateTime",
      align: POSITION,
      scopedSlots:{customRender:"vipUpdateTime"},
    },
    {//操作
      title: table_title[12],
      key: "operation",
      fixed: "right",
      width: 150,
      scopedSlots: { customRender: "action" },
      align: "center"
    }
  ]
  
  
  return arr.filter(x=>x) ;
} 




