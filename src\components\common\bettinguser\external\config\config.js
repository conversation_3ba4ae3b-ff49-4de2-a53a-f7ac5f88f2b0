/*
 * @Desc: 
 * @Date: 2020-01-27 15:42:23
 * @Author:Nice
 * @FilePath: /src/components/common/bettinguser/external/config/config.js
 * @Description:   账户中心-投注用户管理（直营&二级：列表配置）
 */
import { i18n } from 'src/boot/i18n';
 const POSITION = 'left';
 let table_title = i18n.t('external.merchant.bettinguser.config')
 export const tablecolumns_config = [
   {
       //序号
     title: table_title[0],
     width: 80,
     dataIndex: "_index",
     key: "_index",
     fixed: "left",
     align: "center"
   },
   {//用户ID
     title: table_title[1], 
     width: 200,
     dataIndex: "userId",
     key: "userId",
     fixed: "left",
     align: POSITION,
     scopedSlots:{customRender:"userId"},
   },
   {
     //用户名
     title: table_title[2],
     width: 300,
     dataIndex: "userName",
     key: "userName",
     fixed: "left",
     align: POSITION,
     scopedSlots:{customRender:"userName"},
   },
   {
    //用户VIP等级 
    title: i18n.t('internal.merchant.bettinguser.config.userVipLevel'),
    width: 160,
    dataIndex: "userVipLevel",
    key: "userVipLevel",
    align: POSITION,
    scopedSlots:{customRender:"userVipLevel"},
  },  
   {
     //合买开关
     title:  i18n.t("internal.system_level_switch.label38"),
     dataIndex: "userPurchase",
     key: "userPurchase",
     width: 100,
     align: "left",
     scopedSlots: { customRender: "userPurchase" }

 },
   {
      //可用余额
     title: table_title[3], 
     dataIndex: "amount",
     key: "amount",
     width: 160,
     align: POSITION,
     sorter: true,
     scopedSlots: { customRender: "amount" },
   },
   {
     //累计投注额
     title: table_title[4], 
     dataIndex: "betAmount",
     key: "betAmount",
     width: 210,
     align: POSITION,
     sorter: true,
     scopedSlots: { customRender: "betAmount" },
   },
  //  {
  //   //有效投注额
  //   title: table_title[15], 
  //   dataIndex: "orderValidBetMoney",
  //   key: "orderValidBetMoney",
  //   width: 180,
  //   align: POSITION,
  //   sorter: true,
  //   scopedSlots: { customRender: "orderValidBetMoney" },
  // },
   {
     //累计盈利
     title: table_title[5], 
     dataIndex: "profit",
     key: "profit",
     width: 160,
     sorter: true,
     align: POSITION,
     scopedSlots: { customRender: "profit" },
   },
   {
     //用户币种
     title: table_title[6], 
     dataIndex: "currencyCode",
     key: "currencyCode",
     width: 120,
     align: POSITION,
     scopedSlots: { customRender: "currencyCode" },
   },
   {
      //注单数量
     title: table_title[7], 
     dataIndex: "betNum",
     key: "betNum",
     width: 150,
     align: POSITION,
     sorter: true,
     scopedSlots: { customRender: "betNum" },
   },
   {
          //最后投注时间
     title: table_title[8],
     dataIndex: "lastBetStr",
     key: "lastBetStr",
     width: 160,
     align: POSITION,
     
   },

     {//注册时间
      title: table_title[21], 
      dataIndex: "createTime",
      key: "createTime",
      width: 160,
      align: POSITION,
      scopedSlots: { customRender: 'createTime' }, 
      
    },
   {   
     //在线状态
     title: table_title[9], 
     dataIndex: "enabled",
     key: "enabled",
     width: 110,
     align: "center",
     scopedSlots: { customRender: 'enabled' }, 
     filters: table_title[12],
     onFilter: (value, record) => record.enabled == value ,
     filterMultiple: false,
   },
   {
      //最后登录时间
     title: table_title[10], 
     dataIndex: "lastLoginStr",
     key: "lastLogintr",
     width: 120,
     align: POSITION,
     scopedSlots: { customRender: "lastLoginStr" },
   },
   {//体育赔率分组
   title: i18n.t('external.merchant.bettinguser.config.23'), 
    dataIndex: "marketLevel",
    key: "marketLevel",
    width: 180,
    align: POSITION,
    scopedSlots: { customRender: "marketLevel" },
  },
   {// 电竞赔率分组
   title: i18n.t('external.merchant.bettinguser.config.24'), 
    dataIndex: "esMarketLevel",
    key: "esMarketLevel",
    width: 180,
    align: POSITION,
    scopedSlots: { customRender: "esMarketLevel" },
  },
  {//投注延时
    title: table_title[19], 
    dataIndex: "specialBettingLimitDelayTime",
    key: "specialBettingLimitDelayTime",
    width: 150,
    align: POSITION,
    scopedSlots: { customRender: "specialBettingLimitDelayTime" },
  },


   {
      //投注特殊限额
     title: table_title[13], 
     dataIndex: "specialBettingLimitType",
     key: "specialBettingLimitType",
     width: 160,
     align: POSITION,
     scopedSlots: { customRender: "specialBettingLimitType" },
   },
   {
     //特殊限额设置时间
     title: table_title[20], 
     dataIndex: "specialBettingLimitTime",
     key: "specialBettingLimitTime",
     width: 210,
     align: POSITION,
     scopedSlots: { customRender: "specialBettingLimitTime" },
   },
   {
    //VIP升级时间
    title: table_title[16], 
    width: 200,
    dataIndex: "vipUpdateTime",
    key: "vipUpdateTime",
    align: POSITION,
    scopedSlots:{customRender:"vipUpdateTime"},
  },
   {
         //操作
     title: table_title[11], 
     key: "operation",
     fixed: "right",
     width: 200,
     scopedSlots: { customRender: "action" },
     align: "center"
   }
 ];
 