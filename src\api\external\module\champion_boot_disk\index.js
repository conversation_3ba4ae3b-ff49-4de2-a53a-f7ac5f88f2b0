/*  
 * @path: src/api/external/module/champion_boot_disk/index.js
 * @Descripttion: 冠军引导盘
 * @Author: 
 * @Date: 2024-11-06 09:48:04
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_17; //yewu17

//账户中心 冠军引导盘 获取开关状态
export const get_champion_relation_getConfig = (params, url = "/admin/tournament/relation/getConfig") =>
    axios.get(`${prefix}${url}`, { params: { ...params } });

//账户中心 冠军引导盘 开关修改
export const post_champion_relation_updateConfig = (
    params,
    url = "/admin/tournament/relation/updateConfig"
  ) => axios.post(`${prefix}${url}`, params);

//账户中心 冠军引导盘查询大列表
export const post_champion_queryTournamentList = (
    params,
    url = "/admin/tournament/relation/queryTournamentList"
  ) => axios.post(`${prefix}${url}`, params);


//账户中心 冠军引导盘联赛绑定开关-修改
export const post_champion_updateSwitch = (
  params,
  url = "/admin/tournament/relation/update"
) => axios.post(`${prefix}${url}`, params);


// 冠军引导盘联赛绑定总开关-修改
export const post_champion_updateallSwitch = (
  params,
  url = "/admin/tournament/relation/updateAll"
) => axios.post(`${prefix}${url}`, params);