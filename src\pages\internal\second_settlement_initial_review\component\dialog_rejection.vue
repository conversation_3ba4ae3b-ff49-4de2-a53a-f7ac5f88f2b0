
<!--
 * @FilePath:
 * @Description:二次结算初次审核-审核驳回
-->
<template>
    <div >
      <!-- 一键取消第一次的弹窗 -->
      <q-dialog class="text-center" v-model="show_cancel_confirm"  persistent>
        <q-card style="width: 520px">
          <q-card-section>
            <div class="text-center full-width text-h6 text-weight-bolder">
           
          {{ $t('internal.second_settlement_initial_review.text__16') }}
            </div>
          </q-card-section>
          <q-card-section>
            <q-input v-model="cancel_confirm_text" outlined :placeholder="$t('internal.second_settlement_initial_review.text__17')" type="textarea" />
          </q-card-section>
          <q-card-actions>
            <div class="row justify-center q-gutter-x-md full-width">
              <!--确定-->
              <q-btn :label="$t('external.label.label44')" class="q-mr-md" color="primary" @click="handle_begin_process()" />
              <!--返回-->
              <q-btn :label="$t('external.label.label43')" color="primary" @click="$emit('close_dialog')" />
            </div>
          </q-card-actions>
        </q-card>
      </q-dialog>
    </div>
  </template>
  <script>
  import moment from "moment";
  import { i18n } from "src/boot/i18n";
  import { api_finance } from "src/api/index.js";
  import { mapGetters, mapActions } from "vuex";
  
  export default {
  props: {
    cancel_arr:{
      type:Array
    },
    detailObj:{}
  },
    data() {
      return {
      //驳回页面，
      turn_down:'',
        show_cancel_confirm:true,
        cancel_confirm_text: "", //取消注单留言信息

      };
   
    },
    created() {
    },
  computed: {
    page_type(){
      if(this.$route.name == "second_settlement_initial_review"){
        //初次审核
        return 1
      }else if(this.$route.name == "second_settlement_and_second_review"){
        //二次审核
        return 2
      }
      else if(this.$route.name == "second_settlement_initial_review_detail"){
        //初次审核-商户详情
        return 3
      }else if(this.$route.name == "second_settlement_and_second_review_detail"){
        //二次审核-商户详情
        return 4
      } else if(this.$route.name == "second_settlement_initial_review_order_details"){
        //初次审核-注单详情
        return 5
      }else if(this.$route.name == "second_settlement_and_second_review_order_details"){
        //二次审核-注单详情
        return 6
      }
    }
  },
    methods:{
      ...mapActions(['set_compensation_record_count']),
        
  /*
  * 最终提交
  */
  submit_data(){
  let params={
    remark:this.cancel_confirm_text,
  }
  let cancel_arr=this.cancel_arr
  if(this.detailObj?.id&&[5,6].includes(this.page_type)){
    //注单详情
    cancel_arr=[this.detailObj]
  }
    if([1,2].includes(this.page_type)){
      // "pageSource":1,//1：初次待处理，2：二次待处理
    let settleTimes=[]
    cancel_arr.forEach(item => {
      if(item.settleTime){
      let settleTime=moment(item.settleTime).format("YYYYMMDD") 
      settleTimes.push(settleTime)
      }
    });
      params.pageSource=this.page_type
      params.settleTimes=settleTimes
      if( params.settleTimes.length==0){
        this.$message.error(i18n.t("internal.finance.secondarysettlement.label30"), 5);
        return
      }
    }else if([3,4].includes(this.page_type)){

      
    let batchNos=[]
    cancel_arr.forEach(item => {
      batchNos.push(item.batchNo)
    });
    params.batchNos=batchNos
    params.pageSource=this.page_type==3?1:2
      if( params.batchNos.length==0){
        this.$message.error(i18n.t("internal.finance.secondarysettlement.label30"), 5);
        return
      }
    }else if([5,6].includes(this.page_type)){

    let settleOrderIds=[]
    cancel_arr.forEach(item => {
      settleOrderIds.push(item.id)
    });
    params.settleOrderIds=settleOrderIds
    params.pageSource=this.page_type==5?1:2
      if( params.settleOrderIds.length==0){
        this.$message.error(i18n.t("internal.finance.secondarysettlement.label30"), 5);
        return
      }
    }
  api_finance.
  settlement_inquiry.rejectAudit(params)
    .then(res => {
      let code = this.$lodash.get(res, "data.code");
      let msg = this.$lodash.get(res, "data.msg");
      if (code == "0000000") {
        this.$message.success( msg);
        this.set_compensation_record_count('refresh')
        this.$emit('close_dialog',true)
      } else {
        this.$message.error(msg, 5);
      }
    });
},
    
    // 一键取消第二个弹窗的 确定按钮
    handle_begin_process() {
      if(this.cancel_confirm_text!=''){
        this.submit_data()
      }else{
        
        this.$message.warn(i18n.t("internal.second_settlement_initial_review.text__17"));
      }
    },
    },
}
  </script>
  