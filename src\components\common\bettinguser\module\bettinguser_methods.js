//  * @FilePath: src/components/common/bettinguser/module/bettinguser_methods.js
//  * @Description: 共用：投注用户管理(内/外)+投注用户白名单(对内) methods方法
import moment from "moment";
import { i18n } from "src/boot/i18n";
import { mapGetters, mapActions } from "vuex";
import { handleCopy} from "src/util/module/common.js";
import { api_user ,api_account} from "src/api/index.js";
import { tablecolumns_config as tablecolumns_config_internal } from "src/components/common/bettinguser/internal/config/config.js";//商户中心.投注用户管理(列表配置)
import { tablecolumns_config as tablecolumns_config_external} from "src/components/common/bettinguser/external/config/config.js";//账户中心.投注用户管理（直营&二级：列表配置）

const FOR_WHICH = process.env.FOR_WHICH;

export default {
    methods: {
        moment,
        ...mapActions(["clear_data"]),
        handleCopy,
        /**
         * 搜索条件重置按钮
         */
         handle_click_expand() {
          this.is_expand = !this.is_expand;
          this.$nextTick(()=>{
            this.get_max_height();
          })
        },
        /**
         * 计算列表配置项
         */
        compute_columns(){
        let columns=[]
          if(FOR_WHICH=="external"){
            //对外
            columns=tablecolumns_config_external
            if(!this.$_btn_has('userPurchase')){
              //没有合买开关权限
              columns=columns.filter(item=>item.dataIndex!=='userPurchase')
              this.channelConfig=this.channelConfig.filter(item=>item.dataIndex!=='userPurchase')
            }
          }else{
            //对内
          let route_name =   this.$route.name
          columns=tablecolumns_config_internal({route_name})
          }
          this.columns = columns
        },
        /**
         * 重置  searchForm
         */
        rebase_searchForm(){
          this.searchForm ={...this.searchForm_temp}
        },
         // 当路由改变根据路由 计算 当前  用 betting_user 还是 white_betting_users 的 页面配置
        // 或者页面创建的时候去 初始化
        init_config_by_route() {
          let rname = this.$route.name;
          if(this.src_internal){
            let fw = rname == "betting_user" ? "betting_user" : "white_betting_users";
            this.is_for_white_betting_users = fw == "white_betting_users";
          }
          this.compute_columns()
          this.rebase_searchForm()
          this.rebase_pagination()
          this.tabledata=[]
        },
        // 鼠标进入线路vip
        handle_isvip_enter(record) {
          record.show_isvip_edit_icon = true;
        },
        // 鼠标离开线路vip
        handle_isvip_leave(record) {
          record.show_isvip_edit_icon = false;
        },
        //打开线路vip编辑框
        handle_isvip_edit_icon_click(record) {
          record.isvip_editing = true;
          record.show_isvip_edit_icon = false;
        },
        //取消编辑vip
        handle_isvip_edit_cancel(record) {
          record.isvip_editing = false;
          record.show_isvip_edit_icon = false;
          record.isvip_edit  =  record.isvip ;
        },
        //线路vip修改完成
        handle_isvip_edit_complete(record) {
          record.isvip_editing = false;
          record.show_isvip_edit_icon = false;
        this.handle_update_isvipOrExternal(record)
        },
      //修改线路vip
        handle_update_isvipOrExternal(record){
        let {userId,isvip_edit} = record
        let params={
            userId :userId.toString(),
          // isVipDomain i 1或者null  否  2:是
            isVipDomain:isvip_edit
        }
        api_user.get_update_IsVipDomain(params).then(res=>{
         let {code,msg} = res.data 
         if(code=='0000000'){
           //设置成功
            this.$message.success(msg); 
         }else{
            this.$message.warn(msg); 
         }
         this.initTableData()   
        })
      },
    /**
     * @description: 转换显示来源
     * @param {*} record
     * @return {*}
     */
    compute_show_record_allowListSource(record){
      let {allowListSource=''} =record
      if(allowListSource===''){ return ''}
      let arr=  Object.values(this.import_total_source_list).filter(x=>x.value>0)
      let obj = arr.find(x=>x.value==allowListSource)||{}
      return obj.label||''
      },
        //导入白名单 弹窗控制
        handle_whitelist_import(){
           this.show_import_whitelist_dialog = true;
        },
        //关闭导入白名单弹窗
        handle_close_dialog_import_whitelist(){
         this.show_import_whitelist_dialog = false;
         this.initTableData() 
        },
        //导入vip弹窗控制
        handle_vip_import(){
           this.show_import_vip_dialog = true;
        },
        //关闭导入线路VIP弹窗
      handle_close_dialog_import_vip(){
       this.show_import_vip_dialog = false;
       this.initTableData()
        },
        /**
         * @description:   //是否C端vip下拉框切换
         * @param {*} val
         * @return {*}
         */    
        handle_is_vip(val){
        this.searchForm.isvip = val;
        },
        handle_confirm_(){
          if(this.src_internal){
            this.handle_confirm()
          }
        },
        /**
         *  表格配置 根据登录进来用户商户等级匹配不同表格配置
         */
        columns_external() {
          return [1, 10].includes(this.get_user_info.agentLevel)
            ? this.channelConfig
            :  this.columns;
        },
        on_change(dates, dateStrings) {
          if (!(dateStrings[0] && dateStrings[1])) {
            return false;
          }
          Object.assign(this.searchForm, {
            startSpecialBettingLimitTimeL: dateStrings[0] + " 00:00:00",
            endSpecialBettingLimitTimeL: dateStrings[1] + " 23:59:59",
          });
        },
        /**
         * @description 从我的消息跳转过来需要弹出编辑key弹窗
         * @return {undefined} undefined
         */
        init_params() {
          if (this.get_data) {
            this.searchForm.userId = this.get_data.uid + "";
            this.clear_data();
          }
        },
        /**
         * @description 设置c端多语言
         * @param  {Object} record 点击某行数据对象
         * @param  {Number} index 点击某行的索引
         */
        handleSetDesp(record) {
            if(this.src_external){
          let check = [1, 2, 3, 4, 6].includes(record.specialBettingLimitType);
          if (!check) {
            return false;
          }   
            }
          this.showDialogObj = { ...record };
          this.show_Desp = true;
            if(this.src_external){  
          this.dialog_desp_can_edit = false;
            }
        },
        /**
         * @description:查询 限额  备注 详情
         */
        async handle_queryUserBetLimitDetail_(detailObj) {
          //       "specialBettingLimit": {
          //   "1": "无",
          //   "2": "特殊百分比限额",
          //   "3": "特殊单注单场限额",
          //   "4": "特殊VIP限额"
          //    "5": 信用限额
          // },
          let percentageLimit_show_num = "";  //原特殊百分比
          let percent_danri_limit = "";   // 单日
          let percent_danchang_limit = "";  //单场
          let percent_danzhu_limit = "";  //单注
          let autoUpgradeRatio = "";  //自动升额度百分比
    
          let api_request =  api_account.post_order_user_queryUserBetLimitDetail  // post_order_user_queryUserBetLimitDetail 无对内接口
          if(this.src_internal){  //如果对内，走api_user.post_order_user_queryUserBetLimitDetail接口
            api_request = api_user.post_order_user_queryUserBetLimitDetail
          }
          let res = await api_request({  
            userId: detailObj.userId,
          });
          let code = this.$lodash.get(res, "data.code");
          if (code == "0000000") {
            let arr =
              this.$lodash.get(res, "data.data.rcsUserSpecialBetLimitConfigList") ||
              [];
              if (arr.length >= 1) {
                let item_list = arr.find(item => item.status)
                percentageLimit_show_num =
                (item_list.percentageLimit * 100).toFixed(2) + "%";
    
                percent_danri_limit =   (item_list.dailyPercentageLimit * 100).toFixed(2) + "%";    //单日
                percent_danchang_limit =  (item_list.singleGamePercentageLimit * 100).toFixed(2) + "%";   //单场 
                percent_danzhu_limit =   (item_list.singleBetPercentageLimit * 100).toFixed(2) + "%"; //单注 
                item_list.autoUpgradeRatio = item_list.autoUpgradeRatio?item_list.autoUpgradeRatio:0
                autoUpgradeRatio = (item_list.autoUpgradeRatio * 100).toFixed(2) + "%"; // 自动升额度百分比
              } //投注特殊限额选项
              else {
                // percent_danri_limit =percent_danri_limit?percent_danri_limit: (1 * 100).toFixed(2) + "%"; 
    
                autoUpgradeRatio = autoUpgradeRatio? autoUpgradeRatio : '0.00%'  
    
              }
          }
          return Promise.resolve({
            [`index_${detailObj._index}`]: percentageLimit_show_num,  //原百分比
            [`index_dr${detailObj._index}`]: percent_danri_limit,  //单日
            [`index_dc${detailObj._index}`]: percent_danchang_limit,  //单场
            [`index_dz${detailObj._index}`]: percent_danzhu_limit,  //单注
            [`index_autoUp${detailObj._index}`]: autoUpgradeRatio,  //自动升额度百分比
    
          });
        },
        //计算 当前列表数据中所有的 特殊百分比限额的数值
        compute_tabledata_specialBettingLimitType_2(arr) {
          this.tabledata_specialBettingLimitType_2 = {};
          let all_promise = [];
          arr.map((x) => {
            if (x.specialBettingLimitType == 2) {
              all_promise.push(this.handle_queryUserBetLimitDetail_(x));
            }
          });
          // [promise,promise,promise ]
          Promise.all(all_promise).then((res) => {
            // [{_index1:{}},{},{}]
            let obj = {};
            res.map((y) => {
              obj = {
                ...obj,
                ...y,
              };
            });
            this.tabledata_specialBettingLimitType_2 = obj;
       
          });
        },
            //计算 当前列表数据中所有的 自动升额度的值
        compute_tabledata_specialBettingLimitType_6(arr) {
          this.tabledata_specialBettingLimitType_6 = {};  
          let all_promise = [];
          arr.map((x) => {
            if (x.specialBettingLimitType == 6) {
              all_promise.push(this.handle_queryUserBetLimitDetail_(x));
            }
          });
          // [promise,promise,promise ]
          Promise.all(all_promise).then((res) => {
            // [{_index1:{}},{},{}]
            let obj = {};
            res.map((y) => {
              obj = {
                ...obj,
                ...y,
              };
            });
            this.tabledata_specialBettingLimitType_6 = obj;
       
          });
        },
        /**
         * @description 设置备注
         * @param  {Object} record 点击某行数据对象
         * @param  {Number} index 点击某行的索引
         */
        handleCloseDesp() {
          this.show_Desp = false;
          setTimeout(()=>{
            this.initTableData()
          },800)
          // this.initTableData();
        },
        close_odds_grouped(){
          this.show_user_odds_grouped = false
          this.initTableData();
        },
        close_special_delay(){
          this.show_special_delay = false
          this.initTableData();
        },
        timer_init_datalist() {
          setTimeout(()=>{
            this.initTableData()
          },800)
          // setTimeout(this.initTableData.bind(this), 800);
          // requestAnimationFrame(initTableData)
        },
        /**
         * @description: 请求列表数据
         * @return {*}
         */    
        initTableData() {
          this.tabledata_loading = true;
          let params = this.compute_init_tabledata_params();
          params = this.delete_empty_property_with_exclude(params);
          let post_order_user_queryUserBetList=this.compute_api()
          post_order_user_queryUserBetList(params).then((res) => {
            this.tabledata_loading = false;
            let code = this.$lodash.get(res, "data.code");
            if (code == "0000000") {
              let arr = this.$lodash.get(res, "data.data.list") || [];
              this.pagination.start = this.$lodash.get(res, "data.data.startRow");
              this.tabledata = this.rebuild_tabledata_to_needed(arr);
              // if(this.src_external){
              //   this.compute_tabledata_specialBettingLimitType_2(this.tabledata);
              // }
              this.compute_tabledata_specialBettingLimitType_2(this.tabledata);    // 对外接口
              this.compute_tabledata_specialBettingLimitType_6(this.tabledata);    // 对外接口
              
              this.pagination.total =
                this.$lodash.get(res, "data.data.total") * 1 || 0;
            } else {
                this.tabledata = [];
                this.$message.error(res.data.msg);
              }
          });
        },
        /**
         * @description: 计算列表请求接口
         * @return {*}
         */    
        compute_api(){
          // post_order_user_queryUserBetList 对内投注用户管理接口
          // get_order_allowlist_usert_list   对内投注用户白名单接口
          // post_admin_userRepor_queryAllUserStatisticList 对外投注用户管理接口
          if( this.src_internal){
            return this.is_for_white_betting_users ? api_user.get_order_allowlist_usert_list : api_user.post_order_user_queryUserBetList
          }else{
             return api_account.post_admin_userRepor_queryAllUserStatisticList
          }
    
        },
           rebuild_tabledata_to_needed(arr) {
           let all_can = 0;
           arr.map((item, index) => {
            item._index =
              (this.pagination.current - 1) * this.pagination.pageSize + index + 1;
            if (item.orderStatus == 0) {
              item.selected = false;
              all_can += 1;
            }
          });
          arr.map((item, index) => {
          // 是否vip 1否 2是  
            item.isvip_editing = false;
            item.show_isvip_edit_icon = false;
            item.isvip_edit =  item.isvip || 1;
          });
          return arr;
        },
        // 关闭  信用网商 弹窗
        handleCloseCreditLimit() {
          this.show_credit_limit = false;
          this.initTableData();
        },
        /*
        * 特殊延时弹框
        */
        handle_special_delay(record,type) {
          this.show_special_delay = true;
            // 这个是投注延时
            this.showDialogObj = { ...record,dialogtype:type };
            if(type==1){
              //详情
            }else if(type==2){
              //编辑
             this.showDialogObj = { ...record,dialogtype:type };
            }
        },
        // 点击特殊限额 显示特殊限额
        handle_show_special_limit(record, index,type) {
          if(type == 1){
            // 这个是赔率分组
              this.showDialogObj = { ...record,dialogtype:type };
              this.show_user_odds_grouped = true;
          }else{
            let { specialBettingLimitType } = record;
            specialBettingLimitType = Number(specialBettingLimitType);
            if (specialBettingLimitType == 5) {
              //  // 显示  信用网商 弹窗
              this.handle_show_credit_limit(record, index);
            } else {
              // 用户特殊限额为无、或者特殊百分比限额：显示”特殊限额“入口   ,显示百分比的
              this.showDialogObj = { ...record };
              this.show_Desp = true;
              this.dialog_desp_can_edit = true;
            }
          }
        },
        // 点击查询异常
        handle_show_find_abnormal(record) {
          this.show_find_abnormal = true
          this.showDialogObj = { ...record };
    
        },
            
        // 点击查询百家赔分布
        handle_show_find_baijia(record) {
          this.show_find_baijia = true
          this.showDialogObj = { ...record };

        },
    
        // 显示  信用网商 弹窗
        handle_show_credit_limit(record, index) {
          this.showDialogObj = { ...record };
          this.show_credit_limit = true;
        },
        compute_init_tabledata_params() {
          let userIdList
          let {
            userId,
            userName,
            merchantCodeList,
            specialBettingLimitType,
            startSpecialBettingLimitTimeL,
            endSpecialBettingLimitTimeL,
            currencyCode,
            marketLevel,
            esMarketLevel,
            specialBettingLimitDelayTime,
            isvip,
            allowListSource ,//来源
            timeType,  //按时间分类
            startTime,
            endTime
          } = this.searchForm;
    
          if(this.src_internal){
          userIdList=this.searchForm.userIdList;
          }
          let { current, pageSize, sort, orderBy } = this.pagination;
          let params = {
            userId, // 用户ID
            userName,
            merchantCodeList, // 多个商户编码
            specialBettingLimitType, // 投注特殊限额
            startSpecialBettingLimitTimeL: moment(
            startSpecialBettingLimitTimeL
            ).valueOf(),
            endSpecialBettingLimitTimeL: moment(
              endSpecialBettingLimitTimeL
            ).valueOf(),
            pageNum: current,
            pageSize,
            sort,
            orderBy,
            userIdList,
            currencyCode: currencyCode, //所有币种
            marketLevel,//体育赔率分组
            esMarketLevel,//电竞赔率分组
            specialBettingLimitDelayTime,
            isvip,
            allowListSource, //来源
            timeType,  //按时间分类
           beginDateTime: startTime,
           endDateTime: endTime,
          };
          if(this.src_internal){
            //当页面是投注用户白名单管理时，去除此两项参数
            if(this.is_for_white_betting_users){
              delete params.startSpecialBettingLimitTimeL
              delete params.endSpecialBettingLimitTimeL
              delete params.beginDateTime
              delete params.endDateTime
            }
          }
         
          return params;
          // if(this.src_external){
          //   if (/^\+?[0-9][0-9]*$/.test(userId) && params.userId.length > 16) {
          //     params.userId = userId;
          //     params.userName = "";
          //   } else {
          //     params.userName = userId;
          //     params.userId = "";
          //   }
          //   return params;
          // }else{
          //   console.log("====revise_userId_and_fakeNam=====",revise_userId_and_fakeName(params, "userName"))
          // return revise_userId_and_fakeName(params, "userName");
          // }
        },
        /**
         * @description 切换投注币种类型
         * @return {undefined} undefined
         */
        handle_currency(value) {
          // console.log("切换投注币种类型------------value", value);
          this.searchForm.currencyCode = value;
          if(this.src_internal){
          if (!value) {
            this.current_search_currency_text = "人民币";
          } else {
            this.current_search_currency_text =
              this.compute_currencyRate_obj_by_code(value)["countryZh"];
          }
          }
          this.initTableData();
        },
        // 点击搜索按钮
        handle_search() {
          this.initTableData();
        },
        // 全部删除
        hande_all_delete(){
        // 参数 ： 无
        // 返回：data：100  -- 踢出数量
         api_user.post_order_allowlist_user_delAll({}).then(res =>{
          this.searchForm.userId = "";
        
          this.searchForm.merchantCodeList = "";
          this.$message.info(   `已删除${  res.data?.data }条数据`)
          this.initTableData();
        });
      },
     
      //删除单条 白名单记录
      handle_confirm_delete_row(record){
        let params = {
          userId: record.userId,
        };
        api_user.post_order_allowlist_user_del(params).then((res) =>{
          this.$message.info(   `已删除${  res.data?.data }条数据`)
        this.initTableData();
        });
      },
        // 用户vip 阶级查看
        handle_vip_look(record) {
          this.$q.sessionStorage.set("record", record);
          this.$router.push({
            name: "user_vip_detail",
            query: {
              userId: record.userId,
              currencyCode:this.src_internal? this.searchForm.currencyCode: record["currencyCode"],
            },
          });
        },      
        // 查看
        handle_look(record, index) {
          this.$q.sessionStorage.set("record", record);
          this.$router.push({
            name: "betting_user_detail",
            query: {
              userId: record.userId,
              currencyCode:this.src_internal? this.searchForm.currencyCode: record["currencyCode"],
            },
          });
        },
        // 批量启&禁用
        handle_disabled_user(){
          this.show_disabled_user = true;
        },
        //批量导入开启发起用户
        handle_batch_import_starts_with_the_user(){
          this.show_batch_status_user = true;
        },
        // 关闭 批量启&禁用 弹窗
        handleCloseDisabledUser(){
          this.show_disabled_user = false
          this.show_batch_status_user=false
          if(this.timer){
            clearTimeout(this.timer)
            this.timer = null
          }
          this.timer = setTimeout(()=>{
            this.initTableData()
          },800)
        },
        // 启&禁用 开关
        async disabled_swith_user(record){
          // disabled   1 禁用 0启用  2-5 启用 
          // 修改传参 0 启用 1 禁用
          try {
            let params = {
              userIds: record.userId,
              disabled: record.disabled == 1 ? 0: 1,
            }
            const res = await api_user.post_update_Disabled(params)
            const code = this.$lodash.get(res, "data.code");
            const msg = this.$lodash.get(res, "data.msg");
            if(code == "0000000"){
              this.$message.success(msg);
              this.$set(record,"disabled", record.disabled == 1 ? 0: 1)
            }else {
              this.$message.error(msg, 5);
            }
          } catch (error) {
            console.error(error)
          }
        },
        
        /**
       * @description  通用开关 确认变更弹窗数据处理
       * @param  {Object} record 点击某行数据对象
       * @return {undefined} undefined
       */
       handle_click_vs(record, show_which) {
        this.currentRecord = record;
        this.show_which = show_which;
        this.show_which_config = {};
        switch (show_which) {
          //额度转入
          case "userTransferIn":
            this.show_which_config = {
              show_which,
              show_status: record[show_which],
              api_fn: "post_set_user_personalise_new",
              show_keyword: i18n.t("internal.template2.label41"),
            };
            break;
          //额度转出
          case "userTransferOut":
            this.show_which_config = {
              show_which,
              show_status: record[show_which],
              api_fn: "post_set_user_personalise_new",
              show_keyword: i18n.t("internal.template2.label42"),
            };
        // 合买开关
        case "userPurchase":
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "post_set_user_personalise_new",
            show_keyword: i18n.t("internal.template2.label60"),
          };
          break;
            break;
          default:
            this.show_which_config = {};
            break;
        }
        this.vsShow = true;
      },
      
      /**
       * @description  通用 弹窗 回调处理
       * @param  {Boolean} bool 是否更新成功
       * @return {undefined} undefined
       */
       async show_which_callback_fn() {
        let key = this.show_which_config["show_which"];
        // console.log("---------------- key = this.show_which_configshow_which++",key);
        let api_fn = this.show_which_config["api_fn"];
        let value = this.currentRecord[key] == 0 ? 1 : 0;
        let params = {};
       if(['userTransferIn',"userTransferOut","userPurchase"].includes(key)){
        params={
          merchantCode: this.currentRecord.merchantCode,
          uid: this.currentRecord.userId,
          userParams: {
            [key]: ""+value,
          }
          
        }
        }
        try {
          let {
            data: { code, msg },
          } = await api_user[api_fn](params);
          console.log(code, msg);
          if (code == "0000000") {
            this.$message.success(i18n.t("internal.message.label106"));
            this.currentRecord[key] = value;
            this.$forceUpdate();
          } else {
            this.$message.error(i18n.t("internal.message.label107") + `${msg}`);
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.vsShow = false;
        }
      },
         /**
         * @description 导出报表
         * @return {undefined} undefined
         */
        handle_export_excel() {
          if (this.pagination.total > 0) {
            let params = this.compute_init_tabledata_params();
            params = this.delete_empty_property_with_exclude(params);
            //当导出页面是投注用户白名单时，去除 "user-id" "app-id" 参数
              if(this.is_for_white_betting_users){
               Object.assign(
              params,
              { "pageSize": this.pagination.total },
              { url:"/order/allowlist/user/export" }
            );
            }else{
                Object.assign(
              params,
              { "pageSize": this.pagination.total },
              { "user-id": this.get_user_info.userId },
              { "app-id": this.get_user_info.appId },
              { url: "/order/user/queryUserBetListExport" }
            );
            }
            this.export_param = params;
            this.exportExcelShow = true;
          } else {
            // 暂无数据
            this.$message.error( i18n.t('external.message2.label67'))
          }
        },
        /**
         * @description: 天数快选
         * @param {*} value
         * @return {*} time
         */  
        handle_date_range(value) {
          let start_time_3 = `${moment(new Date().setDate(new Date().getDate()- 2 )).format("YYYY-MM-DD")} 00:00:00`
          let start_time_7 = `${moment(new Date().setDate(new Date().getDate()- 6)).format("YYYY-MM-DD")} 00:00:00`
          let start_time_14 = `${moment(new Date().setDate(new Date().getDate()- 13 )).format("YYYY-MM-DD")} 00:00:00`
          if(value == 1 ){
             this.searchForm.startTime = start_time_3 //3天
          }else if(value == 2){
             this.searchForm.startTime = start_time_7  //7天
          }else{
             this.searchForm.startTime = start_time_14  //14天
          }
          this.searchForm.endTime= `${moment(new Date().setDate(new Date().getDate()) + 3).format("YYYY-MM-DD")} 23:59:59` // 结束日期 
        },
       /**
        * @description: 日期选择
        * @param {*} dates
        * @param {*} dateStrings
        * @return {*} 
        */   
       on_change_time(dates, dateStrings){
        if (!(dateStrings[0] && dateStrings[1])) {
            return false;
          }
          this.searchForm.startTime = dateStrings[0]
          this.searchForm.endTime = dateStrings[1]
       },
      //  init_data() {
       
      //     api_user.post_order_user_queryUserBetLimitDetail({userId:this.detailObj.userId}).then(res => {  
      //       let code = this.$lodash.get(res, "data.code");
      //       this.tabledata_loading = false;
      //       if (code == "0000000") {
      //         this.specialBettingLimitRemark=this.$lodash.get(res, "data.data.specialBettingLimitRemark") 
      //         let arr = this.$lodash.get(res, "data.data.rcsUserSpecialBetLimitConfigList") || [];
      //         if([3,4].includes(this.detailObj.specialBettingLimitType)){
      //           arr = this.rebuild_tabledata_to_needed(arr);
      //           this.tabledata=arr.filter(x=>x.orderType==1)
      //           this.tabledata_1=arr.filter(x=>x.orderType==2)
      //         }else if(this.detailObj.specialBettingLimitType==2&&arr.length==1){
      //           this.percentageLimit=arr[0].percentageLimit*100+"%"
      //         }
      //       }
      //     });
      //   },
      },
}