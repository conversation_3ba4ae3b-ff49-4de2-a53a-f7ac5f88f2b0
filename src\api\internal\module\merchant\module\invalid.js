/*
 * @Desc:
 * @Date: 2019-12-28 20:59:34
 * @Author:Nice
 * @FilePath: /src/api/internal/module/merchant/index.js
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_4;
let prefix2 = process.env.API_PREFIX_17
let prefix3 = process.env.API_PREFIX_3;



// 失效商户管理-修改客户端状态
export const update_invalid_management_status = (
    params,
    url = "/order/reportMatch/setValidMerchant"
  ) => axios.post(`${prefix3}${url}`, params);


//失效商户管理-查询列表
export const get_invalid_management_list = (
    params,
    url = "/order/reportMatch/queryValidMerchantsList"
    ) => axios.post(`${prefix3}${url}`, params);

//失效商户管理-历史记录查询
export const get_invalid_management_log = (
    params,
    url = "/order/reportMatch/getValidMerchantHistoryList"
    ) => axios.post(`${prefix3}${url}`, params);
