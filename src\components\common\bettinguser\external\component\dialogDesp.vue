<!--
 * @Desc: 对外-账户中心-投注用户管理-特殊限额
 * @Date: 2019-12-25 15:49:45
 * @Author:Nice
 * @FilePath: /src/components/common/bettinguser/external/component/dialogDesp.vue
 -->
<template>
  <div style="height:auto;max-width:700px;overflow:hidden;" class="text-panda-text-7">
    <q-card class="bg-white text-black">
      <q-card-section class="no-padding">
        <div style="height: 40px;" class="row line-height-40px fs14 bg-panda-dialog text-panda-text-7 pr10x">
          <!-- 查看情况下   非 信用 模式 通用  非 编辑状态   仅仅查看状态 1无       specialBettingLimitType    ：  1   2  3 4    -->
          <div class="pl20x fw_600 col q-pr-md" v-if="!can_edit && ![2,6].includes(detailObj.specialBettingLimitType)">
            <a-tooltip trigger="hover">
              <!-- 对特殊VIP限额、特殊单注单场限额、单注限额百分比的用户限额需求，需联系B端风控 -->
              <template slot="title"> {{ $t("internal.betting.label2") }}</template>
              <a-icon type="question-circle" class=" fs15" />
            </a-tooltip>
            {{ specialBettingLimit_title[detailObj.specialBettingLimitType] }}
            <a-input v-model="percentageLimit"
              v-if="detailObj.specialBettingLimitType == 2 && [1].includes(detailObj.specialBettingLimitType)"
              style="width:70px;margin-top:7px" readonly disabled class="disabled-white" />
          </div>
          <!-- 查看情况下   非 信用 模式 通用  非 编辑状态   仅仅查看状态 2特殊限额       specialBettingLimitType    ：  1   2  3 4    -->
          <div class="pl20x fw_600 col q-pr-md" v-if="!can_edit && [2,6].includes(detailObj.specialBettingLimitType)">
            <a-tooltip trigger="hover">
              <!-- 对特殊VIP限额、特殊单注单场限额、单注限额百分比的用户限额需求，需联系B端风控 -->
              <template slot="title"> {{ $t("internal.betting.label3") }}</template>
              <a-icon type="question-circle" class=" fs15" />
            </a-tooltip>
            {{ $t("internal.betting.label4") }}
            <!-- {{ specialBettingLimit_title[detailObj.specialBettingLimitType] }} -->
          </div>

          <!-- 编辑情况下      //   "2": "特殊百分比限额",  -->
          <div class="pl20x fw_600 col q-pr-md" v-if="
            can_edit && [1,2, 6].includes(detailObj.specialBettingLimitType)
            // can_edit && is_specialBettingLimitType_2

            ">
            <!-- 对特殊VIP限额、特殊单注单场限额、单注限额百分比的用户限额需求 -->
            <a-tooltip trigger="hover">
              <template slot="title">
                {{ $t("internal.betting.label3") }}
              </template>
              <a-icon type="question-circle" class=" fs15" />
            </a-tooltip>
            <!-- 投注特殊限额 -->
            {{ $t("internal.betting.label4") }}
          </div>
          <q-space></q-space>
          <!-- style="margin-top: -40px;" -->
          <div class="col-1 justify-end" style="position: absolute; top:0px; right: 5px;" >
            <q-btn class="mr5x text-panda-dialog-close" icon="close" v-close-popup />
          </div>

        </div>
      </q-card-section>

      <q-separator></q-separator>
      <div></div>

      <div class=" pt10x pl20x pr20x pb20x fixed-footer">
        <!-- 只能查看  2特殊限额 6自动升额度 -->
        <div style="font-size: 14px;" v-if="
          !can_edit && [2,6].includes(detailObj.specialBettingLimitType)
          // !can_edit && is_specialBettingLimitType_2
          ">
       
            <dialogAutoAddLimit 
              :disabled="true" 
              :detailObj="detailObj"
              :percent_limit_obj="percent_limit_obj"
              ></dialogAutoAddLimit>
        </div>

        <!-- 可以编辑 2特殊限额  6自动升额度  -->
        <div style="font-size: 14px;" v-if="
          can_edit && [1,2,6].includes(detailObj.specialBettingLimitType)
          // can_edit &&  is_specialBettingLimitType_2
          ">
 
            <dialogAutoAddLimit
              :disabled="false" 
              :detailObj="detailObj"
              :percent_limit_obj="percent_limit_obj"
              ref="autoAddLimit"
              @handleChange = "handleChangeLimit(arguments)"
              ></dialogAutoAddLimit>
              <!-- @handleChange = "percentageLimit = $event" -->

        </div>

        <!-- //   "3": "特殊单注单场限额",    //   "4": "特殊VIP限额" -->

        <a-table v-if="[3, 4].includes(detailObj.specialBettingLimitType) &&
          tabledata.length > 0
          " :loading="tabledata_loading" :columns="columns" :dataSource="tabledata" :scroll="{ x: 400 }" size="small"
          rowKey="id">
        </a-table>

        <a-table v-if="[3, 4].includes(detailObj.specialBettingLimitType) &&
          tabledata_1.length > 0
          " class="small-table-type-1" :loading="tabledata_loading" :columns="columns_2" :dataSource="tabledata_1"
          :scroll="{ x: 400 }" size="small" rowKey="id">
        </a-table>
        <!-- 注意：早盘、滚球分别计算，都不能超过这里设定的特殊限额。   -->
        <div class="row mt20x mb20x"
          v-if="detailObj.specialBettingLimitType == 3 || detailObj.specialBettingLimitType == 4">
          <div class=" text-panda-text-7 mb12x fw_600 " style="min-width:100px">
            <span class="panda-text-red "> {{ $t("external.account.betting.label8") }}</span>
          </div>
        </div>

        <!-- 备注   -->
        <div class="row mt20x mb20x">
          <div class=" text-panda-text-7 mb12x fw_600 col" style="min-width:60px">
            <span class="panda-text-red ">*</span>
            <span> {{ $t("external.account.betting.label2") }}：</span>
          </div>
          <!-- 请说明限额原因   -->
          <div class="append-handle-btn-input  " style="width: 420px;">
            <a-textarea v-model.trim="specialBettingLimitRemark" :read-only="!can_edit" :disabled="!can_edit"
              class="disabled-white" :autoSize="{ minRows: 3, maxRows: 3 }" />
          </div>
        </div>

        <!-- 确定和返回 按钮  -->

        <div v-if="can_edit" class="row justify-end">
          <q-btn class="panda-btn-primary-dense bg-primary ml20x" style="width:80px;height:30px; "
            :label="$t('external.betting_.label3')" v-close-popup />

          <!--确定-->
          <q-btn class="panda-btn-primary-dense bg-primary ml20x" style="width:80px;height:30px; "
            :label="$t('external.betting_.label4')" @click="handle_post_userReport_updateRcsLimit" />
        </div>
      </div>
    </q-card>
  </div>
</template>
<script>
import { i18n } from "src/boot/i18n";
import { api_account } from "src/api/index.js";
import {
  despconfig_1,
  despconfig_2
} from "src/components/common/bettinguser/external/config/despconfig.js";
import commonmixin from "src/mixins/external/common/commontoolmixin.js";
import dialogSpecialBetLimit from "./dialogSpecialBetLimit.vue"
import dialogAutoAddLimit from "./dialogAutoAddLimit.vue"
export default {
  mixins: [commonmixin],
  components: { 
    dialogSpecialBetLimit,
    dialogAutoAddLimit
  },
  data() {
    return {
      columns: despconfig_1,
      columns_2: despconfig_2,
      tabledata_loading: false,
      loading: false,
      specialBettingLimit_title: i18n.t(
        "internal.betting.specialBettingLimit_title"
      ),
      sportId_name: i18n.t("external.merchant.bettinguser.index.sportId_name"),
      sportId_name_2: i18n.t(
        "external.merchant.bettinguser.index.sportId_name_2"
      ),
      //详情
      specialBettingLimitRemark: "",
      //百分比
      percentageLimit: 100,
      percent_danri_limit:100, //单日百分比
      percent_danchang_limit:100, //单场百分比
      percent_danzhu_limit:100, //单注百分比
      percentag_dr:100, //单日百分比限制最大值
      percentag_dc:100, //单场百分比限制最大值
      percentag_dz:100, //单注百分比限制最大值

      percentageLimit_arr: [1, 2, 5, 10, 25, 50, 75, 100],

      tabledata: [],
      tabledata_1: [],
      special_percentage: i18n.t('internal.betting.label10'),  // 特殊百分比radio
      automatic_limit: i18n.t('internal.betting.label11'),  // 自动升额度radio
      add_limit:true, //自动升额度开关
      autoUpgradeRatio: 100 //自动升额度百分比
    };
  },
  props: {
    detailObj: {
      type: Object,
      default() {
        return {};
      }
    },
    // can_edit:{
    //   type:Boolean,
    //   default:false
    // },
    //   "1": "无", //   "2": "特殊百分比限额",    才能编辑
    can_edit: false
  },
  created() {
    this.init_data();

  },
  computed: {
    // 2 判断是否特殊限额
     is_specialBettingLimitType_2() {
       return this.detailObj.specialBettingLimitType == 2
     },
     percent_limit_obj() {
      
      let obj = {
        percentageLimit:this.percentageLimit, //原百分比
        percent_danri_limit:this.percent_danri_limit, //单日百分比
        percent_danchang_limit:this.percent_danchang_limit, //单场百分比
        percent_danzhu_limit:this.percent_danzhu_limit, //单注百分比

        percentag_dr:this.percentag_dr, //单日百分比限制最大值
        percentag_dc:this.percentag_dc, //单场百分比限制最大值
        percentag_dz:this.percentag_dz, //单注百分比限制最大值
        add_limit:this.add_limit, //自动升额度
        autoUpgradeRatio:this.autoUpgradeRatio, //自动升额度百分比
        initData:this.init_data
        
      }
      return obj
     },

  },
  methods: {
    //       "specialBettingLimit": {
    //   "1": "无",
    //   "2": "特殊百分比限额",

    //   "3": "特殊单注单场限额",
    //   "4": "特殊VIP限额"
    //    "5": 信用限额
    // },

    /**
     * @description:查询详情
     */
    init_data() {
      this.tabledata_loading = true;
      api_account
        .post_order_user_queryUserBetLimitDetail({
          userId: this.detailObj.userId
        })
        .then(res => {
          let code = this.$lodash.get(res, "data.code");
          this.tabledata_loading = false;
          if (code == "0000000") {
            this.specialBettingLimitRemark = this.$lodash.get(
              res,
              "data.data.specialBettingLimitRemark"
            );
            let arr =
              this.$lodash.get(
                res,
                "data.data.rcsUserSpecialBetLimitConfigList"
              ) || [];
            if ([3, 4].includes(this.detailObj.specialBettingLimitType)) {
              //   "3": "特殊单注单场限额", //   "4": "特殊VIP限额"
              arr = this.rebuild_tabledata_to_needed(arr);
              this.tabledata = arr.filter(x => x.orderType == 1);
              this.tabledata_1 = arr.filter(x => x.orderType == 2);
            } 
            else if ( [ 1].includes(this.detailObj.specialBettingLimitType)  ) {
                let  item_list = {}
                item_list.dailyPercentageLimit = 1
                item_list.singleGamePercentageLimit =  1
                item_list.singleBetPercentageLimit = 1
                this.percent_danri_limit =   (item_list.dailyPercentageLimit * 100).toFixed(2) + "%";    //单日
              this.percent_danchang_limit =  (item_list.singleGamePercentageLimit * 100).toFixed(2) + "%";   //单场 
              this.percent_danzhu_limit =   (item_list.singleBetPercentageLimit * 100).toFixed(2) + "%"; //单注 

            }
            
            else if (
              this.detailObj.specialBettingLimitType == 2 
            ) {
              let item_list = arr.find(item => item.status);
              this.percentageLimit =
                (item_list.percentageLimit * 100).toFixed(2) + "%";
              this.percent_danri_limit =   (item_list.dailyPercentageLimit * 100).toFixed(2) + "%";    //单日
              this.percent_danchang_limit =  (item_list.singleGamePercentageLimit * 100).toFixed(2) + "%";   //单场 
              this.percent_danzhu_limit =   (item_list.singleBetPercentageLimit * 100).toFixed(2) + "%"; //单注 
              
            }

            else if (
                this.detailObj.specialBettingLimitType == 6
            ) {
              if(arr.length < 1) { //当arr是空项，说明是type1 无限额
                this.autoUpgradeRatio =  (0).toFixed(2) + "%";  //自动升额度百分比

              }
              else {
                let item_list = arr.find(item => item.status);
                item_list.autoUpgradeRatio = item_list.autoUpgradeRatio?item_list.autoUpgradeRatio: 0
                this.autoUpgradeRatio =  (item_list.autoUpgradeRatio * 100).toFixed(2) + "%";  //自动升额度百分比
              }
            }

          }
        });
      api_account
        .get_order_user_getUserTradeRestrict({  //根据用户Id查看用户延时赛种
          userId: this.detailObj.userId,
        })
        .then((res) => {
          let code = this.$lodash.get(res, "data.code");
          let data = this.$lodash.get(res, "data.data");
          if (code == "0000000") {

            let percentag = data.percentageLimit //原最大百分比

            this.percentag_dr = data.dailyPercentageLimit; //单日百分比
            this.percentag_dc = data.singleGamePercentageLimit; //单场百分比
            this.percentag_dz = data.singleBetPercentageLimit; //单注百分比
            if (percentag && percentag !== 0) {
              let percentageLimit = (percentag * 100).toFixed(2)
              let item_index = this.percentageLimit_arr.findIndex(el => el >
                percentageLimit
              );
              if (item_index >= 0) {
                this.percentageLimit_arr.splice(
                  item_index,
                  this.percentageLimit_arr.length - item_index
                );
              }
            }

          } else {
            this.$message.error(res.data.msg);
          }
        });

    },
    getParams(){
      let params = { };
      // "3": "特殊单注单场限额"   "4": "特殊VIP限额"
      let current_type = this.detailObj.specialBettingLimitType 
      if ([3, 4].includes(current_type)) {
        params.userId = this.detailObj.userId;
        params.specialBettingLimit = this.detailObj.specialBettingLimitType;
        params.remarks = this.specialBettingLimitRemark;
      } 
      else if([1, 2].includes(current_type)){  //特殊百分比限额
        let modelObj  =  this.$refs.autoAddLimit.modelObj;
        
        let percentageLimit = modelObj.percentageLimit.toString().replace("%", "")
        let percent_danri_limit = modelObj.percent_danri_limit.toString().replace("%", "") //单日
        let percent_danchang_limit = modelObj.percent_danchang_limit.toString().replace("%", "") //单场
        let percent_danzhu_limit = modelObj.percent_danzhu_limit.toString().replace("%", "") //单注

        params.userId = this.detailObj.userId;
        params.specialBettingLimit = 2;
        params.percentage = percentageLimit * 1;
        params.dailyPercentageLimit = percent_danri_limit * 1;  //单日
        params.singleGamePercentageLimit = percent_danchang_limit * 1; //单场
        params.singleBetPercentageLimit = percent_danzhu_limit * 1;  //单注
        
        params.remarks = this.specialBettingLimitRemark;
      }
      else if(current_type == 6){  // 自动升额度
        let modelObj  =  this.$refs.autoAddLimit.modelObj;
        let autoUpgradeStatus  =  this.$refs.autoAddLimit.autoUpgradeStatus ? 1 : 0;
        params.userId = this.detailObj.userId;
        if(!autoUpgradeStatus) {
          params.autoUpgradeStatus = autoUpgradeStatus;
          params.specialBettingLimit = 1;
          params.remarks = this.specialBettingLimitRemark; //备注
        } else {
          let modelObj  =  this.$refs.autoAddLimit.modelObj;
          let percentageLimit = modelObj.percentageLimit.toString().replace("%", "")
          let percent_danri_limit = modelObj.percent_danri_limit.toString().replace("%", "") //单日
          let percent_danchang_limit = modelObj.percent_danchang_limit.toString().replace("%", "") //单场
          let percent_danzhu_limit = modelObj.percent_danzhu_limit.toString().replace("%", "") //单注
          params.percentage = percentageLimit * 1;
          params.dailyPercentageLimit = percent_danri_limit * 1;  //单日
          params.singleGamePercentageLimit = percent_danchang_limit * 1; //单场
          params.singleBetPercentageLimit = percent_danzhu_limit * 1;  //单注

          params.remarks = this.specialBettingLimitRemark; //备注
          params.specialBettingLimit = 2;

        }
      }
      return params;
     },
    /**
     * 更新 限额
     */
    handle_post_userReport_updateRcsLimit() {
      let final_params = this.getParams();
        // {"rcsUserConfigVo":{"userId":"169570651347558400","specialBettingLimit":2,[]}]}

      if(this.detailObj.specialBettingLimitType == 2 ){

        let modelObj  =  this.$refs.autoAddLimit.modelObj;
        
        final_params = this.getParams(modelObj);
        if (!final_params.dailyPercentageLimit) {
        //单日百分比必须设置
        this.$message.warn(i18n.t('internal.common.w6'));
        return false;
        }
        if (!final_params.singleGamePercentageLimit) {
          //单场百分比必须设置
          this.$message.warn(i18n.t('internal.common.w6'));
          return false;
        }
        if (!final_params.singleBetPercentageLimit) {
          //单注百分比必须设置
          this.$message.warn(i18n.t('internal.common.w6'));
          return false;
        }
        
      }


      // if (!final_params.percentage) {
      //   //百分比必须设置
      //   this.$message.warn(i18n.t('internal.common.w6'));
      //   return false;
      // }

      // if(this.detailObj.specialBettingLimitType == 2){
      //   if (!final_params.dailyPercentageLimit) {
      //   //单日百分比必须设置
      //   this.$message.warn(i18n.t('internal.common.w6'));
      //   return false;
      //   }
      //   if (!final_params.singleGamePercentageLimit) {
      //     //单场百分比必须设置
      //     this.$message.warn(i18n.t('internal.common.w6'));
      //     return false;
      //   }
      //   if (!final_params.singleBetPercentageLimit) {
      //     //单注百分比必须设置
      //     this.$message.warn(i18n.t('internal.common.w6'));
      //     return false;
      //   }
      // }
 

      if (!final_params.remarks) {
        //备注必须填写
        this.$message.warn(i18n.t('internal.common.w7'));
        return false;
      }

      var params = {
        rcsUserConfigVo:final_params
      }
      // return
      api_account.post_userReport_updateRcsLimit(params).then(res => {
        this.$emit("handleCloseDesp");
      });
 
      this.$emit('updatelimits')
    },
    /**
     * @description:将值扩展
     * @param {type}
     * @return {type}
     */
    rebuild_tabledata_to_needed(arr) {
      let new_arr = {};
      let new_arr_2 = {};
      arr.map((item, index) => {
        if (item.orderType == 1) {
          new_arr[item.sportId + ""] = item;
        } else {
          new_arr_2[item.sportId + ""] = item;
        }
        item.sportId =
          item.orderType == 1
            ? this.sportId_name[item.sportId + ""]
            : this.sportId_name_2[item.sportId + ""];
      });
      //specialBettingLimitType==4的情况下,返回的数据有可能不是固定5条
      let new_arr_3 = [
        new_arr["-1"] || {
          id: 1,
          orderType: 1,
          sportId: this.sportId_name["-1"]
        },
        new_arr["1"] || {
          id: 2,
          orderType: 1,
          sportId: this.sportId_name["1"]
        },
        new_arr["2"] || {
          id: 3,
          orderType: 1,
          sportId: this.sportId_name["2"]
        },
        new_arr["0"] || {
          id: 4,
          orderType: 1,
          sportId: this.sportId_name["0"]
        },
        new_arr_2["-1"] || {
          id: 5,
          orderType: 2,
          sportId: this.sportId_name_2["-1"]
        }
      ];
      arr = new_arr_3;
      return arr;
    },
    handleChangeLimit(data) {
      if(data[1]== 'dr'){
        this.percent_danri_limit = data[0]
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.fixed-footer ::v-deep .ant-pagination {
  display: none;
}

.fixed-footer .small-table-type-1 ::v-deep .ant-table {
  border-top: none;
}

.fixed-footer ::v-deep .ant-table-thead>tr>th {
  background: #f4f5f8 !important;
}

.fixed-footer ::v-deep .ant-table-thead>tr>th,
.fixed-footer ::v-deep .ant-table-tbody>tr>td {
  border-left: 1px solid #e8e8e8;
}

.fixed-footer ::v-deep .ant-table-body {
  overflow-x: hidden !important;
}

.fixed-footer ::v-deep .ant-table {
  border-left: none !important;
}
</style>
