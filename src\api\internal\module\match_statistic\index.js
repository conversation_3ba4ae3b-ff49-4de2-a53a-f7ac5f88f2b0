/*
 * @FilePath: /src/api/internal/module/match_statistic/index.js
 * @Description: 即时赛事统计报表
 */

import axios from "src/api/common/axioswarpper.js";
let prefix3 = process.env.API_PREFIX_3;

//下拉联赛
export const query_pull_down_tournament = (
  params,
  url = "/order/reportMatch/tournamentDataList"
) => axios.post(`${prefix3}${url}`, params);

// 即时赛事统计报表-单个赛事多币种
export const query_MatchStatisticById = (
  params,
  url = "/order/reportMatch/currencyDataList"
) => axios.post(`${prefix3}${url}`, params);

// //赛事投注统计(主列表-对内)
export const query_MatchStatisticList = (
  params,
  url = "/order/reportMatch/reportDateList"
) => axios.post(`${prefix3}${url}`, params);

// 玩法投注统计
export const query_PlayStatisticList = (
  params,
  // url = "/order/match/queryPlayStatisticList"
  url = "/order/reportMatch/marketBetDetailList"
) => axios.post(`${prefix3}${url}`, params);

// 赛事投注统计-多币种汇总查询（对内）
export const post_order_currency_summary_query = (
  params,
  url = "/order/reportMatch/currencySum"
) => axios.post(`${prefix3}${url}`, params);

// 即时赛事统计报表---导出报表---导出赛事统计
export const post_export_report_excel = (params, url="/order/reportMatch/matchBetReportDataExport") => {
  return axios.post(`${prefix3}${url}`, params, {
    responseType: "blob"
  });
}

// 即时赛事统计报表---导出报表---导出玩法统计
export const post_export_markets_report_excel = (params, url = "/order/reportMatch/marketBetReportDataExport")=> {
  return axios.post(`${prefix3}${url}`, params, {
    responseType: "blob",
  });
}

// 即时赛事统计报表-获取比赛阶段select框数据
export const get_match_type = (
  params,
  url = "/order/reportMatch/getMatchType"
) => axios.get(`${prefix3}${url}`, { params: { ...params } });

// 赛事投注统计/即时赛事统计报表-获取操盘方（对内）
export const post_trader_select_list = (
  params,
  url = "/order/reportMatch/getManagerCodeDictInfo"
) => axios.post(`${prefix3}${url}`, params);