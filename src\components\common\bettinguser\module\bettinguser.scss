
//  * @FilePath: src/components/common/bettinguser/module/bettinguser.scss
//  * @Description: 共用：投注用户管理(内/外)+投注用户白名单(对内) css样式

.bettinguser{

	::v-deep .ant-empty-normal {
	   margin: 314px 0;
   }
::v-deep .ant-spin-nested-loading > div > .ant-spin {
	max-height: 760px;
	min-height: 760px;
}
.ellipsis {
	display: block;
	max-width: 160px;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}
.no-left ::v-deep  .q-field__append {
	border-left: none;
}
::v-deep .ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-thead > tr > th {
	background: #f4f5f8;
}
::v-deep .ant-table-middle > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-thead > tr > th {
	background: #f4f5f8;
}
::v-deep .ant-table-middle > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-thead > tr > th {
	background: #f4f5f8;
}
::v-deep .info:hover {
	text-decoration: underline;
}
::v-deep .user .ant-input-affix-wrapper .ant-input-suffix {
	right: 5px;
}
::v-deep .bet:hover {
	text-decoration: underline;
}
.table-svg{
 font-size: 16px;
 width: 20px;
 height: 20px;
 margin-left: 6px;
}
.duihao {
	font-size: 20px;
	color: #f00;
}
.guanbi {
	font-size: 20px;
	color: #008000;
}
.specail_limit{
 width: 200px;
 display: flex;
 // .specail_limit{
 //   padding-top: 20px;
   
 // }
 span{
   width: 150px;
   // vertical-align:20%;
 }
}

/* 启&禁用 备注 tooltip提示框样式 */
.tooltip-content-remark-text  {
	max-width: 350px;
	word-break: break-all;
  }
}