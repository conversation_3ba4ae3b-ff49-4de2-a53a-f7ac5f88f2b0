div#driver-page-overlay { opacity: 0.3 !important; } div#driver-popover-item { display: none; position: absolute; background: #000; opacity: 0.8; color: #fff; margin: 0; padding: 15px; border-radius: 5px; min-width: 250px; max-width: 300px; box-shadow: 0 1px 10px rgba(0, 0, 0, 0.4); z-index: 1000000000; } div#driver-popover-item .driver-popover-tip { border: 5px solid #000; content: ""; position: absolute; } div#driver-popover-item .driver-popover-tip.bottom { bottom: -10px; border-color: #000 transparent transparent; } div#driver-popover-item .driver-popover-tip.bottom.position-center { left: 49%; } div#driver-popover-item .driver-popover-tip.bottom.position-right { right: 20px; } div#driver-popover-item .driver-popover-tip.left { left: -10px; top: 10px; border-color: transparent #000 transparent transparent; } div#driver-popover-item .driver-popover-tip.left.position-center { top: 46%; } div#driver-popover-item .driver-popover-tip.left.position-bottom { top: auto; bottom: 20px; } div#driver-popover-item .driver-popover-tip.right { right: -10px; top: 10px; border-color: transparent transparent transparent #000; } div#driver-popover-item .driver-popover-tip.right.position-center { top: 46%; } div#driver-popover-item .driver-popover-tip.right.position-bottom { top: auto; bottom: 20px; } div#driver-popover-item .driver-popover-tip.top { top: -10px; border-color: transparent transparent #333333; } div#driver-popover-item .driver-popover-tip.top.position-center { left: 49%; } div#driver-popover-item .driver-popover-tip.top.position-right { right: 20px; } div#driver-popover-item .driver-popover-tip.mid-center { display: none; } div#driver-popover-item .driver-popover-footer { display: block; margin-top: 10px; } div#driver-popover-item .driver-popover-footer button { display: inline-block; padding: 3px 10px; border: 1px solid #333; text-decoration: none; text-shadow: 1px 1px 0 #333; color: #1890ff; font: 14px / normal sans-serif; cursor: pointer; outline: 0; background-color: #333; border-radius: 2px; zoom: 1; line-height: 1.3; } div#driver-popover-item .driver-popover-footer button.driver-disabled { color: grey; cursor: default; pointer-events: none; } div#driver-popover-item .driver-popover-footer .driver-close-btn { float: left; } div#driver-popover-item .driver-popover-footer .driver-btn-group, div#driver-popover-item .driver-popover-footer .driver-close-only-btn { float: right; } div#driver-popover-item .driver-popover-title { font: 16px / normal sans-serif; margin: 0 0 5px; font-weight: 400; display: block; position: relative; line-height: 1.5; zoom: 1; } div#driver-popover-item .driver-popover-description { margin-bottom: 0; font: 14px / normal sans-serif; line-height: 1.5; color: #2d2d2d; font-weight: 400; zoom: 1; } .driver-clearfix:after, .driver-clearfix:before { content: ""; display: table; } .driver-clearfix:after { clear: both; } .driver-stage-no-animation { -webkit-transition: none !important; -moz-transition: none !important; -ms-transition: none !important; -o-transition: none !important; transition: none !important; background: transparent !important; outline: 5000px solid rgba(0, 0, 0, 0.75); } div#driver-page-overlay { background: #333; position: fixed; bottom: 0; right: 0; display: block; width: 100%; height: 100%; zoom: 1; filter: alpha(opacity=30); opacity: 0.3; z-index: 100002 !important; -webkit-filter: alpha(opacity=30); } div#driver-highlighted-element-stage, div#driver-page-overlay { top: 0; left: 0; -webkit-transition: all 0.3s; -moz-transition: all 0.3s; -ms-transition: all 0.3s; -o-transition: all 0.3s; transition: all 0.3s; } div#driver-highlighted-element-stage { position: absolute; height: 50px; width: 300px; background: #fff; z-index: 100003 !important; display: none; border-radius: 2px; -webkit-border-radius: 2px; -moz-border-radius: 2px; -ms-border-radius: 2px; -o-border-radius: 2px; } .driver-highlighted-element { z-index: 100004 !important; } .driver-position-relative { position: relative !important; } .driver-fix-stacking { z-index: auto !important; opacity: 1 !important; -webkit-transform: none !important; -moz-transform: none !important; -ms-transform: none !important; -o-transform: none !important; transform: none !important; -webkit-filter: none !important; -moz-filter: none !important; -ms-filter: none !important; -o-filter: none !important; filter: none !important; -webkit-perspective: none !important; -moz-perspective: none !important; -ms-perspective: none !important; -o-perspective: none !important; perspective: none !important; -webkit-transform-style: flat !important; -moz-transform-style: flat !important; -ms-transform-style: flat !important; transform-style: flat !important; -webkit-transform-box: border-box !important; -moz-transform-box: border-box !important; -ms-transform-box: border-box !important; -o-transform-box: border-box !important; transform-box: border-box !important; will-change: unset !important; }