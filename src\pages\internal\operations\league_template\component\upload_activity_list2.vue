<!--
 * @FilePath: src/pages/internal/operations/activity_manage/component/upload_activity_list2.vue
 * @Description: 运营管理/活动管理/活动列表—logo上传
-->
<template>
  <div class="clearfix">
    <a-upload list-type="picture-card" :file-list="fileList" :action="get_upload_url" @preview="handlePreview"
      @change="handleChange" :beforeUpload="beforeUpload" :data="{flag:0}"
      :disabled="is_view"
    >
      <div v-if="fileList.length < 1">
        <a-icon type="plus" />
        <!-- 上传logo        (可添加一张) -->
        <div class="ant-upload-text">
         {{ $t('internal.activity_manage.config2.t29') }}<br>
         {{ $t('internal.activity_manage.config2.t32') }}
    
        </div>
      </div>
    </a-upload>
    <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
      <img alt="example" style="width: 100%" :src="previewImage" />
    </a-modal>
  </div>
</template>
<script>
import { API_DOMIN } from "src/pages/internal/web/config/other_domain.js";

function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}
export default {
  props: {
    defaultFileList: {
      type: Array,
      default() {
        return [];
      }
    },
    is_view: {
      type: Boolean,
      default() {
        return false;
      }
    }
  },
  computed: {
    get_upload_url() {
      return API_DOMIN+"/file/fastdfs/upload/file";
      // let url = "http://api.sportxxxifbdxm2.com/file/fastdfs/upload/file";
      // if (['idc_sandbox', 'idc_pre'].includes(process.env.FRONT_WEB_ENV)) {
      //   url = 'https://api.dbsportxxxwo8.com/file/fastdfs/upload/file'
      // }
      // return url;
    },
  },
  data() {
    return {
      previewVisible: false,
      previewImage: '',
      fileList: [],
      validFile: true,   // 是否是有效文件
    };
  },
  watch: {
    defaultFileList() {
      this.fileList = this.defaultFileList;
    }
  },
  methods: {
    handleCancel() {
      this.previewVisible = false;
    },
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
      this.previewImage = file.url || file.preview;
      this.previewVisible = true;
    },
    beforeUpload(file, fileList) {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
       //只能上传Jpg或者JEPG或者Png格式的图片!
        this.$message.error(this.$t("internal.common.e3"));
      }
      const isLt10K = file.size / 1024  < 10;
      if (!isLt10K) {
        // 图片必须小于10KB!
         this.$message.error('图片必须小于10KB!');
      }
      const isLimitLen = this.fileList.length < 1;
      if (!isLimitLen) {
     //最多只支持上传1张图片!
      this.$message.error(this.$t("internal.common.e4"));
      }
      this.validFile = isJpgOrPng && isLt10K && isLimitLen;
      return isJpgOrPng && isLt10K;
    },
    getThumbnailsData(fileList) {
      let thumbnailsUrls = [];
      fileList.forEach((item) => {
        if (item.isDefault) {
          thumbnailsUrls.push(item.defaultUrl);
        } else if (item.response && item.response.filePath) {
          thumbnailsUrls.push(item.response.filePath);
        }
      });
      return thumbnailsUrls;
    },
    handleChange({ fileList }) {
      if (this.validFile) {
        this.fileList = fileList;
        this.$emit("onImgListUpdate", this.getThumbnailsData(fileList));
      }
    },
  },
};
</script>
<style lang="scss" scoped>
/* you can make up upload button and sample style by using stylesheets */
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
