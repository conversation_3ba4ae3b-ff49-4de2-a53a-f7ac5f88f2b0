
import axios from "src/api/common/axioswarpper.js";

let prefix1 = process.env.API_PREFIX_17;




//结算查询-二次结算汇总商户列表（新接口）POST
export const getSummaryMerchantList = (
  params,
  url = "/admin/orderTimeSettle/getSummaryMerchantList"
  ) => axios.post(`${prefix1}${url}`, params);

  
//结算查询-二次结算汇总列表（新接口）POST
export const getSummaryList = (
  params,
  url = "/admin/orderTimeSettle/getSummaryList"
  ) => axios.post(`${prefix1}${url}`, params);

  
//查看驳回原因接口（新接口）POST
export const getRejectReasonList = (
  params,
  url = "/admin/orderTimeSettle/getRejectReasonList"
  ) => axios.post(`${prefix1}${url}`, params);

  
  
//获取未读记录数
export const getRecordCounts = (
  params,
  url = "/admin/orderTimeSettle/getRecordCounts"
  ) => axios.post(`${prefix1}${url}`, params);

  
//重置记录数
export const resetRecordCounts = (
  params,
  url = "/admin/orderTimeSettle/resetRecordCounts"
  ) => axios.post(`${prefix1}${url}`, params);