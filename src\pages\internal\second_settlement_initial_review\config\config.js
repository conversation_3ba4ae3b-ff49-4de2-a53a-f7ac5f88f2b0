/*
 * @FilePath: /src/pages/internal/data/front_end_domain_query/config/config.js
 * @Description: 数据中心/前端域名查询
 */

import { i18n } from "src/boot/i18n";
// 前端域名查询表格配置
export const tablecolumns_config = [
  {
    width: 80,
    dataIndex: "all_select",
    key: "all_select",
    fixed: "left",
    align: "center",
    scopedSlots: {
      customRender: "all_select",
    },
  },
  {
    title: i18n.t("internal.second_settlement_initial_review.text__10"), // 序号
    key: "_index",
    width: 50,
    align: "center",
    dataIndex: "_index",
  },
  {
    title: i18n.t("internal.second_settlement_initial_review.text__3"), // 申请审批日期
    key: "applicationTime",
    align: "center",
    dataIndex: "applicationTime",
    scopedSlots: {
      customRender: "applicationTime",
    },
  },
  {
    title: i18n.t("internal.second_settlement_initial_review.text__22"), // 结算日期
    key: "settleTime",
    align: "center",
    dataIndex: "settleTime",
    scopedSlots: {
      customRender: "settleTime",
    },
  },
  {
    title: i18n.t("internal.second_settlement_initial_review.text__4"), // 申请人
    key: "applicationUser",
    align: "center",
    dataIndex: "applicationUser",
    scopedSlots: {
      customRender: "applicationUser",
    },
  },
  {
    title: i18n.t("internal.second_settlement_initial_review.text__5"), // 赔偿金额
    key: "changeTotalAmount",
    align: "center",
    dataIndex: "changeTotalAmount",
    scopedSlots: {
      customRender: "changeTotalAmount",
    },
  },
  {
    title: i18n.t("internal.second_settlement_initial_review.text__7"), // 审核结果
    key: "approvalStatus",
    align: "center",
    dataIndex: "approvalStatus",
    scopedSlots: {
      customRender: "approvalStatus",
    },
  },
  {
    title: i18n.t("internal.second_settlement_initial_review.text__11"), // 操作
    key: "action",
    align: "center",
    dataIndex: "action",
    scopedSlots: {
      customRender: "action",
    },
  },
];