<template>
  <div class="pl10x pr10x" :style="{ height: src_internal ? '40vh' : '65vh' }">
    <!-- 搜索 -->
    <div class=" pl10x pr10x search row mt10x mb10x">
      <a-radio-group v-model="device" class="row d-flex" @change="onChangeDevice">
        <div>
          <!-- PC球种排序  -->
          {{ $t('internal.sport_order_list.pc_ball_sorting') }}
          <a-radio :value="'PC'"> </a-radio>
          <a-switch v-model="pcMenuSortSwitch" class="mui-switch mui-active"
            @click="handle_pc_menu_sort_switch(pcMenuSortSwitch)" />
        </div>
        <div class=" pl10x pr10x ">
          <!-- H5球种排序  -->
          {{ $t('internal.sport_order_list.h5_ball_sorting') }}
          <a-radio :value="'H5'"></a-radio>
          <a-switch v-model="h5MenuSortSwitch" class="mui-switch mui-active"
            @click="handle_h5_menu_sort_switch(h5MenuSortSwitch)" />

        </div>
      </a-radio-group>
    </div>

    <!-- 表格区域 -->
    <a-table :columns="columns" :key="device"  id="order-table-box" class="expanded pl10x pr10x" :dataSource="tabledata" :loading="tabledata_loading"
      size="middle" :rowKey="(record) => record.id" :pagination="pagination" @change="handleTableChange"
      :scroll="{ y: src_internal ? '40vh' : '65vh' }" :rowClassName="setRowClassName">

      <template slot="optionName">
        <div>
          <div>
            <!-- 排序调整 -->
            {{ $t('internal.sport_order_list.sort_adjustments') }}
          </div>
          <a-button type="link" @click.stop="changeDefaultListOrderBy" block>
            <!-- 恢复默认排序调整 -->
            {{ $t('internal.sport_order_list.revert_to_default_sort_adjustments') }}
          </a-button>
        </div>
      </template>
      <template slot="index" slot-scope="text, record,index">

        <!-- <span>({{record.level}})</span> -->

        <div v-show="!record.showEdit">
          <span v-show="record.level == 1">{{ record.levelIndex }}</span>
          <span v-show="record.level != 1">{{ record.parentIndex }}</span>
          <span v-show="record.level != 1"> ({{ record.levelIndex }})</span>
          <q-icon v-show="!record.showEdit" @click.native="handle_edit(record)"
            class="pl10x panda-icon panda-icon-bian-ji panda-icon-hover fs14 cursor-pointer"></q-icon>
        </div>

        <div v-if="record.showEdit">
          <a-form :form="form" :label-col="{ span: 12 }" :wrapper-col="{ span: 24 }" @submit="handleSubmit">
            <a-form-item label="">
              <a-input-number v-decorator="[
                `tempIndex`, {
                  initialValue: record.tempIndex,
                  rules: numValueRules,
                },
              ]" />
            </a-form-item>
            <a-form-item :wrapper-col="{ span: 12, offset: 2 }">
              <div class="d-flex">
                <a-button class="order-opt-btn" type="link" html-type="submit" block>
                  <!-- 保存 -->
                  {{ $t('internal.save') }}
                </a-button>
                <a-button class="order-opt-btn cancel" type="link" @click.stop="handleCancelSort(record)" block>
                  <!-- 取消 -->
                  {{ $t('internal.cancel') }}
                </a-button>
              </div>

            </a-form-item>
          </a-form>
        </div>
      </template>
      <!-- 球种 -->
      <template slot="name" slot-scope="text, record">
          <span :class="record?.subSortList?.length?'has-sub-title':''">{{text}}</span>
      </template>
      <!-- 操作 -->
      <template slot="option" slot-scope="text, record">
        <a-button class="order-opt-btn" type="link" @click.stop="handleUpOrder(record)"
          :disabled="record.levelIndex == 1" block>
          <!-- 上升 -->
          {{ $t('internal.sport_order_list.upward') }}
          <img src="~src/assets/internal/img/sportOrder/upOrder.png" class="panda-icon cursor-pointer" />
        </a-button>
        <a-button class="order-opt-btn" type="link" @click.stop="handleDownOrder(record)"
          :disabled="record.levelIndex == record.listLength" block>
          <!-- 下降 -->
          {{ $t('internal.sport_order_list.downward') }}
          <img src="~src/assets/internal/img/sportOrder/downOrder.png" class="panda-icon cursor-pointer" />
        </a-button>
      </template>
    </a-table>

  </div>
</template>
<script>
import { i18n } from "src/boot/i18n";
import { sport_order_columns_config ,num_value_rules_config} from "src/components/common/sport_order_list/config/index.js";
import { api_merchant } from "src/api/index.js";
import { flattenNestedStructure } from "src/components/common/sport_order_list/module/util.js"
import lodash from 'lodash'


export default {
  props: {
    detailObj: "",
  },
  data() {
    return {
      // 表格数据源
      columns: sport_order_columns_config,
      tabledata: [],
      tabledata_loading: false,
      h5MenuSortSwitch: false,
      pcMenuSortSwitch: false,
      device: "PC",
      pagination: {
        current: 1, // 当前页码
        pageSize: 100, // 每页显示条数
        total: 0, // 数据总数
        showSizeChanger: true, // 是否显示每页条数切换器
        pageSizeOptions: ['10', '20', '50', '100'], // 每页条数选项
        showQuickJumper: true, // 是否支持快速跳转到指定页码
        showTotal: total => i18n.t('internal.showTotal_text', [total])// 显示数据总数
      },
      editRow: {},
      form: this.$form.createForm(this, { name: 'coordinated' }),
      numValueRules: num_value_rules_config,
    };
  },
  methods: {
    // 获取表格数据
    getTableData() {
      const params = { "merchantCode": this.detailObj.merchantCode, 'device': this.device, }
      // "pageNum": this.pagination.current, "pageSize": this.pagination.pageSize
      api_merchant.get_sport_list_order_by(params).then(res => {
        if (res.data.code == "0000000") {
          // this.tabledata = res.data.data;
          if (res.data.data.length > 0) {
            const flattenedArray = flattenNestedStructure(res.data.data);
            this.tabledata = flattenedArray;
          }

        } else {
          this.$message.error(res.msg);
        }
      })

    },
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      // this.getTableData(); // 根据分页参数重新加载数据
    },
    // 排序方法
    handleUpOrder(row) {
      const sortNo = row.sort - 10
      const params = { "merchantCode": this.detailObj.merchantCode, "device": row.device, "sortNo": sortNo, "menuId": row.menuId };
      this.changeOrderNo(params)
    },
    handleDownOrder(row) {
      const sortNo = row.sort + 10
      const params = { "merchantCode": this.detailObj.merchantCode, "device": row.device, "sortNo": sortNo, "menuId": row.menuId };
      this.changeOrderNo(params)
    },
    changeOrderNo(params) {
      api_merchant.change_order_no(params).then(res => {
        if (res.data.code == "0000000") {
          let { code, msg } = res.data;
          this.$message.success(msg);
          this.getTableData()
        } else {
          this.$message.error(res.msg);
        }
      })
    },
    changeDefaultListOrderBy() {
      const params = { "merchantCode": this.detailObj.merchantCode, "device": this.device };
      api_merchant.change_default_list_order_by(params).then(res => {
        if (res.data.code == "0000000") {
          let { code, msg } = res.data;
          this.$message.success(msg);
          this.getTableData()
        } else {
          this.$message.error(res.msg);
        }
      })
    },
    handle_pc_menu_sort_switch(value) {
      let params = {
        "merchantCode": this.detailObj.merchantCode,
        "merchantParams":
        {
          "pcMenuSortSwitch": value ? 1 : 0,
          "h5MenuSortSwitch": this.h5MenuSortSwitch ? 1 : 0,
        }
      };
      this.handle_set_merchant_switch(params)
    },
    handle_h5_menu_sort_switch(value) {
      let params = {
        "merchantCode": this.detailObj.merchantCode,
        "merchantParams":
        {
          "pcMenuSortSwitch": this.pcMenuSortSwitch ? 1 : 0,
          "h5MenuSortSwitch": value ? 1 : 0,
        }
      };
      this.handle_set_merchant_switch(params)
    },
    handle_set_merchant_switch(params) {
      api_merchant.post_TiYuGuiZe_update_switchConfig(params).then(res => {
        if (res.data.code == "0000000") {
          // this.merchantParams = res.data.data.merchantParams
        } else {
          this.$message.error(res.msg);
        }
      })
    },
    onChangeDevice() {
      this.getTableData();
    },
    search() {
      this.getTableData();
    },
    // 商户中心-开关
    getMerchantSwitch() {
      const params = { "merchantCode": this.detailObj.merchantCode, "paramKey": ["pcMenuSortSwitch", "h5MenuSortSwitch"] }
      api_merchant.post_switchConfig_getTiYuGuiZe(params).then(res => {
        if (res.data.code == "0000000") {
          // this.merchantParams = res.data.data.merchantParams
          lodash.get(res, 'data.data.merchantParams.h5MenuSortSwitch') == 1 ? this.h5MenuSortSwitch = true : this.h5MenuSortSwitchfalse;
          lodash.get(res, 'data.data.merchantParams.pcMenuSortSwitch') == 1 ? this.pcMenuSortSwitch = true : this.h5MenuSortSwitchfalse;
        } else {
          this.$message.error(res.msg);
        }
      })
    },
    handle_edit(record) {

      this.tabledata.filter((item => {
        if (item.showEdit == true) {
          item.showEdit = false
        }
      }));
      record.tempIndex = record.levelIndex
      record.showEdit = true
      this.editRow = record

    },
    handleSaveSort(row) {
      // 保存
      const sortNo = (row.tempIndex<row.listLength?row.tempIndex: row.listLength)* 10
      const params = { "merchantCode": this.detailObj.merchantCode, "device": row.device, "sortNo": sortNo, "menuId": row.menuId };
      this.changeOrderNo(params)
    },
    handleCancelSort(record) {
      // 取消
      record.showEdit = false
    },
    handleSubmit(e) {
      e.preventDefault();
      this.form.validateFields((err, values) => {
        if (!err) {
          // 表单验证
          this.editRow['tempIndex'] = values['tempIndex']
          this.handleSaveSort(this.editRow)
        }
      });
    },
    setRowClassName(record,index){
      if(record.level !=1){
        return "row-level2"
      }else{
        return ''
      }
     
    }
  },
  mounted() {
    this.getTableData();
    this.getMerchantSwitch();
  }
}
</script>

<style lang="scss" scoped>
@import url('./css/index.scss');
</style>
