/*
 * @Desc: 
 * @Date: 2020-01-27 15:42:23
 * @Author:Nice
 * @FilePath: /src/components/common/bet_slip/internal/config/transferConfig.js
 * @Description：数据中心.注单查询(查看注单账变)弹窗配置
 */
import { i18n } from 'src/boot/i18n';
const POSITION = 'right'
let title_config = i18n.t('internal.data.betslip.transferConfig')
export const transfer_config = [
  {//账变时间
    title:  title_config[0],
    width: 150,
    dataIndex: "createTime",
    key: "createTime",
    align: "center",
    scopedSlots: { customRender: 'createTime' },
  },
  {//账变来源
    title: title_config[1],
    width: 170,
    dataIndex: "remark",
    key: "remark",
    align: 'center',
    scopedSlots: { customRender: 'remark' },
  },
  {//账变金额
    title: title_config[2],
    width: 160,
    dataIndex: "changeAmount",
    key: "changeAmount",
    align: POSITION,
    scopedSlots: { customRender: 'changeAmount' },
  },
  {//账变前余额
    title: title_config[3],
    dataIndex: "beforeTransfer",
    key: "beforeTransfer",
    width: 170,
    align: POSITION,
    scopedSlots:{customRender:"beforeTransfer"},
  },
  {//账变后余额
    title: title_config[4],
    width: 160,
    dataIndex: "afterTransfer",
    key: "afterTransfer",
    align: POSITION,
    scopedSlots: { customRender: 'afterTransfer' },
  },
  {//交易相关注单号
    title:  title_config[5],
    dataIndex: "orderNo",
    key: "orderNo",
    width: 140,
    align: 'center',
    scopedSlots:{customRender:"orderNo"},
  }
];

