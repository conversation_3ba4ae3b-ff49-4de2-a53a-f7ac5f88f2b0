<!--
 * @FilePath: /src/components/common/bettinguser/index.vue
 * @Description: 共用：投注用户管理(内/外)+投注用户白名单(对内)
-->
<template>
  <div class="full-height full-width bettinguser">
    <!--头部面包屑导航-->
    <div class="pl10x pt10x pb10x" id="top1">
      <q-breadcrumbs separator="/" active-color="whiddte" class="panda-text-2">
      <!-- 0:商户中心 -->
        <q-breadcrumbs-el
        v-if="src_internal"
          :label="$t('internal.merchant.bettinguser.index.bar')[0]"
        />
         <!-- 账户中心-->
         <q-breadcrumbs-el
         v-else
          :label="$t('external.merchant.bettinguser.index.bar')[0]"
        />
          <!-- 0:商户中心  1:投注用户管理 2:投注用户白名单 -->
          <q-breadcrumbs-el
          v-if="src_internal"
          :label="$t('internal.merchant.bettinguser.index.bar')[is_for_white_betting_users ? '2' : '1']"
          class="panda-text-1"
        />
          <q-breadcrumbs-el
          v-else
          :label="$t('external.merchant.bettinguser.index.bar')[is_for_white_betting_users ? '2' : '1']"
          class="panda-text-1"
        />
      </q-breadcrumbs>
    </div>
    <div
      class="bg-panda-bg-6 shadow-3 border-radius-4px ml10x"
      style="margin: 0 10px 10px 0"
    >
      <div id="top2" class=" row line-height-30px items-center text-panda-text-dark bg-panda-bg-6 p110x pt10x pb10x border-radius-4px " >
        <!--请输入用户ID-->
        <div class=" no-left append-handle-btn-input row ml10x position-relative w-180 " >
          <a-input
            v-model.trim="searchForm.userId"
            :placeholder="$t('internal.merchant.bettinguser.index.placeholder.userId')"
            @keydown.enter="handle_search"
            autocomplete="off"
            allowClear
          >
            <my-icon
              slot="suffix"
              type="p-icon-chazhao"
              class="text-panda-text-4 fs12"
            />
          </a-input>
          <div class="position-absolute select-left-border-finance"></div>
        </div>
        <!-- 请输入用户名 -->
        <div class=" no-left append-handle-btn-input row ml10x position-relative w-180 " >
            <a-input
              v-model.trim="searchForm.userName"
              :placeholder="$t('internal.placeholder.label16')"
              @keydown.enter="handle_search"
              autocomplete="off"
              allowClear
            >
              <my-icon
                slot="suffix"
                type="p-icon-chazhao"
                class="text-panda-text-4 fs12"
              />
            </a-input>
            <div class="position-absolute select-left-border-finance"></div>
          </div>
        <div
          class="no-left append-handle-btn-input ml10x position-relative"
          style="width: 200px !important; height: 30px"
          v-if="src_internal||src_external&&[1, 10].includes(get_user_info.agentLevel)"
        >
          <tree-select
            :tree_list="src_internal?merchantList:merhchantList"
            v-on:change-value="searchForm.merchantCodeList = $event"
          ></tree-select>
        </div>
        <!--全部-->
        <div class="append-handle-btn-input position-relative ml10x" v-if="src_internal && !is_for_white_betting_users">
          <a-select
            autocomplete
            class="w-200"
            :placeholder=" $t( 'internal.merchant.bettinguser.index.placeholder.specialBettingLimitType' ) "
            v-model.trim="searchForm.specialBettingLimitType"
            @change="handle_search"
          >
            <a-select-option :value="''">
            {{ $t("internal.template.label56") }}
            </a-select-option>
            <a-select-option
              :value="Number(index)"
              v-for="(item, index) in specialBettingLimit"
              :key="index"
              >{{ item }}</a-select-option
            >
          </a-select>
          <div class="position-absolute select-left-border-finance"></div>
        </div>
        <!--特殊限额设置时间-->
        <div class="append-handle-btn-input line-height-30px ml10x" v-if="src_external">
          <div>
            <div class="">
              {{ $t("internal.template.label147") }}&nbsp;
              <a-range-picker
                style="width: 200px"
                showToday
                @change="on_change"
              />
            </div>
          </div>
        </div>
        <!--请选择投注币种-->
        <div class="append-handle-btn-input pl10x position-relative" v-if="src_internal && !is_for_white_betting_users">
          <a-select
            autocomplete
            :filter-option="filterOptionCurrency"
            show-search
            class="w-150"
            :placeholder="
              $t('internal.data.matchbonus.index.placeholder.currencyList')
            "
            @change="handle_currency"
            v-model="searchForm.currencyCode"
          >
            <a-select-option
              :value="item.value"
              v-for="(item, index) in currencyList"
              :key="index"
              >{{ item.label }}</a-select-option
            >
          </a-select>
          <div class="position-absolute select-left-border-finance"></div>
        </div>
         <!--是否线路商vip    -->
        <div class="append-handle-btn-input pl10x position-relative"  v-if="src_internal && !is_for_white_betting_users">
          <a-select
            autocomplete
            class="w-150"
           :value="$t('internal.merchant.bettinguser.config.21')"
           v-model="searchForm.isvip"
           @change="handle_is_vip"
          >
            <a-select-option
              :value="item.value"
               v-for="(item, index) in is_vip_list"
              :key="index"
              >{{ item.label }}</a-select-option
            >
          </a-select>
          <div class="position-absolute select-left-border-finance"></div>
        </div>
      <!--全部来源-->
        <div class="append-handle-btn-input pl10x position-relative"  v-if="src_internal && is_for_white_betting_users">
          <a-select
            autocomplete
            class="w-150"
           :value="$t('internal.merchant.bettinguser.config.35')"
            v-model="searchForm.allowListSource "
          >
          <a-select-option
                    v-for="(item, index) in total_source_list"
                    :key="index"
                    :value="item.value"
                     >
                     {{ item.label }}
                     
              </a-select-option >
          </a-select>
          <div class="position-absolute select-left-border-finance"></div>
        </div>
      <!-- 折叠按钮 -->
      <div class="top line-height-32px"  v-if="!is_for_white_betting_users">
        <my-icon 
        @click="handle_click_expand"      
          :type="is_expand ? 'p-icon-bottom' : 'p-icon-top'"
          class="fs20 ml10x cursor-pointer"
        >
        </my-icon>
      </div>
        <!--搜索-->
        <q-btn
          class="panda-btn-primary-dense bg-primary ml10x"
          style="width: 70px; height: 30px"
          :label="$t('internal.search')"
          @click="handle_search"
        />
        <!-- </div> -->
           <!--全部删除-->
          <a-popconfirm  v-if="src_internal"
            :title="$t('internal.merchant.bettinguser.config.43')"
            @confirm="hande_all_delete(record)"
            :okText="$t('internal.sure')"
            :cancelText="$t('internal.cancel')"
            placement="topRight"
          >
              <!-- 全部删除 -->
            <a-tooltip placement="top" v-if="is_for_white_betting_users" >
            <q-btn
          class="panda-btn-primary-dense bg-primary ml10x"
          style="width: 100px; height: 30px"
         :label="$t('internal.merchant.bettinguser.config.37')"
        />
            </a-tooltip>
          </a-popconfirm>
         <!-- 导入白名单 -->
         <q-btn
          v-if="src_internal && is_for_white_betting_users"
          class="panda-btn-primary-dense bg-primary ml10x"
          style="width: 160px; height: 30px"
          :label="$t('internal.merchant.bettinguser.config.38')"
           @click="handle_whitelist_import()"
        />
        <!-- 导入线路VIP -->
         <q-btn
          v-if="src_internal && !is_for_white_betting_users"
          class="panda-btn-primary-dense bg-primary ml10x"
          style="width: 160px; height: 30px"
          :label="$t('internal.merchant.bettinguser.config.26')"
           @click="handle_vip_import()"
        />
        <!-- 批量启&禁用 -->
        <q-btn
          v-if="src_internal && !is_for_white_betting_users && $_has('Merchant:user:importToDisabled')"
          class="panda-btn-primary-dense bg-primary ml10x"
          style="width: 140px; height: 30px"
          :label="$t('internal.merchant.bettinguser.config.28')"
           @click="handle_disabled_user"
        />
        <!-- 导出 -->
        <q-btn
          v-if="src_internal && $_has('Merchant:Order:user:bet:export')"
          class="panda-btn-primary-dense bg-primary ml10x"
          style="width: 70px; height: 30px"
          :label="$t('internal.label.label42')"
           @click="handle_export_excel"
        />
        <!--批量导入开启发起用户-->
         <q-btn
         v-if="(src_internal|| (src_external && $_btn_has('userPurchase')) ) && !is_for_white_betting_users"
          class="panda-btn-primary-dense bg-primary ml10x"
          style="width: 180px; height: 30px"
          :label="$t('internal.merchant.bettinguser.config.text1')"
          @click="handle_batch_import_starts_with_the_user"
        />
        <template v-if="is_expand">
      <!-- 第二排  开始 -->
      <div class=" row mt5x full-width items-center line-height-30px bg-panda-bg-6 q-gutter-y-sm text-panda-text-dark " >
      <!-- 按时间分类 2330需求 -->
      <div class="append-handle-btn-input pl10x position-relative "  v-if="!is_for_white_betting_users">
        <a-select
          autocomplete
          class="w-150"
          :placeholder="$t('internal.page_multilingual.page_data.text_62')"
          v-model="searchForm.timeType"
        >
          <a-select-option
            :value="item.value"
             v-for="(item, index) in filterByTime"
            :key="index"
            >{{ item.label }}</a-select-option
          >
        </a-select>
        <div class="position-absolute select-left-border-finance"></div>
      </div>
      <!-- 天数快选 -->
      <div class="append-handle-btn-input pl10x position-relative "  v-if="!is_for_white_betting_users ">
        <a-select
          autocomplete
          class="w-150"
          :placeholder="$t('internal.merchant.bettinguser.config.45')"
          v-model="searchForm.dateType"
           @change="handle_date_range"
        >
          <a-select-option
            :value="item.value"
             v-for="(item, index) in numberOfDay"
            :key="index"
            >{{ item.label }}</a-select-option
          >
        </a-select>
        <div class="position-absolute select-left-border-finance"></div>
      </div>
      <!-- 日期选择 -->
      <div class="append-handle-btn-input ml10x w-270"  v-if="!is_for_white_betting_users">
            <a-range-picker 
            :disabled = 'searchForm.dateType !== 4'
            :show-time="true"
            showToday 
            :allowClear="false"
            :value="[
            searchForm.startTime,
            searchForm.endTime, 
              ]"
              @change="on_change_time" 
             />
          </div>
          </div>
          <!-- 第二排  结束 -->
        </template>
      </div>
      <a-table
        class="pl10x pr10x"
        :columns="columns"
        :dataSource="tabledata"
        :scroll="{ x: 2040, y: scrollHeight }"
        :pagination="false"
        :loading="tabledata_loading"
        @change="sorterForTable"
        size="middle"
        :rowKey="(record) => record._index"
      >
        <!-- 用户ID -->
        <span slot="userId" slot-scope="text, record">
          <span
            class="cursor-pointer"
            @click="handleCopy(record.userId, $t('internal.uid_text'))"
            >{{ record.userId }}
            </span >
        </span>
        <!-- 用户名 -->
        <span slot="userName" slot-scope="text, record">
          <div
            class="cursor-pointer"
            style="width: 150px"
            @click="handleCopy(record.userName, $t('internal.username_text'))"
          >
            <table-cell-ellipsis-ant
              :str_all="record.userName"
            ></table-cell-ellipsis-ant>
          </div>
        </span>
          <!-- 投注用户白名单 来源 -->
        <span  slot="allowListSource" slot-scope="text, record">
          <div style="width: 150px" >
           {{  compute_show_record_allowListSource(record)}}
          </div>
        </span>
        <!-- 用户vip 阶级 -->
        <span  slot="userVipLevel" slot-scope="text, record">
          <div v-if="record.userVipLevel != undefined && record.userVipLevel != null && record.userVipLevel != 'null'" >
              <a-button v-if=" record.userVipLevel != null && record.userVipLevel != 'null'"   type="link" @click.stop="handle_vip_look(record, index)">
                <span >{{ text }}</span>
              </a-button>
          </div>
        </span>        
        <!-- 启&禁用 -->
        <span slot="disabled" slot-scope="text, record">
            <!-- 0启用/1禁用   label8启用 / label9禁用 -->
            <template v-if="$_has('Merchant:user:updateDisabled')">
              <a-switch @click="disabled_swith_user(record)" :checked="text == 1 ? false : true"
              :checked-children="$t('internal.message.label8')"
              :un-checked-children="$t('internal.message.label9')" />
              <!-- 提示框 有备注字段-->
              <a-tooltip overlayClassName="tooltip-content-remark-text" v-if="record.remark">
                <template slot="title">
                  <div v-html="record.remark.replace(/\n/g,'<br>')"></div>
                </template>
                <!-- 禁用 && 有备注 才展示提示按钮-->
                <a-icon type="info-circle" class="text-red fs15 cursor-pointer q-ml-xs" v-if="text == 1 && record.remark.length" />
              </a-tooltip>
            </template>
            <template v-else><span>{{ text == 1 ? $t('internal.message.label9'):  $t('internal.message.label8')  }}</span></template>
        </span>
        
        
        <!-- 额度转入 -->
        <span slot="userTransferIn" slot-scope="text, record">
          <span>
            <a-switch @click="handle_click_vs(record, 'userTransferIn')" :checked="record.userTransferIn != 0 ? true : false"
              :checked-children="$t('internal.template.label177')" :un-checked-children="$t('internal.template.label171')" />
          </span>
        </span>
        <!-- 额度转出 -->
        <span slot="userTransferOut" slot-scope="text, record">
          <span>
            <a-switch @click="handle_click_vs(record, 'userTransferOut')" :checked="record.userTransferOut != 0 ? true : false"
              :checked-children="$t('internal.template.label177')" :un-checked-children="$t('internal.template.label171')" />
          </span>
        </span>
        <!-- 所属商户 -->
        <span slot="merchantName" slot-scope="text, record">
          <table-cell-ellipsis-ant
            :span_html=" '<div class=\'ellipsis\'>' + record.merchantName + '</div>' "
            :str_all="'<div>' + record.merchantName + '</div>'"
            :defaultplace="'topLeft'"
            :str="record.merchantName"
            :col_width="180"
          ></table-cell-ellipsis-ant>
        </span>
        <!-- 合买开关 -->
        <span slot="userPurchase" slot-scope="text, record">
          <template v-if="src_internal|| (src_external && $_btn_has('userPurchase')) ">
            <a-switch @click="handle_click_vs(record, 'userPurchase')" :checked="record.userPurchase != 0 ? true : false"
              :checked-children="$t('internal.template.label177')" :un-checked-children="$t('internal.template.label171')" />
            <!-- 提示框 有备注字段-->
            <a-tooltip overlayClassName="tooltip-content-remark-text" v-if="record.userPurchaseRemark">
              <template slot="title">
                <div v-html="record.userPurchaseRemark.replace(/\n/g,'<br>')"></div>
              </template>
              <!-- 关 && 有备注 才展示提示按钮-->
              <a-icon type="info-circle" class="text-red fs15 cursor-pointer q-ml-xs" v-if="text === 0 && record.userPurchaseRemark.length" />
            </a-tooltip>
          </template>
      </span>
        <!-- 线路vip -->
        <span slot="isvip" slot-scope="text, record">
          <div class="text-over"
            v-if="!record.isvip_editing"
            @mouseenter="handle_isvip_enter(record)"
            @mouseleave="handle_isvip_leave(record)">
            <!--  1或者null  否  2:是 -->
            <span>{{ record.isvip == 2 ?$t('internal.common.label63')   : $t('internal.common.label64') }}</span>
             <q-icon
              v-if="record.show_isvip_edit_icon "
              class=" panda-icon panda-icon-bian-ji panda-icon-hover fs18 cursor-pointer ml5x "
              @click="handle_isvip_edit_icon_click(record)"
            >
            </q-icon>
          </div>
              <!-- 编辑状态 -->
            <!-- vip 下拉框 -->
           <div  v-if="record.isvip_editing">
            <a-select
               v-model="record.isvip_edit"
              default-value=""
              style="width: 80px"
            >
           <a-select-option
              v-for="(item, index) in vip_list"
              :key="index"
              :value="item.value"
            >
              {{ item.label }}
              </a-select-option>
            </a-select>
            <a-icon
              type="check"
             @click="handle_isvip_edit_complete(record)"
              class="duihao  q-mx-sm"
            />
            <a-icon
              type="close"
              @click="handle_isvip_edit_cancel(record)"
              class="guanbi"
            />
          </div>
        </span>
        <!-- 可用余额 -->
        <span slot="amount" slot-scope="text, record">
          <div class="text-over">
            <span>{{ record.amount | filterMoneyFormat }}</span>
          </div>
        </span>
        <!-- 累计投注额 -->
        <span slot="betAmount" slot-scope="text, record">
          <div class="text-over">
            <span>{{ record.betAmount | filterMoneyFormat }}</span>
          </div>
        </span>
          <!-- 有效投注额 -->
          <span slot="orderValidBetMoney" slot-scope="text, record">
          <div class="text-over">
            <span>{{ record.orderValidBetMoney | filterMoneyFormat }}</span>
          </div>
        </span>
        <!-- 累计盈利 -->
        <span slot="profit" slot-scope="text, record">
          <div class="text-over">
            <span>{{ record.profit | filterMoneyFormat }}</span>
          </div>
        </span>
        <!-- 注单数量 -->
        <span slot="betNum" slot-scope="text, record">
          <div class="text-over">
            <span v-if="src_internal">{{ record.betNum | filterAmount }}</span>
            <span v-else>{{ record.betNum | amount }}</span>
          </div>
        </span>
        <!-- 用户币种 -->
        <span slot="currencyCode" slot-scope="text, record">
          <div class="text-over">
            <span>{{ filterCurrency[record.currencyCode] }}</span>
          </div>
        </span>
         <!--注册时间 -->
         <span slot="createTime" slot-scope="text, record">
             {{ moment(record.createTime).format("YYYY-MM-DD HH:mm:ss") }}
        </span>
        <!-- 最后登录时间 -->
        <span slot="lastLoginStr" slot-scope="text, record">
          <span
            v-if=" record.lastLoginStr == $t('internal.merchant.bettinguser.index.just_now')[0] "
            >{{ record.lastLoginStr }}</span>
          <span v-else >
            {{ record.lastLoginStr }}
            <span v-if="record.lastLoginStr">
            {{ $t("internal.merchant.bettinguser.index.just_now")[1] }}
            </span>
            </span>
        </span>
        <!-- 在线状态 -->
        <span slot="enabled" slot-scope="enabled">
          <span v-if="enabled == 1">
          {{ $t("internal.merchant.bettinguser.index.online") }}
          </span>
          <span v-else>--</span>
        </span>
        <!-- 投注特殊限额 -->
        <span
          slot="specialBettingLimitType"
          slot-scope="text, record"
          @mouseover="desp_key = record.userId"
          @mouseleave="desp_key = false"
        >
          <div
            class="cursor-pointer display-block specail_limit "
            @click="handleSetDesp(record)"
          >
            <q-icon
              v-if="text&&src_internal || src_external && [1, 2, 3, 4,6].includes(record.specialBettingLimitType)"
              class=" panda-icon panda-icon-cha-kan panda-icon-hover fs18 cursor-pointer float-left mr5x specail_limit_icon"
            ></q-icon>
            <!-- //   "1": "无",  //   "3": "特殊单注单场限额", //   "4": "特殊VIP限额" //    "5": 信用限额  //  "6": 自动升额度      -->
            <span class="float-left"
              v-if=" (src_internal||src_external ) && record.specialBettingLimitType != 2 && record.specialBettingLimitType != 6"
            >
              {{ specialBettingLimit[record.specialBettingLimitType] }} 

            </span>

            <!-- 对外有特殊限额的用户在列表中投注特殊限额字段显示三行单日/单场/单注限额百分比 -->
                 <!-- {{ tabledata_specialBettingLimitType_2[`index_${record._index}`] }} -->
            <span
              style="font-size: 10px; "
              class="float-left" v-if="src_external && record.specialBettingLimitType == 2"
            >
              {{ $t(`internal.betting.label12`) }} {{ tabledata_specialBettingLimitType_2[`index_dr${record._index}`] }} <br />
              {{ $t(`internal.betting.label13`) }} {{ tabledata_specialBettingLimitType_2[`index_dc${record._index}`] }} <br />
              {{ $t(`internal.betting.label14`) }} {{ tabledata_specialBettingLimitType_2[`index_dz${record._index}`] }} <br />
            </span>

            <span
              style="font-size: 10px; "
              class="float-left" v-if="src_internal && record.specialBettingLimitType == 2"
            >
              {{ $t(`internal.betting.label12`) }} {{ tabledata_specialBettingLimitType_2[`index_dr${record._index}`] }} <br />
              {{ $t(`internal.betting.label13`) }} {{ tabledata_specialBettingLimitType_2[`index_dc${record._index}`] }} <br />
              {{ $t(`internal.betting.label14`) }} {{ tabledata_specialBettingLimitType_2[`index_dz${record._index}`] }} <br />
            </span>
            <!--  自动升额度 type 6-->
            <span
              style="font-size: 12px; "
              class="float-left" v-if="src_external && record.specialBettingLimitType == 6"
            >
              {{ $t(`internal.betting.label17`) }}    {{ tabledata_specialBettingLimitType_6[`index_autoUp${record._index}`]  }}
            </span>
            <span
              style="font-size: 12px; "
              class="float-left" v-if="src_internal && record.specialBettingLimitType == 6"
            >
              {{ $t(`internal.betting.label17`) }}    {{ tabledata_specialBettingLimitType_6[`index_autoUp${record._index}`]  }}
            </span>
            
          </div>

      


        </span>
        <!-- 投注延时 -->
        <span
          slot="specialBettingLimitDelayTime"
          slot-scope="text, record"
          >
          <span v-if=" ((src_internal && $_has('view_only_special_delay'))||(src_external &&  $_btn_has('view_only_special_delay')))&& record.specialBettingLimitDelayTime">
            <q-btn
            @click="handle_special_delay(record, 1)"
            outline color="primary"

            :label="$t('external.daily_investigation.text79')"
          /> 
          </span>
          <span v-else>-</span>
          </span>
          
        <!-- 体育赔率分组 -->
        <span slot="marketLevel" slot-scope="text, record" > 
          <span v-if=" ! record.earlyTagMarketLevelId&& ! record.grounderTagMarketLevelId">
            {{ $t('external.merchant.bettinguser.index.text2')}}
          </span>
          <span v-else-if=" record.earlyTagMarketLevelId>=0&& record.grounderTagMarketLevelId>=0">
           {{ $t('external.merchant.bettinguser.index.text3')}}
          </span>
          <span v-else-if=" record.earlyTagMarketLevelId>=0">
           {{ $t('external.merchant.bettinguser.index.text4')}}
          </span>
          <span v-else-if=" record.grounderTagMarketLevelId>=0">
           {{ $t('external.merchant.bettinguser.index.text5')}}
          </span>
          <span v-else>
           {{ $t('external.merchant.bettinguser.index.text2')}}
          </span></span >
          <!-- 电竞赔率分组 -->
          <span slot="esMarketLevel" slot-scope="text, record" > 
          <span v-if="!record.esMarketLevel">
            {{ $t('external.merchant.bettinguser.index.text2')}}
          </span>
          <span v-else-if="record.esMarketLevel>0">
            {{ $t('external.merchant.bettinguser.index.text1')}}
          </span>
          <span v-else>
            {{$t('external.merchant.bettinguser.index.text2')}}
          </span>
          </span >
        <!-- 特殊管控设置时间 -->
        <span
          v-if="text"
          slot="specialBettingLimitTime"
          slot-scope="text, record"
          >
          {{ moment(record.specialBettingLimitTime).format("YYYY-MM-DD HH:mm:ss") }}
          </span>
        <!-- 操作 -->
        <span
          slot="action"
          slot-scope="text, record, index"
          class="row"
        >
         <!-- 特殊限额  请忽删除-->
          <!-- <a
            class="bet q-ml-md"
            v-if="
            src_external&&
              [1, 2, 5].includes(1 * record.specialBettingLimitType) &&
              have_special_limit_permission
            "
            @click.prevent="handle_show_special_limit(record, index)"
            >{{ $t("external.template2.label31") }}</a
          > -->
          <div>
              <a-tooltip placement="bottomRight" v-if="!is_for_white_betting_users">
                <template slot="title">
                  <span>{{ $t("internal.data.user.index.betting_detail") }}</span>
                  <!-- 投注详情 -->
                </template>
                  <img src="~src/assets/external/betting_details.svg" alt="" class="table-svg cp" @click.prevent="handle_look(record, index)">
              </a-tooltip>
                  <!-- <span>赔率分组</span> -->
              <a-tooltip placement="bottomRight">
                <template slot="title">
                  <span>{{ $t("external.account.user_risk_control.label19") }}</span>
                </template>
                <img v-if="src_external" src="~src/assets/external/odds_grouped.svg" alt="" class="table-svg cp" @click.prevent="handle_show_special_limit(record, index,1)">
              </a-tooltip>
              <!-- <span>投注延时</span> -->
              <a-tooltip placement="bottomRight">
                <template slot="title">
                   <span>{{ $t("external.merchant.bettinguser.config.19") }}</span>
                </template>
                <img  
                    v-if="(src_external &&  $_btn_has('special_delay_can_be_set'))"
                     src="~src/assets/external/special_delay.svg" alt="" class="table-svg cp" @click.prevent="handle_special_delay(record, 2)">
              </a-tooltip>
              <a-tooltip placement="bottomRight">
                <template slot="title">
                  <!-- <span>特殊限额</span> -->
                  <span>{{ $t("external.account.user_risk_control.label16") }}</span>
                </template>
                <!-- v-if=" src_external&& [1, 2, 5].includes(1 * record.specialBettingLimitType) && have_special_limit_permission " -->
        
                  <img src="~src/assets/external/special_betting_limits.svg" alt="" class="table-svg cp"  
                  v-if="src_external && have_special_limit_permission"
                @click.prevent="handle_show_special_limit(record, index)">
              </a-tooltip>

              <a-tooltip placement="bottomRight">
                <template slot="title">
                  <!-- <span>查询异常</span> -->
                  <span>{{ $t("external.account.user_risk_control.label38") }}</span>
                </template>
                <!-- v-if=" src_external&& [1, 2, 5].includes(1 * record.specialBettingLimitType) && have_special_limit_permission " -->
                  <img src="~src/assets/external/special_betting_limits.svg" alt="" class="table-svg cp"  
                    v-if="src_external && have_special_limit_permission"
                    @click.prevent="handle_show_find_abnormal(record)">
              </a-tooltip>
              <a-tooltip placement="bottomRight">
                <template slot="title">
                  <!-- <span>白家赔分布</span> -->
                  <span>{{ $t("external.account.user_risk_control.label39") }}</span>
                </template>
                <!-- v-if=" src_external&& [1, 2, 5].includes(1 * record.specialBettingLimitType) && have_special_limit_permission " -->
                 <span  v-if="!is_for_white_betting_users">
                  <img src="~src/assets/external/distribution_of_baijia.svg" alt="" class="table-svg cp"  
                    v-if=" (src_internal && $_has('RiskAnalysisBet'))||(src_external &&  $_btn_has('RiskAnalysisBet'))"
                    style=" width: 16px;"
                    @click.prevent="handle_show_find_baijia(record)">
                    <!-- marketOddsIcon    =  = 1   就展示  告警 -->
                    <a-icon v-if="record.marketOddsIcon==1&& ((src_internal && $_has('RiskAnalysisBet'))||(src_external &&  $_btn_has('RiskAnalysisBet')))" type="exclamation-circle" class="text-red" />
                    </span>
              </a-tooltip>
          </div>
          <!-- 删除单条 白名单记录  你确定删除白名单吗? -->
          <a-popconfirm  v-if="src_internal"
            :title="$t('internal.merchant.bettinguser.config.44')"
            @confirm="handle_confirm_delete_row(record)"
            :okText="$t('internal.sure')"
            :cancelText="$t('internal.cancel')"
            placement="topRight"
          >
              <!-- 删除白名单 -->
            <a-tooltip placement="top" v-if="is_for_white_betting_users" >
              <template slot="title">
                <div class="fs12">
                 {{ $t("internal.merchant.bettinguser.config.42") }}
                </div>
              </template>
              <q-icon class="ml60x panda-icon panda-icon-delete panda-icon-hover fs19 cursor-pointer " ></q-icon>
            </a-tooltip>
          </a-popconfirm>
        </span>
      </a-table>
      <a-pagination
        v-if="tabledata.length > 0"
        :total="pagination.total"
        :current="pagination.current"
        show-size-changer
        show-quick-jumper
        :page-size-options="pagination.pageSizeOptions"
        :page-size="pagination.pageSize"
        :show-total=" (total) => $t('internal.showTotal_text', [pagination.total]) "
        @change="onChange"
        @showSizeChange="onShowSizeChange"
      />
    </div>
    <!-- 修改备注-->
    <q-dialog
      v-model="show_Desp"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-desp
        v-if="src_internal"
        :detailObj="showDialogObj"
        @handleCloseDesp="handleCloseDesp"
      ></dialog-desp>
      <dialog-desp-external
        v-else
        :detailObj="showDialogObj"
        :can_edit="dialog_desp_can_edit"
        @handleCloseDesp="handleCloseDesp"
      ></dialog-desp-external>
  

    </q-dialog>
    <!-- 用户名标签组件-->
    <q-dialog
      v-model="show_user"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <user-name
        :userIdList="this.src_internal?userIdList:''"
        @handle_confirm="handle_confirm_"
      ></user-name>
    </q-dialog>
    <!-- 信用限额 模式  查看弹窗   -->
    <q-dialog
      v-model="show_credit_limit"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-credit-limit
        :detailObj="showDialogObj"
        isindialog
        @close="handleCloseCreditLimit"
      ></dialog-credit-limit>
    </q-dialog>
    <!-- 操作  赔率分组、投注延迟  弹窗-->
    <q-dialog
      v-model="show_user_odds_grouped"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
     <dialogOddsGrouped
        :detailObj="showDialogObj"
        :show_type="showDialogObj.dialogtype"
         isindialog
         @handleCloseDesp="close_odds_grouped"
      ></dialogOddsGrouped>
      <!-- 导入线路vip -->
    </q-dialog>
    <!-- 操作 新投注延迟  弹窗-->
    <q-dialog
      v-model="show_special_delay"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
     <dialogspecialdelay
        :detailObj="showDialogObj"
         isindialog
         @handleCloseDesp="close_special_delay"
      ></dialogspecialdelay>
      <!-- 导入线路vip -->
    </q-dialog>
    <!-- 批量启&禁用 弹窗   -->
    <q-dialog v-model="show_disabled_user" persistent>
      <dialog-disabled-user 
      @on_close="handleCloseDisabledUser" 
      :dialogtitle="$t('internal.merchant.bettinguser.config.30')" 
      titleId="user"
      ></dialog-disabled-user>
    </q-dialog>
    <!--批量导入开启发起用户 弹窗   -->
    <q-dialog v-model="show_batch_status_user" persistent>
      <dialog_batch_status_user 
      @on_close="handleCloseDisabledUser" 
      :dialogtitle="$t('internal.merchant.bettinguser.config.text4')" 
      titleId="purchase"
      ></dialog_batch_status_user>
    </q-dialog>
    <!-- 报表下载弹窗 -->
    <q-dialog
      v-model="exportExcelShow"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-excel :export_param="export_param"></dialog-excel>
    </q-dialog>
<!-- 导入线路VIP -->
<q-dialog
 v-model="show_import_vip_dialog"
 persistent
 transition-show="scale"
 transition-hide="scale"
>
<dialog-import-line-vip
:getailObj="showDialogObj"
@close_dialog_import_line_vip="handle_close_dialog_import_vip"
>
</dialog-import-line-vip>
</q-dialog>
  <!-- 导入白名单  弹窗  -->
    <q-dialog 
     v-model="show_import_whitelist_dialog"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
       <dialog-import-whitelist 
       :detailObj="showDialogObj"
        @close_dialog_import_whitelist="handle_close_dialog_import_whitelist"
       >
      </dialog-import-whitelist>
    </q-dialog>

       <!-- 操作  查询异常  弹窗-->
    <q-dialog
      v-model="show_find_abnormal"
    >
     <dialogFindAabnormal
        :detailObj="showDialogObj"
         isindialog
         @handleCloseDesp="close_odds_grouped"
      ></dialogFindAabnormal>
    
    </q-dialog>
    
       <!-- 操作  百家赔  弹窗-->
       <q-dialog
       v-model="show_find_baijia"
     >
      <dialogFindBaijia
         :detailObj="showDialogObj"
          isindialog
          @handleCloseDesp="close_odds_grouped"
       ></dialogFindBaijia>
     
     </q-dialog>

    <!-- 额度转入转出 -->
    <q-dialog
    v-model="vsShow"
    persistent
    transition-show="scale"
    transition-hide="scale"
  >
    <dialog-vs
    :detail_name="currentRecord.userName"
      :detailObj="currentRecord"
      :show_which_config="show_which_config"
      @handle_confirm="show_which_callback_fn"
    ></dialog-vs>
  </q-dialog>
  </div>
</template>
<script>
import moment from "moment";
import { i18n } from "src/boot/i18n";
import { mapGetters, mapActions } from "vuex";
import mixins from "src/mixins/internal/index.js";
import { api_user ,api_account} from "src/api/index.js";
import mixins_external from "src/mixins/external/index.js";
import constantmixin from "src/mixins/internal/common/constantmixin.js";
import usernamemixin from "src/mixins/internal/common/usernamemixin.js";
import basepagination from "src/mixins/internal/module/basepagination.js";
import constantmixin_external from "src/mixins/external/common/constantmixin.js";
import { handleCopy} from "src/util/module/common.js";
import { channelConfig } from "src/components/common/bettinguser/external/config/channelConfig.js";//账户中心.投注用户管理（渠道：列表配置）
import { tablecolumns_config as tablecolumns_config_external} from "src/components/common/bettinguser/external/config/config.js";//账户中心.投注用户管理（直营&二级：列表配置）
import { tablecolumns_config as tablecolumns_config_internal } from "src/components/common/bettinguser/internal/config/config.js";//商户中心.投注用户管理(列表配置)
import treeSelect from "src/components/common/tree/tree_select.vue";
import dialogExcel from "src/components/common/dialog/dialogExcel.vue";// 导出
import dialogDisabledUser from "src/components/common/dialog_set/dialogDisabledUser.vue";// 批量启&禁用
import dialog_batch_status_user from "src/components/common/bettinguser/internal/component/dialog_batch_status_user.vue";// 批量导入开启发起用户&禁用
import dialogImportWhitelist from "src/components/common/dialog_set/dialogImportWhitelist.vue";// 导入白名单弹窗
import dialogImportLineVip from "src/components/common/dialog_set/importLineVip.vue";// 导入线路VIP弹窗
import tableCellEllipsisAnt from "src/components/internal/table/tableCellEllipsisAnt.vue";
import dialogDesp from "src/components/common/bettinguser/internal/component/dialogDesp.vue";
import dialogCreditLimit from "src/pages/external/account/component/dialogCreditLimitComEdit.vue";
import dialogDespExternal from "src/components/common/bettinguser/external/component/dialogDesp.vue"; // 对外-账户中心-投注用户管理-特殊限额
import dialogspecialdelay from "src/components/common/dialog/dialog_special_delay/dialog_special_delay.vue";//账户中心-投注用户管理（投注延迟  弹窗 ）
import dialogOddsGrouped from "src/components/common/dialog/dialog_odds_grouped_new.vue";//账户中心-投注用户管理（赔率分组 弹窗 ）
import dialogFindAabnormal from "src/components/common/bettinguser/external/component/dialog_find_abnormal.vue";//账户中心-投注用户管理（查询异常  弹窗  弹窗 ）
import bettinguser_methods_mixin from "src/components/common/bettinguser/module/bettinguser_methods.js";
import dialogFindBaijia from "src/components/common/bettinguser/external/component/dialog_find_baijia.vue";//账户中心-投注用户管理（查询异常  弹窗  弹窗 ）

import dialogVs from "src/pages/internal/merchant/merchantc/component/dialogVs.vue"; // 通用开关弹窗
const xinyong_svg_icon = require("src/assets/external/icon/xinyong.svg");
const FOR_WHICH = process.env.FOR_WHICH;
let mixins_custom=  [...mixins, constantmixin, usernamemixin ,basepagination];
if(FOR_WHICH=="external"){
  mixins_custom= [...mixins_external, constantmixin_external,basepagination];
}
export default {
  mixins: [...mixins_custom, bettinguser_methods_mixin],
  data() {
    return {
      vsShow: false, //  通用弹窗开关
      currentRecord: "", // 当前行表格数据对象
      // 通用 开关参数
      show_which_config: {},
      is_expand: false,//展开按钮
      columns:[],
      timer: null,// 定时器 
      desp_key: "",//投注特殊限额
      channelConfig,
      xinyong_svg_icon,
      tabledata: [], // 表格数据   
      export_param: {}, // 导出报表参数
      showDialogObj: {},
      tabledata_specialBettingLimitType_2: {}, //当前列表内所有的 特殊百分比限额的 限额数据
      tabledata_specialBettingLimitType_6: {}, //当前列表内所有的 自动升额度的 数据

      is_for_white_betting_users: false, // betting_user： 投注用户管理    white_betting_users：投注用户白名单
      show_user: false, // 用户名标签弹窗阀值
      show_Desp: false, //设置备注
      exportExcelShow: false,//导出报表阈值
      tabledata_loading: false,//表格loading
      show_credit_limit: false,//信用限额 模式  查看弹窗
      show_disabled_user: false,// 批量启&禁用 弹窗阈值
      show_batch_status_user: false,// 批量导入开启发起用户 弹窗阈值
      dialog_desp_can_edit: false,//非 信用限额 模式  查看弹窗  是否开启 编辑功能
      show_import_vip_dialog:false,//导入vip的弹窗 显示控制
      show_import_whitelist_dialog:false,//导入白名单的弹窗 显示控制
      show_user_odds_grouped :false,//操作的延时和赔率分组
      show_special_delay:false,//操作投注延时分组
      show_find_abnormal :false,//操作的查询异常
      show_find_baijia:false,//百家赔分布弹窗
      is_vip_list: i18n.t("internal.filters.is_vip_list"),//是否线路vip下拉框
      vip_list: i18n.t("internal.filters.vip_list"),//线路 vip编辑下拉框
      total_source_list: i18n.t("internal.filters.total_source"),//全部来源 ：0.全部来源  2.商户VIP  3.测试商户  4.C端测试账户  3.其他
      import_total_source_list: i18n.t("internal.filters.import_total_source"),//请输入白名单来源 下拉选择 :0.请输入白名单来源  2.商户VIP  3.测试商户  4.C端测试账户  5.其他 
      specialBettingLimit: i18n.t("internal.filters.specialBettingLimit"),//全部 投注特殊限额: 1.无  2.特殊百分比限额  3.特殊单注单场限额  4.特殊VIP限额
      filterByTime: i18n.t("internal.filters.filterByTime"), // 注册时间： 1.注册时间  2.最后投注时间 3.最后登录时间
      numberOfDay: i18n.t("internal.filters.numberOfDay"), //日期选择： 1. 3天  2. 7天 3. 14天
      searchForm_temp: {
        userId: "", //商户ID
        userName: "", //用户名
        merchantCodeList: "", //商户名称
        specialBettingLimitType: "", //投注特殊限额   赔率分组/投注延时 
        startSpecialBettingLimitTimeL: null,
        endSpecialBettingLimitTimeL: null,
        currencyCode: "", //全部币种
        specialBettingLimitDelayTime:"",//投注延时
        isvip:"",//默认不选择  1或者null  否  2:是
        allowListSource :"", //全部来源 ：0.全部来源  2.商户VIP  3.测试商户  4.C端测试账户  5.
        dateType:2, //选择查询的时间段
        timeType:1,//选择查询的时间的类型
        startTime: `${moment(new Date().setDate(new Date().getDate()- 6 )).format("YYYY-MM-DD")} 00:00:00`, //开始日期
        endTime: `${moment(new Date().setDate(new Date().getDate()) + 3).format("YYYY-MM-DD")} 23:59:59`, // 结束日期 
      }, 
      searchForm:{},
    };
  },
  components: {
    treeSelect,
    tableCellEllipsisAnt,
    dialogCreditLimit,
    dialogDesp,
    dialogDespExternal,
    dialogOddsGrouped,//账户中心-投注用户管理（赔率分组、投注延迟  弹窗） 
    dialogspecialdelay,//投注延迟  弹窗
    dialogDisabledUser,
    dialogExcel,
    dialogImportWhitelist,
    dialogImportLineVip,//导入线路VIP 弹窗
    dialogFindAabnormal,//账户中心-投注用户管理（查询异常  弹窗） 
    dialogFindBaijia,
    dialogVs,
    //批量导入开启发起用户
    dialog_batch_status_user
  },
  filters:{
  },
  computed: {
    ...mapGetters(["get_user_info", "get_data"]),
    have_special_limit_permission() {
      let { menus, roles } = this.get_user_info;
      let all_menu_name = []; // 接 收 所有的  设置的菜单的   path
      let digui = (menus, all_menu_name) => {
        menus.map((x, i) => {
          all_menu_name.push(x.path);
          digui(x.children || [], all_menu_name);
        });
      };
      digui(menus, all_menu_name);

      // console.log("计算 all_menu_name ----------", all_menu_name);
      let show_special_limit = all_menu_name.includes("special_limit") && roles.includes("special_limit");
      if (!show_special_limit) {
        this.columns[this.columns.length - 1]["width"] = 60;
      }
      return show_special_limit;
    },
    show_credit() {
      let show = false;
      show = [0, 2].includes(Number(this.pagination.agentLevel));
      return show;
    },
    // 欧洲杯投注用户白名单不让用户名查询 禁用
    // disabled_white_betting_users() {
    //   return (this.$route.name == "white_betting_users" && this.src_internal)
    // }
  },
  created() {
    // this.handle_date_range()
    this.init_config_by_route();
    this.columns=this.src_internal?this.columns:this.columns_external();
    this.init_params();
    this.src_internal&&this.init_merchant_list()||
    this.src_external&&[1, 10].includes(this.get_user_info.agentLevel) &&
    this.init_merchant_list();
  },
 watch: {
    $route(newValue, oldValue) {
      this.init_config_by_route();
    }
  },
  beforeDestroy(){
    if(this.timer){
      clearTimeout(this.timer)
      this.timer = null
    }
  },


};
</script>

<style lang="scss" scoped>
  @import 'src/components/common/bettinguser/module/bettinguser.scss';
  </style>