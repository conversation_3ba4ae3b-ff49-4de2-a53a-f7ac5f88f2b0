/*
 * @Desc: 
 * @Date: 2020-02-08 21:46:34
 * @Author:Nice
 * @FilePath: /src/components/common/bet_slip/external/mixin/datacentertablemixin.js
 */
import { mapGetters } from 'vuex'
import { api_data } from "src/api/index.js";
import { getAutoParams, download } from "src/util/module/common.js";
import { i18n } from 'src/boot/i18n';
import debounce from 'lodash/debounce'
import export_file_mixin from  "src/mixins/module/export_file.mixin.js"
let limitSize = 1000000
if (process.env.FRONT_WEB_ENV == "local_test") {
  limitSize = 1000000
}
export default {
  mixins:[export_file_mixin],
  data() {
    this.initTableData = this.initTableData && debounce(this.initTableData, 200);
    return {
      pagination: {
        pageSize: this.$route.name === 'record_query'? 50: 20,
        current: 1,
        filter: this.$route.name === 'bet_slip'? '3': '3', //tab栏中投注-投注时间
        dateType: "day",  // 日周月年
        total: 0,
        start: '', //排序值
        showTotal: total => `共 ${total} 条数据`,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ["10", "20", "50", "100"],
      },
       //全部赛种
       allMatches: [
        { label:  i18n.t('external.common.label52'), value: "" },//全部赛种
        { label:i18n.t('external.common.label53'), value: 1 },//足球
        { label: i18n.t('external.common.label54'), value: 2 },//棒球
        { label: i18n.t('external.common.label56'), value: 4 },//冰球
        { label: i18n.t('external.common.label57'), value: 5 },//网球
        { label: i18n.t('external.common.label58'), value: 6 },//美式足球
        { label: i18n.t('external.common.label59'), value: 7 },//斯若克
        { label: i18n.t('external.common.label60'), value: 8 },//乒乓球
        { label: i18n.t('external.common.label61'), value: 9 },//排球
        { label: i18n.t('external.common.label62'), value: 10 },//羽毛球
        { label: i18n.t('external.common.label63'), value: 18 },//政治娱乐
        // { label: "英雄联盟", value: 100 },
        // { label: "dota2", value: 101 },
        // { label: "cs", value: 102 },
        // { label: "王者荣耀", value: 103 },
        // { label: "绝地求生", value: 104 },
      ],
      totalData:[{
        rowkey:'totalData',
        _index: i18n.t('external.common.label69'),
        merchantName: '',
        merchantLevel: '',
        agentLevel: '',
        currency: "",
        betAmount: '-',
        orderSum:'-',
        returnAmount:'-',
        profit:'-',
        returnRate:'-',
        betUserSum:'-',
        addUser:'',
        registerTotalUserSum:'',
        betUserRate:'',
      }],
      //所有体育
      sportList: [
        {label:  i18n.t('external.common.label64'),value: ''},//所有体育        
        {label:  i18n.t('external.common.label66'),value: 4},//体育赛事
        {label:  i18n.t('external.common.label65'),value: 3},//虚拟体育
        {label:  i18n.t('external.common.label75'),value: 5}//活动
      ],
      // 对账单筛选
      statements_sportList: [
        { label: i18n.t("internal.table_title.label83"), value: "" },//所有体育
        { label: i18n.t("internal.table_title.t17"), value: 4 },//常规体育
        { label: i18n.t("internal.table_title.t18"), value: 3 },//电子赛事
        { label: i18n.t("internal.common.label73"), value: 5 }//活动
      ],
      //投注项结果
      outComeList: [
        // { label: i18n.t("internal.table_title.label104"), value: "" },//全部
        { label: i18n.t("internal.table_title.label977"), value: '2' },//走水
        { label: i18n.t("internal.table_title.label988"), value: "3" },//输
        { label: i18n.t("internal.table_title.label99"), value: "4" },//赢
        { label: i18n.t("internal.table_title.label100"), value: "5" },//赢半
        { label: i18n.t("internal.table_title.label101"), value: "6" },//输半
        // { label: i18n.t("internal.table_title.label102"), value: "7" },//赛事取消
        // { label: i18n.t("internal.table_title.label103"), value: "8" }//赛事延迟
      ],
      merhchantList: [], // 商户名称数据
      scrollHeight: 630,
      totalLoading: false, // 统计数据加载阀值
      exportLoading: false,  // 导出按钮阀值
      limitSize,
      transferTypeList: [
        // 账变类型数组
        { value: "1", label:  i18n.t('external.common.label31') },//转入
        { value: "2", label:  i18n.t('external.common.label32') },//转出
        { value: "3", label:  i18n.t('external.common.label33') },//下注
        { value: "4", label:  i18n.t('external.common.label34') },//结算
        { value: "5", label:  i18n.t('external.common.label78') },//人工加款
        { value: "6", label:  i18n.t('external.common.label79') },//人工扣款
        { value: "7", label:  i18n.t('external.common.label37') },//彩金
        { value: "8", label:  i18n.t('external.common.label38') },//拒单
        { value: "9", label:  i18n.t('external.common.label39') },//结算回滚
        { value: "10", label:  i18n.t('external.common.label40') },//下注取消
        { value: "11", label:  i18n.t('external.common.label41') },//下注取消回滚
        { value: "12", label:  i18n.t('external.common.label80') },//全额提前结算（加钱）
        { value: "121", label:  i18n.t('external.common.label81') },//提前部分结算(加钱)
        { value: "13", label:  i18n.t('external.common.label82') },//订单提前结算取消（减钱）
        { value: "14", label:  i18n.t('external.common.label83') },//订单提前结算取消回滚（加钱）
        { value: "15", label:  i18n.t('external.common.label84') },//结算后人工退款（减钱）
        { value: "16", label: i18n.t("external.common.label85") },//提前结算取消二次处理退回赛果结算金额（减钱）
        { value: "20", label: i18n.t("external.common.label86") },//用户预约下注(扣)
        { value: "21", label: i18n.t("external.common.label87") },//预约投注取消
        { value: "100", label: i18n.t("external.common.label88") },//注单确认
        { value: "22", label: i18n.t("internal.common.label_724_80") },//合买用户下注(减钱)
        { value: "23", label: i18n.t("internal.common.label_724_81") },//合买撤单(加钱)
        { value: "24", label: i18n.t("internal.common.label_724_82") },//合买风控拒单(加钱)
        { value: "25", label: i18n.t("internal.common.label_724_83") },//合买接单保底款回退(加钱)
        { value: "26", label: i18n.t("internal.common.label_724_84") },//合买结算（加钱）
        { value: "27", label: i18n.t("internal.common.label_724_85") },//合买结算回滚（减钱）
        { value: "28", label: i18n.t("internal.common.label_724_86") },//合买佣金（加钱）
        { value: "29", label: i18n.t("internal.common.label_724_87") },//合买佣金（减钱）
        { value: "30", label: i18n.t("internal.common.label_724_88") },//合买取消（减钱）
        { value: "31", label: i18n.t("internal.common.label_724_89") },//合买取消回滚（加钱）
      ],
      eKey: '', // 是否是导出账变记录
      ftpUrl: '',  //超出5w条返回url
      recordShow: false, //交易记录弹窗阀值
    }
  },
  computed: {
    ...mapGetters(['get_window_size_info']),
    // 百家赔页面
    is_bet_slip_order_no_page() {
      return this.$route.name == "bet_slip_order_no_page";
    },
  },
  mounted() {
    this.get_max_height();
  },
  methods: {
    download,
    /**
     * @description 体育/虚拟体育切换
     * @return {undefined} undefined
     */
    handle_sport_change(){
      let { managerCode } = this.searchForm;
      if(managerCode === 3){
        this.searchForm.matchType = 1
      }else if(managerCode === 4 || managerCode === ''){
        this.searchForm.matchType = ''
      }
      // this.handle_search();
    },

         //切换币种类型   注单币种，1:人民币  2:美元  3:欧元   4:新元
         handle_change_currency(){
                let { managerCode } = this.searchForm;
                this.handle_search();
              },
              
               //切换用户类型
          handle_change_user(){
                let { managerCode } = this.searchForm;
                // this.handle_search();
              },
          



    async get_sport_list() {
      try {
        let res = await api_data.get_admin_player_getSportList({})
        if (res.data.code === '0000000') {
          let matches = res.data.data || [];
          matches = matches.map(match => ({
            label:match.name,
            value: match.id
          }))
          matches.unshift({ label:  i18n.t('external.common.label52'), value: "" })
          this.allMatches = matches
        }
      } catch (error) {

      }
    },
    get_max_height(){
      let top = document.getElementById('top');
      let top1 = document.getElementById('top1');
      let top2 = document.getElementById('top2');
      if(top && top1 && top2){
        this.scrollHeight = document.documentElement.clientHeight - top.clientHeight - top1.clientHeight - top2.clientHeight - 60 - 50;
      } 
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    async init_merchant_list(){
      let res = await api_data.post_admin_merchantReport_getMerchantListTree({})
      // console.warn(res.data);
      if ((res.data.code = "0000000")) {
        this.merhchantList = this.recursive_data(res.data.data || []);
        // console.log(this.merhchantList,'merchantList')
      }
    },
    recursive_data(data){
      // console.log(data,'需要处理的结构')
      data.length>0 && data.forEach(item => {
        item.title = item.merchantName
        item.key = item.id
        item.value = item.merchantCode
        item.children = item.trees
        item.children && this.recursive_data(item.children)
      })
      return data || [];
    },
    sorterForTable(pagination, filter, sorter) {
      this.pagination ={...this.pagination, ...pagination};
      this.pagination.sort = (sorter.order && sorter.order.replace('end', ''));
      this.pagination.orderBy = sorter.field;
      this.pagination.outComeList = (filter.outcome && filter.outcome);
      this.pagination.amountTag = filter.afterTransfer && (filter.afterTransfer[0]*1 == 0? "0": "1");  // 交易记录查询需要用到的参数
    },
    rebuild_tabledata_to_needed(arr) {
      arr.map((item, index) => {
        item._index = (this.pagination.current - 1) * this.pagination.pageSize + index + 1;
      });
      return arr;
    },
    onShowSizeChange(current, pageSize){
      this.pagination.current = 1;
      this.pagination.pageSize = pageSize;
    },
    onChange(current) {
      this.pagination.current = current
    },
    handle_error(){
      this.$message.error( i18n.t('external.message2.label67'))
    },
    // 是否是对内或者对外的渠道(代理商户)
    is_has_children() {
      return this.src_internal || (this.src_external && [1, 10].includes(this.get_user_info.agentLevel));
    }
  },
  watch: {
    searchForm: {
      handler(val){
        this.pagination.current = 1;
      },
      deep: true
    },
    ...getAutoParams({
      keys: ["current", "pageSize","sort","orderBy","dateType","outComeList", 'amountTag'],
      obj: "pagination",
      cb: function () {
        return function () {
          if(!this.is_bet_slip_order_no_page) {
            this.initTableData && this.initTableData?.();
          }
        }
      }()
    }),
    get_window_size_info(){
      this.get_max_height();
    }
  },
}