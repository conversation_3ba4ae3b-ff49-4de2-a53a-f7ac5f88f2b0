/*
 * @Descripttion:
 * @Author: 
 * @Date: 2024-07-14 09:48:04
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_17;

//危险投注

export const get_sportList = (params, url = "/admin/sport/list") =>
  axios.post(`${prefix}${url}`, params); //获取球类种类


// 危险联赛池管理
export const post_danger_League_list = (//危险联赛列表
  params,
  url = "/admin/riskTournament/getTyRiskTournamentList"
) => axios.post(`${prefix}${url}`, params);

export const post_add_Risk_Tournament = (//新增危险联赛
  params,
  url = "/admin/riskTournament/addRiskTournament"
) => axios.post(`${prefix}${url}`, params);

export const del_Risk_Tournament_List = (//删除危险联赛列表
  params,
  url = "/admin/riskTournament/delRiskTournamentList"
) => axios.post(`${prefix}${url}`, params);

export const query_disRisk_TournamentList = (//查询非危险联赛列表
  params,
  url = "/admin/riskTournament/queryDisRiskTournamentList"
) => axios.post(`${prefix}${url}`, params);

export const update_Risk_LevelById = (//危险等级更改
  params,
  url = "/admin/riskTournament/updateRiskLevelById"
) => axios.post(`${prefix}${url}`, params);

export const update_Status_ById = (//危险联赛有效状态更改
  params,
  url = "/admin/riskTournament/updateStatusById"
) => axios.post(`${prefix}${url}`, params);

export const query_Region_Listd = (//地区下拉查询
  params,
  url = "/admin/riskCommon/queryRegionList"
) => axios.post(`${prefix}${url}`, params);

// 危险球队池管理
export const post_danger_team_list = (
  params,
  url = "/admin/riskManagement/getRiskTeamList"
) => axios.post(`${prefix}${url}`, params);



export const getMerchantRiskList = (//获取商户信息
  params,
  url = "/manage/merchant/getMerchantRiskList"
) => axios.get(`${yewu21}${url}`, { params })

export const updateMerchantRiskStatus = (// 修改 1756特殊脚本商户
  params,
  url = "/manage/merchant/updateMerchantRiskStatus"
) => axios.post(`${yewu21}${url}`, params);

export const update_merchant_riskStatus = (// 新接口 tagid 267 修改 1756特殊脚本商户
  params,
  url = "/manage/merchant/updateDxMerchantRiskStatus"
) => axios.post(`${yewu21}${url}`, params);

// 危险联赛列表
export const post_getTyRiskTournamentList = (
  params,
  url = "/riskManagement/getTyRiskTournamentList"
) => axios.post(`${prefix}${url}`, params);
//危险球队列表
export const post_get_matchList = (
  params,
  url = "/admin/riskManagement/getRiskTeamList"
) => axios.post(`${prefix}${url}`, params)

// 球队列表
export const get_Team_List = (
  params,
  url = "/admin/riskManagement/getTeamList"
) => axios.post(`${prefix}${url}`, params)

//新增危险球队
export const post_add_RiskTeam = (
  params,
  url = "/admin/riskManagement/addRiskTeam"
) => axios.post(`${prefix}${url}`, params)

// 删除危险球队
export const delete_RiskTeam = (
  params,
  url = "/admin/riskManagement/deleteRiskTeam"
) => axios.post(`${prefix}${url}`, params)

// 危险球队信息危险等级修改
export const update_Risk_TeamLeve = (
  params,
  url = "/admin/riskManagement/updateRiskTeamLevel"
) => axios.post(`${prefix}${url}`, params)

// 危险球队信息状态修改
export const update_RiskTeam_Status = (
  params,
  url = "/admin/riskManagement/updateRiskTeamStatus"
) => axios.post(`${prefix}${url}`, params)

