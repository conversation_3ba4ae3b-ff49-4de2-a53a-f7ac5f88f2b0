
import moment from "moment";
import { i18n } from "src/boot/i18n";
import { mapGetters, mapActions } from "vuex";
import { api_user,api_merchant,api_data, api_match_statistic } from "src/api/index.js";
import { get_match_type, post_trader_select_list } from "src/api/internal/module/match_statistic/index.js";
import commonmixin from "src/mixins/internal/common/commontoolmixin.js";
import constantmixin from "src/mixins/internal/common/constantmixin.js";
import columns_fn from "src/components/common/achievements_page/config/common.js";//数据中心-赛事投注统计-主列表配置(对内/外)
import log_login_mixin from "src/components/common/bet_slip/internal/mixin/index.js";
import { handleCopy, getStrLength, download } from "src/util/module/common.js";
import commonmixin_external from "src/mixins/external/common/commontoolmixin.js";
import dataCenterMixin from "src/mixins/internal/module/datacentertablemixin.js";
import constantmixin_external from "src/mixins/external/common/constantmixin.js";
import dataCenterMixin_external from "src/mixins/external/module/datacentertablemixin.js";

import {matchLevelList,syndicate_results,expand_conditions,matchStatus,purchaseStatus} from "src/components/common/achievements_page/module/index.js";


const FOR_WHICH = process.env.FOR_WHICH;
let mixins_custom= [commonmixin, dataCenterMixin, log_login_mixin, constantmixin];
if(FOR_WHICH=="external"){
  mixins_custom= [commonmixin_external, dataCenterMixin_external, log_login_mixin, constantmixin_external];
}
export default {
  mixins: [...mixins_custom],
  data() {
    return {
      purchaseStatus,
      matchStatus,
      matchLevelList,
      syndicate_results,
      expand_conditions,
      syndicate_results_class:{
        //合买未成功
        2:"black",
        //合买成功
        3:"blue"
      },
      filterstatues_class:{
        "2": "black",
        "3": "black",
        "4": "red",
        "5": "blue",
        "6": "black"
      },
      isDay: false, //  false:账务日     ;    true 自然日
      tabledata: [], // 表格数据
      pull_down_tournament: [],// 联赛名称下拉
      showDialogObj: "",
      tabledata_loading: false,
      // 目前合买只支持单关
      seriesType:1, // 投注方式  1单关投注  2串关投注
      isDay: false, //  false:账务日     ;    true 自然日  
      
      // 用户统计
      userStatistics: {
       /**
       * 发起合买历史数
       */
      countPurchase:"",

      /**
       * 合买累计总金额
       */
      purchaseAmountTotal:"",

      /**
       * 合买中奖总金额
       */
      settleAmountTotal:"",

      /**
       * 合买成功总场次
       */
      successTotal:"",

      /**
       * 合买失败总场次
       */
      failTotal:"",

      /**
       * 合买成功且赢的总场次
       */
      winSessions:"",

      /**
       * 合买胜率
       */
      winningRate:"",
      /**
           * 合买成功且输的总场次
           */
      lostSessions:"",
        currency: "", //币种
      },
      searchForm: {
        range_picker_value: [], // 日期选择器
        sportId: "",
        tournamentId: "",
        userid:"",
        matchId:"",
      },
      dataTable: [], // 多币种数据
    };
  },
  created() {
    this.init();
  },
  destroyed() {
    this.clear_bool();
  },
  computed: {
    ...mapGetters(["get_user_info" ]),
    /**
     * table配置 赛事投注统计 / 即时赛事统计报表
     */
    computed_columns(){
        let columns_list = columns_fn({src_internal: this.src_internal})
        return columns_list;
    },
  },
  watch:{
    // allM
    
    isDay(val) {
      let [startTime, endTime] = this.searchForm.range_picker_value;
      //时间大于12点
      let gt12 = moment().format("HH") > 12;
      let today_d = new Date().getDate();
      let startTime_YMD = startTime.format("YYYY-MM-DD");
      let endTime_YMD = endTime.format("YYYY-MM-DD");
      
      let new_day=gt12?1:0
      let today_YMD = moment(new Date().setDate(today_d- 7+new_day)).format("YYYY-MM-DD");
      let yesterday_YMD = moment(new Date().setDate(today_d+new_day )).format(
        "YYYY-MM-DD"
      );
      if (val) {
        // 自然日是从 00:00:00 到 23:59:59
        if (this.is_date_change) {
          // 手动选中了查询时间
          startTime = `${startTime_YMD} 00:00:00`;
          endTime = `${endTime_YMD} 23:59:59`;
        } else {
          startTime =`${today_YMD} 00:00:00`;
          endTime =`${yesterday_YMD} 23:59:59`;
        }
      } else {
        // 账务日 是从今天的12:00:00 到 明天的11:59:59
        if (this.is_date_change) {
          // 手动选择了查询时间
          startTime = `${startTime_YMD} 12:00:00`;
          endTime = `${endTime_YMD} 11:59:59`;
        } else {
          if (this.src_internal) {
            // 默认查询时间
            startTime =  `${today_YMD} 12:00:00`;
            endTime =`${yesterday_YMD} 11:59:59`;
          } else {
            // 对外默认时间改为最近6小时
            startTime = moment(new Date() - 21600000).format("YYYY-MM-DD HH:mm:ss");
            endTime = `${`${today_YMD}` + ` ${moment().format("HH")}`}:59:59`
          }
        }
      }
      // console.error("----账务日  自然日  切换 ------ startTime----", startTime);
      // console.error("----账务日  自然日  切换 ------ endTime----", endTime);
      this.searchForm.range_picker_value = [
        moment(startTime, "YYYY-MM-DD HH:mm:ss"),
        moment(endTime, "YYYY-MM-DD HH:mm:ss"),
      ];
    },
  },
  methods: {
    ...mapActions(["clear_bool"]),
    getStrLength,
    download,
    moment,
    // 输赢文字显示
    getSubstrString(val) {
      if (val) {
        let i = val.indexOf("时间：");
        if (i > 0) {
          val = val.substr(i);
        }
        return val;
      }
    },
      
    // 通过 单条数据和 key ，赛种  以及 位数  计算显示
    /**
     * record 单条数据
     * key :  赔率
     * 有电竞 赔率 显示 3位 否则 2位
     */
    get_float_str_by_record(record, odds) {
      // console.log('---11---',record, odds);
      if (!odds) {
        return odds;
      }
      // record.orderDetailList
      //计算 有 电竞注单  sportid
      let str = "";
      let has_danjing = false;
        let sid = record["sportId"];
        if (sid >= 100 && sid < 1000) {
          has_danjing = true;
        }
      if (has_danjing) {
        str = this.get_float_str(odds, 3);
      } else {
        //  ("US", "美式盘", 2), ("ID", "印尼盘", 3), ("MY", "马来盘", 4), ("GB", "英式盘", 5); 这些盘口值有负数和分数的情况，取接口返回值直接展示
        let ty_marketType =record.marketType;
        if ( ty_marketType == "ID" || ty_marketType == "US" || ty_marketType == "GB" || ty_marketType == "MY") {
          str =record.oddFinally
        }else {
          str = this.get_float_str(odds, 2);
        }
      }
      return str;
    },
    // 日期选择
    on_range_picker_value_change(dates, dateStrings) {
      // 手动选择了查询时间
      this.is_date_change = true;
      this.show_message_warn_when_filter_eq_3();
      this.init_pull_down_tournament();
    },
    //  //加个提示语，告知使用者只能查最近35天已结算的注单
    show_message_warn_when_filter_eq_3() {
      //合买最大查询不能超过100天
        let [startTime] = this.searchForm.range_picker_value;
        // 搜索的 开始时间的当天的 早上的 00.00.00
        let s = Date.parse(startTime.format("YYYY-MM-DD"));
        let t = Date.parse(moment(new Date()).format("YYYY-MM-DD"));
        let c = (t - s) / (24 * 60 * 60 * 1000) > 100;
        if (c) {
          this.$message.warn(i18n.t("internal.message.label137_1"));
        }
    },
    // 初始化 日期时间选择 默认值
    init_searchForm_range_picker_value() {
      //  false:账务日     ;    true 自然日
      this.isDay = false;
      let gt12 = moment().format("HH") > 12;
      let today_d = new Date().getDate();
      let new_day=gt12?1:0
    let today_YMD = moment(new Date().setDate(today_d- 7+new_day)).format("YYYY-MM-DD");
    let yesterday_YMD = moment(new Date().setDate(today_d+new_day )).format(
      "YYYY-MM-DD"
    );
       let startTime = `${ today_YMD} 12:00:00`;
       let endTime = `${ yesterday_YMD} 11:59:59` ;
        this.searchForm.range_picker_value = [
          moment(startTime, "YYYY-MM-DD HH:mm:ss"),
          moment(endTime, "YYYY-MM-DD HH:mm:ss"),
        ];
      

    },
    init() {
      this.init_searchForm_range_picker_value()
      this.get_sport_list();
      this.init_pull_down_tournament();
    },
    /**
     * @description:下拉选择联赛
     */
    async init_pull_down_tournament() {
      let [startTime, endTime] = this.searchForm.range_picker_value;
      let params = { 
          startTime:  startTime ? startTime.format("YYYY-MM-DD HH:mm:ss") : null, 
          endTime: endTime ? endTime.format("YYYY-MM-DD HH:mm:ss") : null,  };
      params = this.delete_empty_property_with_exclude(params);
      let query_pull_down_tournament=this.src_internal?api_merchant.query_pull_down_tournament:api_data.query_pull_down_tournament
      let res = await query_pull_down_tournament(params);
      console.warn(res);
      if ((res.data.code = "0000000")) {
        this.pull_down_tournament = res.data.data || [];
      }
    },
    
    /**
     * @description:联赛名称取值
     */
    handle_match_tournamentId_change(value) {
      this.searchForm.tournamentId = value;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    /**
     * 即时赛事统计报表-下拉选择联赛
     * @param {*} value 
     */
    change_match_name(value){
      this.searchForm.tournamentId = value;
    },
    handleCopy,
    /**
     * @description 鼠标移上去显示icon
     * @param  {String} 某一行的标识
     * @param  {Number} 1 移入 2 离开
     * @return {undefined} undefined
     */
    tooltip_icotitle_hidden(key, value) {
      let dom = this.$refs[key];
      if (value == 1) {
        dom.classList.remove("hidden");
      } else {
        dom.classList.add("hidden");
      }
    },
    handle_search() {
      if (
        this.pagination.current > 1
      ) {
        this.pagination.current = 1;
      }
      this.initTableData();
    },
    
    /**
     * @description 统计接口
     * @param  {Object} params 传参
     * @return {undefined} undefined
     */
     query_statistics(params) {
      this.userStatistics = {};
      this.totalLoading = true;
      let api_fn = "";
      // 对内
      if (this.src_internal) {
        // 注单查询（ES） : 注单查询
        api_fn =api_user.post_getRecordStatistics
      } else {
        api_fn = api_data.post_getRecordStatistics
      }
      api_fn(params)
        .then((res) => {
          this.totalLoading = false;
          let sus = this.$lodash.get(res, "data.code");
          let data = this.$lodash.get(res, "data.data") || {};
          if (sus == "0000000") {
              this.userStatistics = data;
            this.userStatistics.currency = "";
          }
        })
        .catch((err) => {
          console.error(err);
        });
    },
    
     /**
    * @description:查询列表数据
    * @param {type}
    * @return {type}
    */
    initTableData() {
      this.tabledata_loading = true;
      let params = this.compute_init_tabledata_params();
      params = this.delete_empty_property_with_exclude(params);
      let api_fn=this.src_internal?api_user.post_querySyndicatedRecordList:api_data.post_querySyndicatedRecordList
        api_fn(params).then((res) => {
        this.tabledata_loading = false;
          let code = this.$lodash.get(res, "data.code");
          if (code == "0000000") {
            let arr = this.$lodash.get(res, "data.data.list") || [];
            if(this.src_external){
            this.pagination.start = this.$lodash.get(res, "data.data.start");
            }
            this.tabledata = this.rebuild_tabledata_to_needed(arr);
            this.pagination.total =
              this.$lodash.get(res, "data.data.total") * 1 || 0;
            // 统计
            this.query_statistics(params);
          }else {
            if(this.src_external){
              this.tabledata = [];
              code !== "0400403" && this.$message.error(res.data.msg, 5);
            }
          }
      }).finally(err=>{
        this.tabledata_loading = false;
      });
    },
     /**
    * @description:过滤参数
    * @param {type}
    * @return {type}
    */
    compute_init_tabledata_params() {
      let { current, pageSize, sort, orderBy } = this.pagination;
      let [startTime, endTime] = this.searchForm.range_picker_value;
      let {
        matchId,
        tournamentId,
        sportId,
      } = this.searchForm;
      let obj = {}
        obj = {
          matchId,
          userId: this.$route.query.userId,
          pageNum: current,
          pageSize,
          tournamentId,
          startTime:  startTime ? startTime.format("YYYY-MM-DD HH:mm:ss") : null, 
          endTime: endTime ? endTime.format("YYYY-MM-DD HH:mm:ss") : null, 
          sportId,
          sort,
          orderBy,
        }
      return obj;
    },
    /**
    * @description:列表数据重新组装
    * @param {type}
    * @return {type}
    */
    rebuild_tabledata_to_needed(arr) {
      arr.map((item, index) => {
        item._index =
          (this.pagination.current - 1) * this.pagination.pageSize + index + 1;
        item._id =
          (this.pagination.current - 1) * this.pagination.pageSize + index + 1;
      
      });
      return arr;
    },  
  },
};