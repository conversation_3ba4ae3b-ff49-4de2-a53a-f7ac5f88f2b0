/*
 * @Desc: 
 * @Author:Nice
 * @Date:
 * @FilePath: /src/api/external/module/finance/index.js
 */
import axios from "src/api/common/axioswarpper.js";

let prefix1 = process.env.API_PREFIX_17;

//财务-清算管理列表// 716
export const post_report_financeMonth_queryFinanceMonthList = (
  params,
  url = "/admin/financeMonth/queryFinanceMonthList"
) => axios.post(`${prefix1}${url}`, params);

//财务-清算管理-总计
export const post_report_financeMonth_queryFinanceMonthTotal = (
  params,
  url = "/admin/financeMonth/queryFinanceMonthTotal"
) => axios.post(`${prefix1}${url}`, params);

//财务-清算管理-电子账单// 716
export const post_report_financeMonth_queryFinanceMonthDetail = (
  params,
  url = "/admin/financeMonth/queryFinanceMonthDetail"
) => axios.post(`${prefix1}${url}`, params, {type: 5});

//财务-清算管理-费率修改记录// 716
export const post_report_financeMonth_getFinanceOperateRecordList = (
  params,
  url = "/admin/financeMonth/getFinanceOperateRecordList"
) => axios.post(`${prefix1}${url}`, params,{type: 1});

//财务-清算管理-电子账单-update// 716
export const post_report_financeMonth_updateFinanceMonthDetail = (
  params,
  url = "/admin/financeMonth/updateFinanceMonthDetail"
) => axios.post(`${prefix1}${url}`, params);

//财务-日对账单// 主列表
export const post_report_financeMonth_queryFinanceDay = (
  params,
  url = "/admin/financeMonth/queryFinanceayTotalList"
) => axios.post(`${prefix1}${url}`, params);

//财务-日对账单// 每日明细 弹窗用
export const post_report_financeMonth_queryFinanceDayV2 = (
  params,
  url = "/admin/financeMonth/queryFinanceDayV2"
) => axios.post(`${prefix1}${url}`, params);

// 财务-日对账单-总计 // 713
export const post_report_financeMonth_queryFinanceDayTotal = (
  params,
  url = "/admin/financeMonth/queryFinanceDayTotal"
) => axios.post(`${prefix1}${url}`, params);

//财务-清算管理二级-总计 716
export const post_report_financeMonth_queryFinanceMonthCount = (
  params,
  url = "/admin/financeMonth/queryFinanceMonthCount"
) => axios.post(`${prefix1}${url}`, params);
// 财务中心—对账工具-商户对账-开始对账
export const post_checkFinance = (
  params,
  url = "/admin/check/checkFinance"
) => axios.post(`${prefix1}${url}`, params);
// 财务中心—对账工具-商户对账—更正报表
export const post_editFinance = (
  params,
  url = "/admin/check/editFinance"
) => axios.post(`${prefix1}${url}`, params);

// 财务中心—对账工具-用户对账-开始对账
export const post_checkUserFinance = (
  params, 
  url = "/admin/check/checkUserFinance"
  ) => axios.post(`${prefix1}${url}`, params);
// 财务中心—对账工具-用户对账—更正报表
export const post_editUserFinance = (
  params,
  url = "/admin/check/editUserFinance"
  ) => axios.post(`${prefix1}${url}`, params);

// 对外商户 彩票-对账单
export const post_queryFinCpTotalList = (
  params,
  url = "/admin/financeCpMonth/queryFinCpTotalList"
) => axios.post(`${prefix1}${url}`, params);

// 对外商户 彩票-对账单-总计
export const post_queryFinCpDayTotal = (
  params,
  url = "/admin/financeCpMonth/queryFinCpDayTotal"
) => axios.post(`${prefix1}${url}`, params);

// 对内商户 彩票-对账单-每日明细
export const post_queryFinCpTotalDayList = (
  params,
  url = "/admin/financeCpMonth/queryFinCpTotalDayList"
) => axios.post(`${prefix1}${url}`, params);

// 对外商户 彩票-对账单-导出
// export const post_financeCpFileExport = (
//   params,
//   url = "/admin/financeCpMonth/financeCpFileExport"
// ) => axios.post(`${prefix1}${url}`, params);

// 对外商户 真人-对账单
export const post_queryFinZrTotalList = (
  params,
  url = "/admin/financeZrMonth/queryFinZrTotalList"
) => axios.post(`${prefix1}${url}`, params);

// 对外商户 真人-对账单-总计
export const post_queryFinZrDayTotal = (
  params,
  url = "/admin/financeZrMonth/queryFinZrDayTotal"
) => axios.post(`${prefix1}${url}`, params);

// 对外商户 真人-对账单-导出
// export const post_financeZrFileExport = (
//   params,
//   url = "/admin/financeZrMonth/financeZrFileExport"
// ) => axios.post(`${prefix1}${url}`, params);

// 对外商户 真人-对账单-总计
export const post_queryFinZrTotalDayList = (
  params,
  url = "/admin/financeZrMonth/queryFinZrTotalDayList"
) => axios.post(`${prefix1}${url}`, params);


// 对内商户 真人-数据中心-商户注单统计-列表
export const post_zr_merchant_queryFinZrTotalList = (
  params,
  url = "/admin/financeZrMonth/queryFinZrTotalList"
) => axios.post(`${prefix1}${url}`, params);

// 对内商户 真人-数据中心-商户注单统计-汇总
export const post_zr_merchant_queryFinZrDayTotal = (
  params,
  url = "/admin/financeZrMonth/queryFinZrDayTotal"
) => axios.post(`${prefix1}${url}`, params);

// 对内商户 彩票-数据中心-商户注单统计-列表
export const post_cp_merchant_queryFinCpTotalList = (
  params,
  url = "/admin/financeCpMonth/queryFinCpTotalList"
) => axios.post(`${prefix1}${url}`, params);

// 对内商户 彩票-数据中心-商户注单统计-汇总
export const post_cp_merchant_queryFinCpDayTotal = (
  params,
  url = "/admin/financeCpMonth/queryFinCpDayTotal"
) => axios.post(`${prefix1}${url}`, params);


//对内商户 财务中心/二次结算列表
export const post_secondary_settlemen_pageList = (
  params,
  url = "/admin/orderTimeSettle/pageList"
) => axios.post(`${prefix1}${url}`, params);






  //结算查询
  import * as settlement_inquiry from "src/api/external/module/finance/module/settlement_inquiry.js";

  export {settlement_inquiry}