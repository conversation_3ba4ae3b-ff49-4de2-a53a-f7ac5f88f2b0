<!--
 * @FilePath:
 * @Description: 二次结算查询-驳回原因、驳回历史
-->
<template>
  <div style="width: 600px; height: auto; overflow: hidden;  min-width:600px; " class="text-panda-text-7 bg-white">
    <div class="bg-white text-black">
      <q-card-section class="no-padding">
        <div class="row line-height-40px fs14 bg-panda-dialog text-panda-text-7 pr10x">
          <div style="display: flex; justify-content: center;" class=" text-weight-bolder">
            <div style="display: flex; align-items: center;">
              <a-icon
              type="exclamation-circle"
              class="ml10x mr10x"
              style="color: orange; cursor: pointer; font-size: 20px"
            />{{ $t("internal.finance.secondarysettlement.label17") }}
            </div>
          </div>
          <q-space />
          <q-btn class="mr5x text-panda-dialog-close" icon="close" v-close-popup />
        </div>
      </q-card-section>
      <q-separator></q-separator>
      <div>
        <div 
        v-for="(item, index) in history_list" class="remark-groub">
        <div> {{item.remark}}</div>
        <div class="text-grey"> {{ $t("internal.finance.secondarysettlement.label18") }}：
         {{moment(item.createTime).format("YYYY-MM-DD") }} 
        </div>
         <div  class="text-grey"> {{ $t("internal.finance.secondarysettlement.label19") }}： 
          {{moment(item.applicationTime).format("YYYY-MM-DD") }} 
         </div>
         
        </div>
      </div>
      </div>
  </div>
</template>


<script>
import moment from "moment";
import { handleCopy } from "src/util/module/common.js";
import { api_finance } from "src/api/index.js";
export default {
  name: "reason_for_rejection_dialog",

  props: {
    detailObj: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      history_list:[]
    };
  },
  created() {
    this.getRejectReasonList()
  },
  mounted() {
  },
  methods: {
    moment,
    // 获取驳回原因
    getRejectReasonList(){
      let params={
        settleOrderId:this.detailObj.id
      }
      api_finance.
      settlement_inquiry.getRejectReasonList(params)
        .then(res => {
          let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          if (code == "0000000") {
         let data =  this.$lodash.get(res, "data.data")
         if(data){
          this.history_list=data
         }
          } else {
            this.$message.error(msg, 5);
          }
        });
    },
  },
};
</script>
<style>
  .remark-groub{
    margin: 20px;
    border: 1px solid #dae2ea;
    padding: 10px;
  }
</style>