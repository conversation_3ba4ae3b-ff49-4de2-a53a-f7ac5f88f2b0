/*
 * @Description: 账户中心-异常用户名单(对外) 查询异常 配置
 */
import { i18n } from "src/boot/i18n";
export const dialog_find_baijia_config = ({})=> [
    {//区分分布
      title: i18n.t("external.account.user_risk_control.label44"),
      width: 80,
      dataIndex: "marketOddsType",
      key: "marketOddsType",
      align: "center",
      scopedSlots: { customRender: "marketOddsType" },
    },
  {//数量
    title: i18n.t("external.account.user_risk_control.label45"),
    width: 80,
    dataIndex: "orderCount",
    key: "orderCount",
    align: "center",
    scopedSlots: { customRender: "orderCount" },
  },
  {//占比
    title: i18n.t("external.account.user_risk_control.label46"),
    dataIndex: "ratio",
    key: "ratio",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "ratio" },
  }];

