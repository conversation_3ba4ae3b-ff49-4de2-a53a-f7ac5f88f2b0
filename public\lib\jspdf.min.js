 /* eslint-disable */
!function(t){"function"==typeof define&&define.amd?define(t):t()}(function(){"use strict";
/** @license
   *
   * jsPDF - PDF Document creation from JavaScript
   * Version 2.1.1 Built on 2019-10-11T08:56:17.234Z
   *                      CommitID 0dd01f177e
   *
   * Copyright (c) 2010-2018 <PERSON> <<EMAIL>>, https://github.com/MrRio/jsPDF
   *               2015-2018 yWorks GmbH, http://www.yworks.com
   *               2015-2018 <PERSON><PERSON> <<EMAIL>>, https://github.com/HackbrettXXX
   *               2016-2018 <PERSON><PERSON> <<EMAIL>>
   *               2010 <PERSON>, https://github.com/acspike
   *               2012 Willow Systems Corporation, willow-systems.com
   *               2012 <PERSON>, https://github.com/pablohess
   *               2012 <PERSON>lor<PERSON>, https://github.com/fjenett
   *               2013 <PERSON>, https://github.com/warrenweckesser
   *               2013 <PERSON><PERSON><PERSON>, https://github.com/lifof
   *               2013 <PERSON>, https://github.com/lsdriscoll
   *               2013 <PERSON>, https://github.com/stefslon
   *               2013 <PERSON> Morel, https://github.com/jmorel
   *               2013 <PERSON>mann, https://github.com/chris-rock
   *               2014 Juan Pablo Gaviria, https://github.com/juanpgaviria
   *               2014 James Makes, https://github.com/dollaruw
   *               2014 Diego Casorran, https://github.com/diegocr
   *               2014 Steven Spungin, https://github.com/Flamenco
   *               2014 Kenneth Glassey, https://github.com/Gavvers
   *
   * Licensed under the MIT License
   *
   * Contributor(s):
   *    siefkenj, ahwolf, rickygu, Midnith, saintclair, eaparango,
   *    kim3er, mfo, alnorth, Flamenco
   */function on(t){return(on="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}!function(t){if("object"!==on(t.console)){t.console={};for(var e,n,r=t.console,i=function(){},a=["memory"],o="assert,clear,count,debug,dir,dirxml,error,exception,group,groupCollapsed,groupEnd,info,log,markTimeline,profile,profiles,profileEnd,show,table,time,timeEnd,timeline,timelineEnd,timeStamp,trace,warn".split(",");e=a.pop();)r[e]||(r[e]={});for(;n=o.pop();)r[n]||(r[n]=i)}var s,u,l,c,h="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function f(){}void 0===t.btoa&&(t.btoa=function(t){var e,n,r,i,a,o=0,s=0,u="",l=[];if(!t)return t;for(;e=(a=t.charCodeAt(o++)<<16|t.charCodeAt(o++)<<8|t.charCodeAt(o++))>>18&63,n=a>>12&63,r=a>>6&63,i=63&a,l[s++]=h.charAt(e)+h.charAt(n)+h.charAt(r)+h.charAt(i),o<t.length;);u=l.join("");var c=t.length%3;return(c?u.slice(0,c-3):u)+"===".slice(c||3)}),void 0===t.atob&&(t.atob=function(t){var e,n,r,i,a,o,s=0,u=0,l=[];if(!t)return t;for(t+="";e=(o=h.indexOf(t.charAt(s++))<<18|h.indexOf(t.charAt(s++))<<12|(i=h.indexOf(t.charAt(s++)))<<6|(a=h.indexOf(t.charAt(s++))))>>16&255,n=o>>8&255,r=255&o,l[u++]=64==i?String.fromCharCode(e):64==a?String.fromCharCode(e,n):String.fromCharCode(e,n,r),s<t.length;);return l.join("")}),Array.prototype.map||(Array.prototype.map=function(t){if(null==this||"function"!=typeof t)throw new TypeError;for(var e=Object(this),n=e.length>>>0,r=new Array(n),i=1<arguments.length?arguments[1]:void 0,a=0;a<n;a++)a in e&&(r[a]=t.call(i,e[a],a,e));return r}),Array.isArray||(Array.isArray=function(t){return"[object Array]"===Object.prototype.toString.call(t)}),Array.prototype.reduce||Object.defineProperty(Array.prototype,"reduce",{value:function(t){if(null===this)throw new TypeError("Array.prototype.reduce called on null or undefined");if("function"!=typeof t)throw new TypeError(t+" is not a function");var e,n=Object(this),r=n.length>>>0,i=0;if(2<=arguments.length)e=arguments[1];else{for(;i<r&&!(i in n);)i++;if(r<=i)throw new TypeError("Reduce of empty array with no initial value");e=n[i++]}for(;i<r;)i in n&&(e=t(e,n[i],i,n)),i++;return e}}),Uint8Array.prototype.reduce||Object.defineProperty(Uint8Array.prototype,"reduce",{value:function(t){if(null===this)throw new TypeError("Array.prototype.reduce called on null or undefined");if("function"!=typeof t)throw new TypeError(t+" is not a function");var e,n=Object(this),r=n.length>>>0,i=0;if(2<=arguments.length)e=arguments[1];else{for(;i<r&&!(i in n);)i++;if(r<=i)throw new TypeError("Reduce of empty array with no initial value");e=n[i++]}for(;i<r;)i in n&&(e=t(e,n[i],i,n)),i++;return e}}),Array.prototype.forEach||(Array.prototype.forEach=function(t,e){if(null==this||"function"!=typeof t)throw new TypeError;for(var n=Object(this),r=n.length>>>0,i=0;i<r;i++)i in n&&t.call(e,n[i],i,n)}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(t){if(null==this)throw new TypeError('"this" is null or not defined');var e=Object(this),n=e.length>>>0;if("function"!=typeof t)throw new TypeError("predicate must be a function");for(var r=arguments[1],i=0;i<n;){var a=e[i];if(t.call(r,a,i,e))return a;i++}},configurable:!0,writable:!0}),"function"!=typeof Object.create&&(Object.create=function(t,e){if(t!==Object(t)&&null!==t)throw TypeError("Argument must be an object, or null");f.prototype=t||{},void 0!==e&&Object.defineProperties(f.prototype,e);var n=new f;return(f.prototype=null)===t&&(n.__proto__=null),n}),Object.keys||(Object.keys=(s=Object.prototype.hasOwnProperty,u=!{toString:null}.propertyIsEnumerable("toString"),c=(l=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"]).length,function(t){if("object"!==on(t)&&("function"!=typeof t||null===t))throw new TypeError;var e,n,r=[];for(e in t)s.call(t,e)&&r.push(e);if(u)for(n=0;n<c;n++)s.call(t,l[n])&&r.push(l[n]);return r})),Object.values||(Object.values=function(t){var e=[];for(var n in t)t.hasOwnProperty(n)&&t.propertyIsEnumerable(n)&&e.push(t[n]);return e}),"function"!=typeof Object.assign&&(Object.assign=function(t){var e=arguments;if(null==t)throw new TypeError("Cannot convert undefined or null to object");t=Object(t);for(var n=1;n<arguments.length;n++){var r=e[n];if(null!=r)for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t}),String.prototype.trim||(String.prototype.trim=function(){return this.replace(/^\s+|\s+$/g,"")}),String.prototype.trimLeft||(String.prototype.trimLeft=function(){return this.replace(/^\s+/g,"")}),String.prototype.trimRight||(String.prototype.trimRight=function(){return this.replace(/\s+$/g,"")}),Number.isInteger=Number.isInteger||function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t}}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||"undefined"!=typeof global&&global||Function('return typeof this === "object" && this.content')()||Function("return this")());var sn,t,e,a,n,r,i,o,s,f,u,l,P,k,F,d,p,g,m,b,v,c,y,w,h,x,N,L,A,_,S,C,I,j,B,O,E,M,T,q,R,D,U=(sn="undefined"!=typeof self&&self||"undefined"!=typeof window&&window||"undefined"!=typeof global&&global||Function('return typeof this === "object" && this.content')()||Function("return this")(),ln.API={events:[]},ln.version="2.1.1","function"==typeof define&&define.amd?define(function(){return ln}):"undefined"!=typeof module&&module.exports?(module.exports=ln,module.exports.jsPDF=ln):sn.jsPDF=ln,ln);
/**
   * @license
   * Copyright (c) 2016 Alexander Weidt,
   * https://github.com/BiggA94
   *
   * Licensed under the MIT License. http://opensource.org/licenses/mit-license
   */function un(a){if("object"!==on(a))throw new Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var o={};this.subscribe=function(t,e,n){if(n=n||!1,"string"!=typeof t||"function"!=typeof e||"boolean"!=typeof n)throw new Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");o.hasOwnProperty(t)||(o[t]={});var r=Math.random().toString(35);return o[t][r]=[e,!!n],r},this.unsubscribe=function(t){for(var e in o)if(o[e][t])return delete o[e][t],0===Object.keys(o[e]).length&&delete o[e],!0;return!1},this.publish=function(t){if(o.hasOwnProperty(t)){var e=Array.prototype.slice.call(arguments,1),n=[];for(var r in o[t]){var i=o[t][r];try{i[0].apply(a,e)}catch(t){sn.console&&console.error("jsPDF PubSub Error",t.message,t)}i[1]&&n.push(r)}n.length&&n.forEach(this.unsubscribe)}},this.getTopics=function(){return o}}function ln(t){var r,a="string"==typeof arguments[0]?arguments[0]:"p",e=arguments[1],o=arguments[2],n=arguments[3],i=[],s=1,u=16,l="S";"object"===on(t=t||{})&&(a=t.orientation,e=t.unit||e,o=t.format||o,n=t.compress||t.compressPdf||n,s="number"==typeof t.userUnit?Math.abs(t.userUnit):1,void 0!==t.precision&&(r=t.precision),void 0!==t.floatPrecision&&(u=t.floatPrecision),l=t.defaultPathOperation||"S"),i=t.filters||(!0===n?["FlateEncode"]:i),e=e||"mm",a=(""+(a||"P")).toLowerCase();var c=t.putOnlyUsedFonts||!1,et={},h={internal:{},__private__:{}};h.__private__.PubSub=un;var f="1.3",d=h.__private__.getPdfVersion=function(){return f};h.__private__.setPdfVersion=function(t){f=t};var p={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};h.__private__.getPageFormats=function(){return p};var g=h.__private__.getPageFormat=function(t){return p[t]};o=o||"a4";var m="compat",nt="advanced",rt=m;function b(){this.saveGraphicsState(),st(new Ft(tt,0,0,-tt,0,rn()*tt).toString()+" cm"),this.setFontSize(this.getFontSize()/tt),l="n",rt=nt}function v(){this.restoreGraphicsState(),l="S",rt=m}h.advancedAPI=function(t){var e=rt===m;return e&&b.call(this),"function"!=typeof t||(t(this),e&&v.call(this)),this},h.compatAPI=function(t){var e=rt===nt;return e&&v.call(this),"function"!=typeof t||(t(this),e&&b.call(this)),this},h.isAdvancedAPI=function(){return rt===nt};function it(t){if(rt!==nt)throw new Error(t+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")}var at,y=h.roundToPrecision=h.__private__.roundToPrecision=function(t,e){var n=r||e;if(isNaN(t)||isNaN(n))throw new Error("Invalid argument passed to jsPDF.roundToPrecision");return t.toFixed(n).replace(/0+$/,"")};at=h.hpf=h.__private__.hpf="number"==typeof u?function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.hpf");return y(t,u)}:"smart"===u?function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.hpf");return y(t,-1<t&&t<1?16:5)}:function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.hpf");return y(t,16)};function w(t){return ot(function(t){return rt===m?rn()-t:rt===nt?t:void 0}(t))}var x=h.f2=h.__private__.f2=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.f2");return y(t,2)},N=h.__private__.f3=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.f3");return y(t,3)},ot=h.scale=h.__private__.scale=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.scale");return rt===m?t*tt:rt===nt?t:void 0};h.__private__.setPrecision=h.setPrecision=function(t){"number"==typeof parseInt(t,10)&&(r=parseInt(t,10))};var L,A="00000000000000000000000000000000",_=h.__private__.getFileId=function(){return A},S=h.__private__.setFileId=function(t){return A=void 0!==t&&/^[a-fA-F0-9]{32}$/.test(t)?t.toUpperCase():A.split("").map(function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))}).join("")};h.setFileId=function(t){return S(t),this},h.getFileId=function(){return _()};var P=h.__private__.convertDateToPDFDate=function(t){var e=t.getTimezoneOffset(),n=e<0?"+":"-",r=Math.floor(Math.abs(e/60)),i=Math.abs(e%60),a=[n,j(r),"'",j(i),"'"].join("");return["D:",t.getFullYear(),j(t.getMonth()+1),j(t.getDate()),j(t.getHours()),j(t.getMinutes()),j(t.getSeconds()),a].join("")},k=h.__private__.convertPDFDateToDate=function(t){var e=parseInt(t.substr(2,4),10),n=parseInt(t.substr(6,2),10)-1,r=parseInt(t.substr(8,2),10),i=parseInt(t.substr(10,2),10),a=parseInt(t.substr(12,2),10),o=parseInt(t.substr(14,2),10);return new Date(e,n,r,i,a,o,0)},F=h.__private__.setCreationDate=function(t){var e;if(void 0===t&&(t=new Date),t instanceof Date)e=P(t);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(t))throw new Error("Invalid argument passed to jsPDF.setCreationDate");e=t}return L=e},C=h.__private__.getCreationDate=function(t){var e=L;return"jsDate"===t&&(e=k(L)),e};h.setCreationDate=function(t){return F(t),this},h.getCreationDate=function(t){return C(t)};var I,j=h.__private__.padd2=function(t){return("0"+parseInt(t)).slice(-2)},B=h.__private__.padd2Hex=function(t){return("00"+(t=t.toString())).substr(t.length)},O=0,E=[],M=[],T=0,q=[],R=[],D=!1,U=M;h.__private__.setCustomOutputDestination=function(t){D=!0,U=t};function z(t){D||(U=t)}h.__private__.resetCustomOutputDestination=function(){D=!1,U=M};var st=h.__private__.out=function(t){return t=t.toString(),T+=t.length+1,U.push(t),U},H=h.__private__.write=function(t){return st(1===arguments.length?t.toString():Array.prototype.join.call(arguments," "))},W=h.__private__.getArrayBuffer=function(t){for(var e=t.length,n=new ArrayBuffer(e),r=new Uint8Array(n);e--;)r[e]=t.charCodeAt(e);return n},V=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];h.__private__.getStandardFonts=function(){return V};var ut=t.fontSize||16;h.__private__.setFontSize=h.setFontSize=function(t){return ut=rt===nt?t/tt:t,this};var G,Y=h.__private__.getFontSize=h.getFontSize=function(){return rt===m?ut:ut*tt},lt=t.R2L||!1;h.__private__.setR2L=h.setR2L=function(t){return lt=t,this},h.__private__.getR2L=h.getR2L=function(){return lt};var J,X=h.__private__.setZoomMode=function(t){var e=[void 0,null,"fullwidth","fullheight","fullpage","original"];if(/^\d*\.?\d*%$/.test(t))G=t;else if(isNaN(t)){if(-1===e.indexOf(t))throw new Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+t+'" is not recognized.');G=t}else G=parseInt(t,10)};h.__private__.getZoomMode=function(){return G};var K,Z=h.__private__.setPageMode=function(t){if(-1==[void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(t))throw new Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+t+'" is not recognized.');J=t};h.__private__.getPageMode=function(){return J};var $=h.__private__.setLayoutMode=function(t){if(-1==[void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(t))throw new Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+t+'" is not recognized.');K=t};h.__private__.getLayoutMode=function(){return K},h.__private__.setDisplayMode=h.setDisplayMode=function(t,e,n){return X(t),$(e),Z(n),this};var Q={title:"",subject:"",author:"",keywords:"",creator:""};h.__private__.getDocumentProperty=function(t){if(-1===Object.keys(Q).indexOf(t))throw new Error("Invalid argument passed to jsPDF.getDocumentProperty");return Q[t]},h.__private__.getDocumentProperties=function(){return Q},h.__private__.setDocumentProperties=h.setProperties=h.setDocumentProperties=function(t){for(var e in Q)Q.hasOwnProperty(e)&&t[e]&&(Q[e]=t[e]);return this},h.__private__.setDocumentProperty=function(t,e){if(-1===Object.keys(Q).indexOf(t))throw new Error("Invalid arguments passed to jsPDF.setDocumentProperty");return Q[t]=e};var ct,tt,ht,ft,dt,pt={},gt={},mt=[],bt={},vt={},yt={},wt={},xt=null,Nt=0,Lt=[],At=new un(h),_t=t.hotfixes||[],St={},Pt={},kt=[],Ft=function(t,e,n,r,i,a){var o=[];return Object.defineProperty(this,"sx",{get:function(){return o[0]},set:function(t){o[0]=t}}),Object.defineProperty(this,"shy",{get:function(){return o[1]},set:function(t){o[1]=t}}),Object.defineProperty(this,"shx",{get:function(){return o[2]},set:function(t){o[2]=t}}),Object.defineProperty(this,"sy",{get:function(){return o[3]},set:function(t){o[3]=t}}),Object.defineProperty(this,"tx",{get:function(){return o[4]},set:function(t){o[4]=t}}),Object.defineProperty(this,"ty",{get:function(){return o[5]},set:function(t){o[5]=t}}),Object.defineProperty(this,"a",{get:function(){return o[0]},set:function(t){o[0]=t}}),Object.defineProperty(this,"b",{get:function(){return o[1]},set:function(t){o[1]=t}}),Object.defineProperty(this,"c",{get:function(){return o[2]},set:function(t){o[2]=t}}),Object.defineProperty(this,"d",{get:function(){return o[3]},set:function(t){o[3]=t}}),Object.defineProperty(this,"e",{get:function(){return o[4]},set:function(t){o[4]=t}}),Object.defineProperty(this,"f",{get:function(){return o[5]},set:function(t){o[5]=t}}),Object.defineProperty(this,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(this,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(this,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(this,"isIdentity",{get:function(){return 1===this.sx&&(0===this.shy&&(0===this.shx&&(1===this.sy&&(0===this.tx&&0===this.ty))))}}),this.sx=isNaN(t)?1:t,this.shy=isNaN(e)?0:e,this.shx=isNaN(n)?0:n,this.sy=isNaN(r)?1:r,this.tx=isNaN(i)?0:i,this.ty=isNaN(a)?0:a,this};Ft.prototype.join=function(t){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map(at).join(t)},Ft.prototype.multiply=function(t){var e=t.sx*this.sx+t.shy*this.shx,n=t.sx*this.shy+t.shy*this.sy,r=t.shx*this.sx+t.sy*this.shx,i=t.shx*this.shy+t.sy*this.sy,a=t.tx*this.sx+t.ty*this.shx+this.tx,o=t.tx*this.shy+t.ty*this.sy+this.ty;return new Ft(e,n,r,i,a,o)},Ft.prototype.decompose=function(){var t=this.sx,e=this.shy,n=this.shx,r=this.sy,i=this.tx,a=this.ty,o=Math.sqrt(t*t+e*e),s=(t/=o)*n+(e/=o)*r;n-=t*s,r-=e*s;var u=Math.sqrt(n*n+r*r);return s/=u,t*(r/=u)<e*(n/=u)&&(t=-t,e=-e,s=-s,o=-o),{scale:new Ft(o,0,0,u,0,0),translate:new Ft(1,0,0,1,i,a),rotate:new Ft(t,e,-e,t,0,0),skew:new Ft(1,0,s,1,0,0)}},Ft.prototype.toString=function(t){return this.join(" ")},Ft.prototype.inversed=function(){var t=this.sx,e=this.shy,n=this.shx,r=this.sy,i=this.tx,a=this.ty,o=1/(t*r-e*n),s=r*o,u=-e*o,l=-n*o,c=t*o;return new Ft(s,u,l,c,-s*i-l*a,-u*i-c*a)},Ft.prototype.applyToPoint=function(t){var e=t.x*this.sx+t.y*this.shx+this.tx,n=t.x*this.shy+t.y*this.sy+this.ty;return new Ze(e,n)},Ft.prototype.applyToRectangle=function(t){var e=this.applyToPoint(t),n=this.applyToPoint(new Ze(t.x+t.w,t.y+t.h));return new $e(e.x,e.y,n.x-e.x,n.y-e.y)},Ft.prototype.clone=function(){var t=this.sx,e=this.shy,n=this.shx,r=this.sy,i=this.tx,a=this.ty;return new Ft(t,e,n,r,i,a)},h.Matrix=Ft;var Ct=h.matrixMult=function(t,e){return e.multiply(t)},It=new Ft(1,0,0,1,0,0);h.unitMatrix=h.identityMatrix=It;function jt(t,e){this.gState=t,this.matrix=e,this.id="",this.objectNumber=-1}function Bt(t,e){if(!vt[t]){var n=(e instanceof h.ShadingPattern?"Sh":"P")+(Object.keys(bt).length+1).toString(10);e.id=n,vt[t]=n,bt[n]=e,At.publish("addPattern",e)}}h.ShadingPattern=function(t,e,n,r,i){it("ShadingPattern"),this.type="axial"===t?2:3,this.coords=e,this.colors=n,jt.call(this,r,i)},h.TilingPattern=function(t,e,n,r,i){it("TilingPattern"),this.boundingBox=t,this.xStep=e,this.yStep=n,this.stream="",this.cloneIndex=0,jt.call(this,r,i)},h.TilingPattern.prototype={createClone:function(t,e,n,r,i){var a=new h.TilingPattern(e||this.boundingBox,n||this.xStep,r||this.yStep,this.gState,i||this.matrix);a.stream=this.stream;var o=t+"$$"+this.cloneIndex+++"$$";return Bt(o,a),a}},h.addShadingPattern=function(t,e){return it("addShadingPattern()"),Bt(t,e),this},h.beginTilingPattern=function(t){it("beginTilingPattern()"),Qe(t.boundingBox[0],t.boundingBox[1],t.boundingBox[2]-t.boundingBox[0],t.boundingBox[3]-t.boundingBox[1],t.matrix)},h.endTilingPattern=function(t,e){it("endTilingPattern()"),e.stream=R[I].join("\n"),Bt(t,e),At.publish("endTilingPattern",e),kt.pop().restore()};function Ot(t){t.objectNumber=Gt();var e=[];e.push({key:"Type",value:"/XObject"}),e.push({key:"Subtype",value:"/Form"}),e.push({key:"BBox",value:"["+[at(t.x),at(t.y),at(t.x+t.width),at(t.y+t.height)].join(" ")+"]"}),e.push({key:"Matrix",value:"["+t.matrix.toString()+"]"});var n=t.pages[1].join("\n");ee({data:n,additionalKeyValues:e}),st("endobj")}function Et(t,e){e||(e=21);var n=Gt(),r=function(t,e){var n,r=[],i=1/(e-1);for(n=0;n<1;n+=i)r.push(n);if(r.push(1),0!=t[0].offset){var a={offset:0,color:t[0].color};t.unshift(a)}if(1!=t[t.length-1].offset){var o={offset:1,color:t[t.length-1].color};t.push(o)}for(var s="",u=0,l=0;l<r.length;l++){for(n=r[l];n>t[u+1].offset;)u++;var c=t[u].offset,h=(n-c)/(t[u+1].offset-c),f=t[u].color,d=t[u+1].color;s+=B(Math.round((1-h)*f[0]+h*d[0]).toString(16))+B(Math.round((1-h)*f[1]+h*d[1]).toString(16))+B(Math.round((1-h)*f[2]+h*d[2]).toString(16))}return s.trim()}(t.colors,e),i=[];i.push({key:"FunctionType",value:"0"}),i.push({key:"Domain",value:"[0.0 1.0]"}),i.push({key:"Size",value:"["+e+"]"}),i.push({key:"BitsPerSample",value:"8"}),i.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),i.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),ee({data:r,additionalKeyValues:i,alreadyAppliedFilters:["/ASCIIHexDecode"]}),st("endobj"),t.objectNumber=Gt(),st("<< /ShadingType "+t.type),st("/ColorSpace /DeviceRGB");var a="/Coords ["+at(parseFloat(t.coords[0]))+" "+at(parseFloat(t.coords[1]))+" ";2===t.type?a+=at(parseFloat(t.coords[2]))+" "+at(parseFloat(t.coords[3])):a+=at(parseFloat(t.coords[2]))+" "+at(parseFloat(t.coords[3]))+" "+at(parseFloat(t.coords[4]))+" "+at(parseFloat(t.coords[5])),st(a+="]"),t.matrix&&st("/Matrix ["+t.matrix.toString()+"]"),st("/Function "+n+" 0 R"),st("/Extend [true true]"),st(">>"),st("endobj")}function Mt(t,e){var n=Yt(),r=Gt();e.push({resourcesOid:n,objectOid:r}),t.objectNumber=r;var i=[];i.push({key:"Type",value:"/Pattern"}),i.push({key:"PatternType",value:"1"}),i.push({key:"PaintType",value:"1"}),i.push({key:"TilingType",value:"1"}),i.push({key:"BBox",value:"["+t.boundingBox.map(at).join(" ")+"]"}),i.push({key:"XStep",value:at(t.xStep)}),i.push({key:"YStep",value:at(t.yStep)}),i.push({key:"Resources",value:n+" 0 R"}),t.matrix&&i.push({key:"Matrix",value:"["+t.matrix.toString()+"]"}),ee({data:t.stream,additionalKeyValues:i}),st("endobj")}function Tt(t){for(var e in t.objectNumber=Gt(),st("<<"),t)switch(e){case"opacity":st("/ca "+x(t[e]));break;case"stroke-opacity":st("/CA "+x(t[e]))}st(">>"),st("endobj")}function qt(t){Jt(t.resourcesOid,!0),st("<<"),st("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),function(){for(var t in st("/Font <<"),pt)!pt.hasOwnProperty(t)||(!1===c||!0===c&&et.hasOwnProperty(t))&&st("/"+t+" "+pt[t].objectNumber+" 0 R");st(">>")}(),function(){if(0<Object.keys(bt).length){for(var t in st("/Shading <<"),bt)bt.hasOwnProperty(t)&&bt[t]instanceof h.ShadingPattern&&0<=bt[t].objectNumber&&st("/"+t+" "+bt[t].objectNumber+" 0 R");At.publish("putShadingPatternDict"),st(">>")}}(),function(t){if(0<Object.keys(bt).length){for(var e in st("/Pattern <<"),bt)bt.hasOwnProperty(e)&&bt[e]instanceof h.TilingPattern&&0<=bt[e].objectNumber&&bt[e].objectNumber<t&&st("/"+e+" "+bt[e].objectNumber+" 0 R");At.publish("putTilingPatternDict"),st(">>")}}(t.objectOid),function(){if(0<Object.keys(yt).length){var t;for(t in st("/ExtGState <<"),yt)yt.hasOwnProperty(t)&&0<=yt[t].objectNumber&&st("/"+t+" "+yt[t].objectNumber+" 0 R");At.publish("putGStateDict"),st(">>")}}(),function(){for(var t in st("/XObject <<"),St)St.hasOwnProperty(t)&&0<=St[t].objectNumber&&st("/"+t+" "+St[t].objectNumber+" 0 R");At.publish("putXobjectDict"),st(">>")}(),st(">>"),st("endobj")}function Rt(){var t=[];!function(){for(var t in pt)!pt.hasOwnProperty(t)||(!1===c||!0===c&&et.hasOwnProperty(t))&&(n=pt[t],At.publish("putFont",{font:n,out:st,newObject:Gt,putStream:ee,pdfEscapeWithNeededParanthesis:e}),!0!==n.isAlreadyPutted&&(n.objectNumber=Gt(),st("<<"),st("/Type /Font"),st("/BaseFont /"+e(n.postScriptName)),st("/Subtype /Type1"),"string"==typeof n.encoding&&st("/Encoding /"+n.encoding),st("/FirstChar 32"),st("/LastChar 255"),st(">>"),st("endobj")));function e(t,e){return-1!==t.indexOf(" ")?"("+ie(t,e)+")":ie(t,e)}var n}(),function(){var t;for(t in yt)yt.hasOwnProperty(t)&&Tt(yt[t])}(),function(){for(var t in St)St.hasOwnProperty(t)&&Ot(St[t])}(),function(t){var e;for(e in bt)bt.hasOwnProperty(e)&&(bt[e]instanceof h.ShadingPattern?Et(bt[e]):bt[e]instanceof h.TilingPattern&&Mt(bt[e],t))}(t),At.publish("putResources"),t.forEach(qt),qt({resourcesOid:Zt,objectOid:Number.MAX_SAFE_INTEGER}),At.publish("postPutResources")}function Dt(t){gt[t.fontName]=gt[t.fontName]||{},gt[t.fontName][t.fontStyle]=t.id}function Ut(t,e,n,r,i){var a={id:"F"+(Object.keys(pt).length+1).toString(10),postScriptName:t,fontName:e,fontStyle:n,encoding:r,isStandardFont:i||!1,metadata:{}};return At.publish("addFont",{font:a,instance:this}),pt[a.id]=a,Dt(a),a.id}function zt(t,e){var n,r,i;switch(a=e||a,"string"==typeof t&&(n=g(t.toLowerCase()),Array.isArray(n)&&(r=n[0],i=n[1])),Array.isArray(t)&&(r=t[0]*tt,i=t[1]*tt),isNaN(r)&&(r=o[0],i=o[1]),(14400<r||14400<i)&&(console.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),r=Math.min(14400,r),i=Math.min(14400,i)),o=[r,i],a.substr(0,1)){case"l":r<i&&(o=[i,r]);break;case"p":i<r&&(o=[i,r])}ae(o),Be(je),st(Ue),0!==Ye&&st(Ye+" J"),0!==Je&&st(Je+" j"),At.publish("addPage",{pageNumber:Nt})}function Ht(t,e,n){var r,i=void 0;return n=n||{},t=void 0!==t?t:pt[ct].fontName,e=void 0!==e?e:pt[ct].fontStyle,r=t.toLowerCase(),void 0!==gt[r]&&void 0!==gt[r][e]?i=gt[r][e]:void 0!==gt[t]&&void 0!==gt[t][e]?i=gt[t][e]:!1===n.disableWarning&&console.warn("Unable to look up font label for font '"+t+"', '"+e+"'. Refer to getFontList() for available fonts."),i||n.noFallback||null==(i=gt.times[e])&&(i=gt.times.normal),i}function Wt(t){return!0===Array.isArray(_t)&&-1<_t.indexOf(t)}var Vt,Gt=h.__private__.newObject=function(){var t=Yt();return Jt(t,!0),t},Yt=h.__private__.newObjectDeferred=function(){return E[++O]=function(){return T},O},Jt=function(t,e){return e="boolean"==typeof e&&e,E[t]=T,e&&st(t+" 0 obj"),t},Xt=h.__private__.newAdditionalObject=function(){var t={objId:Yt(),content:""};return q.push(t),t},Kt=Yt(),Zt=Yt(),$t=h.__private__.decodeColorString=function(t){var e=t.split(" ");if(2!==e.length||"g"!==e[1]&&"G"!==e[1]){if(5===e.length&&("k"===e[4]||"K"===e[4])){e=[(1-e[0])*(1-e[3]),(1-e[1])*(1-e[3]),(1-e[2])*(1-e[3]),"r"]}}else{var n=parseFloat(e[0]);e=[n,n,n,"r"]}for(var r="#",i=0;i<3;i++)r+=("0"+Math.floor(255*parseFloat(e[i])).toString(16)).slice(-2);return r},Qt=h.__private__.encodeColorString=function(t){var e;"string"==typeof t&&(t={ch1:t});var n=t.ch1,r=t.ch2,i=t.ch3,a=t.ch4,o="draw"===t.pdfColorType?["G","RG","K"]:["g","rg","k"];if("string"==typeof n&&"#"!==n.charAt(0)){var s=new RGBColor(n);if(s.ok)n=s.toHex();else if(!/^\d*\.?\d*$/.test(n))throw new Error('Invalid color "'+n+'" passed to jsPDF.encodeColorString.')}if("string"==typeof n&&/^#[0-9A-Fa-f]{3}$/.test(n)&&(n="#"+n[1]+n[1]+n[2]+n[2]+n[3]+n[3]),"string"==typeof n&&/^#[0-9A-Fa-f]{6}$/.test(n)){var u=parseInt(n.substr(1),16);n=u>>16&255,r=u>>8&255,i=255&u}if(void 0===r||void 0===a&&n===r&&r===i)if("string"==typeof n)e=n+" "+o[0];else switch(t.precision){case 2:e=x(n/255)+" "+o[0];break;case 3:default:e=N(n/255)+" "+o[0]}else if(void 0===a||"object"===on(a)){if(a&&!isNaN(a.a)&&0===a.a)return e=["1.","1.","1.",o[1]].join(" ");if("string"==typeof n)e=[n,r,i,o[1]].join(" ");else switch(t.precision){case 2:e=[x(n/255),x(r/255),x(i/255),o[1]].join(" ");break;default:case 3:e=[N(n/255),N(r/255),N(i/255),o[1]].join(" ")}}else if("string"==typeof n)e=[n,r,i,a,o[2]].join(" ");else switch(t.precision){case 2:e=[x(n),x(r),x(i),x(a),o[2]].join(" ");break;case 3:default:e=[N(n),N(r),N(i),N(a),o[2]].join(" ")}return e},te=h.__private__.getFilters=function(){return i},ee=h.__private__.putStream=function(t){var e=(t=t||{}).data||"",n=t.filters||te(),r=t.alreadyAppliedFilters||[],i=t.addLength1||!1,a=e.length,o={};!0===n&&(n=["FlateEncode"]);var s=t.additionalKeyValues||[],u=(o=void 0!==ln.API.processDataByFilters?ln.API.processDataByFilters(e,n):{data:e,reverseChain:[]}).reverseChain+(Array.isArray(r)?r.join(" "):r.toString());if(0!==o.data.length&&(s.push({key:"Length",value:o.data.length}),!0===i&&s.push({key:"Length1",value:a})),0!=u.length)if(u.split("/").length-1==1)s.push({key:"Filter",value:u});else{s.push({key:"Filter",value:"["+u+"]"});for(var l=0;l<s.length;l+=1)if("DecodeParms"===s[l].key){for(var c=[],h=0;h<o.reverseChain.split("/").length-1;h+=1)c.push("null");c.push(s[l].value),s[l].value="["+c.join(" ")+"]"}}st("<<");for(var f=0;f<s.length;f++)st("/"+s[f].key+" "+s[f].value);st(">>"),0!==o.data.length&&(st("stream"),st(o.data),st("endstream"))},ne=h.__private__.putPage=function(t){var e=t.number,n=t.data,r=t.objId,i=t.contentsObjId;Jt(r,!0),st("<</Type /Page"),st("/Parent "+t.rootDictionaryObjId+" 0 R"),st("/Resources "+t.resourceDictionaryObjId+" 0 R"),st("/MediaBox ["+parseFloat(at(t.mediaBox.bottomLeftX))+" "+parseFloat(at(t.mediaBox.bottomLeftY))+" "+at(t.mediaBox.topRightX)+" "+at(t.mediaBox.topRightY)+"]"),null!==t.cropBox&&st("/CropBox ["+at(t.cropBox.bottomLeftX)+" "+at(t.cropBox.bottomLeftY)+" "+at(t.cropBox.topRightX)+" "+at(t.cropBox.topRightY)+"]"),null!==t.bleedBox&&st("/BleedBox ["+at(t.bleedBox.bottomLeftX)+" "+at(t.bleedBox.bottomLeftY)+" "+at(t.bleedBox.topRightX)+" "+at(t.bleedBox.topRightY)+"]"),null!==t.trimBox&&st("/TrimBox ["+at(t.trimBox.bottomLeftX)+" "+at(t.trimBox.bottomLeftY)+" "+at(t.trimBox.topRightX)+" "+at(t.trimBox.topRightY)+"]"),null!==t.artBox&&st("/ArtBox ["+at(t.artBox.bottomLeftX)+" "+at(t.artBox.bottomLeftY)+" "+at(t.artBox.topRightX)+" "+at(t.artBox.topRightY)+"]"),"number"==typeof t.userUnit&&1!==t.userUnit&&st("/UserUnit "+t.userUnit),At.publish("putPage",{objId:r,pageContext:Lt[e],pageNumber:e,page:n}),st("/Contents "+i+" 0 R"),st(">>"),st("endobj");var a=n.join("\n");return rt===nt&&(a+="\nQ"),Jt(i,!0),ee({data:a,filters:te()}),st("endobj"),r},re=h.__private__.putPages=function(){var t,e,n=[];for(t=1;t<=Nt;t++)Lt[t].objId=Yt(),Lt[t].contentsObjId=Yt();for(t=1;t<=Nt;t++)n.push(ne({number:t,data:R[t],objId:Lt[t].objId,contentsObjId:Lt[t].contentsObjId,mediaBox:Lt[t].mediaBox,cropBox:Lt[t].cropBox,bleedBox:Lt[t].bleedBox,trimBox:Lt[t].trimBox,artBox:Lt[t].artBox,userUnit:Lt[t].userUnit,rootDictionaryObjId:Kt,resourceDictionaryObjId:Zt}));Jt(Kt,!0),st("<</Type /Pages");var r="/Kids [";for(e=0;e<Nt;e++)r+=n[e]+" 0 R ";st(r+"]"),st("/Count "+Nt),st(">>"),st("endobj"),At.publish("postPutPages")},ie=h.__private__.pdfEscape=h.pdfEscape=function(t,e){return function(t,e){var n,r,i,a,o,s,u,l,c;if(i=(e=e||{}).sourceEncoding||"Unicode",o=e.outputEncoding,(e.autoencode||o)&&pt[ct].metadata&&pt[ct].metadata[i]&&pt[ct].metadata[i].encoding&&(a=pt[ct].metadata[i].encoding,!o&&pt[ct].encoding&&(o=pt[ct].encoding),!o&&a.codePages&&(o=a.codePages[0]),"string"==typeof o&&(o=a[o]),o)){for(u=!1,s=[],n=0,r=t.length;n<r;n++)(l=o[t.charCodeAt(n)])?s.push(String.fromCharCode(l)):s.push(t[n]),s[n].charCodeAt(0)>>8&&(u=!0);t=s.join("")}for(n=t.length;void 0===u&&0!==n;)t.charCodeAt(n-1)>>8&&(u=!0),n--;if(!u)return t;for(s=e.noBOM?[]:[254,255],n=0,r=t.length;n<r;n++){if((c=(l=t.charCodeAt(n))>>8)>>8)throw new Error("Character at position "+n+" of string '"+t+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");s.push(c),s.push(l-(c<<8))}return String.fromCharCode.apply(void 0,s)}(t,e).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},ae=h.__private__.beginPage=function(t){R[++Nt]=[],Lt[Nt]={objId:0,contentsObjId:0,userUnit:Number(s),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(t[0]),topRightY:Number(t[1])}},oe(Nt),z(R[I])},oe=function(t){0<t&&t<=Nt&&(I=t)},se=h.__private__.getNumberOfPages=h.getNumberOfPages=function(){return R.length-1},ue=h.__private__.putInfo=function(){for(var t in Gt(),st("<<"),st("/Producer (jsPDF "+ln.version+")"),Q)Q.hasOwnProperty(t)&&Q[t]&&st("/"+t.substr(0,1).toUpperCase()+t.substr(1)+" ("+ie(Q[t])+")");st("/CreationDate ("+L+")"),st(">>"),st("endobj")},le=h.__private__.putCatalog=function(t){var e=(t=t||{}).rootDictionaryObjId||Kt;switch(Gt(),st("<<"),st("/Type /Catalog"),st("/Pages "+e+" 0 R"),G||(G="fullwidth"),G){case"fullwidth":st("/OpenAction [3 0 R /FitH null]");break;case"fullheight":st("/OpenAction [3 0 R /FitV null]");break;case"fullpage":st("/OpenAction [3 0 R /Fit]");break;case"original":st("/OpenAction [3 0 R /XYZ null null 1]");break;default:var n=""+G;"%"===n.substr(n.length-1)&&(G=parseInt(G)/100),"number"==typeof G&&st("/OpenAction [3 0 R /XYZ null null "+x(G)+"]")}switch(K||(K="continuous"),K){case"continuous":st("/PageLayout /OneColumn");break;case"single":st("/PageLayout /SinglePage");break;case"two":case"twoleft":st("/PageLayout /TwoColumnLeft");break;case"tworight":st("/PageLayout /TwoColumnRight")}J&&st("/PageMode /"+J),At.publish("putCatalog"),st(">>"),st("endobj")},ce=h.__private__.putTrailer=function(){st("trailer"),st("<<"),st("/Size "+(O+1)),st("/Root "+O+" 0 R"),st("/Info "+(O-1)+" 0 R"),st("/ID [ <"+A+"> <"+A+"> ]"),st(">>")},he=h.__private__.putHeader=function(){st("%PDF-"+f),st("%ºß¬à")},fe=h.__private__.putXRef=function(){var t="0000000000";st("xref"),st("0 "+(O+1)),st("0000000000 65535 f");for(var e=1;e<=O;e++){"function"==typeof E[e]?st((t+E[e]()).slice(-10)+" 00000 n"):void 0!==E[e]?st((t+E[e]).slice(-10)+" 00000 n"):st("0000000000 00000 n")}},de=h.__private__.buildDocument=function(){T=O=0,M=[],E=[],q=[],Kt=Yt(),Zt=Yt(),z(M),At.publish("buildDocument"),he(),re(),function(){At.publish("putAdditionalObjects");for(var t=0;t<q.length;t++){var e=q[t];Jt(e.objId,!0),st(e.content),st("endobj")}At.publish("postPutAdditionalObjects")}(),Rt(),ue(),le();var t=T;return fe(),ce(),st("startxref"),st(""+t),st("%%EOF"),z(R[I]),M.join("\n")},pe=h.__private__.getBlob=function(t){return new Blob([W(t)],{type:"application/pdf"})},ge=h.output=h.__private__.output=((Vt=function(t,e){switch("string"==typeof(e=e||{})?e={filename:e}:e.filename=e.filename||"generated.pdf",t){case void 0:return de();case"save":h.save(e.filename);break;case"arraybuffer":return W(de());case"blob":return pe(de());case"bloburi":case"bloburl":if(void 0!==sn.URL&&"function"==typeof sn.URL.createObjectURL)return sn.URL&&sn.URL.createObjectURL(pe(de()))||void 0;console.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var n="",r=de();try{n=btoa(r)}catch(t){n=btoa(unescape(encodeURIComponent(r)))}return"data:application/pdf;filename="+e.filename+";base64,"+n;case"pdfobjectnewwindow":if("[object Window]"!==Object.prototype.toString.call(sn))throw new Error("The option pdfobjectnewwindow just works in a browser-environment.");var i='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+(e.pdfObjectUrl||"https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js")+'"><\/script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(e)+");<\/script></body></html>",a=sn.open();return null!==a&&a.document.write(i),a;case"pdfjsnewwindow":if("[object Window]"!==Object.prototype.toString.call(sn))throw new Error("The option pdfjsnewwindow just works in a browser-environment.");var o='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(e.pdfJsUrl||"examples/PDF.js/web/viewer.html")+'?file=" width="500px" height="400px" /></body></html>',s=sn.open();if(null!==s){s.document.write(o);var u=this;s.document.documentElement.querySelector("#pdfViewer").onload=function(){s.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(u.output("bloburl"))}}return s;case"dataurlnewwindow":if("[object Window]"!==Object.prototype.toString.call(sn))throw new Error("The option dataurlnewwindow just works in a browser-environment.");var l='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",e)+'"></iframe></body></html>',c=sn.open();if(null!==c&&c.document.write(l),c||"undefined"==typeof safari)return c;break;case"datauri":case"dataurl":return sn.document.location.href=this.output("datauristring",e);default:return null}}).foo=function(){try{return Vt.apply(this,arguments)}catch(t){var e=t.stack||"";~e.indexOf(" at ")&&(e=e.split(" at ")[1]);var n="Error in function "+e.split("\n")[0].split("<")[0]+": "+t.message;if(!sn.console)throw new Error(n);sn.console.error(n,t),sn.alert&&alert(n)}},(Vt.foo.bar=Vt).foo);switch(e){case"pt":tt=1;break;case"mm":tt=72/25.4;break;case"cm":tt=72/2.54;break;case"in":tt=72;break;case"px":tt=1==Wt("px_scaling")?.75:96/72;break;case"pc":case"em":tt=12;break;case"ex":tt=6;break;default:throw new Error("Invalid unit: "+e)}F(),S();var me=h.__private__.getPageInfo=h.getPageInfo=function(t){if(isNaN(t)||t%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:Lt[t].objId,pageNumber:t,pageContext:Lt[t]}},be=h.__private__.getPageInfoByObjId=function(t){if(isNaN(t)||t%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var e in Lt)if(Lt[e].objId===t)break;return me(e)},ve=h.__private__.getCurrentPageInfo=h.getCurrentPageInfo=function(){return{objId:Lt[I].objId,pageNumber:I,pageContext:Lt[I]}};h.addPage=function(){return zt.apply(this,arguments),this},h.setPage=function(){return oe.apply(this,arguments),z.call(this,R[I]),this},h.insertPage=function(t){return this.addPage(),this.movePage(I,t),this},h.movePage=function(t,e){var n,r;if(e<t){n=R[t],r=Lt[t];for(var i=t;e<i;i--)R[i]=R[i-1],Lt[i]=Lt[i-1];R[e]=n,Lt[e]=r,this.setPage(e)}else if(t<e){n=R[t],r=Lt[t];for(var a=t;a<e;a++)R[a]=R[a+1],Lt[a]=Lt[a+1];R[e]=n,Lt[e]=r,this.setPage(e)}return this},h.deletePage=function(){return function(t){0<t&&t<=Nt&&(R.splice(t,1),Lt.splice(t,1),--Nt<I&&(I=Nt),this.setPage(I))}.apply(this,arguments),this},h.__private__.text=h.text=function(t,e,n,r,i){var a,o,s,u,l,c,h,f,d=(r=r||{}).scope||this;if("number"==typeof t&&"number"==typeof e&&("string"==typeof n||Array.isArray(n))){var p=n;n=e,e=t,t=p}if(arguments[3]instanceof Ft==!1?(h=arguments[3],s=i,u=arguments[5],"object"===on(h)&&null!==h||("string"==typeof s&&(u=s,s=null),"string"==typeof h&&(u=h,h=null),"number"==typeof h&&(s=h,h=null),r={flags:h,angle:s,align:u})):(it("The transform parameter of text() with a Matrix value"),f=i),isNaN(e)||isNaN(n)||null==t)throw new Error("Invalid arguments passed to jsPDF.text");if(0===t.length)return d;var g,m="",b="number"==typeof r.lineHeightFactor?r.lineHeightFactor:Ie,v=d.internal.scaleFactor;function y(t){for(var e,n=t.concat(),r=[],i=n.length;i--;)"string"==typeof(e=n.shift())?r.push(e):Array.isArray(t)&&(1===e.length||void 0===e[1]&&void 0===e[2])?r.push(e[0]):r.push([e[0],e[1],e[2]]);return r}function w(t,e){var n;if("string"==typeof t)n=e(t)[0];else if(Array.isArray(t)){for(var r,i,a=t.concat(),o=[],s=a.length;s--;)"string"==typeof(r=a.shift())?o.push(e(r)[0]):Array.isArray(r)&&"string"==typeof r[0]&&(i=e(r[0],r[1],r[2]),o.push([i[0],i[1],i[2]]));n=o}return n}var x=!1,N=!0;if("string"==typeof t)x=!0;else if(Array.isArray(t)){var L=t.concat();o=[];for(var A,_=L.length;_--;)("string"!=typeof(A=L.shift())||Array.isArray(A)&&"string"!=typeof A[0])&&(N=!1);x=N}if(!1===x)throw new Error('Type of text must be string or Array. "'+t+'" is not recognized.');"string"==typeof t&&(t=t.match(/[\r?\n]/)?t.split(/\r\n|\r|\n/g):[t]);var S=ut/d.internal.scaleFactor,P=S*(Ie-1);switch(r.baseline){case"bottom":n-=P;break;case"top":n+=S-P;break;case"hanging":n+=S-2*P;break;case"middle":n+=S/2-P}if(0<(c=r.maxWidth||0)&&("string"==typeof t?t=d.splitTextToSize(t,c):"[object Array]"===Object.prototype.toString.call(t)&&(t=d.splitTextToSize(t.join(" "),c))),a={text:t,x:e,y:n,options:r,mutex:{pdfEscape:ie,activeFontKey:ct,fonts:pt,activeFontSize:ut}},At.publish("preProcessText",a),t=a.text,s=(r=a.options).angle,f instanceof Ft==!1&&s&&"number"==typeof s){s*=Math.PI/180,0===r.rotationDirection&&(s=-s),rt===nt&&(s=-s);var k=Math.cos(s),F=Math.sin(s);f=new Ft(k,F,-F,k,0,0)}else s&&s instanceof Ft&&(f=s);rt!==nt||f||(f=It),void 0!==(l=r.charSpace||Ve)&&(m+=at(ot(l))+" Tc\n",this.setCharSpace(this.getCharSpace()||0));r.lang;var C=-1,I=void 0!==r.renderingMode?r.renderingMode:r.stroke,j=d.internal.getCurrentPageInfo().pageContext;switch(I){case 0:case!1:case"fill":C=0;break;case 1:case!0:case"stroke":C=1;break;case 2:case"fillThenStroke":C=2;break;case 3:case"invisible":C=3;break;case 4:case"fillAndAddForClipping":C=4;break;case 5:case"strokeAndAddPathForClipping":C=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":C=6;break;case 7:case"addToPathForClipping":C=7}var B=void 0!==j.usedRenderingMode?j.usedRenderingMode:-1;-1!==C?m+=C+" Tr\n":-1!==B&&(m+="0 Tr\n"),-1!==C&&(j.usedRenderingMode=C),u=r.align||"left";var O,E=ut*b,M=d.internal.pageSize.getWidth(),T=pt[ct];l=r.charSpace||Ve,c=r.maxWidth||0,h={};var q=[];if("[object Array]"===Object.prototype.toString.call(t)){var R;o=y(t),"left"!==u&&(O=o.map(function(t){return d.getStringUnitWidth(t,{font:T,charSpace:l,fontSize:ut,doKerning:!1})*ut/v}));var D,U=0;if("right"===u){e-=O[0],t=[],_=o.length;for(var z=0;z<_;z++)R=0===z?(D=Te(e),qe(n)):(D=ot(U-O[z]),-E),t.push([o[z],D,R]),U=O[z]}else if("center"===u){e-=O[0]/2,t=[],_=o.length;for(var H=0;H<_;H++)R=0===H?(D=Te(e),qe(n)):(D=ot((U-O[H])/2),-E),t.push([o[H],D,R]),U=O[H]}else if("left"===u){t=[],_=o.length;for(var W=0;W<_;W++)t.push(o[W])}else{if("justify"!==u)throw new Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');t=[],_=o.length,c=0!==c?c:M;for(var V=0;V<_;V++)R=0===V?qe(n):-E,D=0===V?Te(e):0,V<_-1&&q.push(at(ot((c-O[V])/(o[V].split(" ").length-1)))),t.push([o[V],D,R])}}!0===("boolean"==typeof r.R2L?r.R2L:lt)&&(t=w(t,function(t,e,n){return[t.split("").reverse().join(""),e,n]})),a={text:t,x:e,y:n,options:r,mutex:{pdfEscape:ie,activeFontKey:ct,fonts:pt,activeFontSize:ut}},At.publish("postProcessText",a),t=a.text,g=a.mutex.isHex||!1;var G=pt[ct].encoding;"WinAnsiEncoding"!==G&&"StandardEncoding"!==G||(t=w(t,function(t,e,n){return[function(t){return t=t.split("\t").join(Array(r.TabLen||9).join(" ")),ie(t,h)}(t),e,n]})),o=y(t),t=[];for(var Y,J,X,K=Array.isArray(o[0])?1:0,Z="",$=function(t,e,n){return n instanceof Ft?(n="number"==typeof r.angle?Ct(n,new Ft(1,0,0,1,t,e)):Ct(new Ft(1,0,0,1,t,e),n),rt===nt&&(n=Ct(new Ft(1,0,0,-1,0,0),n)),n.join(" ")+" Tm\n"):at(t)+" "+at(e)+" Td\n"},Q=0;Q<o.length;Q++){switch(Z="",K){case 1:X=(g?"<":"(")+o[Q][0]+(g?">":")"),Y=parseFloat(o[Q][1]),J=parseFloat(o[Q][2]);break;case 0:X=(g?"<":"(")+o[Q]+(g?">":")"),Y=Te(e),J=qe(n)}void 0!==q&&void 0!==q[Q]&&(Z=q[Q]+" Tw\n"),0===Q?t.push(Z+$(Y,J,f)+X):0==K?t.push(Z+X):1==K&&t.push(Z+$(Y,J,f)+X)}t=0==K?t.join(" Tj\nT* "):t.join(" Tj\n"),t+=" Tj\n";var tt="BT\n/";return tt+=ct+" "+ut+" Tf\n",tt+=at(ut*b)+" TL\n",tt+=He+"\n",tt+=m,tt+=t,st(tt+="ET"),et[ct]=!0,d},h.__private__.lstext=h.lstext=function(t,e,n,r){return this.text(t,e,n,{charSpace:r})};var ye=h.__private__.clip=h.clip=function(t){return st("evenodd"===t?"W*":"W"),this};h.clipEvenOdd=function(){return ye("evenodd")},h.__private__.clip_fixed=h.clip_fixed=function(t){return h.clip(t)},h.__private__.discardPath=h.discardPath=function(){return st("n"),this};var we=h.__private__.isValidStyle=function(t){var e=!1;return-1!==[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(t)&&(e=!0),e};h.__private__.setDefaultPathOperation=h.setDefaultPathOperation=function(t){return we(t)&&(l=t),this};var xe=h.__private__.getStyle=h.getStyle=function(t){var e=l;switch(t){case"D":case"S":e="S";break;case"F":e="f";break;case"FD":case"DF":e="B";break;case"f":case"f*":case"B":case"B*":e=t}return e},Ne=h.close=function(){return st("h"),this};h.stroke=function(){return st("S"),this},h.fill=function(t){return Ae("f",t),this},h.fillEvenOdd=function(t){return Ae("f*",t),this},h.fillStroke=function(t){return Ae("B",t),this},h.fillStrokeEvenOdd=function(t){return Ae("B*",t),this};function Le(t,e,n){null===t||rt===nt&&void 0===t||(t=xe(t),e?(n||(n={matrix:It}),n instanceof Ft&&(n={matrix:n}),n.key=e,n||(n=It),_e(n,t)):st(t))}var Ae=function(t,e){"object"===on(e)?_e(e,t):st(t)},_e=function(t,e){var n=vt[t.key],r=bt[n];if(r instanceof h.ShadingPattern)st("q"),st(Se(e)),r.gState&&h.setGState(r.gState),st(t.matrix.toString()+" cm"),st("/"+n+" sh"),st("Q");else if(r instanceof h.TilingPattern){var i=new Ft(1,0,0,-1,0,rn());t.matrix&&(i=i.multiply(t.matrix||It),n=r.createClone(t.key,t.boundingBox,t.xStep,t.yStep,i).id),st("q"),st("/Pattern cs"),st("/"+n+" scn"),r.gState&&h.setGState(r.gState),st(e),st("Q")}},Se=function(t){switch(t){case"f":case"F":return"W n";case"f*":return"W* n";case"B":return"W S";case"B*":return"W* S";case"S":return"W S";case"n":return"W n"}},Pe=h.moveTo=function(t,e){return st(at(ot(t))+" "+at(w(e))+" m"),this},ke=h.lineTo=function(t,e){return st(at(ot(t))+" "+at(w(e))+" l"),this},Fe=h.curveTo=function(t,e,n,r,i,a){return st([at(ot(t)),at(w(e)),at(ot(n)),at(w(r)),at(ot(i)),at(w(a)),"c"].join(" ")),this};h.__private__.line=h.line=function(t,e,n,r,i){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||!we(i))throw new Error("Invalid arguments passed to jsPDF.line");return rt===m?this.lines([[n-t,r-e]],t,e,[1,1],i||"S"):this.lines([[n-t,r-e]],t,e,[1,1]).stroke()},h.__private__.lines=h.lines=function(t,e,n,r,i,a,o,s){var u,l,c,h,f,d,p,g,m,b,v,y;if("number"==typeof t&&(y=n,n=e,e=t,t=y),r=r||[1,1],a=a||!1,isNaN(e)||isNaN(n)||!Array.isArray(t)||!Array.isArray(r)||!we(i)||"boolean"!=typeof a)throw new Error("Invalid arguments passed to jsPDF.lines");for(Pe(e,n),u=r[0],l=r[1],h=t.length,b=e,v=n,c=0;c<h;c++)2===(f=t[c]).length?(b=f[0]*u+b,v=f[1]*l+v,ke(b,v)):(d=f[0]*u+b,p=f[1]*l+v,g=f[2]*u+b,m=f[3]*l+v,b=f[4]*u+b,v=f[5]*l+v,Fe(d,p,g,m,b,v));return a&&Ne(),Le(i,o,s),this},h.path=function(t,e,n,r){for(var i=0;i<t.length;i++){var a=t[i],o=a.c;switch(a.op){case"m":Pe(o[0],o[1]);break;case"l":ke(o[0],o[1]);break;case"c":Fe.apply(this,o);break;case"h":Ne()}}return Le(e,n,r),this},h.__private__.rect=h.rect=function(t,e,n,r,i,a,o){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||!we(i))throw new Error("Invalid arguments passed to jsPDF.rect");return rt===m&&(r=-r),st([at(ot(t)),at(w(e)),at(ot(n)),at(ot(r)),"re"].join(" ")),Le(i,a,o),this},h.__private__.triangle=h.triangle=function(t,e,n,r,i,a,o,s,u){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||isNaN(i)||isNaN(a)||!we(o))throw new Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[n-t,r-e],[i-n,a-r],[t-i,e-a]],t,e,[1,1],o,!0,s,u),this},h.__private__.roundedRect=h.roundedRect=function(t,e,n,r,i,a,o,s,u){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||isNaN(i)||isNaN(a)||!we(o))throw new Error("Invalid arguments passed to jsPDF.roundedRect");var l=4/3*(Math.SQRT2-1);return i=Math.min(i,.5*n),a=Math.min(a,.5*r),this.lines([[n-2*i,0],[i*l,0,i,a-a*l,i,a],[0,r-2*a],[0,a*l,-i*l,a,-i,a],[2*i-n,0],[-i*l,0,-i,-a*l,-i,-a],[0,2*a-r],[0,-a*l,i*l,-a,i,-a]],t+i,e,[1,1],o,!0,s,u),this},h.__private__.ellipse=h.ellipse=function(t,e,n,r,i,a,o){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||!we(i))throw new Error("Invalid arguments passed to jsPDF.ellipse");var s=4/3*(Math.SQRT2-1)*n,u=4/3*(Math.SQRT2-1)*r;return Pe(t+n,e),Fe(t+n,e-u,t+s,e-r,t,e-r),Fe(t-s,e-r,t-n,e-u,t-n,e),Fe(t-n,e+u,t-s,e+r,t,e+r),Fe(t+s,e+r,t+n,e+u,t+n,e),Le(i,a,o),this},h.__private__.circle=h.circle=function(t,e,n,r,i,a){if(isNaN(t)||isNaN(e)||isNaN(n)||!we(r))throw new Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(t,e,n,n,r,i,a)},h.setFont=function(t,e){return ct=Ht(t,e,{disableWarning:!1}),this};var Ce=h.__private__.getFont=h.getFont=function(){return pt[Ht.apply(h,arguments)]};h.setFontStyle=h.setFontType=function(t){return ct=Ht(void 0,t),this},h.__private__.getFontList=h.getFontList=function(){var t,e,n={};for(t in gt)if(gt.hasOwnProperty(t))for(e in n[t]=[],gt[t])gt[t].hasOwnProperty(e)&&n[t].push(e);return n},h.addFont=function(t,e,n,r){return Ut.call(this,t,e,n,r=r||"Identity-H")};var Ie,je=t.lineWidth||.200025,Be=h.__private__.setLineWidth=h.setLineWidth=function(t){return st(at(ot(t))+" w"),this};h.__private__.setLineDash=ln.API.setLineDash=ln.API.setLineDashPattern=function(t,e){if(t=t||[],e=e||0,isNaN(e)||!Array.isArray(t))throw new Error("Invalid arguments passed to jsPDF.setLineDash");return t=t.map(function(t){return at(ot(t))}).join(" "),e=at(ot(e)),st("["+t+"] "+e+" d"),this};var Oe=h.__private__.getLineHeight=h.getLineHeight=function(){return ut*Ie};h.__private__.getLineHeight=h.getLineHeight=function(){return ut*Ie};var Ee=h.__private__.setLineHeightFactor=h.setLineHeightFactor=function(t){return"number"==typeof(t=t||1.15)&&(Ie=t),this},Me=h.__private__.getLineHeightFactor=h.getLineHeightFactor=function(){return Ie};Ee(t.lineHeight);var Te=h.__private__.getHorizontalCoordinate=function(t){return ot(t)},qe=h.__private__.getVerticalCoordinate=function(t){return rt===nt?t:Lt[I].mediaBox.topRightY-Lt[I].mediaBox.bottomLeftY-ot(t)},Re=h.__private__.getHorizontalCoordinateString=h.getHorizontalCoordinateString=function(t){return at(Te(t))},De=h.__private__.getVerticalCoordinateString=h.getVerticalCoordinateString=function(t){return at(qe(t))},Ue=t.strokeColor||"0 G";h.__private__.getStrokeColor=h.getDrawColor=function(){return $t(Ue)},h.__private__.setStrokeColor=h.setDrawColor=function(t,e,n,r){return Ue=Qt({ch1:t,ch2:e,ch3:n,ch4:r,pdfColorType:"draw",precision:2}),st(Ue),this};var ze=t.fillColor||"0 g";h.__private__.getFillColor=h.getFillColor=function(){return $t(ze)},h.__private__.setFillColor=h.setFillColor=function(t,e,n,r){return ze=Qt({ch1:t,ch2:e,ch3:n,ch4:r,pdfColorType:"fill",precision:2}),st(ze),this};var He=t.textColor||"0 g",We=h.__private__.getTextColor=h.getTextColor=function(){return $t(He)};h.__private__.setTextColor=h.setTextColor=function(t,e,n,r){return He=Qt({ch1:t,ch2:e,ch3:n,ch4:r,pdfColorType:"text",precision:3}),this};var Ve=t.charSpace,Ge=h.__private__.getCharSpace=h.getCharSpace=function(){return parseFloat(Ve||0)};h.__private__.setCharSpace=h.setCharSpace=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.setCharSpace");return Ve=t,this};var Ye=0;h.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},h.__private__.setLineCap=h.setLineCap=function(t){var e=h.CapJoinStyles[t];if(void 0===e)throw new Error("Line cap style of '"+t+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return st((Ye=e)+" J"),this};var Je=0;h.__private__.setLineJoin=h.setLineJoin=function(t){var e=h.CapJoinStyles[t];if(void 0===e)throw new Error("Line join style of '"+t+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return st((Je=e)+" j"),this},h.__private__.setLineMiterLimit=h.__private__.setMiterLimit=h.setLineMiterLimit=h.setMiterLimit=function(t){if(t=t||0,isNaN(t))throw new Error("Invalid argument passed to jsPDF.setLineMiterLimit");return st(at(ot(t))+" M"),this},h.GState=function(t){var e="opacity,stroke-opacity".split(",");for(var n in t)t.hasOwnProperty(n)&&0<=e.indexOf(n)&&(this[n]=t[n]);this.id="",this.objectNumber=-1},h.GState.prototype.equals=function(t){var e,n="id,objectNumber,equals";if(!t||on(t)!==on(this))return!1;var r=0;for(e in this)if(!(0<=n.indexOf(e))){if(this.hasOwnProperty(e)&&!t.hasOwnProperty(e))return!1;if(this[e]!==t[e])return!1;r++}for(e in t)t.hasOwnProperty(e)&&n.indexOf(e)<0&&r--;return 0===r},h.setGState=function(t){(t="string"==typeof t?yt[wt[t]]:Xe(null,t)).equals(xt)||(st("/"+t.id+" gs"),xt=t)};var Xe=function(t,e){if(!t||!wt[t]){var n=!1;for(var r in yt)if(yt.hasOwnProperty(r)&&yt[r].equals(e)){n=!0;break}if(n)e=yt[r];else{var i="GS"+(Object.keys(yt).length+1).toString(10);(yt[i]=e).id=i}return t&&(wt[t]=e.id),At.publish("addGState",e),e}};h.addGState=function(t,e){return Xe(t,e),this},h.saveGraphicsState=function(){return st("q"),mt.push({key:ct,size:ut,color:He}),this},h.restoreGraphicsState=function(){st("Q");var t=mt.pop();return ct=t.key,ut=t.size,He=t.color,xt=null,this},h.setCurrentTransformationMatrix=function(t){return st(t.toString()+" cm"),this},h.comment=function(t){return st("#"+t),this};function Ke(){this.page=Nt,this.currentPage=I,this.pages=R.slice(0),this.pagesContext=Lt.slice(0),this.x=ht,this.y=ft,this.matrix=dt,this.width=en(I),this.height=rn(I),this.outputDestination=U,this.id="",this.objectNumber=-1}var Ze=function(t,e){var n=t||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return n},set:function(t){isNaN(t)||(n=parseFloat(t))}});var r=e||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return r},set:function(t){isNaN(t)||(r=parseFloat(t))}});var i="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return i},set:function(t){i=t.toString()}}),this},$e=function(t,e,n,r){Ze.call(this,t,e),this.type="rect";var i=n||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return i},set:function(t){isNaN(t)||(i=parseFloat(t))}});var a=r||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return a},set:function(t){isNaN(t)||(a=parseFloat(t))}}),this};Ke.prototype.restore=function(){Nt=this.page,I=this.currentPage,Lt=this.pagesContext,R=this.pages,ht=this.x,ft=this.y,dt=this.matrix,nn(I,this.width),an(I,this.height),U=this.outputDestination};var Qe=function(t,e,n,r,i){kt.push(new Ke),Nt=I=0,R=[],ht=t,ft=e,dt=i,ae([n,r])};for(var tn in h.beginFormObject=function(t,e,n,r,i){return Qe(t,e,n,r,i),this},h.endFormObject=function(t){return function(t){if(!Pt[t]){var e=new Ke,n="Xo"+(Object.keys(St).length+1).toString(10);e.id=n,Pt[t]=n,St[n]=e,At.publish("addFormObject",e),kt.pop().restore()}}(t),this},h.doFormObject=function(t,e){var n=St[Pt[t]];return st("q"),st(e.toString()+" cm"),st("/"+n.id+" Do"),st("Q"),this},h.getFormObject=function(t){var e=St[Pt[t]];return{x:e.x,y:e.y,width:e.width,height:e.height,matrix:e.matrix}},h.save=function(r,t){if(r=r||"generated.pdf",(t=t||{}).returnPromise=t.returnPromise||!1,!1!==t.returnPromise)return new Promise(function(t,e){try{var n=saveAs(pe(de()),r);"function"!=typeof saveAs.unload||sn.setTimeout&&setTimeout(saveAs.unload,911),t(n)}catch(t){e(t.message)}});saveAs(pe(de()),r),"function"!=typeof saveAs.unload||sn.setTimeout&&setTimeout(saveAs.unload,911)},ln.API)ln.API.hasOwnProperty(tn)&&("events"===tn&&ln.API.events.length?function(t,e){var n,r,i;for(i=e.length-1;-1!==i;i--)n=e[i][0],r=e[i][1],t.subscribe.apply(t,[n].concat("function"==typeof r?[r]:r))}(At,ln.API.events):h[tn]=ln.API[tn]);var en=h.getPageWidth=function(t){return(Lt[t=t||I].mediaBox.topRightX-Lt[t].mediaBox.bottomLeftX)/tt},nn=h.setPageWidth=function(t,e){Lt[t].mediaBox.topRightX=e*tt+Lt[t].mediaBox.bottomLeftX},rn=h.getPageHeight=function(t){return(Lt[t=t||I].mediaBox.topRightY-Lt[t].mediaBox.bottomLeftY)/tt},an=h.setPageHeight=function(t,e){Lt[t].mediaBox.topRightY=e*tt+Lt[t].mediaBox.bottomLeftY};return h.internal={pdfEscape:ie,getStyle:xe,getFont:Ce,getFontSize:Y,getCharSpace:Ge,getTextColor:We,getLineHeight:Oe,getLineHeightFactor:Me,write:H,getHorizontalCoordinate:Te,getVerticalCoordinate:qe,getCoordinateString:Re,getVerticalCoordinateString:De,collections:{},newObject:Gt,newAdditionalObject:Xt,newObjectDeferred:Yt,newObjectDeferredBegin:Jt,getFilters:te,putStream:ee,events:At,scaleFactor:tt,pageSize:{getWidth:function(){return en(I)},setWidth:function(t){nn(I,t)},getHeight:function(){return rn(I)},setHeight:function(t){an(I,t)}},output:ge,getNumberOfPages:se,pages:R,out:st,f2:x,f3:N,getPageInfo:me,getPageInfoByObjId:be,getCurrentPageInfo:ve,getPDFVersion:d,Point:Ze,Rectangle:$e,Matrix:Ft,hasHotfix:Wt},Object.defineProperty(h.internal.pageSize,"width",{get:function(){return en(I)},set:function(t){nn(I,t)},enumerable:!0,configurable:!0}),Object.defineProperty(h.internal.pageSize,"height",{get:function(){return rn(I)},set:function(t){an(I,t)},enumerable:!0,configurable:!0}),function(t){for(var e=0,n=V.length;e<n;e++){var r=Ut(t[e][0],t[e][1],t[e][2],V[e][3],!0);!1===c&&(et[r]=!0);var i=t[e][0].split("-");Dt({id:r,fontName:i[0],fontStyle:i[1]||""})}At.publish("addFonts",{fonts:pt,dictionary:gt})}(V),ct="F1",zt(o,a),At.publish("initialized"),h}function z(t){if(void 0!==t&&""!=t)return!0}function H(){var e=void 0;Object.defineProperty(this,"pdf",{get:function(){return e},set:function(t){e=t}});var n=150;Object.defineProperty(this,"width",{get:function(){return n},set:function(t){n=isNaN(t)||!1===Number.isInteger(t)||t<0?150:t,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=n+1)}});var r=300;Object.defineProperty(this,"height",{get:function(){return r},set:function(t){r=isNaN(t)||!1===Number.isInteger(t)||t<0?300:t,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=r+1)}});var i=[];Object.defineProperty(this,"childNodes",{get:function(){return i},set:function(t){i=t}});var a={};Object.defineProperty(this,"style",{get:function(){return a},set:function(t){a=t}}),Object.defineProperty(this,"parentNode",{})}function W(t){var r,e,n,i,a,o,s,u,l,c;for(/[^\x00-\xFF]/.test(t),e=[],n=0,i=(t+=r="\0\0\0\0".slice(t.length%4||4)).length;n<i;n+=4)0!==(a=(t.charCodeAt(n)<<24)+(t.charCodeAt(n+1)<<16)+(t.charCodeAt(n+2)<<8)+t.charCodeAt(n+3))?(o=(a=((a=((a=((a=(a-(c=a%85))/85)-(l=a%85))/85)-(u=a%85))/85)-(s=a%85))/85)%85,e.push(33+o,33+s,33+u,33+l,33+c)):e.push(122);return function(t,e){for(var n=r.length;0<n;n--)t.pop()}(e),String.fromCharCode.apply(String,e)+"~>"}function V(t){var r,e,n,i,a,o=String,s="length",u="charCodeAt",l="slice",c="replace";for(t[l](-2),t=t[l](0,-2)[c](/\s/g,"")[c]("z","!!!!!"),n=[],i=0,a=(t+=r="uuuuu"[l](t[s]%5||5))[s];i<a;i+=5)e=52200625*(t[u](i)-33)+614125*(t[u](i+1)-33)+7225*(t[u](i+2)-33)+85*(t[u](i+3)-33)+(t[u](i+4)-33),n.push(255&e>>24,255&e>>16,255&e>>8,255&e);return function(t,e){for(var n=r[s];0<n;n--)t.pop()}(n),o.fromCharCode.apply(o,n)}function G(t){var e=new RegExp(/^([0-9A-Fa-f]{2})+$/);if(-1!==(t=t.replace(/\s/g,"")).indexOf(">")&&(t=t.substr(0,t.indexOf(">"))),t.length%2&&(t+="0"),!1===e.test(t))return"";for(var n="",r=0;r<t.length;r+=2)n+=String.fromCharCode("0x"+(t[r]+t[r+1]));return n}function Y(t){for(var e,n,r=[],i=t.length;i--;)r[i]=t.charCodeAt(i);return e=a.adler32cs.from(t),t=function(t,e){var n=new Uint8Array(t.byteLength+e.byteLength);return n.set(new Uint8Array(t),0),n.set(new Uint8Array(e),t.byteLength),n}(t=(n=new Deflater(6)).append(new Uint8Array(r)),n.flush()),(r=new Uint8Array(t.byteLength+6)).set(new Uint8Array([120,156])),r.set(t,2),r.set(new Uint8Array([255&e,e>>8&255,e>>16&255,e>>24&255]),t.byteLength+2),t=r.reduce(function(t,e){return t+String.fromCharCode(e)},"")}function J(t,e,n,r){var i=5,a=m;switch(r){case P.image_compression.FAST:i=3,a=g;break;case P.image_compression.MEDIUM:i=6,a=b;break;case P.image_compression.SLOW:i=9,a=v}t=p(t,e,n,a);var o=new Uint8Array(d(i)),s=U.API.adler32cs.fromBuffer(t.buffer),u=new Deflater(i),l=u.append(t),c=u.flush(),h=o.length+l.length+c.length,f=new Uint8Array(h+4);return f.set(o),f.set(l,o.length),f.set(c,o.length+l.length),f[h++]=s>>>24&255,f[h++]=s>>>16&255,f[h++]=s>>>8&255,f[h++]=255&s,P.__addimage__.arrayBufferToBinaryString(f)}function X(t){var e=Array.apply([],t);return e.unshift(0),e}function K(t,e,n,r){for(var i=[],a=0,o=t.length,s=0;a!==o&&s+e[a]<n;)s+=e[a],a++;i.push(t.slice(0,a));var u=a;for(s=0;a!==o;)s+e[a]>r&&(i.push(t.slice(u,a)),s=0,u=a),s+=e[a],a++;return u!==a&&i.push(t.slice(u,a)),i}function Z(t,e,n){n||(n={});var r,i,a,o,s,u,l,c=[],h=[c],f=n.textIndent||0,d=0,p=0,g=t.split(" "),m=A.apply(this,[" ",n])[0];if(u=-1===n.lineIndent?g[0].length+2:n.lineIndent||0){var b=Array(u).join(" "),v=[];g.map(function(t){1<(t=t.split(/\s*\n/)).length?v=v.concat(t.map(function(t,e){return(e&&t.length?"\n":"")+t})):v.push(t[0])}),g=v,u=_.apply(this,[b,n])}for(a=0,o=g.length;a<o;a++){var y=0;if(r=g[a],u&&"\n"==r[0]&&(r=r.substr(1),y=1),e<f+d+(p=(i=A.apply(this,[r,n])).reduce(function(t,e){return t+e},0))||y){if(e<p){for(s=K.apply(this,[r,i,e-(f+d),e]),c.push(s.shift()),c=[s.pop()];s.length;)h.push([s.shift()]);p=i.slice(r.length-(c[0]?c[0].length:0)).reduce(function(t,e){return t+e},0)}else c=[r];h.push(c),f=p+u,d=m}else c.push(r),f+=d+p,d=m}return l=u?function(t,e){return(e?b:"")+t.join(" ")}:function(t){return t.join(" ")},h.map(l)}function $(t){for(var e=t.length,n=new Uint8Array(e),r=0;r<e;r++)n[r]=t.charCodeAt(r);return n}function Q(){var t='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',e=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),n=unescape(encodeURIComponent(t)),r=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),i=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),a=unescape(encodeURIComponent("</x:xmpmeta>")),o=n.length+r.length+i.length+e.length+a.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+o+" >>"),this.internal.write("stream"),this.internal.write(e+n+r+i+a),this.internal.write("endstream"),this.internal.write("endobj")}function tt(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")}function et(){return void 0===this.internal.vFS&&(this.internal.vFS={}),!0}function nt(x){var t=0;if(71!==x[t++]||73!==x[t++]||70!==x[t++]||56!==x[t++]||56!=(x[t++]+1&253)||97!==x[t++])throw new Error("Invalid GIF 87a/89a header.");var N=x[t++]|x[t++]<<8,e=x[t++]|x[t++]<<8,n=x[t++],r=n>>7,i=1<<1+(7&n);x[t++];x[t++];var a=null,o=null;r&&(a=t,t+=3*(o=i));var s=!0,u=[],l=0,c=null,h=0,f=null;for(this.width=N,this.height=e;s&&t<x.length;)switch(x[t++]){case 33:switch(x[t++]){case 255:if(11!==x[t]||78==x[t+1]&&69==x[t+2]&&84==x[t+3]&&83==x[t+4]&&67==x[t+5]&&65==x[t+6]&&80==x[t+7]&&69==x[t+8]&&50==x[t+9]&&46==x[t+10]&&48==x[t+11]&&3==x[t+12]&&1==x[t+13]&&0==x[t+16])t+=14,f=x[t++]|x[t++]<<8,t++;else for(t+=12;;){if(!(0<=(P=x[t++])))throw Error("Invalid block size");if(0===P)break;t+=P}break;case 249:if(4!==x[t++]||0!==x[t+4])throw new Error("Invalid graphics extension block.");var d=x[t++];l=x[t++]|x[t++]<<8,c=x[t++],0==(1&d)&&(c=null),h=d>>2&7,t++;break;case 254:for(;;){if(!(0<=(P=x[t++])))throw Error("Invalid block size");if(0===P)break;t+=P}break;default:throw new Error("Unknown graphic control label: 0x"+x[t-1].toString(16))}break;case 44:var p=x[t++]|x[t++]<<8,g=x[t++]|x[t++]<<8,m=x[t++]|x[t++]<<8,b=x[t++]|x[t++]<<8,v=x[t++],y=v>>6&1,w=1<<1+(7&v),L=a,A=o,_=!1;if(v>>7){_=!0;L=t,t+=3*(A=w)}var S=t;for(t++;;){var P;if(!(0<=(P=x[t++])))throw Error("Invalid block size");if(0===P)break;t+=P}u.push({x:p,y:g,width:m,height:b,has_local_palette:_,palette_offset:L,palette_size:A,data_offset:S,data_length:t-S,transparent_index:c,interlaced:!!y,delay:l,disposal:h});break;case 59:s=!1;break;default:throw new Error("Unknown gif block: 0x"+x[t-1].toString(16))}this.numFrames=function(){return u.length},this.loopCount=function(){return f},this.frameInfo=function(t){if(t<0||t>=u.length)throw new Error("Frame index out of range.");return u[t]},this.decodeAndBlitFrameBGRA=function(t,e){var n=this.frameInfo(t),r=n.width*n.height,i=new Uint8Array(r);rt(x,n.data_offset,i,r);var a=n.palette_offset,o=n.transparent_index;null===o&&(o=256);var s=n.width,u=N-s,l=s,c=4*(n.y*N+n.x),h=4*((n.y+n.height)*N+n.x),f=c,d=4*u;!0===n.interlaced&&(d+=4*N*7);for(var p=8,g=0,m=i.length;g<m;++g){var b=i[g];if(0===l&&(l=s,h<=(f+=d)&&(d=4*u+4*N*(p-1),f=c+(s+u)*(p<<1),p>>=1)),b===o)f+=4;else{var v=x[a+3*b],y=x[a+3*b+1],w=x[a+3*b+2];e[f++]=w,e[f++]=y,e[f++]=v,e[f++]=255}--l}},this.decodeAndBlitFrameRGBA=function(t,e){var n=this.frameInfo(t),r=n.width*n.height,i=new Uint8Array(r);rt(x,n.data_offset,i,r);var a=n.palette_offset,o=n.transparent_index;null===o&&(o=256);var s=n.width,u=N-s,l=s,c=4*(n.y*N+n.x),h=4*((n.y+n.height)*N+n.x),f=c,d=4*u;!0===n.interlaced&&(d+=4*N*7);for(var p=8,g=0,m=i.length;g<m;++g){var b=i[g];if(0===l&&(l=s,h<=(f+=d)&&(d=4*u+4*N*(p-1),f=c+(s+u)*(p<<1),p>>=1)),b===o)f+=4;else{var v=x[a+3*b],y=x[a+3*b+1],w=x[a+3*b+2];e[f++]=v,e[f++]=y,e[f++]=w,e[f++]=255}--l}}}function rt(t,e,n,r){for(var i=t[e++],a=1<<i,o=1+a,s=1+o,u=i+1,l=(1<<u)-1,c=0,h=0,f=0,d=t[e++],p=new Int32Array(4096),g=null;;){for(;c<16&&0!==d;)h|=t[e++]<<c,c+=8,1===d?d=t[e++]:--d;if(c<u)break;var m=h&l;if(h>>=u,c-=u,m!=a){if(m==o)break;for(var b=m<s?m:g,v=0,y=b;a<y;)y=p[y]>>8,++v;var w=y;if(r<f+v+(b!==m?1:0))return void console.log("Warning, gif stream longer than expected.");n[f++]=w;var x=f+=v;for(b!==m&&(n[f++]=w),y=b;v--;)y=p[y],n[--x]=255&y,y>>=8;null!==g&&s<4096&&(p[s++]=g<<8|w,l+1<=s&&u<12&&(++u,l=l<<1|1)),g=m}else s=1+o,l=(1<<(u=i+1))-1,g=null}return f!==r&&console.log("Warning, gif stream shorter than expected."),n}
/**
   * @license
   * Copyright (c) 2016 Alexander Weidt,
   * https://github.com/BiggA94
   *
   * Licensed under the MIT License. http://opensource.org/licenses/mit-license
   */
!function(t,e){function A(t){return t.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")}function b(t){return t.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")}function _(t){return t.toFixed(2)}function s(t){return t.toFixed(5)}var h,n=t.API,r=1;n.__acroform__={};function i(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t}function v(t){return t*r}function y(t){return t/r}function u(t){var e=new O,n=J.internal.getHeight(t)||0,r=J.internal.getWidth(t)||0;return e.BBox=[0,0,Number(_(r)),Number(_(n))],e}function f(t){if(t.appearanceStreamContent)return t.appearanceStreamContent;if(t.V||t.DV){var e=[],n=t.V||t.DV,r=L(t,n),i=h.internal.getFont(t.fontName,t.fontStyle).id;e.push("/Tx BMC"),e.push("q"),e.push("BT"),e.push(h.__private__.encodeColorString(t.color)),e.push("/"+i+" "+_(r.fontSize)+" Tf"),e.push("1 0 0 1 0 0 Tm"),e.push(r.text),e.push("ET"),e.push("Q"),e.push("EMC");var a=new u(t);return a.stream=e.join("\n"),a}}function a(){h.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var t=h.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var e in t)if(t.hasOwnProperty(e)){var n=t[e];n.objId=void 0,n.hasAnnotation&&k.call(h,n)}}function o(){if(void 0===h.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("putCatalogCallback: Root missing.");h.internal.write("/AcroForm "+h.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")}function l(){h.internal.events.unsubscribe(h.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete h.internal.acroformPlugin.acroFormDictionaryRoot._eventID,h.internal.acroformPlugin.printedOut=!0}function c(t){var e=!t;for(var n in t||(h.internal.newObjectDeferredBegin(h.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),h.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),t=t||h.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(t.hasOwnProperty(n)){var r=t[n],i=[],a=r.Rect;if(r.Rect&&(r.Rect=m.call(this,r.Rect)),h.internal.newObjectDeferredBegin(r.objId,!0),r.DA=J.createDefaultAppearanceStream(r),"object"===on(r)&&"function"==typeof r.getKeyValueListForStream&&(i=r.getKeyValueListForStream()),r.Rect=a,r.hasAppearanceStream&&!r.appearanceStreamContent){var o=f.call(this,r);i.push({key:"AP",value:"<</N "+o+">>"}),h.internal.acroformPlugin.xForms.push(o)}if(r.appearanceStreamContent){var s="";for(var u in r.appearanceStreamContent)if(r.appearanceStreamContent.hasOwnProperty(u)){var l=r.appearanceStreamContent[u];if(s+="/"+u+" ",s+="<<",1<=Object.keys(l).length||Array.isArray(l)){for(var n in l)if(l.hasOwnProperty(n)){var c=l[n];"function"==typeof c&&(c=c.call(this,r)),s+="/"+n+" "+c+" ",0<=h.internal.acroformPlugin.xForms.indexOf(c)||h.internal.acroformPlugin.xForms.push(c)}}else"function"==typeof(c=l)&&(c=c.call(this,r)),s+="/"+n+" "+c,0<=h.internal.acroformPlugin.xForms.indexOf(c)||h.internal.acroformPlugin.xForms.push(c);s+=">>"}i.push({key:"AP",value:"<<\n"+s+">>"})}h.internal.putStream({additionalKeyValues:i}),h.internal.out("endobj")}e&&F.call(this,h.internal.acroformPlugin.xForms)}var d=n.__acroform__.setBit=function(t,e){if(t=t||0,e=e||0,isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return t|=1<<e},p=n.__acroform__.clearBit=function(t,e){if(t=t||0,e=e||0,isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return t&=~(1<<e)},g=n.__acroform__.getBit=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return 0==(t&1<<e)?0:1},w=n.__acroform__.getBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return g(t,e-1)},x=n.__acroform__.setBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return d(t,e-1)},N=n.__acroform__.clearBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return p(t,e-1)},m=n.__acroform__.calculateCoordinates=function(t){var e=this.internal.getHorizontalCoordinate,n=this.internal.getVerticalCoordinate,r=t[0],i=t[1],a=t[2],o=t[3],s={};return s.lowerLeft_X=e(r)||0,s.lowerLeft_Y=n(i+o)||0,s.upperRight_X=e(r+a)||0,s.upperRight_Y=n(i)||0,[Number(_(s.lowerLeft_X)),Number(_(s.lowerLeft_Y)),Number(_(s.upperRight_X)),Number(_(s.upperRight_Y))]},L=function(i,t){var e=0===i.fontSize?i.maxFontSize:i.fontSize,n={text:"",fontSize:""},a=(t=")"==(t="("==t.substr(0,1)?t.substr(1):t).substr(t.length-1)?t.substr(0,t.length-1):t).split(" "),r=e,o=J.internal.getHeight(i)||0;o=o<0?-o:o;var s=J.internal.getWidth(i)||0;s=s<0?-s:s;function u(t,e,n){if(t+1<a.length){var r=e+" "+a[t+1];return S(r,i,n).width<=s-4}return!1}r++;t:for(;0<r;){t="";var l,c,h=S("3",i,--r).height,f=i.multiline?o-r:(o-h)/2,d=f+=2,p=0,g=0;if(r<=0){t="(...) Tj\n",t+="% Width of Text: "+S(t,i,r=12).width+", FieldWidth:"+s+"\n";break}var m="",b=0;for(var v in a)if(a.hasOwnProperty(v)){m=" "==(m+=a[v]+" ").substr(m.length-1)?m.substr(0,m.length-1):m;var y=parseInt(v),w=u(y,m,r),x=v>=a.length-1;if(w&&!x){m+=" ";continue}if(w||x){if(x)g=y;else if(i.multiline&&o<(h+2)*(b+2)+2)continue t}else{if(!i.multiline)continue t;if(o<(h+2)*(b+2)+2)continue t;g=y}for(var N="",L=p;L<=g;L++)N+=a[L]+" ";switch(N=" "==N.substr(N.length-1)?N.substr(0,N.length-1):N,c=S(N,i,r).width,i.textAlign){case"right":l=s-c-2;break;case"center":l=(s-c)/2;break;case"left":default:l=2}t+=_(l)+" "+_(d)+" Td\n",t+="("+A(N)+") Tj\n",t+=-_(l)+" 0 Td\n",d=-(r+2),c=0,p=g+1,b++,m=""}else;break}return n.text=t,n.fontSize=r,n},S=function(t,e,n){var r=h.internal.getFont(e.fontName,e.fontStyle),i=h.getStringUnitWidth(t,{font:r,fontSize:parseFloat(n),charSpace:0})*parseFloat(n);return{height:h.getStringUnitWidth("3",{font:r,fontSize:parseFloat(n),charSpace:0})*parseFloat(n)*1.5,width:i}},P={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},k=function(t){var e={type:"reference",object:t};void 0===h.internal.getPageInfo(t.page).pageContext.annotations.find(function(t){return t.type===e.type&&t.object===e.object})&&h.internal.getPageInfo(t.page).pageContext.annotations.push(e)},F=function(t){for(var e in t)if(t.hasOwnProperty(e)){var n=e,r=t[e];h.internal.newObjectDeferredBegin(r&&r.objId,!0),"object"===on(r)&&"function"==typeof r.putStream&&r.putStream(),delete t[n]}},C=function(){if(void 0!==this.internal&&(void 0===this.internal.acroformPlugin||!1===this.internal.acroformPlugin.isInitialized)){if(h=this,M.FieldNum=0,this.internal.acroformPlugin=JSON.parse(JSON.stringify(P)),this.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("Exception while creating AcroformDictionary");r=h.internal.scaleFactor,h.internal.acroformPlugin.acroFormDictionaryRoot=new E,h.internal.acroformPlugin.acroFormDictionaryRoot._eventID=h.internal.events.subscribe("postPutResources",l),h.internal.events.subscribe("buildDocument",a),h.internal.events.subscribe("putCatalog",o),h.internal.events.subscribe("postPutPages",c),h.internal.acroformPlugin.isInitialized=!0}},I=n.__acroform__.arrayToPdfArray=function(t){if(Array.isArray(t)){for(var e="[",n=0;n<t.length;n++)switch(0!==n&&(e+=" "),on(t[n])){case"boolean":case"number":case"object":e+=t[n].toString();break;case"string":"/"!==t[n].substr(0,1)?e+="("+A(t[n].toString())+")":e+=t[n].toString()}return e+="]"}throw new Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")};function j(t){return(t=t||"").toString(),t="("+A(t)+")"}function B(){var e;Object.defineProperty(this,"objId",{configurable:!0,get:function(){return e||(e=h.internal.newObjectDeferred()),e},set:function(t){e=t}})}B.prototype.toString=function(){return this.objId+" 0 R"},B.prototype.putStream=function(){var t=this.getKeyValueListForStream();h.internal.putStream({data:this.stream,additionalKeyValues:t}),h.internal.out("endobj")},B.prototype.getKeyValueListForStream=function(){return function(t){var e=[],n=Object.getOwnPropertyNames(t).filter(function(t){return"content"!=t&&"appearanceStreamContent"!=t&&"_"!=t.substring(0,1)});for(var r in n)if(!1===Object.getOwnPropertyDescriptor(t,n[r]).configurable){var i=n[r],a=t[i];a&&(Array.isArray(a)?e.push({key:i,value:I(a)}):a instanceof B?e.push({key:i,value:a.objId+" 0 R"}):"function"!=typeof a&&e.push({key:i,value:a}))}return e}(this)};var O=function(){B.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writeable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writeable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writeable:!0});var e,n=[];Object.defineProperty(this,"BBox",{configurable:!1,writeable:!0,get:function(){return n},set:function(t){n=t}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writeable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(t){e=t.trim()},get:function(){return e||null}})};i(O,B);var E=function(){B.call(this);var e,t=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return 0<t.length?t:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return t}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(e)return"("+e+")"},set:function(t){e=t}})};i(E,B);var M=function t(){B.call(this);var e=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return e},set:function(t){if(isNaN(t))throw new Error('Invalid value "'+t+'" for attribute F supplied.');e=t}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(e,3))},set:function(t){!0===Boolean(t)?this.F=x(e,3):this.F=N(e,3)}});var n=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return n},set:function(t){if(isNaN(t))throw new Error('Invalid value "'+t+'" for attribute Ff supplied.');n=t}});var r=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(0!==r.length)return r},set:function(t){r=void 0!==t?t:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[0])?0:y(r[0])},set:function(t){r[0]=v(t)}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[1])?0:y(r[1])},set:function(t){r[1]=v(t)}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[2])?0:y(r[2])},set:function(t){r[2]=v(t)}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[3])?0:y(r[3])},set:function(t){r[3]=v(t)}});var i="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return i},set:function(t){switch(t){case"/Btn":case"/Tx":case"/Ch":case"/Sig":i=t;break;default:throw new Error('Invalid value "'+t+'" for attribute FT supplied.')}}});var a=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!a||a.length<1){if(this instanceof W)return;a="FieldObject"+t.FieldNum++}return"("+A(a)+")"},set:function(t){a=t.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return a},set:function(t){a=t}});var o="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return o},set:function(t){o=t}});var s="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return s},set:function(t){s=t}});var u=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return y(u)},set:function(t){u=v(t)}});var l=50;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return y(l)},set:function(t){l=v(t)}});var c="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return c},set:function(t){c=t}});var h="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!h||this instanceof W||this instanceof G))return j(h)},set:function(t){t=t.toString(),h=t}});var f=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(f)return this instanceof U==!1?j(f):f},set:function(t){t=t.toString(),f=this instanceof U==!1?"("===t.substr(0,1)?b(t.substr(1,t.length-2)):b(t):t}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof U==!0?b(f.substr(1,f.length-1)):f},set:function(t){t=t.toString(),f=this instanceof U==!0?"/"+t:t}});var d=null;Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(d)return this instanceof U==!1?j(d):d},set:function(t){t=t.toString(),d=this instanceof U==!1?"("===t.substr(0,1)?b(t.substr(1,t.length-2)):b(t):t}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof U==!0?b(d.substr(1,d.length-1)):d},set:function(t){t=t.toString(),d=this instanceof U==!0?"/"+t:t}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var p,g=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,writeable:!0,get:function(){return g},set:function(t){t=Boolean(t),g=t}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,writeable:!0,get:function(){if(p)return p},set:function(t){p=t}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,1))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,1):this.Ff=N(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,2))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,2):this.Ff=N(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,3))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,3):this.Ff=N(this.Ff,3)}});var m=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(null!==m)return m},set:function(t){if(-1===[0,1,2].indexOf(t))throw new Error('Invalid value "'+t+'" for attribute Q supplied.');m=t}}),Object.defineProperty(this,"textAlign",{get:function(){var t;switch(m){case 0:default:t="left";break;case 1:t="center";break;case 2:t="right"}return t},configurable:!0,enumerable:!0,set:function(t){switch(t){case"right":case 2:m=2;break;case"center":case 1:m=1;break;case"left":case 0:default:m=0}}})};i(M,B);function T(){M.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var e=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return e},set:function(t){e=t}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return e},set:function(t){e=t}});var n=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return I(n)},set:function(t){n=function(t){var e=[];return"string"==typeof t&&(e=function(t,e,n){n||(n=1);for(var r,i=[];r=e.exec(t);)i.push(r[n]);return i}(t,/\((.*?)\)/g)),e}(t)}}),this.getOptions=function(){return n},this.setOptions=function(t){n=t,this.sort&&n.sort()},this.addOption=function(t){t=(t=t||"").toString(),n.push(t),this.sort&&n.sort()},this.removeOption=function(t,e){for(e=e||!1,t=(t=t||"").toString();-1!==n.indexOf(t)&&(n.splice(n.indexOf(t),1),!1!==e););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,18))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,18):this.Ff=N(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,19))},set:function(t){!0===this.combo&&(!0===Boolean(t)?this.Ff=x(this.Ff,19):this.Ff=N(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,20))},set:function(t){!0===Boolean(t)?(this.Ff=x(this.Ff,20),n.sort()):this.Ff=N(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,22))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,22):this.Ff=N(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,23))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,23):this.Ff=N(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,27))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,27):this.Ff=N(this.Ff,27)}}),this.hasAppearanceStream=!1}i(T,M);function q(){T.call(this),this.fontName="helvetica",this.combo=!1}i(q,T);function R(){q.call(this),this.combo=!0}i(R,q);function D(){R.call(this),this.edit=!0}i(D,R);var U=function(){M.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,15))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,15):this.Ff=N(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,16))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,16):this.Ff=N(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,17))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,17):this.Ff=N(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,26))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,26):this.Ff=N(this.Ff,26)}});var e,n={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){if(0!==Object.keys(n).length){var t,e=[];for(t in e.push("<<"),n)e.push("/"+t+" ("+n[t]+")");return e.push(">>"),e.join("\n")}},set:function(t){"object"===on(t)&&(n=t)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return n.CA||""},set:function(t){"string"==typeof t&&(n.CA=t)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return e},set:function(t){e=t}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return e.substr(1,e.length-1)},set:function(t){e="/"+t}})};i(U,M);function z(){U.call(this),this.pushButton=!0}i(z,U);function H(){U.call(this),this.radio=!0,this.pushButton=!1;var e=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return e},set:function(t){e=void 0!==t?t:[]}})}i(H,U);var W=function(){var e,n;M.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return e},set:function(t){e=t}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return n},set:function(t){n=t}});var r,i={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var t,e=[];for(t in e.push("<<"),i)e.push("/"+t+" ("+i[t]+")");return e.push(">>"),e.join("\n")},set:function(t){"object"===on(t)&&(i=t)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return i.CA||""},set:function(t){"string"==typeof t&&(i.CA=t)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return r},set:function(t){r=t}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return r.substr(1,r.length-1)},set:function(t){r="/"+t}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=J.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};i(W,M),H.prototype.setAppearance=function(t){if(!("createAppearanceStream"in t&&"getCA"in t))throw new Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var e in this.Kids)if(this.Kids.hasOwnProperty(e)){var n=this.Kids[e];n.appearanceStreamContent=t.createAppearanceStream(n.optionName),n.caption=t.getCA()}},H.prototype.createOption=function(t){var e=new W;return e.Parent=this,e.optionName=t,this.Kids.push(e),X.call(this,e),e};function V(){U.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=J.CheckBox.createAppearanceStream()}i(V,U);var G=function(){M.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,13))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,13):this.Ff=N(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,21))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,21):this.Ff=N(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,23))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,23):this.Ff=N(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,24))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,24):this.Ff=N(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,25))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,25):this.Ff=N(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,26))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,26):this.Ff=N(this.Ff,26)}});var e=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return e},set:function(t){e=t}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return e},set:function(t){Number.isInteger(t)&&(e=t)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};i(G,M);function Y(){G.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return Boolean(w(this.Ff,14))},set:function(t){!0===Boolean(t)?this.Ff=x(this.Ff,14):this.Ff=N(this.Ff,14)}}),this.password=!0}i(Y,G);var J={CheckBox:{createAppearanceStream:function(){return{N:{On:J.CheckBox.YesNormal},D:{On:J.CheckBox.YesPushDown,Off:J.CheckBox.OffPushDown}}},YesPushDown:function(t){var e=new u(t),n=[],r=h.internal.getFont(t.fontName,t.fontStyle).id,i=h.__private__.encodeColorString(t.color),a=L(t,t.caption);return n.push("0.749023 g"),n.push("0 0 "+_(J.internal.getWidth(t))+" "+_(J.internal.getHeight(t))+" re"),n.push("f"),n.push("BMC"),n.push("q"),n.push("0 0 1 rg"),n.push("/"+r+" "+_(a.fontSize)+" Tf "+i),n.push("BT"),n.push(a.text),n.push("ET"),n.push("Q"),n.push("EMC"),e.stream=n.join("\n"),e},YesNormal:function(t){var e=new u(t),n=h.internal.getFont(t.fontName,t.fontStyle).id,r=h.__private__.encodeColorString(t.color),i=[],a=J.internal.getHeight(t),o=J.internal.getWidth(t),s=L(t,t.caption);return i.push("1 g"),i.push("0 0 "+_(o)+" "+_(a)+" re"),i.push("f"),i.push("q"),i.push("0 0 1 rg"),i.push("0 0 "+_(o-1)+" "+_(a-1)+" re"),i.push("W"),i.push("n"),i.push("0 g"),i.push("BT"),i.push("/"+n+" "+_(s.fontSize)+" Tf "+r),i.push(s.text),i.push("ET"),i.push("Q"),e.stream=i.join("\n"),e},OffPushDown:function(t){var e=new u(t),n=[];return n.push("0.749023 g"),n.push("0 0 "+_(J.internal.getWidth(t))+" "+_(J.internal.getHeight(t))+" re"),n.push("f"),e.stream=n.join("\n"),e}},RadioButton:{Circle:{createAppearanceStream:function(t){var e={D:{Off:J.RadioButton.Circle.OffPushDown},N:{}};return e.N[t]=J.RadioButton.Circle.YesNormal,e.D[t]=J.RadioButton.Circle.YesPushDown,e},getCA:function(){return"l"},YesNormal:function(t){var e=new u(t),n=[],r=J.internal.getWidth(t)<=J.internal.getHeight(t)?J.internal.getWidth(t)/4:J.internal.getHeight(t)/4;r=Number((.9*r).toFixed(5));var i=J.internal.Bezier_C,a=Number((r*i).toFixed(5));return n.push("q"),n.push("1 0 0 1 "+s(J.internal.getWidth(t)/2)+" "+s(J.internal.getHeight(t)/2)+" cm"),n.push(r+" 0 m"),n.push(r+" "+a+" "+a+" "+r+" 0 "+r+" c"),n.push("-"+a+" "+r+" -"+r+" "+a+" -"+r+" 0 c"),n.push("-"+r+" -"+a+" -"+a+" -"+r+" 0 -"+r+" c"),n.push(a+" -"+r+" "+r+" -"+a+" "+r+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join("\n"),e},YesPushDown:function(t){var e=new u(t),n=[],r=J.internal.getWidth(t)<=J.internal.getHeight(t)?J.internal.getWidth(t)/4:J.internal.getHeight(t)/4,i=(r=Number((.9*r).toFixed(5)),Number((2*r).toFixed(5))),a=Number((i*J.internal.Bezier_C).toFixed(5)),o=Number((r*J.internal.Bezier_C).toFixed(5));return n.push("0.749023 g"),n.push("q"),n.push("1 0 0 1 "+s(J.internal.getWidth(t)/2)+" "+s(J.internal.getHeight(t)/2)+" cm"),n.push(i+" 0 m"),n.push(i+" "+a+" "+a+" "+i+" 0 "+i+" c"),n.push("-"+a+" "+i+" -"+i+" "+a+" -"+i+" 0 c"),n.push("-"+i+" -"+a+" -"+a+" -"+i+" 0 -"+i+" c"),n.push(a+" -"+i+" "+i+" -"+a+" "+i+" 0 c"),n.push("f"),n.push("Q"),n.push("0 g"),n.push("q"),n.push("1 0 0 1 "+s(J.internal.getWidth(t)/2)+" "+s(J.internal.getHeight(t)/2)+" cm"),n.push(r+" 0 m"),n.push(r+" "+o+" "+o+" "+r+" 0 "+r+" c"),n.push("-"+o+" "+r+" -"+r+" "+o+" -"+r+" 0 c"),n.push("-"+r+" -"+o+" -"+o+" -"+r+" 0 -"+r+" c"),n.push(o+" -"+r+" "+r+" -"+o+" "+r+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join("\n"),e},OffPushDown:function(t){var e=new u(t),n=[],r=J.internal.getWidth(t)<=J.internal.getHeight(t)?J.internal.getWidth(t)/4:J.internal.getHeight(t)/4;r=Number((.9*r).toFixed(5));var i=Number((2*r).toFixed(5)),a=Number((i*J.internal.Bezier_C).toFixed(5));return n.push("0.749023 g"),n.push("q"),n.push("1 0 0 1 "+s(J.internal.getWidth(t)/2)+" "+s(J.internal.getHeight(t)/2)+" cm"),n.push(i+" 0 m"),n.push(i+" "+a+" "+a+" "+i+" 0 "+i+" c"),n.push("-"+a+" "+i+" -"+i+" "+a+" -"+i+" 0 c"),n.push("-"+i+" -"+a+" -"+a+" -"+i+" 0 -"+i+" c"),n.push(a+" -"+i+" "+i+" -"+a+" "+i+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join("\n"),e}},Cross:{createAppearanceStream:function(t){var e={D:{Off:J.RadioButton.Cross.OffPushDown},N:{}};return e.N[t]=J.RadioButton.Cross.YesNormal,e.D[t]=J.RadioButton.Cross.YesPushDown,e},getCA:function(){return"8"},YesNormal:function(t){var e=new u(t),n=[],r=J.internal.calculateCross(t);return n.push("q"),n.push("1 1 "+_(J.internal.getWidth(t)-2)+" "+_(J.internal.getHeight(t)-2)+" re"),n.push("W"),n.push("n"),n.push(_(r.x1.x)+" "+_(r.x1.y)+" m"),n.push(_(r.x2.x)+" "+_(r.x2.y)+" l"),n.push(_(r.x4.x)+" "+_(r.x4.y)+" m"),n.push(_(r.x3.x)+" "+_(r.x3.y)+" l"),n.push("s"),n.push("Q"),e.stream=n.join("\n"),e},YesPushDown:function(t){var e=new u(t),n=J.internal.calculateCross(t),r=[];return r.push("0.749023 g"),r.push("0 0 "+_(J.internal.getWidth(t))+" "+_(J.internal.getHeight(t))+" re"),r.push("f"),r.push("q"),r.push("1 1 "+_(J.internal.getWidth(t)-2)+" "+_(J.internal.getHeight(t)-2)+" re"),r.push("W"),r.push("n"),r.push(_(n.x1.x)+" "+_(n.x1.y)+" m"),r.push(_(n.x2.x)+" "+_(n.x2.y)+" l"),r.push(_(n.x4.x)+" "+_(n.x4.y)+" m"),r.push(_(n.x3.x)+" "+_(n.x3.y)+" l"),r.push("s"),r.push("Q"),e.stream=r.join("\n"),e},OffPushDown:function(t){var e=new u(t),n=[];return n.push("0.749023 g"),n.push("0 0 "+_(J.internal.getWidth(t))+" "+_(J.internal.getHeight(t))+" re"),n.push("f"),e.stream=n.join("\n"),e}}},createDefaultAppearanceStream:function(t){var e=h.internal.getFont(t.fontName,t.fontStyle).id,n=h.__private__.encodeColorString(t.color);return"/"+e+" "+t.fontSize+" Tf "+n}};J.internal={Bezier_C:.551915024494,calculateCross:function(t){var e=J.internal.getWidth(t),n=J.internal.getHeight(t),r=Math.min(e,n);return{x1:{x:(e-r)/2,y:(n-r)/2+r},x2:{x:(e-r)/2+r,y:(n-r)/2},x3:{x:(e-r)/2,y:(n-r)/2},x4:{x:(e-r)/2+r,y:(n-r)/2+r}}}},J.internal.getWidth=function(t){var e=0;return"object"===on(t)&&(e=v(t.Rect[2])),e},J.internal.getHeight=function(t){var e=0;return"object"===on(t)&&(e=v(t.Rect[3])),e};var X=n.addField=function(t){if(C.call(this),!(t instanceof M))throw new Error("Invalid argument passed to jsPDF.addField.");return function(t){h.internal.acroformPlugin.printedOut&&(h.internal.acroformPlugin.printedOut=!1,h.internal.acroformPlugin.acroFormDictionaryRoot=null),h.internal.acroformPlugin.acroFormDictionaryRoot||C.call(h),h.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(t)}.call(this,t),t.page=h.internal.getCurrentPageInfo().pageNumber,this};n.addButton=function(t){if(t instanceof U==!1)throw new Error("Invalid argument passed to jsPDF.addButton.");return X.call(this,t)},n.addTextField=function(t){if(t instanceof G==!1)throw new Error("Invalid argument passed to jsPDF.addTextField.");return X.call(this,t)},n.addChoiceField=function(t){if(t instanceof T==!1)throw new Error("Invalid argument passed to jsPDF.addChoiceField.");return X.call(this,t)},"object"==on(e)&&void 0===e.ChoiceField&&void 0===e.ListBox&&void 0===e.ComboBox&&void 0===e.EditBox&&void 0===e.Button&&void 0===e.PushButton&&void 0===e.RadioButton&&void 0===e.CheckBox&&void 0===e.TextField&&void 0===e.PasswordField?(e.ChoiceField=T,e.ListBox=q,e.ComboBox=R,e.EditBox=D,e.Button=U,e.PushButton=z,e.RadioButton=H,e.CheckBox=V,e.TextField=G,e.PasswordField=Y,e.AcroForm={Appearance:J}):console.warn("AcroForm-Classes are not populated into global-namespace, because the class-Names exist already. This avoids conflicts with the already used framework."),n.AcroFormChoiceField=T,n.AcroFormListBox=q,n.AcroFormComboBox=R,n.AcroFormEditBox=D,n.AcroFormButton=U,n.AcroFormPushButton=z,n.AcroFormRadioButton=H,n.AcroFormCheckBox=V,n.AcroFormTextField=G,n.AcroFormPasswordField=Y,n.AcroFormAppearance=J,n.AcroForm={ChoiceField:T,ListBox:q,ComboBox:R,EditBox:D,Button:U,PushButton:z,RadioButton:H,CheckBox:V,TextField:G,PasswordField:Y,Appearance:J},t.AcroForm={ChoiceField:T,ListBox:q,ComboBox:R,EditBox:D,Button:U,PushButton:z,RadioButton:H,CheckBox:V,TextField:G,PasswordField:Y,Appearance:J}}(U,"undefined"!=typeof window&&window||"undefined"!=typeof global&&global),
/** @license
   * jsPDF addImage plugin
   * Copyright (c) 2012 Jason Siefken, https://github.com/siefkenj/
   *               2013 Chris Dowling, https://github.com/gingerchris
   *               2013 Trinh Ho, https://github.com/ineedfat
   *               2013 Edwin Alejandro Perez, https://github.com/eaparango
   *               2013 Norah Smith, https://github.com/burnburnrocket
   *               2014 Diego Casorran, https://github.com/diegocr
   *               2014 James Robb, https://github.com/jamesbrobb
   *
   * 
   */
function(s){var p="addImage_";s.__addimage__={};function h(t){for(var e=this.internal.write,n=this.internal.putStream,r=(0,this.internal.getFilters)();-1!==r.indexOf("FlateEncode");)r.splice(r.indexOf("FlateEncode"),1);t.objectId=this.internal.newObject();var i=[];if(i.push({key:"Type",value:"/XObject"}),i.push({key:"Subtype",value:"/Image"}),i.push({key:"Width",value:t.width}),i.push({key:"Height",value:t.height}),t.colorSpace===v.INDEXED?i.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(t.palette.length/3-1)+" "+("sMask"in t&&void 0!==t.sMask?t.objectId+2:t.objectId+1)+" 0 R]"}):(i.push({key:"ColorSpace",value:"/"+t.colorSpace}),t.colorSpace===v.DEVICE_CMYK&&i.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),i.push({key:"BitsPerComponent",value:t.bitsPerComponent}),"decodeParameters"in t&&void 0!==t.decodeParameters&&i.push({key:"DecodeParms",value:"<<"+t.decodeParameters+">>"}),"transparency"in t&&Array.isArray(t.transparency)){for(var a="",o=0,s=t.transparency.length;o<s;o++)a+=t.transparency[o]+" "+t.transparency[o]+" ";i.push({key:"Mask",value:"["+a+"]"})}void 0!==t.sMask&&i.push({key:"SMask",value:t.objectId+1+" 0 R"});var u=void 0!==t.filter?["/"+t.filter]:void 0;if(n({data:t.data,additionalKeyValues:i,alreadyAppliedFilters:u}),e("endobj"),"sMask"in t&&void 0!==t.sMask){var l="/Predictor "+t.predictor+" /Colors 1 /BitsPerComponent "+t.bitsPerComponent+" /Columns "+t.width,c={width:t.width,height:t.height,colorSpace:"DeviceGray",bitsPerComponent:t.bitsPerComponent,decodeParameters:l,data:t.sMask};"filter"in t&&(c.filter=t.filter),h.call(this,c)}t.colorSpace===v.INDEXED&&(this.internal.newObject(),n({data:A(new Uint8Array(t.palette))}),e("endobj"))}function t(){var t=this.internal.collections[p+"images"];for(var e in t)h.call(this,t[e])}function e(){var t,e=this.internal.collections[p+"images"],n=this.internal.write;for(var r in e)n("/I"+(t=e[r]).index,t.objectId,"0","R")}function g(){this.internal.collections[p+"images"]||(this.internal.collections[p+"images"]={},this.internal.events.subscribe("putResources",t),this.internal.events.subscribe("putXobjectDict",e))}function u(t){return"function"==typeof s["process"+t.toUpperCase()]}function f(t){return"object"===on(t)&&1===t.nodeType}function l(t,e){if("IMG"===t.nodeName&&t.hasAttribute("src")){var n=""+t.getAttribute("src");if(0===n.indexOf("data:image/"))return atob(unescape(n).split("base64,").pop());var r=s.loadFile(n,!0);if(void 0!==r)return r}if("CANVAS"===t.nodeName){var i;switch(e){case"PNG":i="image/png";break;case"WEBP":i="image/webp";break;case"JPEG":case"JPG":default:i="image/jpeg"}return atob(t.toDataURL(i,1).split("base64,").pop())}}function d(t,e,n,r,i,a){var o=function(t,e,n){return t||e||(e=t=-96),t<0&&(t=-1*n.width*72/t/this.internal.scaleFactor),e<0&&(e=-1*n.height*72/e/this.internal.scaleFactor),0===t&&(t=e*n.width/n.height),0===e&&(e=t*n.height/n.width),[t,e]}.call(this,n,r,i),s=this.internal.getCoordinateString,u=this.internal.getVerticalCoordinateString,l=function(){var t=this.internal.collections[p+"images"];return g.call(this),t}.call(this);if(n=o[0],r=o[1],l[i.index]=i,a){a*=Math.PI/180;var c=Math.cos(a),h=Math.sin(a),f=function(t){return t.toFixed(4)},d=[f(c),f(h),f(-1*h),f(c),0,0,"cm"]}this.internal.write("q"),a?(this.internal.write([1,"0","0",1,s(t),u(e+r),"cm"].join(" ")),this.internal.write(d.join(" ")),this.internal.write([s(n),"0","0",s(r),"0","0","cm"].join(" "))):this.internal.write([s(n),"0","0",s(r),s(t),u(e+r),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write([1,0,0,-1,0,0,"cm"].join(" ")),this.internal.write("/I"+i.index+" Do"),this.internal.write("Q")}var m="UNKNOWN",c={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},b=s.__addimage__.getImageFileTypeByImageData=function(t,e){var n,r;e=e||m;var i,a,o,s=m;if(N(t))for(o in c)for(i=c[o],n=0;n<i.length;n+=1){for(a=!0,r=0;r<i[n].length;r+=1)if(void 0!==i[n][r]&&i[n][r]!==t[r]){a=!1;break}if(!0===a){s=o;break}}else for(o in c)for(i=c[o],n=0;n<i.length;n+=1){for(a=!0,r=0;r<i[n].length;r+=1)if(void 0!==i[n][r]&&i[n][r]!==t.charCodeAt(r)){a=!1;break}if(!0===a){s=o;break}}return s===m&&e!==m&&(s=e),s},v=s.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"};s.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"};var y=s.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},w=s.__addimage__.sHashCode=function(t){var e,n,r=0;if("string"==typeof t)for(n=t.length,e=0;e<n;e++)r=(r<<5)-r+t.charCodeAt(e),r|=0;else if(N(t))for(n=t.byteLength/2,e=0;e<n;e++)r=(r<<5)-r+t[e],r|=0;return r},a=s.__addimage__.validateStringAsBase64=function(t){(t=t||"").toString().trim();var e=!0;return 0===t.length&&(e=!1),t.length%4!=0&&(e=!1),!1===/^[A-Za-z0-9+/]+$/.test(t.substr(0,t.length-2))&&(e=!1),!1===/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(t.substr(-2))&&(e=!1),e},o=s.__addimage__.extractImageFromDataUrl=function(t){var e=(t=t||"").split("base64,"),n=null;if(2===e.length){var r=/^data:(\w*\/\w*);*(charset=[\w=-]*)*;*$/.exec(e[0]);Array.isArray(r)&&(n={mimeType:r[1],charset:r[2],data:e[1]})}return n},x=s.__addimage__.supportsArrayBuffer=function(){return"undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array};s.__addimage__.isArrayBuffer=function(t){return x()&&t instanceof ArrayBuffer};var N=s.__addimage__.isArrayBufferView=function(t){return x()&&"undefined"!=typeof Uint32Array&&(t instanceof Int8Array||t instanceof Uint8Array||"undefined"!=typeof Uint8ClampedArray&&t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array)},L=s.__addimage__.binaryStringToUint8Array=function(t){for(var e=t.length,n=new Uint8Array(e),r=0;r<e;r++)n[r]=t.charCodeAt(r);return n},A=s.__addimage__.arrayBufferToBinaryString=function(e){try{return atob(btoa(String.fromCharCode.apply(null,e)))}catch(t){if("undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.reduce)return new Uint8Array(e).reduce(function(t,e){return t.push(String.fromCharCode(e)),t},[]).join("")}};s.addImage=function(){var t,e,n,r,i,a,o,s,u;if(t=arguments[0],u="number"==typeof arguments[1]?(e=m,n=arguments[1],r=arguments[2],i=arguments[3],a=arguments[4],o=arguments[5],s=arguments[6],arguments[7]):(e=arguments[1],n=arguments[2],r=arguments[3],i=arguments[4],a=arguments[5],o=arguments[6],s=arguments[7],arguments[8]),"object"===on(t)&&!f(t)&&"imageData"in t){var l=t;t=l.imageData,e=l.format||e||m,n=l.x||n||0,r=l.y||r||0,i=l.w||l.width||i,a=l.h||l.height||a,o=l.alias||o,s=l.compression||s,u=l.rotation||l.angle||u}var c=this.internal.getFilters();if(void 0===s&&-1!==c.indexOf("FlateEncode")&&(s="SLOW"),isNaN(n)||isNaN(r))throw new Error("Invalid coordinates passed to jsPDF.addImage");g.call(this);var h=_.call(this,t,e,o,s);return d.call(this,n,r,i,a,h,u),this};var _=function(t,e,n,r){var i,a;if("string"==typeof t&&b(t)===m){t=unescape(t);var o=S(t,!1);""!==o?t=o:void 0!==(o=s.loadFile(t,!0))&&(t=o)}if(f(t)&&(t=l(t,e)),e=b(t,e),!u(e))throw new Error("addImage does not support files of type '"+e+"', please ensure that a plugin for '"+e+"' support is added.");if(function(t){return null==t||0===t.length}(n)&&(n=function(t){return"string"==typeof t||N(t)?w(t):null}(t)),(i=function(t){var e=this.internal.collections[p+"images"];if(e)for(var n in e)if(t===e[n].alias)return e[n]}.call(this,n))||(x()&&(t instanceof Uint8Array||(t=L(a=t))),i=this["process"+e.toUpperCase()](t,function(){return Object.keys(this.internal.collections[p+"images"]).length}.call(this),n,function(t){return t&&"string"==typeof t&&(t=t.toUpperCase()),t in s.image_compression?t:y.NONE}(r),a)),!i)throw new Error("An unknown error occurred whilst processing the image.");return i},S=s.__addimage__.convertBase64ToBinaryString=function(t,e){var n;e="boolean"!=typeof e||e;var r,i="";if("string"==typeof t){r=null!==(n=o(t))?n.data:t;try{i=atob(r)}catch(t){if(e)throw a(r)?new Error("atob-Error in jsPDF.convertBase64ToBinaryString "+t.message):new Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return i};s.getImageProperties=function(t){var e,n,r="";if(f(t)&&(t=l(t)),"string"==typeof t&&b(t)===m&&(""===(r=S(t,!1))&&(r=s.loadFile(t)||""),t=r),n=b(t),!u(n))throw new Error("addImage does not support files of type '"+n+"', please ensure that a plugin for '"+n+"' support is added.");if(!x()||t instanceof Uint8Array||(t=L(t)),!(e=this["process"+n.toUpperCase()](t)))throw new Error("An unknown error occurred whilst processing the image");return e.fileType=n,e}}(U.API),
/**
   * @license
   * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>
   *
   * Licensed under the MIT License.
   * http://opensource.org/licenses/mit-license
   */
t=U.API,U.API.events.push(["addPage",function(t){this.internal.getPageInfo(t.pageNumber).pageContext.annotations=[]}]),t.events.push(["putPage",function(t){for(var e,n,r,i=this.internal.getCoordinateString,a=this.internal.getVerticalCoordinateString,o=this.internal.getPageInfoByObjId(t.objId),s=t.pageContext.annotations,u=!1,l=0;l<s.length&&!u;l++)switch((e=s[l]).type){case"link":(z(e.options.url)||z(e.options.pageNumber))&&(u=!0);break;case"reference":case"text":case"freetext":u=!0}if(0!=u){this.internal.write("/Annots [");for(var c=0;c<s.length;c++)switch((e=s[c]).type){case"reference":this.internal.write(" "+e.object.objId+" 0 R ");break;case"text":var h=this.internal.newAdditionalObject(),f=this.internal.newAdditionalObject(),d=e.title||"Note";r="<</Type /Annot /Subtype /Text "+(n="/Rect ["+i(e.bounds.x)+" "+a(e.bounds.y+e.bounds.h)+" "+i(e.bounds.x+e.bounds.w)+" "+a(e.bounds.y)+"] ")+"/Contents ("+e.contents+")",r+=" /Popup "+f.objId+" 0 R",r+=" /P "+o.objId+" 0 R",r+=" /T ("+d+") >>",h.content=r;var p=h.objId+" 0 R";r="<</Type /Annot /Subtype /Popup "+(n="/Rect ["+i(e.bounds.x+30)+" "+a(e.bounds.y+e.bounds.h)+" "+i(e.bounds.x+e.bounds.w+30)+" "+a(e.bounds.y)+"] ")+" /Parent "+p,e.open&&(r+=" /Open true"),r+=" >>",f.content=r,this.internal.write(h.objId,"0 R",f.objId,"0 R");break;case"freetext":n="/Rect ["+i(e.bounds.x)+" "+a(e.bounds.y)+" "+i(e.bounds.x+e.bounds.w)+" "+a(e.bounds.y+e.bounds.h)+"] ";var g=e.color||"#000000";r="<</Type /Annot /Subtype /FreeText "+n+"/Contents ("+e.contents+")",r+=" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+g+")",r+=" /Border [0 0 0]",r+=" >>",this.internal.write(r);break;case"link":if(e.options.name){var m=this.annotations._nameMap[e.options.name];e.options.pageNumber=m.page,e.options.top=m.y}else e.options.top||(e.options.top=0);if(n="/Rect ["+i(e.x)+" "+a(e.y)+" "+i(e.x+e.w)+" "+a(e.y+e.h)+"] ",r="",e.options.url)r="<</Type /Annot /Subtype /Link "+n+"/Border [0 0 0] /A <</S /URI /URI ("+e.options.url+") >>";else if(e.options.pageNumber)switch(r="<</Type /Annot /Subtype /Link "+n+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(e.options.pageNumber).objId+" 0 R",e.options.magFactor=e.options.magFactor||"XYZ",e.options.magFactor){case"Fit":r+=" /Fit]";break;case"FitH":r+=" /FitH "+e.options.top+"]";break;case"FitV":e.options.left=e.options.left||0,r+=" /FitV "+e.options.left+"]";break;case"XYZ":default:var b=a(e.options.top);e.options.left=e.options.left||0,void 0===e.options.zoom&&(e.options.zoom=0),r+=" /XYZ "+e.options.left+" "+b+" "+e.options.zoom+"]"}""!=r&&(r+=" >>",this.internal.write(r))}this.internal.write("]")}}]),t.createAnnotation=function(t){var e=this.internal.getCurrentPageInfo();switch(t.type){case"link":this.link(t.bounds.x,t.bounds.y,t.bounds.w,t.bounds.h,t);break;case"text":case"freetext":e.pageContext.annotations.push(t)}},t.link=function(t,e,n,r,i){this.internal.getCurrentPageInfo().pageContext.annotations.push({x:t,y:e,w:n,h:r,options:i,type:"link"})},t.textWithLink=function(t,e,n,r){var i=this.getTextWidth(t),a=this.internal.getLineHeight()/this.internal.scaleFactor;return this.text(t,e,n,r),n+=.2*a,this.link(e,n-a,i,a,r),i},t.getTextWidth=function(t){var e=this.internal.getFontSize();return this.getStringUnitWidth(t)*e/this.internal.scaleFactor},
/**
   * @license
   * Copyright (c) 2017 Aras Abbasi
   *
   * Licensed under the MIT License.
   * http://opensource.org/licenses/mit-license
   */
function(t){var l={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},a={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},e={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},n=[1570,1571,1573,1575];t.__arabicParser__={};var r=t.__arabicParser__.isInArabicSubstitutionA=function(t){return void 0!==l[t.charCodeAt(0)]},c=t.__arabicParser__.isArabicLetter=function(t){return"string"==typeof t&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(t)},i=t.__arabicParser__.isArabicEndLetter=function(t){return c(t)&&r(t)&&l[t.charCodeAt(0)].length<=2},o=t.__arabicParser__.isArabicAlfLetter=function(t){return c(t)&&0<=n.indexOf(t.charCodeAt(0))};t.__arabicParser__.arabicLetterHasIsolatedForm=function(t){return c(t)&&r(t)&&1<=l[t.charCodeAt(0)].length};var s=t.__arabicParser__.arabicLetterHasFinalForm=function(t){return c(t)&&r(t)&&2<=l[t.charCodeAt(0)].length};t.__arabicParser__.arabicLetterHasInitialForm=function(t){return c(t)&&r(t)&&3<=l[t.charCodeAt(0)].length};var u=t.__arabicParser__.arabicLetterHasMedialForm=function(t){return c(t)&&r(t)&&4==l[t.charCodeAt(0)].length},h=t.__arabicParser__.resolveLigatures=function(t){var e=0,n=a,r="",i=0;for(e=0;e<t.length;e+=1)void 0!==n[t.charCodeAt(e)]?(i++,"number"==typeof(n=n[t.charCodeAt(e)])&&(r+=String.fromCharCode(n),n=a,i=0),e===t.length-1&&(n=a,r+=t.charAt(e-(i-1)),e-=i-1,i=0)):(n=a,r+=t.charAt(e-i),e-=i,i=0);return r};t.__arabicParser__.isArabicDiacritic=function(t){return void 0!==t&&void 0!==e[t.charCodeAt(0)]};function f(t){var e=0,n=0,r=0,i="",a="",o="",s=(t=t||"").split("\\s+"),u=[];for(e=0;e<s.length;e+=1){for(u.push(""),n=0;n<s[e].length;n+=1)i=s[e][n],a=s[e][n-1],o=s[e][n+1],c(i)?(r=d(i,a,o),u[e]+=-1!==r?String.fromCharCode(l[i.charCodeAt(0)][r]):i):u[e]+=i;u[e]=h(u[e])}return u.join(" ")}var d=t.__arabicParser__.getCorrectForm=function(t,e,n){return c(t)?!1===r(t)?-1:!s(t)||!c(e)&&!c(n)||!c(n)&&i(e)||i(t)&&!c(e)||i(t)&&o(e)||i(t)&&i(e)?0:u(t)&&c(e)&&!i(e)&&c(n)&&s(n)?3:i(t)||!c(n)?1:2:-1},p=t.__arabicParser__.processArabic=t.processArabic=function(){var t,e="string"==typeof arguments[0]?arguments[0]:arguments[0].text,n=[];if(Array.isArray(e)){var r=0;for(n=[],r=0;r<e.length;r+=1)Array.isArray(e[r])?n.push([f(e[r][0]),e[r][1],e[r][2]]):n.push([f(e[r])]);t=n}else t=f(e);return"string"==typeof arguments[0]?t:(arguments[0].text=t,arguments[0])};t.events.push(["preProcessText",p])}(U.API),U.API.autoPrint=function(t){var e;switch((t=t||{}).variant=t.variant||"non-conform",t.variant){case"javascript":this.addJS("print({});");break;case"non-conform":default:this.internal.events.subscribe("postPutResources",function(){e=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.out("/OpenAction "+e+" 0 R")})}return this},
/**
   * @license
   * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>
   *
   * Licensed under the MIT License.
   * http://opensource.org/licenses/mit-license
   */
e=U.API,H.prototype.getContext=function(t,e){var n;if("2d"!==(t=t||"2d"))return null;for(n in e)this.pdf.context2d.hasOwnProperty(n)&&(this.pdf.context2d[n]=e[n]);return(this.pdf.context2d._canvas=this).pdf.context2d},H.prototype.toDataURL=function(){throw new Error("toDataURL is not implemented.")},e.events.push(["initialized",function(){this.canvas=new H,this.canvas.pdf=this}]),
/**
   * @license
   * ====================================================================
   * Copyright (c) 2013 Youssef Beddad, <EMAIL>
   *               2013 Eduardo Menezes de Morais, <EMAIL>
   *               2013 Lee Driscoll, https://github.com/lsdriscoll
   *               2014 Juan Pablo Gaviria, https://github.com/juanpgaviria
   *               2014 James Hall, <EMAIL>
   *               2014 Diego Casorran, https://github.com/diegocr
   *
   * 
   * ====================================================================
   */
function(t){function S(){void 0===this.internal.__cell__&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},P),this.internal.__cell__.margins.width=this.getPageWidth(),k.call(this))}var P={left:0,top:0,bottom:0,right:0},o=!1,k=function(){this.internal.__cell__.lastCell=new F,this.internal.__cell__.pages=1},F=function(){var e=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return e},set:function(t){e=t}});var n=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return n},set:function(t){n=t}});var r=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return r},set:function(t){r=t}});var i=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return i},set:function(t){i=t}});var a=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return a},set:function(t){a=t}});var o=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return o},set:function(t){o=t}});var s=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return s},set:function(t){s=t}}),this};F.prototype.clone=function(){return new F(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},F.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},t.setHeaderFunction=function(t){return S.call(this),this.internal.__cell__.headerFunction="function"==typeof t?t:void 0,this},t.getTextDimensions=function(t,e){S.call(this);var n=(e=e||{}).fontSize||this.getFontSize(),r=e.font||this.getFont(),i=e.scaleFactor||this.internal.scaleFactor,a=0,o=0,s=0;if(!Array.isArray(t)&&"string"!=typeof t)throw new Error("getTextDimensions expects text-parameter to be of type String or an Array of Strings.");t=Array.isArray(t)?t:[t];for(var u=0;u<t.length;u++)a<(s=this.getStringUnitWidth(t[u],{font:r})*n)&&(a=s),0!==a&&(o=t.length);return{w:a/=i,h:Math.max((o*n*this.getLineHeightFactor()-n*(this.getLineHeightFactor()-1))/i,0)}},t.cellAddPage=function(){S.call(this),this.addPage();var t=this.internal.__cell__.margins||P;return this.internal.__cell__.lastCell=new F(t.left,t.top,void 0,void 0),this.internal.__cell__.pages+=1,this},t.cellInitialize=function(){S.call(this),k.call(this)};var C=t.cell=function(){var t;t=arguments[0]instanceof F?arguments[0]:new F(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),S.call(this);var e=this.internal.__cell__.lastCell,n=this.internal.__cell__.padding,r=this.internal.__cell__.margins||P,i=this.internal.__cell__.tableHeaderRow,a=this.internal.__cell__.printHeaders;return void 0!==e.lineNumber&&(e.lineNumber===t.lineNumber?(t.x=(e.x||0)+(e.width||0),t.y=e.y||0):e.y+e.height+t.height+r.bottom>this.getPageHeight()?(this.cellAddPage(),t.y=r.top,a&&i&&(this.printHeaderRow(t.lineNumber,!0),t.y+=i[0].height)):t.y=e.y+e.height||t.y),void 0!==t.text[0]&&(this.rect(t.x,t.y,t.width,t.height,!0===o?"FD":void 0),"right"===t.align?this.text(t.text,t.x+t.width-n,t.y+n,{align:"right",baseline:"top"}):"center"===t.align?this.text(t.text,t.x+t.width/2,t.y+n,{align:"center",baseline:"top",maxWidth:t.width-n-n}):this.text(t.text,t.x+n,t.y+n,{align:"left",baseline:"top",maxWidth:t.width-n-n})),this.internal.__cell__.lastCell=t,this};t.table=function(e,n,t,r,i){if(S.call(this),!t)throw new Error("No data for PDF table.");var a,o,s,u,l=[],c=[],h=[],f={},d={},p=[],g=[],m=(i=i||{}).autoSize||!1,b=!1!==i.printHeaders,v=i.css&&void 0!==i.css["font-size"]?16*i.css["font-size"]:i.fontSize||12,y=i.margins||Object.assign({width:this.getPageWidth()},P),w="number"==typeof i.padding?i.padding:3,x=i.headerBackgroundColor||"#c8c8c8";if(k.call(this),this.internal.__cell__.printHeaders=b,this.internal.__cell__.margins=y,this.internal.__cell__.table_font_size=v,this.internal.__cell__.padding=w,this.internal.__cell__.headerBackgroundColor=x,this.setFontSize(v),null==r)h=(c=l=Object.keys(t[0])).map(function(){return"left"});else if(Array.isArray(r)&&"object"===on(r[0]))for(l=r.map(function(t){return t.name}),c=r.map(function(t){return t.prompt||t.name||""}),h=l.map(function(t){return t.align||"left"}),a=0;a<r.length;a+=1)d[r[a].name]=r[a].width*(19.049976/25.4);else Array.isArray(r)&&"string"==typeof r[0]&&(h=(c=l=r).map(function(){return"left"}));if(m)for(a=0;a<l.length;a+=1){for(f[u=l[a]]=t.map(function(t){return t[u]}),this.setFontStyle("bold"),p.push(this.getTextDimensions(c[a],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),o=f[u],this.setFontStyle("normal"),s=0;s<o.length;s+=1)p.push(this.getTextDimensions(o[s],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);d[u]=Math.max.apply(null,p)+w+w,p=[]}if(b){var N={};for(a=0;a<l.length;a+=1)N[l[a]]={},N[l[a]].text=c[a],N[l[a]].align=h[a];var L=I.call(this,N,d);g=l.map(function(t){return new F(e,n,d[t],L,N[t].text,void 0,N[t].align)}),this.setTableHeaderRow(g),this.printHeaderRow(1,!1)}var A=r.reduce(function(t,e){return t[e.name]=e.align,t},{});for(a=0;a<t.length;a+=1){var _=I.call(this,t[a],d);for(s=0;s<l.length;s+=1)C.call(this,new F(e,n,d[l[s]],_,t[a][l[s]],a+2,A[l[s]]))}return this.internal.__cell__.table_x=e,this.internal.__cell__.table_y=n,this};var I=function(t,e){var n=this.internal.__cell__.padding,r=this.internal.__cell__.table_font_size,i=this.internal.scaleFactor;return Object.keys(t).map(function(t){return"object"===on(t)?t.text:t}).map(function(t){return this.splitTextToSize(t,e[t]-n-n)},this).map(function(t){return this.getLineHeightFactor()*t.length*r/i+n+n},this).reduce(function(t,e){return Math.max(t,e)},0)};t.setTableHeaderRow=function(t){S.call(this),this.internal.__cell__.tableHeaderRow=t},t.printHeaderRow=function(t,e){if(S.call(this),!this.internal.__cell__.tableHeaderRow)throw new Error("Property tableHeaderRow does not exist.");var n;if(o=!0,"function"==typeof this.internal.__cell__.headerFunction){var r=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new F(r[0],r[1],r[2],r[3],void 0,-1)}this.setFontStyle("bold");for(var i=[],a=0;a<this.internal.__cell__.tableHeaderRow.length;a+=1)n=this.internal.__cell__.tableHeaderRow[a].clone(),e&&(n.y=this.internal.__cell__.margins.top||0,i.push(n)),n.lineNumber=t,this.setFillColor(this.internal.__cell__.headerBackgroundColor),C.call(this,n);0<i.length&&this.setTableHeaderRow(i),this.setFontStyle("normal"),o=!1}}(U.API),
/**
   * jsPDF Context2D PlugIn Copyright (c) 2014 Steven Spungin (TwelveTone LLC) <EMAIL>
   *
   * Licensed under the MIT License. http://opensource.org/licenses/mit-license
   */
function(t){function c(t){return t=t||{},this.isStrokeTransparent=t.isStrokeTransparent||!1,this.strokeOpacity=t.strokeOpacity||1,this.strokeStyle=t.strokeStyle||"#000000",this.fillStyle=t.fillStyle||"#000000",this.isFillTransparent=t.isFillTransparent||!1,this.fillOpacity=t.fillOpacity||1,this.font=t.font||"10px sans-serif",this.textBaseline=t.textBaseline||"alphabetic",this.textAlign=t.textAlign||"left",this.lineWidth=t.lineWidth||1,this.lineJoin=t.lineJoin||"miter",this.lineCap=t.lineCap||"butt",this.path=t.path||[],this.transform=void 0!==t.transform?t.transform.clone():new P,this.globalCompositeOperation=t.globalCompositeOperation||"normal",this.globalAlpha=t.globalAlpha||1,this.clip_path=t.clip_path||[],this.currentPoint=t.currentPoint||new v,this.miterLimit=t.miterLimit||10,this.lastPoint=t.lastPoint||new v,this.ignoreClearRect="boolean"!=typeof t.ignoreClearRect||t.ignoreClearRect,this}var u,i,a,l,h,v,k,P,f;t.events.push(["initialized",function(){this.context2d=new e(this),u=this.internal.f2,i=this.internal.getCoordinateString,a=this.internal.getVerticalCoordinateString,l=this.internal.getHorizontalCoordinate,h=this.internal.getVerticalCoordinate,v=this.internal.Point,k=this.internal.Rectangle,P=this.internal.Matrix,f=new c}]);var e=function(t){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}});var e=t;Object.defineProperty(this,"pdf",{get:function(){return e}});var n=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return n},set:function(t){n=Boolean(t)}});var r=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return r},set:function(t){r=Boolean(t)}});var i=0;Object.defineProperty(this,"posX",{get:function(){return i},set:function(t){isNaN(t)||(i=t)}});var a=0;Object.defineProperty(this,"posY",{get:function(){return a},set:function(t){isNaN(t)||(a=t)}});var o=!1;Object.defineProperty(this,"autoPaging",{get:function(){return o},set:function(t){o=Boolean(t)}});var s=0;Object.defineProperty(this,"lastBreak",{get:function(){return s},set:function(t){s=t}});var u=[];Object.defineProperty(this,"pageBreaks",{get:function(){return u},set:function(t){u=t}}),Object.defineProperty(this,"ctx",{get:function(){return f},set:function(t){t instanceof c&&(f=t)}}),Object.defineProperty(this,"path",{get:function(){return f.path},set:function(t){f.path=t}});var l=[];Object.defineProperty(this,"ctxStack",{get:function(){return l},set:function(t){l=t}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(t){var e;e=d(t),this.ctx.fillStyle=e.style,this.ctx.isFillTransparent=0===e.a,this.ctx.fillOpacity=e.a,this.pdf.setFillColor(e.r,e.g,e.b,{a:e.a}),this.pdf.setTextColor(e.r,e.g,e.b,{a:e.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(t){var e=d(t);this.ctx.strokeStyle=e.style,this.ctx.isStrokeTransparent=0===e.a,this.ctx.strokeOpacity=e.a,0===e.a?this.pdf.setDrawColor(255,255,255):(e.a,this.pdf.setDrawColor(e.r,e.g,e.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(t){-1!==["butt","round","square"].indexOf(t)&&(this.ctx.lineCap=t,this.pdf.setLineCap(t))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(t){isNaN(t)||(this.ctx.lineWidth=t,this.pdf.setLineWidth(t))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(t){-1!==["bevel","round","miter"].indexOf(t)&&(this.ctx.lineJoin=t,this.pdf.setLineJoin(t))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(t){isNaN(t)||(this.ctx.miterLimit=t,this.pdf.setMiterLimit(t))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(t){this.ctx.textBaseline=t}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(t){-1!==["right","end","center","left","start"].indexOf(t)&&(this.ctx.textAlign=t)}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(t){var e;if(this.ctx.font=t,null!==(e=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(t))){var n=e[1],r=(e[2],e[3]),i=e[4],a=(e[5],e[6]),o=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(i)[2];i="px"===o?Math.floor(parseFloat(i)):"em"===o?Math.floor(parseFloat(i)*this.pdf.getFontSize()):Math.floor(parseFloat(i)),this.pdf.setFontSize(i);var s="";("bold"===r||700<=parseInt(r,10)||"bold"===n)&&(s="bold"),"italic"===n&&(s+="italic"),0===s.length&&(s="normal");for(var u="",l=a.toLowerCase().replace(/"|'/g,"").split(/\s*,\s*/),c={arial:"Helvetica",verdana:"Helvetica",helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",courier:"Courier",times:"Times",cursive:"Times",fantasy:"Times",serif:"Times"},h=0;h<l.length;h++){if(void 0!==this.pdf.internal.getFont(l[h],s,{noFallback:!0,disableWarning:!0})){u=l[h];break}if("bolditalic"===s&&void 0!==this.pdf.internal.getFont(l[h],"bold",{noFallback:!0,disableWarning:!0}))u=l[h],s="bold";else if(void 0!==this.pdf.internal.getFont(l[h],"normal",{noFallback:!0,disableWarning:!0})){u=l[h],s="normal";break}}if(""===u)for(var f=0;f<l.length;f++)if(c[l[f]]){u=c[l[f]];break}u=""===u?"Times":u,this.pdf.setFont(u,s)}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(t){this.ctx.globalCompositeOperation=t}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(t){this.ctx.globalAlpha=t}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(t){this.ctx.ignoreClearRect=Boolean(t)}})};e.prototype.fill=function(){n.call(this,"fill",!1)},e.prototype.stroke=function(){n.call(this,"stroke",!1)},e.prototype.beginPath=function(){this.path=[{type:"begin"}]},e.prototype.moveTo=function(t,e){if(isNaN(t)||isNaN(e))throw console.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.moveTo");var n=this.ctx.transform.applyToPoint(new v(t,e));this.path.push({type:"mt",x:n.x,y:n.y}),this.ctx.lastPoint=new v(t,e)},e.prototype.closePath=function(){var t=new v(0,0),e=0;for(e=this.path.length-1;-1!==e;e--)if("begin"===this.path[e].type&&"object"===on(this.path[e+1])&&"number"==typeof this.path[e+1].x){t=new v(this.path[e+1].x,this.path[e+1].y),this.path.push({type:"lt",x:t.x,y:t.y});break}"object"===on(this.path[e+2])&&"number"==typeof this.path[e+2].x&&this.path.push(JSON.parse(JSON.stringify(this.path[e+2]))),this.path.push({type:"close"}),this.ctx.lastPoint=new v(t.x,t.y)},e.prototype.lineTo=function(t,e){if(isNaN(t)||isNaN(e))throw console.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.lineTo");var n=this.ctx.transform.applyToPoint(new v(t,e));this.path.push({type:"lt",x:n.x,y:n.y}),this.ctx.lastPoint=new v(n.x,n.y)},e.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),n.call(this,null,!0)},e.prototype.quadraticCurveTo=function(t,e,n,r){if(isNaN(n)||isNaN(r)||isNaN(t)||isNaN(e))throw console.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var i=this.ctx.transform.applyToPoint(new v(n,r)),a=this.ctx.transform.applyToPoint(new v(t,e));this.path.push({type:"qct",x1:a.x,y1:a.y,x:i.x,y:i.y}),this.ctx.lastPoint=new v(i.x,i.y)},e.prototype.bezierCurveTo=function(t,e,n,r,i,a){if(isNaN(i)||isNaN(a)||isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw console.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var o=this.ctx.transform.applyToPoint(new v(i,a)),s=this.ctx.transform.applyToPoint(new v(t,e)),u=this.ctx.transform.applyToPoint(new v(n,r));this.path.push({type:"bct",x1:s.x,y1:s.y,x2:u.x,y2:u.y,x:o.x,y:o.y}),this.ctx.lastPoint=new v(o.x,o.y)},e.prototype.arc=function(t,e,n,r,i,a){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||isNaN(i))throw console.error("jsPDF.context2d.arc: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.arc");if(a=Boolean(a),!this.ctx.transform.isIdentity){var o=this.ctx.transform.applyToPoint(new v(t,e));t=o.x,e=o.y;var s=this.ctx.transform.applyToPoint(new v(0,n)),u=this.ctx.transform.applyToPoint(new v(0,0));n=Math.sqrt(Math.pow(s.x-u.x,2)+Math.pow(s.y-u.y,2))}Math.abs(i-r)>=2*Math.PI&&(r=0,i=2*Math.PI),this.path.push({type:"arc",x:t,y:e,radius:n,startAngle:r,endAngle:i,counterclockwise:a})},e.prototype.arcTo=function(t,e,n,r,i){throw new Error("arcTo not implemented.")},e.prototype.rect=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw console.error("jsPDF.context2d.rect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(t,e),this.lineTo(t+n,e),this.lineTo(t+n,e+r),this.lineTo(t,e+r),this.lineTo(t,e),this.lineTo(t+n,e),this.lineTo(t,e)},e.prototype.fillRect=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw console.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!x.call(this)){var i={};"butt"!==this.lineCap&&(i.lineCap=this.lineCap,this.lineCap="butt"),"miter"!==this.lineJoin&&(i.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(t,e,n,r),this.fill(),i.hasOwnProperty("lineCap")&&(this.lineCap=i.lineCap),i.hasOwnProperty("lineJoin")&&(this.lineJoin=i.lineJoin)}},e.prototype.strokeRect=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw console.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeRect");N.call(this)||(this.beginPath(),this.rect(t,e,n,r),this.stroke())},e.prototype.clearRect=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw console.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(t,e,n,r))},e.prototype.save=function(t){t="boolean"!=typeof t||t;for(var e=this.pdf.internal.getCurrentPageInfo().pageNumber,n=0;n<this.pdf.internal.getNumberOfPages();n++)this.pdf.setPage(n+1),this.pdf.internal.out("q");if(this.pdf.setPage(e),t){this.ctx.fontSize=this.pdf.internal.getFontSize();var r=new c(this.ctx);this.ctxStack.push(this.ctx),this.ctx=r}},e.prototype.restore=function(t){t="boolean"!=typeof t||t;for(var e=this.pdf.internal.getCurrentPageInfo().pageNumber,n=0;n<this.pdf.internal.getNumberOfPages();n++)this.pdf.setPage(n+1),this.pdf.internal.out("Q");this.pdf.setPage(e),t&&0!==this.ctxStack.length&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin)},e.prototype.toDataURL=function(){throw new Error("toDataUrl not implemented.")};var d=function(t){var e,n,r,i;if(!0===t.isCanvasGradient&&(t=t.getColor()),!t)return{r:0,g:0,b:0,a:0,style:t};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(t))i=r=n=e=0;else{var a=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(t);if(null!==a)e=parseInt(a[1]),n=parseInt(a[2]),r=parseInt(a[3]),i=1;else if(null!==(a=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(t)))e=parseInt(a[1]),n=parseInt(a[2]),r=parseInt(a[3]),i=parseFloat(a[4]);else{if(i=1,"string"==typeof t&&"#"!==t.charAt(0)){var o=new RGBColor(t);t=o.ok?o.toHex():"#000000"}4===t.length?(e=t.substring(1,2),e+=e,n=t.substring(2,3),n+=n,r=t.substring(3,4),r+=r):(e=t.substring(1,3),n=t.substring(3,5),r=t.substring(5,7)),e=parseInt(e,16),n=parseInt(n,16),r=parseInt(r,16)}}return{r:e,g:n,b:r,a:i,style:t}},x=function(){return this.ctx.isFillTransparent||0==this.globalAlpha},N=function(){return Boolean(this.ctx.isStrokeTransparent||0==this.globalAlpha)};e.prototype.fillText=function(t,e,n,r){if(isNaN(e)||isNaN(n)||"string"!=typeof t)throw console.error("jsPDF.context2d.fillText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillText");if(r=isNaN(r)?void 0:r,!x.call(this)){n=o.call(this,n);var i=B(this.ctx.transform.rotation),a=this.ctx.transform.scaleX;s.call(this,{text:t,x:e,y:n,scale:a,angle:i,align:this.textAlign,maxWidth:r})}},e.prototype.strokeText=function(t,e,n,r){if(isNaN(e)||isNaN(n)||"string"!=typeof t)throw console.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!N.call(this)){r=isNaN(r)?void 0:r,n=o.call(this,n);var i=B(this.ctx.transform.rotation),a=this.ctx.transform.scaleX;s.call(this,{text:t,x:e,y:n,scale:a,renderingMode:"stroke",angle:i,align:this.textAlign,maxWidth:r})}},e.prototype.measureText=function(t){if("string"!=typeof t)throw console.error("jsPDF.context2d.measureText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.measureText");var e=this.pdf,n=this.pdf.internal.scaleFactor,r=e.internal.getFontSize(),i=e.getStringUnitWidth(t)*r/e.internal.scaleFactor;return new function(t){var e=(t=t||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return e}}),this}({width:i*=Math.round(96*n/72*1e4)/1e4})},e.prototype.scale=function(t,e){if(isNaN(t)||isNaN(e))throw console.error("jsPDF.context2d.scale: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.scale");var n=new P(t,0,0,e,0,0);this.ctx.transform=this.ctx.transform.multiply(n)},e.prototype.rotate=function(t){if(isNaN(t))throw console.error("jsPDF.context2d.rotate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rotate");var e=new P(Math.cos(t),Math.sin(t),-Math.sin(t),Math.cos(t),0,0);this.ctx.transform=this.ctx.transform.multiply(e)},e.prototype.translate=function(t,e){if(isNaN(t)||isNaN(e))throw console.error("jsPDF.context2d.translate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.translate");var n=new P(1,0,0,1,t,e);this.ctx.transform=this.ctx.transform.multiply(n)},e.prototype.transform=function(t,e,n,r,i,a){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||isNaN(i)||isNaN(a))throw console.error("jsPDF.context2d.transform: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.transform");var o=new P(t,e,n,r,i,a);this.ctx.transform=this.ctx.transform.multiply(o)},e.prototype.setTransform=function(t,e,n,r,i,a){t=isNaN(t)?1:t,e=isNaN(e)?0:e,n=isNaN(n)?0:n,r=isNaN(r)?1:r,i=isNaN(i)?0:i,a=isNaN(a)?0:a,this.ctx.transform=new P(t,e,n,r,i,a)},e.prototype.drawImage=function(t,e,n,r,i,a,o,s,u){var l=this.pdf.getImageProperties(t),c=1,h=1,f=1,d=1;void 0!==r&&void 0!==s&&(f=s/r,d=u/i,c=l.width/r*s/r,h=l.height/i*u/i),void 0===a&&(a=e,o=n,n=e=0),void 0!==r&&void 0===s&&(s=r,u=i),void 0===r&&void 0===s&&(s=l.width,u=l.height);for(var p,g=this.ctx.transform.decompose(),m=B(g.rotate.shx),b=new P,v=(b=(b=(b=b.multiply(g.translate)).multiply(g.skew)).multiply(g.scale)).applyToRectangle(new k(a-e*f,o-n*d,r*c,i*h)),y=F.call(this,v),w=[],x=0;x<y.length;x+=1)-1===w.indexOf(y[x])&&w.push(y[x]);if(w.sort(),this.autoPaging)for(var N=w[0],L=w[w.length-1],A=N;A<L+1;A++){if(this.pdf.setPage(A),0!==this.ctx.clip_path.length){var _=this.path;p=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=C(p,this.posX,-1*this.pdf.internal.pageSize.height*(A-1)+this.posY),I.call(this,"fill",!0),this.path=_}var S=JSON.parse(JSON.stringify(v));S=C([S],this.posX,-1*this.pdf.internal.pageSize.height*(A-1)+this.posY)[0],this.pdf.addImage(t,"JPEG",S.x,S.y,S.w,S.h,null,null,m)}else this.pdf.addImage(t,"JPEG",v.x,v.y,v.w,v.h,null,null,m)};var F=function(t,e,n){var r=[];switch(e=e||this.pdf.internal.pageSize.width,n=n||this.pdf.internal.pageSize.height,t.type){default:case"mt":case"lt":r.push(Math.floor((t.y+this.posY)/n)+1);break;case"arc":r.push(Math.floor((t.y+this.posY-t.radius)/n)+1),r.push(Math.floor((t.y+this.posY+t.radius)/n)+1);break;case"qct":var i=y(this.ctx.lastPoint.x,this.ctx.lastPoint.y,t.x1,t.y1,t.x,t.y);r.push(Math.floor(i.y/n)+1),r.push(Math.floor((i.y+i.h)/n)+1);break;case"bct":var a=O(this.ctx.lastPoint.x,this.ctx.lastPoint.y,t.x1,t.y1,t.x2,t.y2,t.x,t.y);r.push(Math.floor(a.y/n)+1),r.push(Math.floor((a.y+a.h)/n)+1);break;case"rect":r.push(Math.floor((t.y+this.posY)/n)+1),r.push(Math.floor((t.y+t.h+this.posY)/n)+1)}for(var o=0;o<r.length;o+=1)for(;this.pdf.internal.getNumberOfPages()<r[o];)w.call(this);return r},w=function(){var t=this.fillStyle,e=this.strokeStyle,n=this.font,r=this.lineCap,i=this.lineWidth,a=this.lineJoin;this.pdf.addPage(),this.fillStyle=t,this.strokeStyle=e,this.font=n,this.lineCap=r,this.lineWidth=i,this.lineJoin=a},C=function(t,e,n){for(var r=0;r<t.length;r++)switch(t[r].type){case"bct":t[r].x2+=e,t[r].y2+=n;case"qct":t[r].x1+=e,t[r].y1+=n;case"mt":case"lt":case"arc":default:t[r].x+=e,t[r].y+=n}return t},n=function(t,e){for(var n,r,i=this.fillStyle,a=this.strokeStyle,o=this.lineCap,s=this.lineWidth,u=this.lineJoin,l=JSON.parse(JSON.stringify(this.path)),c=JSON.parse(JSON.stringify(this.path)),h=[],f=0;f<c.length;f++)if(void 0!==c[f].x)for(var d=F.call(this,c[f]),p=0;p<d.length;p+=1)-1===h.indexOf(d[p])&&h.push(d[p]);for(var g=0;g<h.length;g++)for(;this.pdf.internal.getNumberOfPages()<h[g];)w.call(this);if(h.sort(),this.autoPaging)for(var m=h[0],b=h[h.length-1],v=m;v<b+1;v++){if(this.pdf.setPage(v),this.fillStyle=i,this.strokeStyle=a,this.lineCap=o,this.lineWidth=s,this.lineJoin=u,0!==this.ctx.clip_path.length){var y=this.path;n=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=C(n,this.posX,-1*this.pdf.internal.pageSize.height*(v-1)+this.posY),I.call(this,t,!0),this.path=y}r=JSON.parse(JSON.stringify(l)),this.path=C(r,this.posX,-1*this.pdf.internal.pageSize.height*(v-1)+this.posY),!1!==e&&0!==v||I.call(this,t,e)}else I.call(this,t,e);this.path=l},I=function(t,e){if(("stroke"!==t||e||!N.call(this))&&("stroke"===t||e||!x.call(this))){for(var n,r,i=[],a=this.path,o=0;o<a.length;o++){var s=a[o];switch(s.type){case"begin":i.push({begin:!0});break;case"close":i.push({close:!0});break;case"mt":i.push({start:s,deltas:[],abs:[]});break;case"lt":var u=i.length;if(!isNaN(a[o-1].x)&&(n=[s.x-a[o-1].x,s.y-a[o-1].y],0<u))for(;0<=u;u--)if(!0!==i[u-1].close&&!0!==i[u-1].begin){i[u-1].deltas.push(n),i[u-1].abs.push(s);break}break;case"bct":n=[s.x1-a[o-1].x,s.y1-a[o-1].y,s.x2-a[o-1].x,s.y2-a[o-1].y,s.x-a[o-1].x,s.y-a[o-1].y],i[i.length-1].deltas.push(n);break;case"qct":var l=a[o-1].x+2/3*(s.x1-a[o-1].x),c=a[o-1].y+2/3*(s.y1-a[o-1].y),h=s.x+2/3*(s.x1-s.x),f=s.y+2/3*(s.y1-s.y),d=s.x,p=s.y;n=[l-a[o-1].x,c-a[o-1].y,h-a[o-1].x,f-a[o-1].y,d-a[o-1].x,p-a[o-1].y],i[i.length-1].deltas.push(n);break;case"arc":i.push({deltas:[],abs:[],arc:!0}),Array.isArray(i[i.length-1].abs)&&i[i.length-1].abs.push(s)}}r=e?null:"stroke"===t?"stroke":"fill";for(var g=0;g<i.length;g++){if(i[g].arc){for(var m=i[g].abs,b=0;b<m.length;b++){var v=m[b];"arc"===v.type?L.call(this,v.x,v.y,v.radius,v.startAngle,v.endAngle,v.counterclockwise,void 0,e):S.call(this,v.x,v.y)}A.call(this,r),this.pdf.internal.out("h")}if(!i[g].arc&&!0!==i[g].close&&!0!==i[g].begin){var y=i[g].start.x,w=i[g].start.y;j.call(this,i[g].deltas,y,w)}}r&&A.call(this,r),e&&_.call(this)}},o=function(t){var e=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,n=e*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return t-n;case"top":return t+e-n;case"hanging":return t+e-2*n;case"middle":return t+e/2-n;case"ideographic":return t;case"alphabetic":default:return t}};e.prototype.createLinearGradient=function(){function t(){}return t.colorStops=[],t.addColorStop=function(t,e){this.colorStops.push([t,e])},t.getColor=function(){return 0===this.colorStops.length?"#000000":this.colorStops[0][1]},t.isCanvasGradient=!0,t},e.prototype.createPattern=function(){return this.createLinearGradient()},e.prototype.createRadialGradient=function(){return this.createLinearGradient()};var L=function(t,e,n,r,i,a,o,s){for(var u=m.call(this,n,r,i,a),l=0;l<u.length;l++){var c=u[l];0===l&&p.call(this,c.x1+t,c.y1+e),g.call(this,t,e,c.x2,c.y2,c.x3,c.y3,c.x4,c.y4)}s?_.call(this):A.call(this,o)},A=function(t){switch(t){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},_=function(){this.pdf.clip(),this.pdf.discardPath()},p=function(t,e){this.pdf.internal.out(i(t)+" "+a(e)+" m")},s=function(t){var e;switch(t.align){case"right":case"end":e="right";break;case"center":e="center";break;case"left":case"start":default:e="left"}var n=this.ctx.transform.applyToPoint(new v(t.x,t.y)),r=this.ctx.transform.decompose(),i=new P;i=(i=(i=i.multiply(r.translate)).multiply(r.skew)).multiply(r.scale);for(var a,o,s=this.pdf.getTextDimensions(t.text),u=this.ctx.transform.applyToRectangle(new k(t.x,t.y,s.w,s.h)),l=i.applyToRectangle(new k(t.x,t.y-s.h,s.w,s.h)),c=F.call(this,l),h=[],f=0;f<c.length;f+=1)-1===h.indexOf(c[f])&&h.push(c[f]);if(h.sort(),!0===this.autoPaging)for(var d=h[0],p=h[h.length-1],g=d;g<p+1;g++){if(this.pdf.setPage(g),0!==this.ctx.clip_path.length){var m=this.path;a=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=C(a,this.posX,-1*this.pdf.internal.pageSize.height*(g-1)+this.posY),I.call(this,"fill",!0),this.path=m}var b=JSON.parse(JSON.stringify(u));b=C([b],this.posX,-1*this.pdf.internal.pageSize.height*(g-1)+this.posY)[0],.01<=t.scale&&(o=this.pdf.internal.getFontSize(),this.pdf.setFontSize(o*t.scale)),this.pdf.text(t.text,b.x,b.y,{angle:t.angle,align:e,renderingMode:t.renderingMode,maxWidth:t.maxWidth}),.01<=t.scale&&this.pdf.setFontSize(o)}else.01<=t.scale&&(o=this.pdf.internal.getFontSize(),this.pdf.setFontSize(o*t.scale)),this.pdf.text(t.text,n.x+this.posX,n.y+this.posY,{angle:t.angle,align:e,renderingMode:t.renderingMode,maxWidth:t.maxWidth}),.01<=t.scale&&this.pdf.setFontSize(o)},S=function(t,e,n,r){n=n||0,r=r||0,this.pdf.internal.out(i(t+n)+" "+a(e+r)+" l")},j=function(t,e,n){return this.pdf.lines(t,e,n,null,null)},g=function(t,e,n,r,i,a,o,s){this.pdf.internal.out([u(l(n+t)),u(h(r+e)),u(l(i+t)),u(h(a+e)),u(l(o+t)),u(h(s+e)),"c"].join(" "))},m=function(t,e,n,r){for(var i=2*Math.PI,a=Math.PI/2;n<e;)e-=i;var o=Math.abs(n-e);o<i&&r&&(o=i-o);for(var s=[],u=r?-1:1,l=e;1e-5<o;){var c=l+u*Math.min(o,a);s.push(b.call(this,t,l,c)),o-=Math.abs(c-l),l=c}return s},b=function(t,e,n){var r=(n-e)/2,i=t*Math.cos(r),a=t*Math.sin(r),o=i,s=-a,u=o*o+s*s,l=u+o*i+s*a,c=4/3*(Math.sqrt(2*u*l)-l)/(o*a-s*i),h=o-c*s,f=s+c*o,d=h,p=-f,g=r+e,m=Math.cos(g),b=Math.sin(g);return{x1:t*Math.cos(e),y1:t*Math.sin(e),x2:h*m-f*b,y2:h*b+f*m,x3:d*m-p*b,y3:d*b+p*m,x4:t*Math.cos(n),y4:t*Math.sin(n)}},B=function(t){return 180*t/Math.PI},y=function(t,e,n,r,i,a){var o=t+.5*(n-t),s=e+.5*(r-e),u=i+.5*(n-i),l=a+.5*(r-a),c=Math.min(t,i,o,u),h=Math.max(t,i,o,u),f=Math.min(e,a,s,l),d=Math.max(e,a,s,l);return new k(c,f,h-c,d-f)},O=function(t,e,n,r,i,a,o,s){var u,l,c,h,f,d,p,g,m,b,v,y,w,x,N=n-t,L=r-e,A=i-n,_=a-r,S=o-i,P=s-a;for(l=0;l<41;l++)m=(p=(c=t+(u=l/40)*N)+u*((f=n+u*A)-c))+u*(f+u*(i+u*S-f)-p),b=(g=(h=e+u*L)+u*((d=r+u*_)-h))+u*(d+u*(a+u*P-d)-g),x=0==l?(w=v=m,y=b):(v=Math.min(v,m),y=Math.min(y,b),w=Math.max(w,m),Math.max(x,b));return new k(Math.round(v),Math.round(y),Math.round(w-v),Math.round(x-y))}}(U.API),(
/**
   * jsPDF filters PlugIn
   * Copyright (c) 2014 Aras Abbasi
   *
   * Licensed under the MIT License.
   * http://opensource.org/licenses/mit-license
   */
a=U.API).processDataByFilters=function(t,e){var n=0,r=t||"",i=[];for("string"==typeof(e=e||[])&&(e=[e]),n=0;n<e.length;n+=1)switch(e[n]){case"ASCII85Decode":case"/ASCII85Decode":r=V(r),i.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":r=W(r),i.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":r=G(r),i.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":r=r.split("").map(function(t){return("0"+t.charCodeAt().toString(16)).slice(-2)}).join("")+">",i.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":r=Y(r),i.push("/FlateDecode");break;default:throw new Error('The filter: "'+e[n]+'" is not implemented')}return{data:r,reverseChain:i.reverse().join(" ")}},(
/**
   * jsPDF fileloading PlugIn
   * Copyright (c) 2018 Aras Abbasi (<EMAIL>)
   *
   * Licensed under the MIT License.
   * http://opensource.org/licenses/mit-license
   */
n=U.API).loadFile=function(t,e,n){e=!1!==e,n="function"==typeof n?n:function(){};var r=void 0;try{r=function(t,e,n){function r(t){var e=t.length,n=[],r=String.fromCharCode;for(a=0;a<e;a+=1)n.push(r(255&t.charCodeAt(a)));return n.join("")}var i=new XMLHttpRequest,a=0;if(i.open("GET",t,!e),i.overrideMimeType("text/plain; charset=x-user-defined"),!1===e&&(i.onload=function(){200===i.status?n(r(this.responseText)):n(void 0)}),i.send(null),e&&200===i.status)return r(i.responseText)}(t,e,n)}catch(t){}return r},n.loadImageFile=n.loadFile,
/**
   * Copyright (c) 2018 Erik Koopmans
   * Released under the MIT License.
   *
   * Licensed under the MIT License.
   * http://opensource.org/licenses/mit-license
   */
function(t,e){if("undefined"!=typeof Promise){var n=function(t){var e=on(t);return"undefined"===e?"undefined":"string"===e||t instanceof String?"string":"number"===e||t instanceof Number?"number":"function"===e||t instanceof Function?"function":t&&t.constructor===Array?"array":t&&1===t.nodeType?"element":"object"===e?"object":"unknown"},r=function(t,e){var n=document.createElement(t);if(e.className&&(n.className=e.className),e.innerHTML){n.innerHTML=e.innerHTML;for(var r=n.getElementsByTagName("script"),i=r.length;0<i--;)r[i].parentNode.removeChild(r[i])}for(var a in e.style)n.style[a]=e.style[a];return n},o=function t(e){var n=Object.assign(t.convert(Promise.resolve()),JSON.parse(JSON.stringify(t.template))),r=t.convert(Promise.resolve(),n);return r=(r=r.setProgress(1,t,1,[t])).set(e)};((o.prototype=Object.create(Promise.prototype)).constructor=o).convert=function(t,e){return t.__proto__=e||o.prototype,t},o.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{}}},o.prototype.from=function(t,e){return this.then(function(){switch(e=e||function(t){switch(n(t)){case"string":return"string";case"element":return"canvas"===t.nodeName.toLowerCase?"canvas":"element";default:return"unknown"}}(t)){case"string":return this.set({src:r("div",{innerHTML:t})});case"element":return this.set({src:t});case"canvas":return this.set({canvas:t});case"img":return this.set({img:t});default:return this.error("Unknown source type.")}})},o.prototype.to=function(t){switch(t){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},o.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var t={position:"relative",display:"inline-block",width:Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:"white"},e=function t(e,n){for(var r=3===e.nodeType?document.createTextNode(e.nodeValue):e.cloneNode(!1),i=e.firstChild;i;i=i.nextSibling)!0!==n&&1===i.nodeType&&"SCRIPT"===i.nodeName||r.appendChild(t(i,n));return 1===e.nodeType&&("CANVAS"===e.nodeName?(r.width=e.width,r.height=e.height,r.getContext("2d").drawImage(e,0,0)):"TEXTAREA"!==e.nodeName&&"SELECT"!==e.nodeName||(r.value=e.value),r.addEventListener("load",function(){r.scrollTop=e.scrollTop,r.scrollLeft=e.scrollLeft},!0)),r}(this.prop.src,this.opt.html2canvas.javascriptEnabled);"BODY"===e.tagName&&(t.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=r("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=r("div",{className:"html2pdf__container",style:t}),this.prop.container.appendChild(e),this.prop.container.firstChild.appendChild(r("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"})},o.prototype.toCanvas=function(){var t=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(t).then(function(){var t=Object.assign({},this.opt.html2canvas);if(delete t.onrendered,this.isHtml2CanvasLoaded())return html2canvas(this.prop.container,t)}).then(function(t){(this.opt.html2canvas.onrendered||function(){})(t),this.prop.canvas=t,document.body.removeChild(this.prop.overlay)})},o.prototype.toContext2d=function(){var t=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(t).then(function(){var t=this.opt.jsPDF,e=Object.assign({async:!0,allowTaint:!0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete e.onrendered,t.context2d.autoPaging=!0,t.context2d.posX=this.opt.x,t.context2d.posY=this.opt.y,e.windowHeight=e.windowHeight||0,e.windowHeight=0==e.windowHeight?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):e.windowHeight,this.isHtml2CanvasLoaded())return html2canvas(this.prop.container,e)}).then(function(t){(this.opt.html2canvas.onrendered||function(){})(t),this.prop.canvas=t,document.body.removeChild(this.prop.overlay)})},o.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var t=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=t})},o.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then(function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF})},o.prototype.output=function(t,e,n){return"img"===(n=n||"pdf").toLowerCase()||"image"===n.toLowerCase()?this.outputImg(t,e):this.outputPdf(t,e)},o.prototype.outputPdf=function(t,e){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(t,e)})},o.prototype.outputImg=function(t){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(t){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+t+'" is not supported.'}})},o.prototype.isHtml2CanvasLoaded=function(){var t=void 0!==e.html2canvas;if(!t)throw new Error("html2canvas not loaded.");return t},o.prototype.save=function(t){if(this.isHtml2CanvasLoaded())return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(t?{filename:t}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},o.prototype.doCallback=function(){if(this.isHtml2CanvasLoaded())return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){this.prop.callback(this.prop.pdf)})},o.prototype.set=function(e){if("object"!==n(e))return this;var t=Object.keys(e||{}).map(function(t){if(t in o.template.prop)return function(){this.prop[t]=e[t]};switch(t){case"margin":return this.setMargin.bind(this,e.margin);case"jsPDF":return function(){return this.opt.jsPDF=e.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,e.pageSize);default:return function(){this.opt[t]=e[t]}}},this);return this.then(function(){return this.thenList(t)})},o.prototype.get=function(e,n){return this.then(function(){var t=e in o.template.prop?this.prop[e]:this.opt[e];return n?n(t):t})},o.prototype.setMargin=function(t){return this.then(function(){switch(n(t)){case"number":t=[t,t,t,t];case"array":if(2===t.length&&(t=[t[0],t[1],t[0],t[1]]),4===t.length)break;default:return this.error("Invalid margin array.")}this.opt.margin=t}).then(this.setPageSize)},o.prototype.setPageSize=function(t){function e(t,e){return Math.floor(t*e/72*96)}return this.then(function(){(t=t||U.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(t.inner={width:t.width-this.opt.margin[1]-this.opt.margin[3],height:t.height-this.opt.margin[0]-this.opt.margin[2]},t.inner.px={width:e(t.inner.width,t.k),height:e(t.inner.height,t.k)},t.inner.ratio=t.inner.height/t.inner.width),this.prop.pageSize=t})},o.prototype.setProgress=function(t,e,n,r){return null!=t&&(this.progress.val=t),null!=e&&(this.progress.state=e),null!=n&&(this.progress.n=n),null!=r&&(this.progress.stack=r),this.progress.ratio=this.progress.val/this.progress.state,this},o.prototype.updateProgress=function(t,e,n,r){return this.setProgress(t?this.progress.val+t:null,e||null,n?this.progress.n+n:null,r?this.progress.stack.concat(r):null)},o.prototype.then=function(t,e){var n=this;return this.thenCore(t,e,function(e,t){return n.updateProgress(null,null,1,[e]),Promise.prototype.then.call(this,function(t){return n.updateProgress(null,e),t}).then(e,t).then(function(t){return n.updateProgress(1),t})})},o.prototype.thenCore=function(t,e,n){n=n||Promise.prototype.then;var r=this;t&&(t=t.bind(r)),e&&(e=e.bind(r));var i=-1!==Promise.toString().indexOf("[native code]")&&"Promise"===Promise.name?r:o.convert(Object.assign({},r),Promise.prototype),a=n.call(i,t,e);return o.convert(a,r.__proto__)},o.prototype.thenExternal=function(t,e){return Promise.prototype.then.call(this,t,e)},o.prototype.thenList=function(t){var e=this;return t.forEach(function(t){e=e.thenCore(t)}),e},o.prototype.catch=function(t){t&&(t=t.bind(this));var e=Promise.prototype.catch.call(this,t);return o.convert(e,this)},o.prototype.catchExternal=function(t){return Promise.prototype.catch.call(this,t)},o.prototype.error=function(t){return this.then(function(){throw new Error(t)})},o.prototype.using=o.prototype.set,o.prototype.saveAs=o.prototype.save,o.prototype.export=o.prototype.output,o.prototype.run=o.prototype.then,U.getPageSize=function(t,e,n){if("object"===on(t)){var r=t;t=r.orientation,e=r.unit||e,n=r.format||n}e=e||"mm",n=n||"a4",t=(""+(t||"P")).toLowerCase();var i,a=(""+n).toLowerCase(),o={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(e){case"pt":i=1;break;case"mm":i=72/25.4;break;case"cm":i=72/2.54;break;case"in":i=72;break;case"px":i=.75;break;case"pc":case"em":i=12;break;case"ex":i=6;break;default:throw"Invalid unit: "+e}var s,u=0,l=0;if(o.hasOwnProperty(a))u=o[a][1]/i,l=o[a][0]/i;else try{u=n[1],l=n[0]}catch(t){throw new Error("Invalid format: "+n)}if("p"===t||"portrait"===t)t="p",u<l&&(s=l,l=u,u=s);else{if("l"!==t&&"landscape"!==t)throw"Invalid orientation: "+t;t="l",l<u&&(s=l,l=u,u=s)}return{width:l,height:u,unit:e,k:i,orientation:t}},t.html=function(t,e){(e=e||{}).callback=e.callback||function(){},e.html2canvas=e.html2canvas||{},e.html2canvas.canvas=e.html2canvas.canvas||this.canvas,e.jsPDF=e.jsPDF||this;var n=new o(e);return e.worker?n:n.from(t).doCallback()}}else console.warn("Promise not found. html-Plugin will not work")}(U.API,"undefined"!=typeof window&&window||"undefined"!=typeof global&&global),U.API.addJS=function(t){return o=t,this.internal.events.subscribe("postPutResources",function(){r=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(r+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),i=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+o+")"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){void 0!==r&&void 0!==i&&this.internal.out("/Names <</JavaScript "+r+" 0 R>>")}),this},(
/**
   * @license
   * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>
   *
   * Licensed under the MIT License.
   * http://opensource.org/licenses/mit-license
   */
s=U.API).events.push(["postPutResources",function(){var t=this,e=/^(\d+) 0 obj$/;if(0<this.outline.root.children.length)for(var n=t.outline.render().split(/\r\n/),r=0;r<n.length;r++){var i=n[r],a=e.exec(i);if(null!=a){var o=a[1];t.internal.newObjectDeferredBegin(o,!1)}t.internal.write(i)}if(this.outline.createNamedDestinations){var s=this.internal.pages.length,u=[];for(r=0;r<s;r++){var l=t.internal.newObject();u.push(l);var c=t.internal.getPageInfo(r+1);t.internal.write("<< /D["+c.objId+" 0 R /XYZ null null null]>> endobj")}var h=t.internal.newObject();for(t.internal.write("<< /Names [ "),r=0;r<u.length;r++)t.internal.write("(page_"+(r+1)+")"+u[r]+" 0 R");t.internal.write(" ] >>","endobj"),f=t.internal.newObject(),t.internal.write("<< /Dests "+h+" 0 R"),t.internal.write(">>","endobj")}}]),s.events.push(["putCatalog",function(){0<this.outline.root.children.length&&(this.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&this.internal.write("/Names "+f+" 0 R"))}]),s.events.push(["initialized",function(){var s=this;s.outline={createNamedDestinations:!1,root:{children:[]}},s.outline.add=function(t,e,n){var r={title:e,options:n,children:[]};return null==t&&(t=this.root),t.children.push(r),r},s.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=s,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},s.outline.genIds_r=function(t){t.id=s.internal.newObjectDeferred();for(var e=0;e<t.children.length;e++)this.genIds_r(t.children[e])},s.outline.renderRoot=function(t){this.objStart(t),this.line("/Type /Outlines"),0<t.children.length&&(this.line("/First "+this.makeRef(t.children[0])),this.line("/Last "+this.makeRef(t.children[t.children.length-1]))),this.line("/Count "+this.count_r({count:0},t)),this.objEnd()},s.outline.renderItems=function(t){for(var e=this.ctx.pdf.internal.getVerticalCoordinateString,n=0;n<t.children.length;n++){var r=t.children[n];this.objStart(r),this.line("/Title "+this.makeString(r.title)),this.line("/Parent "+this.makeRef(t)),0<n&&this.line("/Prev "+this.makeRef(t.children[n-1])),n<t.children.length-1&&this.line("/Next "+this.makeRef(t.children[n+1])),0<r.children.length&&(this.line("/First "+this.makeRef(r.children[0])),this.line("/Last "+this.makeRef(r.children[r.children.length-1])));var i=this.count=this.count_r({count:0},r);if(0<i&&this.line("/Count "+i),r.options&&r.options.pageNumber){var a=s.internal.getPageInfo(r.options.pageNumber);this.line("/Dest ["+a.objId+" 0 R /XYZ 0 "+e(0)+" 0]")}this.objEnd()}for(var o=0;o<t.children.length;o++)this.renderItems(t.children[o])},s.outline.line=function(t){this.ctx.val+=t+"\r\n"},s.outline.makeRef=function(t){return t.id+" 0 R"},s.outline.makeString=function(t){return"("+s.internal.pdfEscape(t)+")"},s.outline.objStart=function(t){this.ctx.val+="\r\n"+t.id+" 0 obj\r\n<<\r\n"},s.outline.objEnd=function(){this.ctx.val+=">> \r\nendobj\r\n"},s.outline.count_r=function(t,e){for(var n=0;n<e.children.length;n++)t.count++,this.count_r(t,e.children[n]);return t.count}}]),
/**
   * @license
   *
   * Licensed under the MIT License.
   * http://opensource.org/licenses/mit-license
   */
u=U.API,l=[192,193,194,195,196,197,198,199],u.processJPEG=function(t,e,n,r,i,a){var o,s=this.decode.DCT_DECODE,u=null;if("string"==typeof t||this.__addimage__.isArrayBuffer(t)||this.__addimage__.isArrayBufferView(t)){switch(t=i||t,t=this.__addimage__.isArrayBuffer(t)?new Uint8Array(t):t,(o=function(t){for(var e,n=256*t.charCodeAt(4)+t.charCodeAt(5),r=t.length,i={width:0,height:0,numcomponents:1},a=4;a<r;a+=2){if(a+=n,-1!==l.indexOf(t.charCodeAt(a+1))){e=256*t.charCodeAt(a+5)+t.charCodeAt(a+6),i={width:256*t.charCodeAt(a+7)+t.charCodeAt(a+8),height:e,numcomponents:t.charCodeAt(a+9)};break}n=256*t.charCodeAt(a+2)+t.charCodeAt(a+3)}return i}(t=this.__addimage__.isArrayBufferView(t)?this.__addimage__.arrayBufferToBinaryString(t):t)).numcomponents){case 1:a=this.color_spaces.DEVICE_GRAY;break;case 4:a=this.color_spaces.DEVICE_CMYK;break;case 3:a=this.color_spaces.DEVICE_RGB}u={data:t,width:o.width,height:o.height,colorSpace:a,bitsPerComponent:8,filter:s,index:e,alias:n}}return u},
/**
   * @license
   *
   * Copyright (c) 2014 James Robb, https://github.com/jamesbrobb
   *
   * 
   * ====================================================================
   */
P=U.API,k="undefined"!=typeof self&&self||"undefined"!=typeof window&&window||"undefined"!=typeof global&&global||Function('return typeof this === "object" && this.content')()||Function("return this")(),F=function(){return"function"==typeof Deflater},d=function(t){var e=30720;return e|=Math.min(3,(t-1&255)>>1)<<6,e|=0,[120,255&(e+=31-e%31)]},p=function(t,e,n,r){for(var i,a,o,s=t.length/e,u=new Uint8Array(t.length+s),l=y(),c=0;c<s;c+=1){if(o=c*e,i=t.subarray(o,o+e),r)u.set(r(i,n,a),o+c);else{for(var h,f=l.length,d=[];h<f;h+=1)d[h]=l[h](i,n,a);var p=w(d.concat());u.set(d[p],o+c)}a=i}return u},g=function(t,e){var n,r=[],i=t.length;r[0]=1;for(var a=0;a<i;a+=1)n=t[a-e]||0,r[a+1]=t[a]-n+256&255;return r},m=function(t,e,n){var r,i=[],a=t.length;i[0]=2;for(var o=0;o<a;o+=1)r=n&&n[o]||0,i[o+1]=t[o]-r+256&255;return i},b=function(t,e,n){var r,i,a=[],o=t.length;a[0]=3;for(var s=0;s<o;s+=1)r=t[s-e]||0,i=n&&n[s]||0,a[s+1]=t[s]+256-(r+i>>>1)&255;return a},v=function(t,e,n){var r,i,a,o,s=[],u=t.length;s[0]=4;for(var l=0;l<u;l+=1)r=t[l-e]||0,i=n&&n[l]||0,a=n&&n[l-e]||0,o=c(r,i,a),s[l+1]=t[l]-o+256&255;return s},c=function(t,e,n){if(t===e&&e===n)return t;var r=Math.abs(e-n),i=Math.abs(t-n),a=Math.abs(t+e-n-n);return r<=i&&r<=a?t:i<=a?e:n},y=function(){return[X,g,m,b,v]},w=function(t){var e=t.map(function(t){return t.reduce(function(t,e){return t+Math.abs(e)},0)});return e.indexOf(Math.min.apply(null,e))},P.processPNG=function(t,e,n,r){var i,a,o,s,u,l,c,h,f,d,p,g,m,b,v,y=this.decode.FLATE_DECODE,w="";if(this.__addimage__.isArrayBuffer(t)&&(t=new Uint8Array(t)),this.__addimage__.isArrayBufferView(t)){if("function"!=typeof k.PNG||"function"!=typeof k.FlateStream)throw new Error("PNG support requires png.js and zlib.js");if(t=(o=new PNG(t)).imgData,a=o.bits,i=o.colorSpace,u=o.colors,-1!==[4,6].indexOf(o.colorType)){if(8===o.bits){f=(h=32==o.pixelBitlength?new Uint32Array(o.decodePixels().buffer):16==o.pixelBitlength?new Uint16Array(o.decodePixels().buffer):new Uint8Array(o.decodePixels().buffer)).length,p=new Uint8Array(f*o.colors),d=new Uint8Array(f);var x,N=o.pixelBitlength-o.bits;for(v=b=0;b<f;b++){for(m=h[b],x=0;x<N;)p[v++]=m>>>x&255,x+=o.bits;d[b]=m>>>x&255}}if(16===o.bits){f=(h=new Uint32Array(o.decodePixels().buffer)).length,p=new Uint8Array(f*(32/o.pixelBitlength)*o.colors),d=new Uint8Array(f*(32/o.pixelBitlength)),g=1<o.colors;for(var L=v=b=0;b<f;)m=h[b++],p[v++]=m>>>0&255,g&&(p[v++]=m>>>16&255,m=h[b++],p[v++]=m>>>0&255),d[L++]=m>>>16&255;a=8}!function(t){return t!==P.image_compression.NONE&&F()}(r)?(t=p,c=d,y=void 0):(t=J(p,o.width*o.colors,o.colors,r),c=J(d,o.width,1,r))}if(3===o.colorType&&(i=this.color_spaces.INDEXED,l=o.palette,o.transparency.indexed)){var A=o.transparency.indexed,_=0;for(b=0,f=A.length;b<f;++b)_+=A[b];if((_/=255)==f-1&&-1!==A.indexOf(0))s=[A.indexOf(0)];else if(_!==f){for(h=o.decodePixels(),d=new Uint8Array(h.length),b=0,f=h.length;b<f;b++)d[b]=A[h[b]];c=J(d,o.width,1)}}var S=function(t){var e;switch(t){case P.image_compression.FAST:e=11;break;case P.image_compression.MEDIUM:e=13;break;case P.image_compression.SLOW:e=14;break;default:e=12}return e}(r);return y===this.decode.FLATE_DECODE&&(w="/Predictor "+S+" "),w+="/Colors "+u+" /BitsPerComponent "+a+" /Columns "+o.width,(this.__addimage__.isArrayBuffer(t)||this.__addimage__.isArrayBufferView(t))&&(t=this.__addimage__.arrayBufferToBinaryString(t)),(c&&this.__addimage__.isArrayBuffer(c)||this.__addimage__.isArrayBufferView(c))&&(c=this.__addimage__.arrayBufferToBinaryString(c)),{alias:n,data:t,index:e,filter:y,decodeParameters:w,transparency:s,palette:l,sMask:c,predictor:S,width:o.width,height:o.height,bitsPerComponent:a,colorSpace:i}}},(
/**
   * @license
   * Copyright (c) 2017 Aras Abbasi
   *
   * Licensed under the MIT License.
   * http://opensource.org/licenses/mit-license
   */
h=U.API).processGIF89A=function(t,e,n,r){var i=new nt(t),a=i.width,o=i.height,s=[];i.decodeAndBlitFrameRGBA(0,s);var u={data:s,width:a,height:o},l=new it(100).encode(u,100);return h.processJPEG.call(this,l,e,n,r)},h.processGIF87A=h.processGIF89A,(
/**
   * Copyright (c) 2018 Aras Abbasi
   *
   * Licensed under the MIT License.
   * http://opensource.org/licenses/mit-license
   */
x=U.API).processBMP=function(t,e,n,r){var i=new at(t,!1),a=i.width,o=i.height,s={data:i.getData(),width:a,height:o},u=new it(100).encode(s,100);return x.processJPEG.call(this,u,e,n,r)},(
/**
   * @license
   * Copyright (c) 2019 Aras Abbasi
   *
   * Licensed under the MIT License.
   * http://opensource.org/licenses/mit-license
   */
N=U.API).processWEBP=function(t,e,n,r){var i=new ot(t,!1),a=i.width,o=i.height,s={data:i.getData(),width:a,height:o},u=new it(100).encode(s,100);return N.processJPEG.call(this,u,e,n,r)},U.API.setLanguage=function(t){return void 0===this.internal.languageSettings&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),void 0!=={af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"FYRO Macedonian",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"}[t]&&(this.internal.languageSettings.languageCode=t,!1===this.internal.languageSettings.isSubscribed&&(this.internal.events.subscribe("putCatalog",function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")}),this.internal.languageSettings.isSubscribed=!0)),this},
/** @license
   * MIT license.
   * Copyright (c) 2012 Willow Systems Corporation, willow-systems.com
   *               2014 Diego Casorran, https://github.com/diegocr
   *
   * 
   * ====================================================================
   */
L=U.API,A=L.getCharWidthsArray=function(t,e){var n,r,i=(e=e||{}).font||this.internal.getFont(),a=e.fontSize||this.internal.getFontSize(),o=e.charSpace||this.internal.getCharSpace(),s=e.widths?e.widths:i.metadata.Unicode.widths,u=s.fof?s.fof:1,l=e.kerning?e.kerning:i.metadata.Unicode.kerning,c=l.fof?l.fof:1,h=!1!==e.doKerning,f=0,d=t.length,p=0,g=s[0]||u,m=[];for(n=0;n<d;n++)r=t.charCodeAt(n),"function"==typeof i.metadata.widthOfString?m.push((i.metadata.widthOfGlyph(i.metadata.characterToGlyph(r))+o*(1e3/a)||0)/1e3):(h&&"object"===on(l[r])&&!isNaN(parseInt(l[r][p],10))&&(f=l[r][p]/c),m.push((s[r]||g)/u+f)),p=r;return m},_=L.getStringUnitWidth=function(t,e){var n=(e=e||{}).fontSize||this.internal.getFontSize(),r=e.font||this.internal.getFont(),i=e.charSpace||this.internal.getCharSpace();return L.processArabic&&(t=L.processArabic(t)),"function"==typeof r.metadata.widthOfString?r.metadata.widthOfString(t,n,i)/n:A.apply(this,arguments).reduce(function(t,e){return t+e},0)},L.splitTextToSize=function(t,e,n){var r,i=(n=n||{}).fontSize||this.internal.getFontSize(),a=function(t){if(t.widths&&t.kerning)return{widths:t.widths,kerning:t.kerning};var e=this.internal.getFont(t.fontName,t.fontStyle),n="Unicode";return e.metadata[n]?{widths:e.metadata[n].widths||{0:1},kerning:e.metadata[n].kerning||{}}:{font:e.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}.call(this,n);r=Array.isArray(t)?t:t.split(/\r?\n/);var o=1*this.internal.scaleFactor*e/i;a.textIndent=n.textIndent?1*n.textIndent*this.internal.scaleFactor/i:0,a.lineIndent=n.lineIndent;var s,u,l=[];for(s=0,u=r.length;s<u;s++)l=l.concat(Z.apply(this,[r[s],o,a]));return l},
/** @license
   jsPDF standard_fonts_metrics plugin
   * Copyright (c) 2012 Willow Systems Corporation, willow-systems.com
   * MIT license.
   * 
   * ====================================================================
   */
/**
   * This file adds the standard font metrics to jsPDF.
   *
   * Font metrics data is reprocessed derivative of contents of
   * "Font Metrics for PDF Core 14 Fonts" package, which exhibits the following copyright and license:
   *
   * Copyright (c) 1989, 1990, 1991, 1992, 1993, 1997 Adobe Systems Incorporated. All Rights Reserved.
   *
   * This file and the 14 PostScript(R) AFM files it accompanies may be used,
   * copied, and distributed for any purpose and without charge, with or without
   * modification, provided that all copyright notices are retained; that the AFM
   * files are not distributed without this file; that all modifications to this
   * file or any of the AFM files are prominently noted in the modified file(s);
   * and that this paragraph is not modified. Adobe Systems has no responsibility
   * or obligation to support the use of the AFM files.
   *
   * @name standard_fonts_metrics
   * @module
   */
function(t){t.__fontmetrics__=t.__fontmetrics__||{};for(var e="0123456789abcdef",n="klmnopqrstuvwxyz",d={},o={},r=0;r<n.length;r++)d[n[r]]=e[r],o[e[r]]=n[r];function s(t){return"0x"+parseInt(t,10).toString(16)}var u=t.__fontmetrics__.compress=function(t){var e,n,r,i=["{"];for(var a in t){if(e=t[a],n=isNaN(parseInt(a,10))?"'"+a+"'":(a=parseInt(a,10),(n=s(a).slice(2)).slice(0,-1)+o[n.slice(-1)]),"number"==typeof e)r=(e<0?(r=s(e).slice(3),"-"):(r=s(e).slice(2),""))+r.slice(0,-1)+o[r.slice(-1)];else{if("object"!==on(e))throw new Error("Don't know what to do with value type "+on(e)+".");r=u(e)}i.push(n+r)}return i.push("}"),i.join("")},i=t.__fontmetrics__.uncompress=function(t){if("string"!=typeof t)throw new Error("Invalid argument passed to uncompress.");for(var e,n,r,i,a={},o=1,s=a,u=[],l="",c="",h=t.length-1,f=1;f<h;f+=1)"'"==(i=t[f])?e=e?void(r=e.join("")):[]:e?e.push(i):"{"==i?(u.push([s,r]),s={},r=void 0):"}"==i?((n=u.pop())[0][n[1]]=s,r=void 0,s=n[0]):"-"==i?o=-1:void 0===r?d.hasOwnProperty(i)?(l+=d[i],r=parseInt(l,16)*o,o=1,l=""):l+=i:d.hasOwnProperty(i)?(c+=d[i],s[r]=parseInt(c,16)*o,o=1,r=void 0,c=""):c+=i;return a},a={codePages:["WinAnsiEncoding"],WinAnsiEncoding:i("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},l={Unicode:{Courier:a,"Courier-Bold":a,"Courier-BoldOblique":a,"Courier-Oblique":a,Helvetica:a,"Helvetica-Bold":a,"Helvetica-BoldOblique":a,"Helvetica-Oblique":a,"Times-Roman":a,"Times-Bold":a,"Times-BoldItalic":a,"Times-Italic":a}},c={Unicode:{"Courier-Oblique":i("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":i("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":i("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:i("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":i("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":i("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:i("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:i("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":i("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:i("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":i("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":i("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":i("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":i("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};t.events.push(["addFont",function(t){var e=t.font,n=c.Unicode[e.postScriptName];n&&(e.metadata.Unicode={},e.metadata.Unicode.widths=n.widths,e.metadata.Unicode.kerning=n.kerning);var r=l.Unicode[e.postScriptName];r&&(e.metadata.Unicode.encoding=r,e.encoding=r.codePages[0])}])}(U.API),(
/**
   * @license
   * Licensed under the MIT License.
   * http://opensource.org/licenses/mit-license
   */
S=U).API.events.push(["addFont",function(t){var e=void 0,n=t.font,r=t.instance;if(void 0!==r){if("string"!=typeof(e=!1===r.existsFileInVFS(n.postScriptName)?r.loadFile(n.postScriptName):r.getFileFromVFS(n.postScriptName)))throw new Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+n.postScriptName+"').");!function(t,e){e=/^\x00\x01\x00\x00/.test(e)?$(e):$(atob(e)),t.metadata=S.API.TTFFont.open(e),t.metadata.Unicode=t.metadata.Unicode||{encoding:{},kerning:{},widths:[]},t.metadata.glyIdsUsed=[0]}(n,e)}else if(!1===n.isStandardFont)throw new Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+n.postScriptName+"').")}]),(
/** @license
   * Copyright (c) 2012 Willow Systems Corporation, willow-systems.com
   *
   * 
   * ====================================================================
   */
C=U.API).addSvg=function(t,e,n,r,i){if(void 0===e||void 0===n)throw new Error("addSVG needs values for 'x' and 'y'");function a(t){for(var e=parseFloat(t[1]),n=parseFloat(t[2]),r=[],i=3,a=t.length;i<a;)"c"===t[i]?(r.push([parseFloat(t[i+1]),parseFloat(t[i+2]),parseFloat(t[i+3]),parseFloat(t[i+4]),parseFloat(t[i+5]),parseFloat(t[i+6])]),i+=7):"l"===t[i]?(r.push([parseFloat(t[i+1]),parseFloat(t[i+2])]),i+=3):i+=1;return[e,n,r]}var o,s,u,l,c,h,f,d,p=(f=document,d=f.createElement("iframe"),l=".jsPDF_sillysvg_iframe {display:none;position:absolute;}",(h=(c=f).createElement("style")).type="text/css",h.styleSheet?h.styleSheet.cssText=l:h.appendChild(c.createTextNode(l)),c.getElementsByTagName("head")[0].appendChild(h),d.name="childframe",d.setAttribute("width",0),d.setAttribute("height",0),d.setAttribute("frameborder","0"),d.setAttribute("scrolling","no"),d.setAttribute("seamless","seamless"),d.setAttribute("class","jsPDF_sillysvg_iframe"),f.body.appendChild(d),d),g=(o=t,(u=((s=p).contentWindow||s.contentDocument).document).write(o),u.close(),u.getElementsByTagName("svg")[0]),m=[1,1],b=parseFloat(g.getAttribute("width")),v=parseFloat(g.getAttribute("height"));b&&v&&(r&&i?m=[r/b,i/v]:r?m=[r/b,r/b]:i&&(m=[i/v,i/v]));var y,w,x,N,L=g.childNodes;for(y=0,w=L.length;y<w;y++)(x=L[y]).tagName&&"PATH"===x.tagName.toUpperCase()&&((N=a(x.getAttribute("d").split(-1===x.getAttribute("d").indexOf(",")?" ":",")))[0]=N[0]*m[0]+e,N[1]=N[1]*m[1]+n,this.lines.call(this,N[2],N[0],N[1],m));return this},C.addSVG=C.addSvg,C.addSvgAsImage=function(t,e,n,r,i,a,o,s){if(isNaN(e)||isNaN(n))throw console.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),new Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(r)||isNaN(i))throw console.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),new Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var u=document.createElement("canvas");u.width=r,u.height=i;var l=u.getContext("2d");return l.fillStyle="#fff",l.fillRect(0,0,u.width,u.height),canvg(u,t,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0}),this.addImage(u.toDataURL("image/jpeg",1),e,n,r,i,o,s),this},U.API.putTotalPages=function(t){var e,n=0;n=parseInt(this.internal.getFont().id.substr(1),10)<15?(e=new RegExp(t,"g"),this.internal.getNumberOfPages()):(e=new RegExp(this.pdfEscape16(t,this.internal.getFont()),"g"),this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var r=1;r<=this.internal.getNumberOfPages();r++)for(var i=0;i<this.internal.pages[r].length;i++)this.internal.pages[r][i]=this.internal.pages[r][i].replace(e,n);return this},U.API.viewerPreferences=function(t,e){var n;t=t||{},e=e||!1;var r,i,a,o={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},s=Object.keys(o),u=[],l=0,c=0,h=0;function f(t,e){var n,r=!1;for(n=0;n<t.length;n+=1)t[n]===e&&(r=!0);return r}if(void 0===this.internal.viewerpreferences&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(o)),this.internal.viewerpreferences.isSubscribed=!1),n=this.internal.viewerpreferences.configuration,"reset"===t||!0===e){var d=s.length;for(h=0;h<d;h+=1)n[s[h]].value=n[s[h]].defaultValue,n[s[h]].explicitSet=!1}if("object"===on(t))for(i in t)if(a=t[i],f(s,i)&&void 0!==a){if("boolean"===n[i].type&&"boolean"==typeof a)n[i].value=a;else if("name"===n[i].type&&f(n[i].valueSet,a))n[i].value=a;else if("integer"===n[i].type&&Number.isInteger(a))n[i].value=a;else if("array"===n[i].type){for(l=0;l<a.length;l+=1)if(r=!0,1===a[l].length&&"number"==typeof a[l][0])u.push(String(a[l]-1));else if(1<a[l].length){for(c=0;c<a[l].length;c+=1)"number"!=typeof a[l][c]&&(r=!1);!0===r&&u.push([a[l][0]-1,a[l][1]-1].join(" "))}n[i].value="["+u.join(" ")+"]"}else n[i].value=n[i].defaultValue;n[i].explicitSet=!0}return!1===this.internal.viewerpreferences.isSubscribed&&(this.internal.events.subscribe("putCatalog",function(){var t,e=[];for(t in n)!0===n[t].explicitSet&&("name"===n[t].type?e.push("/"+t+" /"+n[t].value):e.push("/"+t+" "+n[t].value));0!==e.length&&this.internal.write("/ViewerPreferences\n<<\n"+e.join("\n")+"\n>>")}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=n,this},U.API.addMetadata=function(t,e){return void 0===this.internal.__metadata__&&(this.internal.__metadata__={metadata:t,namespaceuri:e||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",tt),this.internal.events.subscribe("postPutResources",Q)),this},function(p){function g(t){var e,n,r,i,a,o,s;for(a="/CIDInit /ProcSet findresource begin\n12 dict begin\nbegincmap\n/CIDSystemInfo <<\n  /Registry (Adobe)\n  /Ordering (UCS)\n  /Supplement 0\n>> def\n/CMapName /Adobe-Identity-UCS def\n/CMapType 2 def\n1 begincodespacerange\n<0000><ffff>\nendcodespacerange",r=[],o=0,s=(n=Object.keys(t).sort(function(t,e){return t-e})).length;o<s;o++)e=n[o],100<=r.length&&(a+="\n"+r.length+" beginbfchar\n"+r.join("\n")+"\nendbfchar",r=[]),void 0!==t[e]&&null!==t[e]&&"function"==typeof t[e].toString&&(i=("0000"+t[e].toString(16)).slice(-4),e=("0000"+(+e).toString(16)).slice(-4),r.push("<"+e+"><"+i+">"));return r.length&&(a+="\n"+r.length+" beginbfchar\n"+r.join("\n")+"\nendbfchar\n"),a+="endcmap\nCMapName currentdict /CMap defineresource pop\nend\nend"}var t=p.API,m=t.pdfEscape16=function(t,e){for(var n,r=e.metadata.Unicode.widths,i=["","0","00","000","0000"],a=[""],o=0,s=t.length;o<s;++o){if(n=e.metadata.characterToGlyph(t.charCodeAt(o)),e.metadata.glyIdsUsed.push(n),e.metadata.toUnicode[n]=t.charCodeAt(o),-1==r.indexOf(n)&&(r.push(n),r.push([parseInt(e.metadata.widthOfGlyph(n),10)])),"0"==n)return a.join("");n=n.toString(16),a.push(i[4-n.length],n)}return a.join("")};t.events.push(["putFont",function(t){!function(t){var e=t.font,n=t.out,r=t.newObject,i=t.putStream,a=t.pdfEscapeWithNeededParanthesis;if(e.metadata instanceof p.API.TTFFont&&"Identity-H"===e.encoding){for(var o=e.metadata.Unicode.widths,s=e.metadata.subset.encode(e.metadata.glyIdsUsed,1),u="",l=0;l<s.length;l++)u+=String.fromCharCode(s[l]);var c=r();i({data:u,addLength1:!0}),n("endobj");var h=r();i({data:g(e.metadata.toUnicode),addLength1:!0}),n("endobj");var f=r();n("<<"),n("/Type /FontDescriptor"),n("/FontName /"+a(e.fontName)),n("/FontFile2 "+c+" 0 R"),n("/FontBBox "+p.API.PDFObject.convert(e.metadata.bbox)),n("/Flags "+e.metadata.flags),n("/StemV "+e.metadata.stemV),n("/ItalicAngle "+e.metadata.italicAngle),n("/Ascent "+e.metadata.ascender),n("/Descent "+e.metadata.decender),n("/CapHeight "+e.metadata.capHeight),n(">>"),n("endobj");var d=r();n("<<"),n("/Type /Font"),n("/BaseFont /"+a(e.fontName)),n("/FontDescriptor "+f+" 0 R"),n("/W "+p.API.PDFObject.convert(o)),n("/CIDToGIDMap /Identity"),n("/DW 1000"),n("/Subtype /CIDFontType2"),n("/CIDSystemInfo"),n("<<"),n("/Supplement 0"),n("/Registry (Adobe)"),n("/Ordering ("+e.encoding+")"),n(">>"),n(">>"),n("endobj"),e.objectNumber=r(),n("<<"),n("/Type /Font"),n("/Subtype /Type0"),n("/ToUnicode "+h+" 0 R"),n("/BaseFont /"+e.fontName),n("/Encoding /"+e.encoding),n("/DescendantFonts ["+d+" 0 R]"),n(">>"),n("endobj"),e.isAlreadyPutted=!0}}(t)}]);t.events.push(["putFont",function(t){!function(t){var e=t.font,n=t.out,r=t.newObject,i=t.putStream,a=t.pdfEscapeWithNeededParanthesis;if(e.metadata instanceof p.API.TTFFont&&"WinAnsiEncoding"===e.encoding){for(var o=e.metadata.rawData,s="",u=0;u<o.length;u++)s+=String.fromCharCode(o[u]);var l=r();i({data:s,addLength1:!0}),n("endobj");var c=r();i({data:g(e.metadata.toUnicode),addLength1:!0}),n("endobj");var h=r();n("<<"),n("/Descent "+e.metadata.decender),n("/CapHeight "+e.metadata.capHeight),n("/StemV "+e.metadata.stemV),n("/Type /FontDescriptor"),n("/FontFile2 "+l+" 0 R"),n("/Flags 96"),n("/FontBBox "+p.API.PDFObject.convert(e.metadata.bbox)),n("/FontName /"+a(e.fontName)),n("/ItalicAngle "+e.metadata.italicAngle),n("/Ascent "+e.metadata.ascender),n(">>"),n("endobj"),e.objectNumber=r();for(var f=0;f<e.metadata.hmtx.widths.length;f++)e.metadata.hmtx.widths[f]=parseInt(e.metadata.hmtx.widths[f]*(1e3/e.metadata.head.unitsPerEm));n("<</Subtype/TrueType/Type/Font/ToUnicode "+c+" 0 R/BaseFont/"+e.fontName+"/FontDescriptor "+h+" 0 R/Encoding/"+e.encoding+" /FirstChar 29 /LastChar 255 /Widths "+p.API.PDFObject.convert(e.metadata.hmtx.widths)+">>"),n("endobj"),e.isAlreadyPutted=!0}}(t)}]);function a(t){var e,n=t.text||"",r=t.x,i=t.y,a=t.options||{},o=t.mutex||{},s=o.pdfEscape,u=o.activeFontKey,l=o.fonts,c=u,h="",f=0,d="",p=l[c].encoding;if("Identity-H"!==l[c].encoding)return{text:n,x:r,y:i,options:a,mutex:o};for(d=n,c=u,Array.isArray(n)&&(d=n[0]),f=0;f<d.length;f+=1)l[c].metadata.hasOwnProperty("cmap")&&(e=l[c].metadata.cmap.unicode.codeMap[d[f].charCodeAt(0)]),e?h+=d[f]:d[f].charCodeAt(0)<256&&l[c].metadata.hasOwnProperty("Unicode")?h+=d[f]:h+="";var g="";return parseInt(c.slice(1))<14||"WinAnsiEncoding"===p?g=s(h,c).split("").map(function(t){return t.charCodeAt(0).toString(16)}).join(""):"Identity-H"===p&&(g=m(h,l[c])),o.isHex=!0,{text:g,x:r,y:i,options:a,mutex:o}}t.events.push(["postProcessText",function(t){var e=t.text||"",n=[],r={text:e,x:t.x,y:t.y,options:t.options,mutex:t.mutex};if(Array.isArray(e)){var i=0;for(i=0;i<e.length;i+=1)Array.isArray(e[i])&&3===e[i].length?n.push([a(Object.assign({},r,{text:e[i][0]})).text,e[i][1],e[i][2]]):n.push(a(Object.assign({},r,{text:e[i]})).text);t.text=n}else t.text=a(Object.assign({},r,{text:e})).text}])}(U),(I=U.API).existsFileInVFS=function(t){return et.call(this),void 0!==this.internal.vFS[t]},I.addFileToVFS=function(t,e){return et.call(this),this.internal.vFS[t]=e,this},I.getFileFromVFS=function(t){return et.call(this),void 0!==this.internal.vFS[t]?this.internal.vFS[t]:null},U.API.addHTML=function(t,p,g,s,m){if("undefined"==typeof html2canvas&&"undefined"==typeof rasterizeHTML)throw new Error("You need either https://github.com/niklasvh/html2canvas or https://github.com/cburgmer/rasterizeHTML.js");"number"!=typeof p&&(s=p,m=g),"function"==typeof s&&(m=s,s=null),"function"!=typeof m&&(m=function(){});var e=this.internal,b=e.scaleFactor,v=e.pageSize.getWidth(),y=e.pageSize.getHeight();if((s=s||{}).onrendered=function(u){p=parseInt(p)||0,g=parseInt(g)||0;var t=s.dim||{},l=Object.assign({top:0,right:0,bottom:0,left:0,useFor:"content"},s.margin),e=t.h||Math.min(y,u.height/b),c=t.w||Math.min(v,u.width/b)-p,h=s.format||"JPEG",f=s.imageCompression||"SLOW";if(u.height>y-l.top-l.bottom&&s.pagesplit){var d=function(t,e,n,r,i){var a=document.createElement("canvas");a.height=i,a.width=r;var o=a.getContext("2d");return o.mozImageSmoothingEnabled=!1,o.webkitImageSmoothingEnabled=!1,o.msImageSmoothingEnabled=!1,o.imageSmoothingEnabled=!1,o.fillStyle=s.backgroundColor||"#ffffff",o.fillRect(0,0,r,i),o.drawImage(t,e,n,r,i,0,0,r,i),a},n=function(){for(var t,e,n,r,i=0,a=0,o={},s=!1;;){if(a=0,o.top=0!==i?l.top:g,o.left=0!==i?l.left:p,s=(v-l.left-l.right)*b<u.width,"content"===l.useFor?0===i?(t=Math.min((v-l.left)*b,u.width),e=Math.min((y-l.top)*b,u.height-i)):(t=Math.min(v*b,u.width),e=Math.min(y*b,u.height-i),o.top=0):(t=Math.min((v-l.left-l.right)*b,u.width),e=Math.min((y-l.bottom-l.top)*b,u.height-i)),s)for(;"content"===l.useFor&&(0===a?t=Math.min((v-l.left)*b,u.width):(t=Math.min(v*b,u.width-a),o.left=0)),r=[n=d(u,a,i,t,e),o.left,o.top,n.width/b,n.height/b,h,null,f],this.addImage.apply(this,r),!((a+=t)>=u.width);)this.addPage();else r=[n=d(u,0,i,t,e),o.left,o.top,n.width/b,n.height/b,h,null,f],this.addImage.apply(this,r);if((i+=e)>=u.height)break;this.addPage()}m(c,i,null,r)}.bind(this);if("CANVAS"===u.nodeName){var r=new Image;r.onload=n,r.src=u.toDataURL("image/png"),u=r}else n()}else{var i=Math.random().toString(35),a=[u,p,g,c,e,h,i,f];this.addImage.apply(this,a),m(c,e,i,a)}}.bind(this),"undefined"!=typeof html2canvas&&!s.rstz)return html2canvas(t,s);if("undefined"==typeof rasterizeHTML)return null;var n="drawDocument";return"string"==typeof t&&(n=/^http/.test(t)?"drawURL":"drawHTML"),s.width=s.width||v*b,rasterizeHTML[n](t,void 0,s).then(function(t){s.onrendered(t.image)},function(t){m(null,t)})},
/**
   * jsPDF fromHTML plugin. BETA stage. API subject to change. Needs browser
   * Copyright (c) 2012 Willow Systems Corporation, willow-systems.com
   *               2014 Juan Pablo Gaviria, https://github.com/juanpgaviria
   *               2014 Diego Casorran, https://github.com/diegocr
   *               2014 Daniel Husar, https://github.com/danielhusar
   *               2014 Wolfgang Gassler, https://github.com/woolfg
   *               2014 Steven Spungin, https://github.com/flamenco
   *
   * @license
   * 
   * ====================================================================
   */
function(t){var k,F,i,o,s,u,l,c,C,y,f,h,d,n,I,j,p,g,m,B;function e(){}k=function(t){return e.prototype=t,new e},y=function(t){var e,n,r,i,a,o,s;for(n=0,r=t.length,e=void 0,o=i=!1;!i&&n!==r;)(e=t[n]=t[n].trimLeft())&&(i=!0),n++;for(n=r-1;r&&!o&&-1!==n;)(e=t[n]=t[n].trimRight())&&(o=!0),n--;for(a=/\s+$/g,s=!0,n=0;n!==r;)"\u2028"!=t[n]&&(e=t[n].replace(/\s+/g," "),s&&(e=e.trimLeft()),e&&(s=a.test(e)),t[n]=e),n++;return t},h=function(t){var e,n,r;for(e=void 0,n=(r=t.split(",")).shift();!e&&n;)e=i[n.trim().toLowerCase()],n=r.shift();return e},d=function(t){var e;return-1<(t="auto"===t?"0px":t).indexOf("em")&&!isNaN(Number(t.replace("em","")))&&(t=18.719*Number(t.replace("em",""))+"px"),-1<t.indexOf("pt")&&!isNaN(Number(t.replace("pt","")))&&(t=1.333*Number(t.replace("pt",""))+"px"),void 0,16,(e=n[t])?e:void 0!==(e={"xx-small":9,"x-small":11,small:13,medium:16,large:19,"x-large":23,"xx-large":28,auto:0}[t])?n[t]=e/16:(e=parseFloat(t))?n[t]=e/16:(e=t.match(/([\d\.]+)(px)/),Array.isArray(e)&&3===e.length?n[t]=parseFloat(e[1])/16:n[t]=1)},C=function(t){var e,n,r,i,a;return a=t,i=document.defaultView&&document.defaultView.getComputedStyle?document.defaultView.getComputedStyle(a,null):a.currentStyle?a.currentStyle:a.style,n=void 0,(e={})["font-family"]=h((r=function(t){return t=t.replace(/-\D/g,function(t){return t.charAt(1).toUpperCase()}),i[t]})("font-family"))||"times",e["font-style"]=o[r("font-style")]||"normal",e["text-align"]=s[r("text-align")]||"left","bold"===(n=u[r("font-weight")]||"normal")&&("normal"===e["font-style"]?e["font-style"]=n:e["font-style"]=n+e["font-style"]),e["font-size"]=d(r("font-size"))||1,e["line-height"]=d(r("line-height"))||1,e.display="inline"===r("display")?"inline":"block",n="block"===e.display,e["margin-top"]=n&&d(r("margin-top"))||0,e["margin-bottom"]=n&&d(r("margin-bottom"))||0,e["padding-top"]=n&&d(r("padding-top"))||0,e["padding-bottom"]=n&&d(r("padding-bottom"))||0,e["margin-left"]=n&&d(r("margin-left"))||0,e["margin-right"]=n&&d(r("margin-right"))||0,e["padding-left"]=n&&d(r("padding-left"))||0,e["padding-right"]=n&&d(r("padding-right"))||0,e["page-break-before"]=r("page-break-before")||"auto",e.float=l[r("cssFloat")]||"none",e.clear=c[r("clear")]||"none",e.color=r("color"),e},I=function(t,e,n){var r,i,a,o,s;if(a=!1,o=i=void 0,r=n["#"+t.id])if("function"==typeof r)a=r(t,e);else for(i=0,o=r.length;!a&&i!==o;)a=r[i](t,e),i++;if(r=n[t.nodeName],!a&&r)if("function"==typeof r)a=r(t,e);else for(i=0,o=r.length;!a&&i!==o;)a=r[i](t,e),i++;for(s="string"==typeof t.className?t.className.split(" "):[],i=0;i<s.length;i++)if(r=n["."+s[i]],!a&&r)if("function"==typeof r)a=r(t,e);else for(i=0,o=r.length;!a&&i!==o;)a=r[i](t,e),i++;return a},B=function(t,e){var n,r,i,a,o,s,u,l,c;for(n=[],r=[],i=0,c=t.rows[0].cells.length,u=t.clientWidth;i<c;)l=t.rows[0].cells[i],r[i]={name:l.textContent.toLowerCase().replace(/\s+/g,""),prompt:l.textContent.replace(/\r?\n/g,""),width:l.clientWidth/u*e.pdf.internal.pageSize.getWidth()},i++;for(i=1;i<t.rows.length;){for(s=t.rows[i],o={},a=0;a<s.cells.length;)o[r[a].name]=s.cells[a].textContent.replace(/\r?\n/g,""),a++;n.push(o),i++}return{rows:n,headers:r}};var O={SCRIPT:1,STYLE:1,NOSCRIPT:1,OBJECT:1,EMBED:1,SELECT:1},E=1;F=function(t,i,e){var n,r,a,o,s,u,l,c;for(r=t.childNodes,n=void 0,(s="block"===(a=C(t)).display)&&(i.setBlockBoundary(),i.setBlockStyle(a)),o=0,u=r.length;o<u;){if("object"===on(n=r[o])){if(i.executeWatchFunctions(n),1===n.nodeType&&"HEADER"===n.nodeName){var h=n,f=i.pdf.margins_doc.top;i.pdf.internal.events.subscribe("addPage",function(t){i.y=f,F(h,i,e),i.pdf.margins_doc.top=i.y+10,i.y+=10},!1)}if(8===n.nodeType&&"#comment"===n.nodeName)~n.textContent.indexOf("ADD_PAGE")&&(i.pdf.addPage(),i.y=i.pdf.margins_doc.top);else if(1!==n.nodeType||O[n.nodeName])if(3===n.nodeType){var d=n.nodeValue;if(n.nodeValue&&"LI"===n.parentNode.nodeName)if("OL"===n.parentNode.parentNode.nodeName)d=E+++". "+d;else{var p=a["font-size"],g=(3-.75*p)*i.pdf.internal.scaleFactor,m=.75*p*i.pdf.internal.scaleFactor,b=1.74*p/i.pdf.internal.scaleFactor;c=function(t,e){this.pdf.circle(t+g,e+m,b,"FD")}}16&n.ownerDocument.body.compareDocumentPosition(n)&&i.addText(d,a)}else"string"==typeof n&&i.addText(n,a);else{var v;if("IMG"===n.nodeName){var y=n.getAttribute("src");v=j[i.pdf.API.__addimage__.sHashCode(y)||y]}if(v){i.pdf.internal.pageSize.getHeight()-i.pdf.margins_doc.bottom<i.y+n.height&&i.y>i.pdf.margins_doc.top&&(i.pdf.addPage(),i.y=i.pdf.margins_doc.top,i.executeWatchFunctions(n));var w=C(n),x=i.x,N=12/i.pdf.internal.scaleFactor,L=(w["margin-left"]+w["padding-left"])*N,A=(w["margin-right"]+w["padding-right"])*N,_=(w["margin-top"]+w["padding-top"])*N,S=(w["margin-bottom"]+w["padding-bottom"])*N;void 0!==w.float&&"right"===w.float?x+=i.settings.width-n.width-A:x+=L,i.pdf.addImage(v,x,i.y+_,n.width,n.height),v=void 0,"right"===w.float||"left"===w.float?(i.watchFunctions.push(function(t,e,n,r){return i.y>=e?(i.x+=t,i.settings.width+=n,!0):!!(r&&1===r.nodeType&&!O[r.nodeName]&&i.x+r.width>i.pdf.margins_doc.left+i.pdf.margins_doc.width)&&(i.x+=t,i.y=e,i.settings.width+=n,!0)}.bind(this,"left"===w.float?-n.width-L-A:0,i.y+n.height+_+S,n.width)),i.watchFunctions.push(function(t,e,n){return!(i.y<t&&e===i.pdf.internal.getNumberOfPages())||1===n.nodeType&&"both"===C(n).clear&&(i.y=t,!0)}.bind(this,i.y+n.height,i.pdf.internal.getNumberOfPages())),i.settings.width-=n.width+L+A,"left"===w.float&&(i.x+=n.width+L+A)):i.y+=n.height+_+S}else if("TABLE"===n.nodeName)l=B(n,i),i.y+=10,i.pdf.table(i.x,i.y,l.rows,l.headers,{autoSize:!1,printHeaders:e.printHeaders,margins:i.pdf.margins_doc,css:C(n)}),i.y=i.pdf.lastCellPos.y+i.pdf.lastCellPos.h+20;else if("OL"===n.nodeName||"UL"===n.nodeName)E=1,I(n,i,e)||F(n,i,e),i.y+=10;else if("LI"===n.nodeName){var P=i.x;i.x+=20/i.pdf.internal.scaleFactor,i.y+=3,I(n,i,e)||F(n,i,e),i.x=P}else"BR"===n.nodeName?(i.y+=a["font-size"]*i.pdf.internal.scaleFactor,i.addText("\u2028",k(a))):I(n,i,e)||F(n,i,e)}}o++}if(e.outY=i.y,s)return i.setBlockBoundary(c)},j={},p=function(t,a,e,n){var o,r=t.getElementsByTagName("img"),i=r.length,s=0;function u(){a.pdf.internal.events.publish("imagesLoaded"),n(o)}function l(e,n,r){if(e){var i=new Image;o=++s,i.crossOrigin="",i.onerror=i.onload=function(){if(i.complete&&(0===i.src.indexOf("data:image/")&&(i.width=n||i.width||0,i.height=r||i.height||0),i.width+i.height)){var t=a.pdf.API.__addimage__.sHashCode(e)||e;j[t]=j[t]||i}--s||u()},i.src=e}}for(;i--;)l(r[i].getAttribute("src"),r[i].width,r[i].height);return s||u()},g=function(t,a,o){var s=t.getElementsByTagName("footer");if(0<s.length){s=s[0];var e=a.pdf.internal.write,n=a.y;a.pdf.internal.write=function(){},F(s,a,o);var u=Math.ceil(a.y-n)+5;a.y=n,a.pdf.internal.write=e,a.pdf.margins_doc.bottom+=u;for(var r=function(t){var e=void 0!==t?t.pageNumber:1,n=a.y;a.y=a.pdf.internal.pageSize.getHeight()-a.pdf.margins_doc.bottom,a.pdf.margins_doc.bottom-=u;for(var r=s.getElementsByTagName("span"),i=0;i<r.length;++i)-1<(" "+r[i].className+" ").replace(/[\n\t]/g," ").indexOf(" pageCounter ")&&(r[i].innerHTML=e),-1<(" "+r[i].className+" ").replace(/[\n\t]/g," ").indexOf(" totalPages ")&&(r[i].innerHTML="###jsPDFVarTotalPages###");F(s,a,o),a.pdf.margins_doc.bottom+=u,a.y=n},i=s.getElementsByTagName("span"),l=0;l<i.length;++l)-1<(" "+i[l].className+" ").replace(/[\n\t]/g," ").indexOf(" totalPages ")&&a.pdf.internal.events.subscribe("htmlRenderingFinished",a.pdf.putTotalPages.bind(a.pdf,"###jsPDFVarTotalPages###"),!0);a.pdf.internal.events.subscribe("addPage",r,!1),r(),O.FOOTER=1}},m=function(t,e,n,r,i,a){if(!e)return!1;var o,s,u,l;"string"==typeof e||e.parentNode||(e=""+e.innerHTML),"string"==typeof e&&(o=e.replace(/<\/?script[^>]*?>/gi,""),l="jsPDFhtmlText"+Date.now().toString()+(1e3*Math.random()).toFixed(0),(u=document.createElement("div")).style.cssText="position: absolute !important;clip: rect(1px 1px 1px 1px); /* IE6, IE7 */clip: rect(1px, 1px, 1px, 1px);padding:0 !important;border:0 !important;height: 1px !important;width: 1px !important; top:auto;left:-100px;overflow: hidden;",u.innerHTML='<iframe style="height:1px;width:1px" name="'+l+'" />',document.body.appendChild(u),(s=window.frames[l]).document.open(),s.document.writeln(o),s.document.close(),e=s.document.body);var c,h=new f(t,n,r,i);return p.call(this,e,h,i.elementHandlers,function(t){g(e,h,i.elementHandlers),F(e,h,i.elementHandlers),h.pdf.internal.events.publish("htmlRenderingFinished"),c=h.dispose(),"function"==typeof a?a(c):t&&console.error("jsPDF Warning: rendering issues? provide a callback to fromHTML!")}),c||{x:h.x,y:h.y}},(f=function(t,e,n,r){return this.pdf=t,this.x=e,this.y=n,this.settings=r,this.watchFunctions=[],this.init(),this}).prototype.init=function(){return this.paragraph={text:[],style:[]},this.pdf.internal.write("q")},f.prototype.dispose=function(){return this.pdf.internal.write("Q"),{x:this.x,y:this.y,ready:!0}},f.prototype.executeWatchFunctions=function(t){var e=!1,n=[];if(0<this.watchFunctions.length){for(var r=0;r<this.watchFunctions.length;++r)!0===this.watchFunctions[r](t)?e=!0:n.push(this.watchFunctions[r]);this.watchFunctions=n}return e},f.prototype.splitFragmentsIntoLines=function(t,e){var n,r,i,a,o,s,u,l,c,h,f,d,p,g;for(12,h=this.pdf.internal.scaleFactor,a={},s=u=l=g=o=i=c=r=void 0,d=[f=[]],n=0,p=this.settings.width;t.length;)if(o=t.shift(),g=e.shift(),o)if((i=a[(r=g["font-family"])+(c=g["font-style"])])||(i=this.pdf.internal.getFont(r,c).metadata.Unicode,a[r+c]=i),l={widths:i.widths,kerning:i.kerning,fontSize:12*g["font-size"],textIndent:n},u=this.pdf.getStringUnitWidth(o,l)*l.fontSize/h,"\u2028"==o)f=[],d.push(f);else if(p<n+u){for(s=this.pdf.splitTextToSize(o,p,l),f.push([s.shift(),g]);s.length;)f=[[s.shift(),g]],d.push(f);n=this.pdf.getStringUnitWidth(f[0][0],l)*l.fontSize/h}else f.push([o,g]),n+=u;if(void 0!==g["text-align"]&&("center"===g["text-align"]||"right"===g["text-align"]||"justify"===g["text-align"]))for(var m=0;m<d.length;++m){var b=this.pdf.getStringUnitWidth(d[m][0][0],l)*l.fontSize/h;0<m&&(d[m][0][1]=k(d[m][0][1]));var v=p-b;if("right"===g["text-align"])d[m][0][1]["margin-left"]=v;else if("center"===g["text-align"])d[m][0][1]["margin-left"]=v/2;else if("justify"===g["text-align"]){var y=d[m][0][0].split(" ").length-1;d[m][0][1]["word-spacing"]=v/y,m===d.length-1&&(d[m][0][1]["word-spacing"]=0)}}return d},f.prototype.RenderTextFragment=function(t,e){var n,r;r=0,this.pdf.internal.pageSize.getHeight()-this.pdf.margins_doc.bottom<this.y+this.pdf.internal.getFontSize()&&(this.pdf.internal.write("ET","Q"),this.pdf.addPage(),this.y=this.pdf.margins_doc.top,this.pdf.internal.write("q","BT",this.getPdfColor(e.color),this.pdf.internal.getCoordinateString(this.x),this.pdf.internal.getVerticalCoordinateString(this.y),"Td"),r=Math.max(r,e["line-height"],e["font-size"]),this.pdf.internal.write(0,(-12*r).toFixed(2),"Td")),n=this.pdf.internal.getFont(e["font-family"],e["font-style"]);var i=this.getPdfColor(e.color);i!==this.lastTextColor&&(this.pdf.internal.write(i),this.lastTextColor=i),void 0!==e["word-spacing"]&&0<e["word-spacing"]&&this.pdf.internal.write(e["word-spacing"].toFixed(2),"Tw"),this.pdf.internal.write("/"+n.id,(12*e["font-size"]).toFixed(2),"Tf","("+this.pdf.internal.pdfEscape(t)+") Tj"),void 0!==e["word-spacing"]&&this.pdf.internal.write(0,"Tw")},f.prototype.getPdfColor=function(t){var e,n,r,i=/rgb\s*\(\s*(\d+),\s*(\d+),\s*(\d+\s*)\)/.exec(t);if(null!=i)e=parseInt(i[1]),n=parseInt(i[2]),r=parseInt(i[3]);else{if("string"==typeof t&&"#"!=t.charAt(0)){var a=new RGBColor(t);t=a.ok?a.toHex():"#000000"}e=t.substring(1,3),e=parseInt(e,16),n=t.substring(3,5),n=parseInt(n,16),r=t.substring(5,7),r=parseInt(r,16)}if("string"==typeof e&&/^#[0-9A-Fa-f]{6}$/.test(e)){var o=parseInt(e.substr(1),16);e=o>>16&255,n=o>>8&255,r=255&o}var s=this.f3;return 0===e&&0===n&&0===r||void 0===n?s(e/255)+" g":[s(e/255),s(n/255),s(r/255),"rg"].join(" ")},f.prototype.f3=function(t){return t.toFixed(3)},f.prototype.renderParagraph=function(t){var e,n,r,i,a,o,s,u,l,c,h,f,d;if(r=y(this.paragraph.text),f=this.paragraph.style,e=this.paragraph.blockstyle,this.paragraph={text:[],style:[],blockstyle:{},priorblockstyle:e},r.join("").trim()){s=this.splitFragmentsIntoLines(r,f),u=o=void 0,n=12/this.pdf.internal.scaleFactor,this.priorMarginBottom=this.priorMarginBottom||0,h=(Math.max((e["margin-top"]||0)-this.priorMarginBottom,0)+(e["padding-top"]||0))*n,c=((e["margin-bottom"]||0)+(e["padding-bottom"]||0))*n,this.priorMarginBottom=e["margin-bottom"]||0,"always"===e["page-break-before"]&&(this.pdf.addPage(),this.y=0,h=((e["margin-top"]||0)+(e["padding-top"]||0))*n),l=this.pdf.internal.write,a=i=void 0,this.y+=h,l("q","BT 0 g",this.pdf.internal.getCoordinateString(this.x),this.pdf.internal.getVerticalCoordinateString(this.y),"Td");for(var p=0;s.length;){for(i=u=0,a=(o=s.shift()).length;i!==a;)o[i][0].trim()&&(u=Math.max(u,o[i][1]["line-height"],o[i][1]["font-size"]),d=7*o[i][1]["font-size"]),i++;var g=0,m=0;for(void 0!==o[0][1]["margin-left"]&&0<o[0][1]["margin-left"]&&(g=(m=this.pdf.internal.getCoordinateString(o[0][1]["margin-left"]))-p,p=m),l(g+Math.max(e["margin-left"]||0,0)*n,(-12*u).toFixed(2),"Td"),i=0,a=o.length;i!==a;)o[i][0]&&this.RenderTextFragment(o[i][0],o[i][1]),i++;if(this.y+=u*n,this.executeWatchFunctions(o[0][1])&&0<s.length){var b=[],v=[];s.forEach(function(t){for(var e=0,n=t.length;e!==n;)t[e][0]&&(b.push(t[e][0]+" "),v.push(t[e][1])),++e}),s=this.splitFragmentsIntoLines(y(b),v),l("ET","Q"),l("q","BT 0 g",this.pdf.internal.getCoordinateString(this.x),this.pdf.internal.getVerticalCoordinateString(this.y),"Td")}}return t&&"function"==typeof t&&t.call(this,this.x-9,this.y-d/2),l("ET","Q"),this.y+=c}},f.prototype.setBlockBoundary=function(t){return this.renderParagraph(t)},f.prototype.setBlockStyle=function(t){return this.paragraph.blockstyle=t},f.prototype.addText=function(t,e){return this.paragraph.text.push(t),this.paragraph.style.push(e)},i={helvetica:"helvetica","sans-serif":"helvetica","times new roman":"times",serif:"times",times:"times",monospace:"courier",courier:"courier"},u={100:"normal",200:"normal",300:"normal",400:"normal",500:"bold",600:"bold",700:"bold",800:"bold",900:"bold",normal:"normal",bold:"bold",bolder:"bold",lighter:"normal"},o={normal:"normal",italic:"italic",oblique:"italic"},s={left:"left",right:"right",center:"center",justify:"justify"},l={none:"none",right:"right",left:"left"},c={none:"none",both:"both"},n={normal:1},t.fromHTML=function(t,e,n,r,i,a){return this.margins_doc=a||{top:0,bottom:0},r||(r={}),r.elementHandlers||(r.elementHandlers={}),m(this,t,isNaN(e)?4:e,isNaN(n)?4:n,r,i)}}(U.API),U.API,("undefined"!=typeof window&&window||"undefined"!=typeof global&&global).html2pdf=function(t,o,e){var n=o.canvas;if(n){var r,i;if((n.pdf=o).annotations={_nameMap:[],createAnnotation:function(t,e){var n,r=o.context2d._wrapX(e.left),i=o.context2d._wrapY(e.top),a=t.indexOf("#");n=0<=a?{name:t.substring(a+1)}:{url:t},o.link(r,i,e.right-e.left,e.bottom-e.top,n)},setName:function(t,e){var n=o.context2d._wrapX(e.left),r=o.context2d._wrapY(e.top),i=o.context2d._page(e.top);this._nameMap[t]={page:i,x:n,y:r}}},n.annotations=o.annotations,o.context2d._pageBreakAt=function(t){this.pageBreaks.push(t)},o.context2d._gotoPage=function(t){for(;o.internal.getNumberOfPages()<t;)o.addPage();o.setPage(t)},"string"==typeof t){t=t.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"");var a,s=document.createElement("iframe");document.body.appendChild(s),(a=s.contentDocument||s.contentWindow.document).open(),a.write(t),a.close(),r=a.body}else r=t;var u={async:!0,allowTaint:!0,backgroundColor:"#ffffff",canvas:n,imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1,windowHeight:i=o.internal.pageSize.getHeight(),scrollY:i};return o.context2d.pageWrapYEnabled=!0,o.context2d.pageWrapY=o.internal.pageSize.getHeight(),html2canvas(r,u).then(function(){e&&(s&&s.parentElement.removeChild(s),e(o))})}alert("jsPDF canvas plugin not installed")},function(f){var r=f.BlobBuilder||f.WebKitBlobBuilder||f.MSBlobBuilder||f.MozBlobBuilder;f.URL=f.URL||f.webkitURL||function(t,e){return(e=document.createElement("a")).href=t,e};var n=f.Blob,d=URL.createObjectURL,p=URL.revokeObjectURL,a=f.Symbol&&f.Symbol.toStringTag,t=!1,e=!1,g=!!f.ArrayBuffer,i=r&&r.prototype.append&&r.prototype.getBlob;try{t=2===new Blob(["ä"]).size,e=2===new Blob([new Uint8Array([1,2])]).size}catch(t){}function o(t){return t.map(function(t){if(t.buffer instanceof ArrayBuffer){var e=t.buffer;if(t.byteLength!==e.byteLength){var n=new Uint8Array(t.byteLength);n.set(new Uint8Array(e,t.byteOffset,t.byteLength)),e=n.buffer}return e}return t})}function s(t,e){e=e||{};var n=new r;return o(t).forEach(function(t){n.append(t)}),e.type?n.getBlob(e.type):n.getBlob()}function u(t,e){return new n(o(t),e||{})}if(f.Blob&&(s.prototype=Blob.prototype,u.prototype=Blob.prototype),a)try{File.prototype[a]="File",Blob.prototype[a]="Blob",FileReader.prototype[a]="FileReader"}catch(t){}function l(){var t=!!f.ActiveXObject||"-ms-scroll-limit"in document.documentElement.style&&"-ms-ime-align"in document.documentElement.style,e=f.XMLHttpRequest&&f.XMLHttpRequest.prototype.send;t&&e&&(XMLHttpRequest.prototype.send=function(t){t instanceof Blob&&this.setRequestHeader("Content-Type",t.type),e.call(this,t)});try{new File([],"")}catch(t){try{var n=new Function('class File extends Blob {constructor(chunks, name, opts) {opts = opts || {};super(chunks, opts || {});this.name = name;this.lastModifiedDate = opts.lastModified ? new Date(opts.lastModified) : new Date;this.lastModified = +this.lastModifiedDate;}};return new File([], ""), File')();f.File=n}catch(t){n=function(t,e,n){var r=new Blob(t,n),i=n&&void 0!==n.lastModified?new Date(n.lastModified):new Date;return r.name=e,r.lastModifiedDate=i,r.lastModified=+i,r.toString=function(){return"[object File]"},a&&(r[a]="File"),r};f.File=n}}}t?(l(),f.Blob=e?f.Blob:u):i?(l(),f.Blob=s):function(){function o(t){for(var e=[],n=0;n<t.length;n++){var r=t.charCodeAt(n);r<128?e.push(r):r<2048?e.push(192|r>>6,128|63&r):r<55296||57344<=r?e.push(224|r>>12,128|r>>6&63,128|63&r):(n++,r=65536+((1023&r)<<10|1023&t.charCodeAt(n)),e.push(240|r>>18,128|r>>12&63,128|r>>6&63,128|63&r))}return e}function e(t){var e,n,r,i,a,o;for(e="",r=t.length,n=0;n<r;)switch((i=t[n++])>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:e+=String.fromCharCode(i);break;case 12:case 13:a=t[n++],e+=String.fromCharCode((31&i)<<6|63&a);break;case 14:a=t[n++],o=t[n++],e+=String.fromCharCode((15&i)<<12|(63&a)<<6|(63&o)<<0)}return e}function s(t){for(var e=new Array(t.byteLength),n=new Uint8Array(t),r=e.length;r--;)e[r]=n[r];return e}function n(t){for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",n=[],r=0;r<t.length;r+=3){var i=t[r],a=r+1<t.length,o=a?t[r+1]:0,s=r+2<t.length,u=s?t[r+2]:0,l=i>>2,c=(3&i)<<4|o>>4,h=(15&o)<<2|u>>6,f=63&u;s||(f=64,a||(h=64)),n.push(e[l],e[c],e[h],e[f])}return n.join("")}var t=Object.create||function(t){function e(){}return e.prototype=t,new e};if(g)var r=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],u=ArrayBuffer.isView||function(t){return t&&-1<r.indexOf(Object.prototype.toString.call(t))};function l(t,e){for(var n=0,r=(t=t||[]).length;n<r;n++){var i=t[n];i instanceof l?t[n]=i._buffer:"string"==typeof i?t[n]=o(i):g&&(ArrayBuffer.prototype.isPrototypeOf(i)||u(i))?t[n]=s(i):g&&(a=i)&&DataView.prototype.isPrototypeOf(a)?t[n]=s(i.buffer):t[n]=o(String(i))}var a;this._buffer=[].concat.apply([],t),this.size=this._buffer.length,this.type=e&&e.type||""}function i(t,e,n){var r=l.call(this,t,n=n||{})||this;return r.name=e,r.lastModifiedDate=n.lastModified?new Date(n.lastModified):new Date,r.lastModified=+r.lastModifiedDate,r}if(l.prototype.slice=function(t,e,n){return new l([this._buffer.slice(t||0,e||this._buffer.length)],{type:n})},l.prototype.toString=function(){return"[object Blob]"},(i.prototype=t(l.prototype)).constructor=i,Object.setPrototypeOf)Object.setPrototypeOf(i,l);else try{i.__proto__=l}catch(t){}function a(){if(!(this instanceof a))throw new TypeError("Failed to construct 'FileReader': Please use the 'new' operator, this DOM object constructor cannot be called as a function.");var n=document.createDocumentFragment();this.addEventListener=n.addEventListener,this.dispatchEvent=function(t){var e=this["on"+t.type];"function"==typeof e&&e(t),n.dispatchEvent(t)},this.removeEventListener=n.removeEventListener}function c(t,e,n){if(!(e instanceof l))throw new TypeError("Failed to execute '"+n+"' on 'FileReader': parameter 1 is not of type 'Blob'.");t.result="",setTimeout(function(){this.readyState=a.LOADING,t.dispatchEvent(new Event("load")),t.dispatchEvent(new Event("loadend"))})}i.prototype.toString=function(){return"[object File]"},a.EMPTY=0,a.LOADING=1,a.DONE=2,a.prototype.error=null,a.prototype.onabort=null,a.prototype.onerror=null,a.prototype.onload=null,a.prototype.onloadend=null,a.prototype.onloadstart=null,a.prototype.onprogress=null,a.prototype.readAsDataURL=function(t){c(this,t,"readAsDataURL"),this.result="data:"+t.type+";base64,"+n(t._buffer)},a.prototype.readAsText=function(t){c(this,t,"readAsText"),this.result=e(t._buffer)},a.prototype.readAsArrayBuffer=function(t){c(this,t,"readAsText"),this.result=t._buffer.slice()},a.prototype.abort=function(){},URL.createObjectURL=function(t){return t instanceof l?"data:"+t.type+";base64,"+n(t._buffer):d.call(URL,t)},URL.revokeObjectURL=function(t){p&&p.call(URL,t)};var h=f.XMLHttpRequest&&f.XMLHttpRequest.prototype.send;h&&(XMLHttpRequest.prototype.send=function(t){t instanceof l?(this.setRequestHeader("Content-Type",t.type),h.call(this,e(t._buffer))):h.call(this,t)}),f.FileReader=a,f.File=i,f.Blob=l}()}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||"undefined"!=typeof global&&global||Function('return typeof this === "object" && this.content')()||Function("return this")());try{exports.GifWriter=function(b,t,e,n){var v=0,r=void 0===(n=void 0===n?{}:n).loop?null:n.loop,y=void 0===n.palette?null:n.palette;if(t<=0||e<=0||65535<t||65535<e)throw new Error("Width/Height invalid.");function w(t){var e=t.length;if(e<2||256<e||e&e-1)throw new Error("Invalid code/color length, must be power of 2 and 2 .. 256.");return e}b[v++]=71,b[v++]=73,b[v++]=70,b[v++]=56,b[v++]=57,b[v++]=97;var i=0,a=0;if(null!==y){for(var o=w(y);o>>=1;)++i;if(o=1<<i,--i,void 0!==n.background){if(o<=(a=n.background))throw new Error("Background index out of range.");if(0===a)throw new Error("Background index explicitly passed as 0.")}}if(b[v++]=255&t,b[v++]=t>>8&255,b[v++]=255&e,b[v++]=e>>8&255,b[v++]=(null!==y?128:0)|i,b[v++]=a,b[v++]=0,null!==y)for(var s=0,u=y.length;s<u;++s){var l=y[s];b[v++]=l>>16&255,b[v++]=l>>8&255,b[v++]=255&l}if(null!==r){if(r<0||65535<r)throw new Error("Loop count invalid.");b[v++]=33,b[v++]=255,b[v++]=11,b[v++]=78,b[v++]=69,b[v++]=84,b[v++]=83,b[v++]=67,b[v++]=65,b[v++]=80,b[v++]=69,b[v++]=50,b[v++]=46,b[v++]=48,b[v++]=3,b[v++]=1,b[v++]=255&r,b[v++]=r>>8&255,b[v++]=0}var x=!1;this.addFrame=function(t,e,n,r,i,a){if(!0===x&&(--v,x=!1),a=void 0===a?{}:a,t<0||e<0||65535<t||65535<e)throw new Error("x/y invalid.");if(n<=0||r<=0||65535<n||65535<r)throw new Error("Width/Height invalid.");if(i.length<n*r)throw new Error("Not enough pixels for the frame size.");var o=!0,s=a.palette;if(null==s&&(o=!1,s=y),null==s)throw new Error("Must supply either a local or global palette.");for(var u=w(s),l=0;u>>=1;)++l;u=1<<l;var c=void 0===a.delay?0:a.delay,h=void 0===a.disposal?0:a.disposal;if(h<0||3<h)throw new Error("Disposal out of range.");var f=!1,d=0;if(void 0!==a.transparent&&null!==a.transparent&&(f=!0,(d=a.transparent)<0||u<=d))throw new Error("Transparent color index.");if(0===h&&!f&&0===c||(b[v++]=33,b[v++]=249,b[v++]=4,b[v++]=h<<2|(!0===f?1:0),b[v++]=255&c,b[v++]=c>>8&255,b[v++]=d,b[v++]=0),b[v++]=44,b[v++]=255&t,b[v++]=t>>8&255,b[v++]=255&e,b[v++]=e>>8&255,b[v++]=255&n,b[v++]=n>>8&255,b[v++]=255&r,b[v++]=r>>8&255,b[v++]=!0===o?128|l-1:0,!0===o)for(var p=0,g=s.length;p<g;++p){var m=s[p];b[v++]=m>>16&255,b[v++]=m>>8&255,b[v++]=255&m}return v=function(e,n,t,r){e[n++]=t;var i=n++,a=1<<t,o=a-1,s=1+a,u=1+s,l=t+1,c=0,h=0;function f(t){for(;t<=c;)e[n++]=255&h,h>>=8,c-=8,n===i+256&&(e[i]=255,i=n++)}function d(t){h|=t<<c,c+=l,f(8)}var p=r[0]&o,g={};d(a);for(var m=1,b=r.length;m<b;++m){var v=r[m]&o,y=p<<8|v,w=g[y];if(void 0===w){for(h|=p<<c,c+=l;8<=c;)e[n++]=255&h,h>>=8,c-=8,n===i+256&&(e[i]=255,i=n++);4096===u?(d(a),u=1+s,l=t+1,g={}):(1<<l<=u&&++l,g[y]=u++),p=v}else p=w}return d(p),d(s),f(1),i+1===n?e[i]=0:(e[i]=n-i-1,e[n++]=0),n}(b,v,l<2?2:l,i)},this.end=function(){return!1===x&&(b[v++]=59,x=!0),v},this.getOutputBuffer=function(){return b},this.setOutputBuffer=function(t){b=t},this.getOutputBufferPosition=function(){return v},this.setOutputBufferPosition=function(t){v=t}},exports.GifReader=nt}catch(t){}
/*
   * Copyright (c) 2012 chick307 <<EMAIL>>
   *
   * Licensed under the MIT License.
   * http://opensource.org/licenses/mit-license
   */
/*
    Copyright (c) 2008, Adobe Systems Incorporated
    All rights reserved.

    Redistribution and use in source and binary forms, with or without 
    modification, are permitted provided that the following conditions are
    met:

    * Redistributions of source code must retain the above copyright notice, 
      this list of conditions and the following disclaimer.
    
    * Redistributions in binary form must reproduce the above copyright
      notice, this list of conditions and the following disclaimer in the 
      documentation and/or other materials provided with the distribution.
    
    * Neither the name of Adobe Systems Incorporated nor the names of its 
      contributors may be used to endorse or promote products derived from 
      this software without specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
    IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
    THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
    PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
    CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
    EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
    PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
    PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
    LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
    NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
    SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  */
function it(t){var w,x,N,L,e,h=Math.floor,A=new Array(64),_=new Array(64),S=new Array(64),P=new Array(64),b=new Array(65535),v=new Array(65535),Z=new Array(64),y=new Array(64),k=[],F=0,C=7,I=new Array(64),j=new Array(64),B=new Array(64),n=new Array(256),O=new Array(2048),E=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],M=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],T=[0,1,2,3,4,5,6,7,8,9,10,11],q=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],R=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],D=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],U=[0,1,2,3,4,5,6,7,8,9,10,11],z=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],H=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function r(t,e){for(var n=0,r=0,i=new Array,a=1;a<=16;a++){for(var o=1;o<=t[a];o++)i[e[r]]=[],i[e[r]][0]=n,i[e[r]][1]=a,r++,n++;n*=2}return i}function W(t){for(var e=t[0],n=t[1]-1;0<=n;)e&1<<n&&(F|=1<<C),n--,--C<0&&(255==F?(V(255),V(0)):V(F),C=7,F=0)}function V(t){k.push(t)}function G(t){V(t>>8&255),V(255&t)}function Y(t,e,n,r,i){for(var a,o=i[0],s=i[240],u=function(t,e){var n,r,i,a,o,s,u,l,c,h,f=0;for(c=0;c<8;++c){n=t[f],r=t[f+1],i=t[f+2],a=t[f+3],o=t[f+4],s=t[f+5],u=t[f+6];var d=n+(l=t[f+7]),p=n-l,g=r+u,m=r-u,b=i+s,v=i-s,y=a+o,w=a-o,x=d+y,N=d-y,L=g+b,A=g-b;t[f]=x+L,t[f+4]=x-L;var _=.707106781*(A+N);t[f+2]=N+_,t[f+6]=N-_;var S=.382683433*((x=w+v)-(A=m+p)),P=.5411961*x+S,k=1.306562965*A+S,F=.707106781*(L=v+m),C=p+F,I=p-F;t[f+5]=I+P,t[f+3]=I-P,t[f+1]=C+k,t[f+7]=C-k,f+=8}for(c=f=0;c<8;++c){n=t[f],r=t[f+8],i=t[f+16],a=t[f+24],o=t[f+32],s=t[f+40],u=t[f+48];var j=n+(l=t[f+56]),B=n-l,O=r+u,E=r-u,M=i+s,T=i-s,q=a+o,R=a-o,D=j+q,U=j-q,z=O+M,H=O-M;t[f]=D+z,t[f+32]=D-z;var W=.707106781*(H+U);t[f+16]=U+W,t[f+48]=U-W;var V=.382683433*((D=R+T)-(H=E+B)),G=.5411961*D+V,Y=1.306562965*H+V,J=.707106781*(z=T+E),X=B+J,K=B-J;t[f+40]=K+G,t[f+24]=K-G,t[f+8]=X+Y,t[f+56]=X-Y,f++}for(c=0;c<64;++c)h=t[c]*e[c],Z[c]=0<h?.5+h|0:h-.5|0;return Z}(t,e),l=0;l<64;++l)y[E[l]]=u[l];var c=y[0]-n;n=y[0],0==c?W(r[0]):(W(r[v[a=32767+c]]),W(b[a]));for(var h=63;0<h&&0==y[h];)h--;if(0==h)return W(o),n;for(var f,d=1;d<=h;){for(var p=d;0==y[d]&&d<=h;)++d;var g=d-p;if(16<=g){f=g>>4;for(var m=1;m<=f;++m)W(s);g&=15}a=32767+y[d],W(i[(g<<4)+v[a]]),W(b[a]),d++}return 63!=h&&W(o),n}function J(t){(t=Math.min(Math.max(t,1),100),e!=t)&&(function(t){for(var e=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],n=0;n<64;n++){var r=h((e[n]*t+50)/100);r=Math.min(Math.max(r,1),255),A[E[n]]=r}for(var i=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],a=0;a<64;a++){var o=h((i[a]*t+50)/100);o=Math.min(Math.max(o,1),255),_[E[a]]=o}for(var s=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],u=0,l=0;l<8;l++)for(var c=0;c<8;c++)S[u]=1/(A[E[u]]*s[l]*s[c]*8),P[u]=1/(_[E[u]]*s[l]*s[c]*8),u++}(t<50?Math.floor(5e3/t):Math.floor(200-2*t)),e=t)}this.encode=function(t,e){e&&J(e),k=new Array,F=0,C=7,G(65496),G(65504),G(16),V(74),V(70),V(73),V(70),V(0),V(1),V(1),V(0),G(1),G(1),V(0),V(0),function(){G(65499),G(132),V(0);for(var t=0;t<64;t++)V(A[t]);V(1);for(var e=0;e<64;e++)V(_[e])}(),function(t,e){G(65472),G(17),V(8),G(e),G(t),V(3),V(1),V(17),V(0),V(2),V(17),V(1),V(3),V(17),V(1)}(t.width,t.height),function(){G(65476),G(418),V(0);for(var t=0;t<16;t++)V(M[t+1]);for(var e=0;e<=11;e++)V(T[e]);V(16);for(var n=0;n<16;n++)V(q[n+1]);for(var r=0;r<=161;r++)V(R[r]);V(1);for(var i=0;i<16;i++)V(D[i+1]);for(var a=0;a<=11;a++)V(U[a]);V(17);for(var o=0;o<16;o++)V(z[o+1]);for(var s=0;s<=161;s++)V(H[s])}(),G(65498),G(12),V(3),V(1),V(0),V(2),V(17),V(3),V(17),V(0),V(63),V(0);var n=0,r=0,i=0;F=0,C=7,this.encode.displayName="_encode_";for(var a,o,s,u,l,c,h,f,d,p=t.data,g=t.width,m=t.height,b=4*g,v=0;v<m;){for(a=0;a<b;){for(l=b*v+a,h=-1,d=f=0;d<64;d++)c=l+(f=d>>3)*b+(h=4*(7&d)),m<=v+f&&(c-=b*(v+1+f-m)),b<=a+h&&(c-=a+h-b+4),o=p[c++],s=p[c++],u=p[c++],I[d]=(O[o]+O[s+256>>0]+O[u+512>>0]>>16)-128,j[d]=(O[o+768>>0]+O[s+1024>>0]+O[u+1280>>0]>>16)-128,B[d]=(O[o+1280>>0]+O[s+1536>>0]+O[u+1792>>0]>>16)-128;n=Y(I,S,n,w,N),r=Y(j,P,r,x,L),i=Y(B,P,i,x,L),a+=32}v+=8}if(0<=C){var y=[];y[1]=C+1,y[0]=(1<<C+1)-1,W(y)}return G(65497),new Uint8Array(k)},t=t||50,function(){for(var t=String.fromCharCode,e=0;e<256;e++)n[e]=t(e)}(),w=r(M,T),x=r(D,U),N=r(q,R),L=r(z,H),function(){for(var t=1,e=2,n=1;n<=15;n++){for(var r=t;r<e;r++)v[32767+r]=n,b[32767+r]=[],b[32767+r][1]=n,b[32767+r][0]=r;for(var i=-(e-1);i<=-t;i++)v[32767+i]=n,b[32767+i]=[],b[32767+i][1]=n,b[32767+i][0]=e-1+i;t<<=1,e<<=1}}(),function(){for(var t=0;t<256;t++)O[t]=19595*t,O[t+256>>0]=38470*t,O[t+512>>0]=7471*t+32768,O[t+768>>0]=-11059*t,O[t+1024>>0]=-21709*t,O[t+1280>>0]=32768*t+8421375,O[t+1536>>0]=-27439*t,O[t+1792>>0]=-5329*t}(),J(t)}U.API.adler32cs=(j="function"==typeof ArrayBuffer&&"function"==typeof Uint8Array,B=null,O=function(){if(!j)return function(){return!1};try{var t={};"function"==typeof t.Buffer&&(B=t.Buffer)}catch(t){}return function(t){return t instanceof ArrayBuffer||null!==B&&t instanceof B}}(),E=null!==B?function(t){return new B(t,"utf8").toString("binary")}:function(t){return unescape(encodeURIComponent(t))},M=function(t,e){for(var n=65535&t,r=t>>>16,i=0,a=e.length;i<a;i++)n=(n+(255&e.charCodeAt(i)))%65521,r=(r+n)%65521;return(r<<16|n)>>>0},T=function(t,e){for(var n=65535&t,r=t>>>16,i=0,a=e.length;i<a;i++)n=(n+e[i])%65521,r=(r+n)%65521;return(r<<16|n)>>>0},R=(q={}).Adler32=function(){var n=function(t){if(!(this instanceof n))throw new TypeError("Constructor cannot called be as a function.");if(!isFinite(t=null===t?1:+t))throw new Error("First arguments needs to be a finite number.");this.checksum=t>>>0},e=n.prototype={};return(e.constructor=n).from=function(t){return t.prototype=e,t}(function(t){if(!(this instanceof n))throw new TypeError("Constructor cannot called be as a function.");if(null===t)throw new Error("First argument needs to be a string.");this.checksum=M(1,t.toString())}),n.fromUtf8=function(t){return t.prototype=e,t}(function(t){if(!(this instanceof n))throw new TypeError("Constructor cannot called be as a function.");if(null===t)throw new Error("First argument needs to be a string.");var e=E(t.toString());this.checksum=M(1,e)}),j&&(n.fromBuffer=function(t){return t.prototype=e,t}(function(t){if(!(this instanceof n))throw new TypeError("Constructor cannot called be as a function.");if(!O(t))throw new Error("First argument needs to be ArrayBuffer.");var e=new Uint8Array(t);return this.checksum=T(1,e)})),e.update=function(t){if(null===t)throw new Error("First argument needs to be a string.");return t=t.toString(),this.checksum=M(this.checksum,t)},e.updateUtf8=function(t){if(null===t)throw new Error("First argument needs to be a string.");var e=E(t.toString());return this.checksum=M(this.checksum,e)},j&&(e.updateBuffer=function(t){if(!O(t))throw new Error("First argument needs to be ArrayBuffer.");var e=new Uint8Array(t);return this.checksum=T(this.checksum,e)}),e.clone=function(){return new R(this.checksum)},n}(),q.from=function(t){if(null===t)throw new Error("First argument needs to be a string.");return M(1,t.toString())},q.fromUtf8=function(t){if(null===t)throw new Error("First argument needs to be a string.");var e=E(t.toString());return M(1,e)},j&&(q.fromBuffer=function(t){if(!O(t))throw new Error("First argument need to be ArrayBuffer.");var e=new Uint8Array(t);return T(1,e)}),q),function(t){t.__bidiEngine__=t.prototype.__bidiEngine__=function(t){var p,g,h,f,i,a,o,s=e,m=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],b=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],v={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},u={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},l=["(",")","(","<",">","<","[","]","[","{","}","{","«","»","«","‹","›","‹","⁅","⁆","⁅","⁽","⁾","⁽","₍","₎","₍","≤","≥","≤","〈","〉","〈","﹙","﹚","﹙","﹛","﹜","﹛","﹝","﹞","﹝","﹤","﹥","﹤"],c=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),y=!1,w=0;this.__bidiEngine__={};function x(t){var e=t.charCodeAt(),n=e>>8,r=u[n];return void 0!==r?s[256*r+(255&e)]:252==n||253==n?"AL":c.test(n)?"L":8==n?"R":"N"}function N(t,e,n,r){var i,a,o,s,u=e[r];switch(u){case"L":case"R":y=!1;break;case"N":case"AN":break;case"EN":y&&(u="AN");break;case"AL":y=!0,u="R";break;case"WS":u="N";break;case"CS":r<1||r+1>=e.length||"EN"!==(i=n[r-1])&&"AN"!==i||"EN"!==(a=e[r+1])&&"AN"!==a?u="N":y&&(a="AN"),u=a===i?a:"N";break;case"ES":u="EN"===(i=0<r?n[r-1]:"B")&&r+1<e.length&&"EN"===e[r+1]?"EN":"N";break;case"ET":if(0<r&&"EN"===n[r-1]){u="EN";break}if(y){u="N";break}for(o=r+1,s=e.length;o<s&&"ET"===e[o];)o++;u=o<s&&"EN"===e[o]?"EN":"N";break;case"NSM":if(h&&!f){for(s=e.length,o=r+1;o<s&&"NSM"===e[o];)o++;if(o<s){var l=t[r],c=1425<=l&&l<=2303||64286===l;if(i=e[o],c&&("R"===i||"AL"===i)){u="R";break}}}u=r<1||"B"===(i=e[r-1])?"N":n[r-1];break;case"B":p=!(y=!1),u=w;break;case"S":g=!0,u="N";break;case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":y=!1;break;case"BN":u="N"}return u}function d(t,e,n){var r=t.split("");return n&&S(r,n,{hiLevel:w}),r.reverse(),e&&e.reverse(),r.join("")}function L(t,e,n,r,i){if(!(i.hiLevel<t)){if(1===t&&1===w&&!p)return e.reverse(),void(n&&n.reverse());for(var a,o,s,u,l=e.length,c=0;c<l;){if(r[c]>=t){for(s=c+1;s<l&&r[s]>=t;)s++;for(u=c,o=s-1;u<o;u++,o--)a=e[u],e[u]=e[o],e[o]=a,n&&(a=n[u],n[u]=n[o],n[o]=a);c=s}c++}}}function A(t,e,n){var r=t.split(""),i={hiLevel:w};return n||(n=[]),S(r,n,i),function(t,e,n){if(0!==n.hiLevel&&o)for(var r,i=0;i<t.length;i++)1!==e[i]||0<=(r=l.indexOf(t[i]))&&(t[i]=l[r+1])}(r,n,i),L(2,r,e,n,i),L(1,r,e,n,i),r.join("")}var _=function(t){for(var e,n=0;n<t.length;n++){if("L"===(e=x(t.charAt(n))))return!1;if("R"===e)return!0}return!1},S=function(t,e,n){var r,i,a,o,s,u=-1,l=t.length,c=0,h=[],f=w?b:m,d=[];for(g=p=y=!1,i=0;i<l;i++)d[i]=x(t[i]);for(a=0;a<l;a++){if(s=c,h[a]=N(t,d,h,a),r=240&(c=f[s][v[h[a]]]),c&=15,e[a]=o=f[c][5],0<r)if(16==r){for(i=u;i<a;i++)e[i]=1;u=-1}else u=-1;if(f[c][6])-1===u&&(u=a);else if(-1<u){for(i=u;i<a;i++)e[i]=o;u=-1}"B"===d[a]&&(e[a]=0),n.hiLevel|=o}g&&function(t,e,n){for(var r=0;r<n;r++)if("S"===t[r]){e[r]=w;for(var i=r-1;0<=i&&"WS"===t[i];i--)e[i]=w}}(d,e,l)};return this.__bidiEngine__.doBidiReorder=function(t,e,n){if(function(t,e){if(e)for(var n=0;n<t.length;n++)e[n]=n;void 0===f&&(f=_(t)),void 0===a&&(a=_(t))}(t,e),h||!i||a)if(h&&i&&f^a)w=f?1:0,t=d(t,e,n);else if(!h&&i&&a)w=f?1:0,t=A(t,e,n),t=d(t,e);else if(!h||f||i||a){if(h&&!i&&f^a)t=d(t,e),t=f?(w=0,A(t,e,n)):(w=1,t=A(t,e,n),d(t,e));else if(h&&f&&!i&&a)w=1,t=A(t,e,n),t=d(t,e);else if(!h&&!i&&f^a){var r=o;f?(w=1,t=A(t,e,n),w=0,o=!1,t=A(t,e,n),o=r):(w=0,t=A(t,e,n),t=d(t,e),o=!(w=1),t=A(t,e,n),o=r,t=d(t,e))}}else w=0,t=A(t,e,n);else w=f?1:0,t=A(t,e,n);return t},this.__bidiEngine__.setOptions=function(t){t&&(h=t.isInputVisual,i=t.isOutputVisual,f=t.isInputRtl,a=t.isOutputRtl,o=t.isSymmetricSwapping)},this.__bidiEngine__.setOptions(t),this.__bidiEngine__};var e=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],a=new t.__bidiEngine__({isInputVisual:!0});t.API.events.push(["postProcessText",function(t){var e=t.text,n=(t.x,t.y,t.options||{}),r=(t.mutex,n.lang,[]);if(n.isInputVisual="boolean"!=typeof n.isInputVisual||n.isInputVisual,a.setOptions(n),"[object Array]"===Object.prototype.toString.call(e)){var i=0;for(r=[],i=0;i<e.length;i+=1)"[object Array]"===Object.prototype.toString.call(e[i])?r.push([a.doBidiReorder(e[i][0]),e[i][1],e[i][2]]):r.push([a.doBidiReorder(e[i])]);t.text=r}else t.text=a.doBidiReorder(e);a.setOptions({isInputVisual:!0})}])}(U);try{exports.JPEGEncoder=it}catch(t){}function at(t,e){if(this.pos=0,this.buffer=t,this.datav=new DataView(t.buffer),this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,-1===["BM","BA","CI","CP","IC","PT"].indexOf(this.flag))throw new Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}at.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,16===this.bitPP&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var t=0===this.colors?1<<this.bitPP:this.colors;this.palette=new Array(t);for(var e=0;e<t;e++){var n=this.datav.getUint8(this.pos++,!0),r=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0);this.palette[e]={red:i,green:r,blue:n,quad:a}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},at.prototype.parseBGR=function(){this.pos=this.offset;try{var t="bit"+this.bitPP,e=this.width*this.height*4;this.data=new Uint8Array(e),this[t]()}catch(t){console.log("bit decode error:"+t)}},at.prototype.bit1=function(){var t,e=Math.ceil(this.width/8),n=e%4;for(t=this.height-1;0<=t;t--){for(var r=this.bottom_up?t:this.height-1-t,i=0;i<e;i++)for(var a=this.datav.getUint8(this.pos++,!0),o=r*this.width*4+8*i*4,s=0;s<8&&8*i+s<this.width;s++){var u=this.palette[a>>7-s&1];this.data[o+4*s]=u.blue,this.data[o+4*s+1]=u.green,this.data[o+4*s+2]=u.red,this.data[o+4*s+3]=255}0!=n&&(this.pos+=4-n)}},at.prototype.bit4=function(){for(var t=Math.ceil(this.width/2),e=t%4,n=this.height-1;0<=n;n--){for(var r=this.bottom_up?n:this.height-1-n,i=0;i<t;i++){var a=this.datav.getUint8(this.pos++,!0),o=r*this.width*4+2*i*4,s=a>>4,u=15&a,l=this.palette[s];if(this.data[o]=l.blue,this.data[1+o]=l.green,this.data[2+o]=l.red,this.data[3+o]=255,2*i+1>=this.width)break;l=this.palette[u],this.data[4+o]=l.blue,this.data[4+o+1]=l.green,this.data[4+o+2]=l.red,this.data[4+o+3]=255}0!=e&&(this.pos+=4-e)}},at.prototype.bit8=function(){for(var t=this.width%4,e=this.height-1;0<=e;e--){for(var n=this.bottom_up?e:this.height-1-e,r=0;r<this.width;r++){var i=this.datav.getUint8(this.pos++,!0),a=n*this.width*4+4*r;if(i<this.palette.length){var o=this.palette[i];this.data[a]=o.red,this.data[1+a]=o.green,this.data[2+a]=o.blue,this.data[3+a]=255}else this.data[a]=255,this.data[1+a]=255,this.data[2+a]=255,this.data[3+a]=255}0!=t&&(this.pos+=4-t)}},at.prototype.bit15=function(){for(var t=this.width%3,e=parseInt("11111",2),n=this.height-1;0<=n;n--){for(var r=this.bottom_up?n:this.height-1-n,i=0;i<this.width;i++){var a=this.datav.getUint16(this.pos,!0);this.pos+=2;var o=(a&e)/e*255|0,s=(a>>5&e)/e*255|0,u=(a>>10&e)/e*255|0,l=a>>15?255:0,c=r*this.width*4+4*i;this.data[c]=u,this.data[1+c]=s,this.data[2+c]=o,this.data[3+c]=l}this.pos+=t}},at.prototype.bit16=function(){for(var t=this.width%3,e=parseInt("11111",2),n=parseInt("111111",2),r=this.height-1;0<=r;r--){for(var i=this.bottom_up?r:this.height-1-r,a=0;a<this.width;a++){var o=this.datav.getUint16(this.pos,!0);this.pos+=2;var s=(o&e)/e*255|0,u=(o>>5&n)/n*255|0,l=(o>>11)/e*255|0,c=i*this.width*4+4*a;this.data[c]=l,this.data[1+c]=u,this.data[2+c]=s,this.data[3+c]=255}this.pos+=t}},at.prototype.bit24=function(){for(var t=this.height-1;0<=t;t--){for(var e=this.bottom_up?t:this.height-1-t,n=0;n<this.width;n++){var r=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),o=e*this.width*4+4*n;this.data[o]=a,this.data[1+o]=i,this.data[2+o]=r,this.data[3+o]=255}this.pos+=this.width%4}},at.prototype.bit32=function(){for(var t=this.height-1;0<=t;t--)for(var e=this.bottom_up?t:this.height-1-t,n=0;n<this.width;n++){var r=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),s=e*this.width*4+4*n;this.data[s]=a,this.data[1+s]=i,this.data[2+s]=r,this.data[3+s]=o}},at.prototype.getData=function(){return this.data};try{exports.BmpDecoder=at}catch(t){}function ot(t){function Mi(t){if(!t)throw Error("assert :P")}function Ti(t,e,n){for(var r=0;r<4;r++)if(t[e+r]!=n.charCodeAt(r))return!0;return!1}function qi(t,e,n,r,i){for(var a=0;a<i;a++)t[e+a]=n[r+a]}function Ri(t,e,n,r){for(var i=0;i<r;i++)t[e+i]=n}function Di(t){return new Int32Array(t)}function Ui(t,e){for(var n=[],r=0;r<t;r++)n.push(new e);return n}function zi(t,o){var e=[];return function t(e,n,r){for(var i=r[n],a=0;a<i&&(e.push(r.length>n+1?[]:new o),!(r.length<n+1));a++)t(e[a],n+1,r)}(e,0,t),e}function g(t,e){for(var n="",r=0;r<4;r++)n+=String.fromCharCode(t[e++]);return n}function m(t,e){return(t[e+0]<<0|t[e+1]<<8|t[e+2]<<16)>>>0}function b(t,e){return(t[e+0]<<0|t[e+1]<<8|t[e+2]<<16|t[e+3]<<24)>>>0}new(ot=function(){var m=this;function y(t,e){for(var n=1<<e-1>>>0;t&n;)n>>>=1;return n?(t&n-1)+n:t}function w(t,e,n,r,i){for(Mi(!(r%n));t[e+(r-=n)]=i,0<r;);}function $t(t,e,n,r,i){if(Mi(i<=2328),i<=512)var a=Di(512);else if(null==(a=Di(i)))return 0;return function(t,e,n,r,i,a){var o,s,u=e,l=1<<n,c=Di(16),h=Di(16);for(Mi(0!=i),Mi(null!=r),Mi(null!=t),Mi(0<n),s=0;s<i;++s){if(15<r[s])return 0;++c[r[s]]}if(c[0]==i)return 0;for(h[1]=0,o=1;o<15;++o){if(c[o]>1<<o)return 0;h[o+1]=h[o]+c[o]}for(s=0;s<i;++s)o=r[s],0<r[s]&&(a[h[o]++]=s);if(1==h[15])return(r=new Qt).g=0,r.value=a[0],w(t,u,1,l,r),l;var f,d=-1,p=l-1,g=0,m=1,b=1,v=1<<n;for(s=0,o=1,i=2;o<=n;++o,i<<=1){if(m+=b<<=1,(b-=c[o])<0)return 0;for(;0<c[o];--c[o])(r=new Qt).g=o,r.value=a[s++],w(t,u+g,i,v,r),g=y(g,o)}for(o=n+1,i=2;o<=15;++o,i<<=1){if(m+=b<<=1,(b-=c[o])<0)return 0;for(;0<c[o];--c[o]){if(r=new Qt,(g&p)!=d){for(u+=v,f=1<<(d=o)-n;d<15&&!((f-=c[d])<=0);)++d,f<<=1;l+=v=1<<(f=d-n),t[e+(d=g&p)].g=f+n,t[e+d].value=u-e-d}r.g=o-n,r.value=a[s++],w(t,u+(g>>n),i,v,r),g=y(g,o)}}return m!=2*h[15]-1?0:l}(t,e,n,r,i,a)}function Qt(){this.value=this.g=0}function t(){this.value=this.g=0}function te(){this.G=Ui(5,Qt),this.H=Di(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=Ui(jn,t)}function p(t,e,n,r){Mi(null!=t),Mi(null!=e),Mi(r<2147483648),t.Ca=254,t.I=0,t.b=-8,t.Ka=0,t.oa=e,t.pa=n,t.Jd=e,t.Yc=n+r,t.Zc=4<=r?n+r-4+1:n,v(t)}function g(t,e){for(var n=0;0<e--;)n|=T(t,128)<<e;return n}function b(t,e){var n=g(t,e);return x(t)?-n:n}function k(t,e,n,r){var i,a=0;for(Mi(null!=t),Mi(null!=e),Mi(r<4294967288),t.Sb=r,t.Ra=0,t.u=0,4<r&&(r=4),i=t.h=0;i<r;++i)a+=e[n+i]<<8*i;t.Ra=a,t.bb=r,t.oa=e,t.pa=n}function r(t){for(;8<=t.u&&t.bb<t.Sb;)t.Ra>>>=8,t.Ra+=t.oa[t.pa+t.bb]<<En-8>>>0,++t.bb,t.u-=8;A(t)&&(t.h=1,t.u=0)}function ee(t,e){if(Mi(0<=e),!t.h&&e<=On){var n=ne(t)&Bn[e];return t.u+=e,r(t),n}return t.h=1,t.u=0}function e(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function F(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function ne(t){return t.Ra>>>(t.u&En-1)>>>0}function A(t){return Mi(t.bb<=t.Sb),t.h||t.bb==t.Sb&&t.u>En}function re(t,e){t.u=e,t.h=A(t)}function ie(t){t.u>=Mn&&(Mi(t.u>=Mn),r(t))}function v(t){Mi(null!=t&&null!=t.oa),t.pa<t.Zc?(t.I=(t.oa[t.pa++]|t.I<<8)>>>0,t.b+=8):(Mi(null!=t&&null!=t.oa),t.pa<t.Yc?(t.b+=8,t.I=t.oa[t.pa++]|t.I<<8):t.Ka?t.b=0:(t.I<<=8,t.b+=8,t.Ka=1))}function x(t){return g(t,1)}function T(t,e){var n=t.Ca;t.b<0&&v(t);var r=t.b,i=n*e>>>8,a=(t.I>>>r>i)+0;for(a?(n-=i,t.I-=i+1<<r>>>0):n=i+1,r=n,i=0;256<=r;)i+=8,r>>=8;return r=7^i+Tn[r],t.b-=r,t.Ca=(n<<r)-1,a}function o(t,e,n){t[e+0]=n>>24&255,t[e+1]=n>>16&255,t[e+2]=n>>8&255,t[e+3]=n>>0&255}function n(t,e){return t[e+0]<<0|t[e+1]<<8}function C(t,e){return n(t,e)|t[e+2]<<16}function I(t,e){return n(t,e)|n(t,e+2)<<16}function ae(t,e){var n=1<<e;return Mi(null!=t),Mi(0<e),t.X=Di(n),null==t.X?0:(t.Mb=32-e,t.Xa=e,1)}function _(t,e){Mi(null!=t),Mi(null!=e),Mi(t.Xa==e.Xa),qi(e.X,0,t.X,0,1<<e.Xa)}function i(){this.X=[],this.Xa=this.Mb=0}function N(t,e,n,r){Mi(null!=n),Mi(null!=r);var i=n[0],a=r[0];return 0==i&&(i=(t*a+e/2)/e),0==a&&(a=(e*i+t/2)/t),i<=0||a<=0?0:(n[0]=i,r[0]=a,1)}function oe(t,e){return t+(1<<e)-1>>>e}function se(t,e){return((4278255360&t)+(4278255360&e)>>>0&4278255360)+((16711935&t)+(16711935&e)>>>0&16711935)>>>0}function a(l,t){m[t]=function(t,e,n,r,i,a,o){var s;for(s=0;s<i;++s){var u=m[l](a[o+s-1],n,r+s);a[o+s]=se(t[e+s],u)}}}function j(){this.ud=this.hd=this.jd=0}function s(t,e){return((4278124286&(t^e))>>>1)+(t&e)>>>0}function u(t){return 0<=t&&t<256?t:t<0?0:255<t?255:void 0}function l(t,e){return u(t+(t-e+.5>>1))}function c(t,e,n){return Math.abs(e-n)-Math.abs(t-n)}function B(t,e,n,r,i,a,o){for(r=a[o-1],n=0;n<i;++n)a[o+n]=r=se(t[e+n],r)}function h(t,e,n,r,i){var a;for(a=0;a<n;++a){var o=t[e+a],s=o>>8&255,u=16711935&(u=(u=16711935&o)+((s<<16)+s));r[i+a]=(4278255360&o)+u>>>0}}function O(t,e){e.jd=t>>0&255,e.hd=t>>8&255,e.ud=t>>16&255}function f(t,e,n,r,i,a){var o;for(o=0;o<r;++o){var s=e[n+o],u=s>>>8,l=s,c=255&(c=(c=s>>>16)+((t.jd<<24>>24)*(u<<24>>24)>>>5));l=255&(l=(l=l+((t.hd<<24>>24)*(u<<24>>24)>>>5))+((t.ud<<24>>24)*(c<<24>>24)>>>5));i[a+o]=(4278255360&s)+(c<<16)+l}}function d(t,e,d,p,g){m[e]=function(t,e,n,r,i,a,o,s,u){for(r=o;r<s;++r)for(o=0;o<u;++o)i[a++]=g(n[p(t[e++])])},m[t]=function(t,e,n,r,i,a,o){var s=8>>t.b,u=t.Ea,l=t.K[0],c=t.w;if(s<8)for(t=(1<<t.b)-1,c=(1<<s)-1;e<n;++e){var h,f=0;for(h=0;h<u;++h)h&t||(f=p(r[i++])),a[o++]=g(l[f&c]),f>>=s}else m["VP8LMapColor"+d](r,i,l,c,a,o,e,n,u)}}function L(t,e,n,r,i){for(n=e+n;e<n;){var a=t[e++];r[i++]=a>>16&255,r[i++]=a>>8&255,r[i++]=a>>0&255}}function S(t,e,n,r,i){for(n=e+n;e<n;){var a=t[e++];r[i++]=a>>16&255,r[i++]=a>>8&255,r[i++]=a>>0&255,r[i++]=a>>24&255}}function P(t,e,n,r,i){for(n=e+n;e<n;){var a=(o=t[e++])>>16&240|o>>12&15,o=o>>0&240|o>>28&15;r[i++]=a,r[i++]=o}}function E(t,e,n,r,i){for(n=e+n;e<n;){var a=(o=t[e++])>>16&248|o>>13&7,o=o>>5&224|o>>3&31;r[i++]=a,r[i++]=o}}function M(t,e,n,r,i){for(n=e+n;e<n;){var a=t[e++];r[i++]=a>>0&255,r[i++]=a>>8&255,r[i++]=a>>16&255}}function q(t,e,n,r,i,a){if(0==a)for(n=e+n;e<n;)o(r,((a=t[e++])[0]>>24|a[1]>>8&65280|a[2]<<8&16711680|a[3]<<24)>>>0),i+=32;else qi(r,i,t,e,n)}function R(t,e){m[e][0]=m[t+"0"],m[e][1]=m[t+"1"],m[e][2]=m[t+"2"],m[e][3]=m[t+"3"],m[e][4]=m[t+"4"],m[e][5]=m[t+"5"],m[e][6]=m[t+"6"],m[e][7]=m[t+"7"],m[e][8]=m[t+"8"],m[e][9]=m[t+"9"],m[e][10]=m[t+"10"],m[e][11]=m[t+"11"],m[e][12]=m[t+"12"],m[e][13]=m[t+"13"],m[e][14]=m[t+"0"],m[e][15]=m[t+"0"]}function D(t){return t==Mr||t==Tr||t==qr||t==Rr}function U(){this.eb=[],this.size=this.A=this.fb=0}function z(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function H(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new U,this.f.kb=new z,this.sd=null}function W(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function V(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function G(t){return alert("todo:WebPSamplerProcessPlane"),t.T}function Y(t,e){var n=t.T,r=e.ba.f.RGBA,i=r.eb,a=r.fb+t.ka*r.A,o=ci[e.ba.S],s=t.y,u=t.O,l=t.f,c=t.N,h=t.ea,f=t.W,d=e.cc,p=e.dc,g=e.Mc,m=e.Nc,b=t.ka,v=t.ka+t.T,y=t.U,w=y+1>>1;for(0==b?o(s,u,null,null,l,c,h,f,l,c,h,f,i,a,null,null,y):(o(e.ec,e.fc,s,u,d,p,g,m,l,c,h,f,i,a-r.A,i,a,y),++n);b+2<v;b+=2)d=l,p=c,g=h,m=f,c+=t.Rc,f+=t.Rc,a+=2*r.A,o(s,(u+=2*t.fa)-t.fa,s,u,d,p,g,m,l,c,h,f,i,a-r.A,i,a,y);return u+=t.fa,t.j+v<t.o?(qi(e.ec,e.fc,s,u,y),qi(e.cc,e.dc,l,c,w),qi(e.Mc,e.Nc,h,f,w),n--):1&v||o(s,u,null,null,l,c,h,f,l,c,h,f,i,a+r.A,null,null,y),n}function J(t,e,n){var r=t.F,i=[t.J];if(null!=r){var a=t.U,o=e.ba.S,s=o==Br||o==qr;e=e.ba.f.RGBA;var u=[0],l=t.ka;u[0]=t.T,t.Kb&&(0==l?--u[0]:(--l,i[0]-=t.width),t.j+t.ka+t.T==t.o&&(u[0]=t.o-t.j-l));var c=e.eb;l=e.fb+l*e.A;t=vr(r,i[0],t.width,a,u,c,l+(s?0:3),e.A),Mi(n==u),t&&D(o)&&mr(c,l,s,a,u,e.A)}return 0}function X(t){var e=t.ma,n=e.ba.S,r=n<11,i=n==Cr||n==jr||n==Br||n==Or||12==n||D(n);if(e.memory=null,e.Ib=null,e.Jb=null,e.Nd=null,!Fn(e.Oa,t,i?11:12))return 0;if(i&&D(n)&&fn(),t.da)alert("todo:use_scaling");else{if(r){if(e.Ib=G,t.Kb){if(n=t.U+1>>1,e.memory=Di(t.U+2*n),null==e.memory)return 0;e.ec=e.memory,e.fc=0,e.cc=e.ec,e.dc=e.fc+t.U,e.Mc=e.cc,e.Nc=e.dc+n,e.Ib=Y,fn()}}else alert("todo:EmitYUV");i&&(e.Jb=J,r&&cn())}if(r&&!Ai){for(t=0;t<256;++t)_i[t]=89858*(t-128)+yi>>vi,ki[t]=-22014*(t-128)+yi,Pi[t]=-45773*(t-128),Si[t]=113618*(t-128)+yi>>vi;for(t=wi;t<xi;++t)e=76283*(t-16)+yi>>vi,Fi[t-wi]=At(e,255),Ci[t-wi]=At(e+8>>4,15);Ai=1}return 1}function K(t){var e=t.ma,n=t.U,r=t.T;return Mi(!(1&t.ka)),n<=0||r<=0?0:(n=e.Ib(t,e),null!=e.Jb&&e.Jb(t,e,n),e.Dc+=n,1)}function Z(t){t.ma.memory=null}function $(t,e,n,r){return 47!=ee(t,8)?0:(e[0]=ee(t,14)+1,n[0]=ee(t,14)+1,r[0]=ee(t,1),0!=ee(t,3)?0:!t.h)}function Q(t,e){if(t<4)return t+1;var n=t-2>>1;return(2+(1&t)<<n)+ee(e,n)+1}function tt(t,e){return 120<e?e-120:1<=(n=((n=Vr[e-1])>>4)*t+(8-(15&n)))?n:1;var n}function et(t,e,n){var r=ne(n),i=t[e+=255&r].g-8;return 0<i&&(re(n,n.u+8),r=ne(n),e+=t[e].value,e+=r&(1<<i)-1),re(n,n.u+t[e].g),t[e].value}function ue(t,e,n){return n.g+=t.g,n.value+=t.value<<e>>>0,Mi(n.g<=8),t.g}function nt(t,e,n){var r=t.xc;return Mi((e=0==r?0:t.vc[t.md*(n>>r)+(e>>r)])<t.Wb),t.Ya[e]}function rt(t,e,n,r){var i=t.ab,a=t.c*e,o=t.C;e=o+e;var s=n,u=r;for(r=t.Ta,n=t.Ua;0<i--;){var l=t.gc[i],c=o,h=e,f=s,d=u,p=(u=r,s=n,l.Ea);switch(Mi(c<h),Mi(h<=l.nc),l.hc){case 2:Dn(f,d,(h-c)*p,u,s);break;case 0:var g=c,m=h,b=u,v=s,y=(A=l).Ea;0==g&&(qn(f,d,null,null,1,b,v),B(f,d+1,0,0,y-1,b,v+1),d+=y,v+=y,++g);for(var w=1<<A.b,x=w-1,N=oe(y,A.b),L=A.K,A=A.w+(g>>A.b)*N;g<m;){var _=L,S=A,P=1;for(Rn(f,d,b,v-y,1,b,v);P<y;){var k=(P&~x)+w;y<k&&(k=y),(0,Vn[_[S++]>>8&15])(f,d+ +P,b,v+P-y,k-P,b,v+P),P=k}d+=y,v+=y,++g&x||(A+=N)}h!=l.nc&&qi(u,s-p,u,s+(h-c-1)*p,p);break;case 1:for(p=f,m=d,y=(f=l.Ea)-(v=f&~(b=(d=1<<l.b)-1)),g=oe(f,l.b),w=l.K,l=l.w+(c>>l.b)*g;c<h;){for(x=w,N=l,L=new j,A=m+v,_=m+f;m<A;)O(x[N++],L),Gn(L,p,m,d,u,s),m+=d,s+=d;m<_&&(O(x[N++],L),Gn(L,p,m,y,u,s),m+=y,s+=y),++c&b||(l+=g)}break;case 3:if(f==u&&d==s&&0<l.b){for(f=p=s+(h-c)*p-(v=(h-c)*oe(l.Ea,l.b)),d=m=u,b=s,g=[],v=(y=v)-1;0<=v;--v)g[v]=d[b+v];for(v=y-1;0<=v;--v)m[f+v]=g[v];Un(l,c,h,u,p,u,s)}else Un(l,c,h,f,d,u,s)}s=r,u=n}u!=n&&qi(r,n,s,u,a)}function it(t,e){var n=t.V,r=t.Ba+t.c*t.C,i=e-t.C;if(Mi(e<=t.l.o),Mi(i<=16),0<i){var a=t.l,o=t.Ta,s=t.Ua,u=a.width;if(rt(t,i,n,r),i=s=[s],Mi((n=t.C)<(r=e)),Mi(a.v<a.va),r>a.o&&(r=a.o),n<a.j){var l=a.j-n;n=a.j;i[0]+=l*u}if(n=r<=n?0:(i[0]+=4*a.v,a.ka=n-a.j,a.U=a.va-a.v,a.T=r-n,1)){if(s=s[0],(n=t.ca).S<11){var c=n.f.RGBA,h=(r=n.S,i=a.U,a=a.T,l=c.eb,c.A),f=a;for(c=c.fb+t.Ma*c.A;0<f--;){var d=o,p=s,g=i,m=l,b=c;switch(r){case Fr:Yn(d,p,g,m,b);break;case Cr:Jn(d,p,g,m,b);break;case Mr:Jn(d,p,g,m,b),mr(m,b,0,g,1,0);break;case Ir:Zn(d,p,g,m,b);break;case jr:q(d,p,g,m,b,1);break;case Tr:q(d,p,g,m,b,1),mr(m,b,0,g,1,0);break;case Br:q(d,p,g,m,b,0);break;case qr:q(d,p,g,m,b,0),mr(m,b,1,g,1,0);break;case Or:Xn(d,p,g,m,b);break;case Rr:Xn(d,p,g,m,b),br(m,b,g,1,0);break;case Er:Kn(d,p,g,m,b);break;default:Mi(0)}s+=u,c+=h}t.Ma+=a}else alert("todo:EmitRescaledRowsYUVA");Mi(t.Ma<=n.height)}}t.C=e,Mi(t.C<=t.i)}function at(t){var e;if(0<t.ua)return 0;for(e=0;e<t.Wb;++e){var n=t.Ya[e].G,r=t.Ya[e].H;if(0<n[1][r[1]+0].g||0<n[2][r[2]+0].g||0<n[3][r[3]+0].g)return 0}return 1}function ot(t,e,n,r,i,a){if(0!=t.Z){var o=t.qd,s=t.rd;for(Mi(null!=li[t.Z]);e<n;++e)li[t.Z](o,s,r,i,r,i,a),o=r,s=i,i+=a;t.qd=o,t.rd=s}}function st(t,e){var n=t.l.ma,r=0==n.Z||1==n.Z?t.l.j:t.C;r=t.C<r?r:t.C;if(Mi(e<=t.l.o),r<e){var i=t.l.width,a=n.ca,o=n.tb+i*r,s=t.V,u=t.Ba+t.c*r,l=t.gc;Mi(1==t.ab),Mi(3==l[0].hc),Hn(l[0],r,e,s,u,a,o),ot(n,r,e,a,o,i)}t.C=t.Ma=e}function le(t,e,n,r,i,a,o){var s=t.$/r,u=t.$%r,l=t.m,c=t.s,h=n+t.$,f=h;i=n+r*i;var d=n+r*a,p=280+c.ua,g=t.Pb?s:16777216,m=0<c.ua?c.Wa:null,b=c.wc,v=h<d?nt(c,u,s):null;Mi(t.C<a),Mi(d<=i);var y=!1;t:for(;;){for(;y||h<d;){var w=0;if(g<=s){var x=h-n;Mi((g=t).Pb),g.wd=g.m,g.xd=x,0<g.s.ua&&_(g.s.Wa,g.s.vb),g=s+Yr}if(u&b||(v=nt(c,u,s)),Mi(null!=v),v.Qb&&(e[h]=v.qb,y=!0),!y)if(ie(l),v.jc){w=l,x=e;var N=h,L=v.pd[ne(w)&jn-1];Mi(v.jc),0==(w=L.g<256?(re(w,w.u+L.g),x[N]=L.value,0):(re(w,w.u+L.g-256),Mi(256<=L.value),L.value))&&(y=!0)}else w=et(v.G[0],v.H[0],l);if(l.h)break;if(y||w<256){if(!y)if(v.nd)e[h]=(v.qb|w<<8)>>>0;else{if(ie(l),y=et(v.G[1],v.H[1],l),ie(l),x=et(v.G[2],v.H[2],l),N=et(v.G[3],v.H[3],l),l.h)break;e[h]=(N<<24|y<<16|w<<8|x)>>>0}if(y=!1,++h,r<=++u&&(u=0,++s,null!=o&&s<=a&&!(s%16)&&o(t,s),null!=m))for(;f<h;)w=e[f++],m.X[(506832829*w&4294967295)>>>m.Mb]=w}else if(w<280){if(w=Q(w-256,l),x=et(v.G[4],v.H[4],l),ie(l),x=tt(r,x=Q(x,l)),l.h)break;if(h-n<x||i-h<w)break t;for(N=0;N<w;++N)e[h+N]=e[h+N-x];for(h+=w,u+=w;r<=u;)u-=r,++s,null!=o&&s<=a&&!(s%16)&&o(t,s);if(Mi(h<=i),u&b&&(v=nt(c,u,s)),null!=m)for(;f<h;)w=e[f++],m.X[(506832829*w&4294967295)>>>m.Mb]=w}else{if(!(w<p))break t;for(y=w-280,Mi(null!=m);f<h;)w=e[f++],m.X[(506832829*w&4294967295)>>>m.Mb]=w;w=h,Mi(!(y>>>(x=m).Xa)),e[w]=x.X[y],y=!0}y||Mi(l.h==A(l))}if(t.Pb&&l.h&&h<i)Mi(t.m.h),t.a=5,t.m=t.wd,t.$=t.xd,0<t.s.ua&&_(t.s.vb,t.s.Wa);else{if(l.h)break t;null!=o&&o(t,a<s?a:s),t.a=0,t.$=h-n}return 1}return t.a=3,0}function ce(t){Mi(null!=t),t.vc=null,t.yc=null,t.Ya=null;var e=t.Wa;null!=e&&(e.X=null),Mi((t.vb=null)!=t)}function ut(){var t=new Qe;return null==t?null:(t.a=0,t.xb=ui,R("Predictor","VP8LPredictors"),R("Predictor","VP8LPredictors_C"),R("PredictorAdd","VP8LPredictorsAdd"),R("PredictorAdd","VP8LPredictorsAdd_C"),Dn=h,Gn=f,Yn=L,Jn=S,Xn=P,Kn=E,Zn=M,m.VP8LMapColor32b=zn,m.VP8LMapColor8b=Wn,t)}function he(t,e,n,r,i){var a=1,o=[t],s=[e],u=r.m,l=r.s,c=null,h=0;t:for(;;){if(n)for(;a&&ee(u,1);){var f=o,d=s,p=r,g=1,m=p.m,b=p.gc[p.ab],v=ee(m,2);if(p.Oc&1<<v)a=0;else{switch(p.Oc|=1<<v,b.hc=v,b.Ea=f[0],b.nc=d[0],b.K=[null],++p.ab,Mi(p.ab<=4),v){case 0:case 1:b.b=ee(m,3)+2,g=he(oe(b.Ea,b.b),oe(b.nc,b.b),0,p,b.K),b.K=b.K[0];break;case 3:var y,w=ee(m,8)+1,x=16<w?0:4<w?1:2<w?2:3;if(f[0]=oe(b.Ea,x),b.b=x,y=g=he(w,1,0,p,b.K)){var N,L=w,A=b,_=1<<(8>>A.b),S=Di(_);if(null==S)y=0;else{var P=A.K[0],k=A.w;for(S[0]=A.K[0][0],N=1;N<1*L;++N)S[N]=se(P[k+N],S[N-1]);for(;N<4*_;++N)S[N]=0;A.K[0]=null,A.K[0]=S,y=1}}g=y;break;case 2:break;default:Mi(0)}a=g}}if(o=o[0],s=s[0],a&&ee(u,1)&&!(a=1<=(h=ee(u,4))&&h<=11)){r.a=3;break t}var F;if(F=a)e:{var C,I,j,B=r,O=o,E=s,M=h,T=n,q=B.m,R=B.s,D=[null],U=1,z=0,H=Gr[M];n:for(;;){if(T&&ee(q,1)){var W=ee(q,3)+2,V=oe(O,W),G=oe(E,W),Y=V*G;if(!he(V,G,0,B,D))break n;for(D=D[0],R.xc=W,C=0;C<Y;++C){var J=D[C]>>8&65535;U<=(D[C]=J)&&(U=1+J)}}if(q.h)break n;for(I=0;I<5;++I){var X=zr[I];!I&&0<M&&(X+=1<<M),z<X&&(z=X)}var K=Ui(U*H,Qt),Z=U,$=Ui(Z,te);if(null==$)var Q=null;else Mi(Z<=65536),Q=$;var tt=Di(z);if(null==Q||null==tt||null==K){B.a=1;break n}var et=K;for(C=j=0;C<U;++C){var nt=Q[C],rt=nt.G,it=nt.H,at=0,ot=1,st=0;for(I=0;I<5;++I){X=zr[I],!I&&0<M&&(X+=1<<M);r:{var ut,lt=X,ct=B,ht=tt,ft=rt[I]=et,dt=it[I]=j,pt=0,gt=ct.m,mt=ee(gt,1);if(Ri(ht,0,0,lt),mt){var bt=ee(gt,1)+1,vt=ee(gt,0==ee(gt,1)?1:8);ht[vt]=1,2==bt&&(ht[vt=ee(gt,8)]=1);var yt=1}else{var wt=Di(19),xt=ee(gt,4)+4;if(19<xt){ct.a=3;var Nt=0;break r}for(ut=0;ut<xt;++ut)wt[Wr[ut]]=ee(gt,3);var Lt=void 0,At=void 0,_t=ct,St=wt,Pt=lt,kt=ht,Ft=0,Ct=_t.m,It=8,jt=Ui(128,Qt);i:for(;$t(jt,0,7,St,19);){if(ee(Ct,1)){if(Pt<(Lt=2+ee(Ct,2+2*ee(Ct,3))))break i}else Lt=Pt;for(At=0;At<Pt&&Lt--;){ie(Ct);var Bt=jt[0+(127&ne(Ct))];re(Ct,Ct.u+Bt.g);var Ot=Bt.value;if(Ot<16)0!=(kt[At++]=Ot)&&(It=Ot);else{var Et=16==Ot,Mt=Ot-16,Tt=Ur[Mt],qt=ee(Ct,Dr[Mt])+Tt;if(Pt<At+qt)break i;for(var Rt=Et?It:0;0<qt--;)kt[At++]=Rt}}Ft=1;break i}Ft||(_t.a=3),yt=Ft}(yt=yt&&!gt.h)&&(pt=$t(ft,dt,8,ht,lt)),Nt=yt&&0!=pt?pt:(ct.a=3,0)}if(0==Nt)break n;if(ot&&1==Hr[I]&&(ot=0==et[j].g),at+=et[j].g,j+=Nt,I<=3){var Dt,Ut=tt[0];for(Dt=1;Dt<X;++Dt)tt[Dt]>Ut&&(Ut=tt[Dt]);st+=Ut}}if(nt.nd=ot,nt.Qb=0,ot&&(nt.qb=(rt[3][it[3]+0].value<<24|rt[1][it[1]+0].value<<16|rt[2][it[2]+0].value)>>>0,0==at&&rt[0][it[0]+0].value<256&&(nt.Qb=1,nt.qb+=rt[0][it[0]+0].value<<8)),nt.jc=!nt.Qb&&st<6,nt.jc){var zt,Ht=nt;for(zt=0;zt<jn;++zt){var Wt=zt,Vt=Ht.pd[Wt],Gt=Ht.G[0][Ht.H[0]+Wt];256<=Gt.value?(Vt.g=Gt.g+256,Vt.value=Gt.value):(Vt.g=0,Vt.value=0,Wt>>=ue(Gt,8,Vt),Wt>>=ue(Ht.G[1][Ht.H[1]+Wt],16,Vt),Wt>>=ue(Ht.G[2][Ht.H[2]+Wt],0,Vt),ue(Ht.G[3][Ht.H[3]+Wt],24,Vt))}}}R.vc=D,R.Wb=U,R.Ya=Q,R.yc=K,F=1;break e}F=0}if(!(a=F)){r.a=3;break t}if(0<h){if(l.ua=1<<h,!ae(l.Wa,h)){r.a=1,a=0;break t}}else l.ua=0;var Yt=r,Jt=o,Xt=s,Kt=Yt.s,Zt=Kt.xc;if(Yt.c=Jt,Yt.i=Xt,Kt.md=oe(Jt,Zt),Kt.wc=0==Zt?-1:(1<<Zt)-1,n){r.xb=si;break t}if(null==(c=Di(o*s))){r.a=1,a=0;break t}a=(a=le(r,c,0,o,s,s,null))&&!u.h;break t}return a?(null!=i?i[0]=c:(Mi(null==c),Mi(n)),r.$=0,n||ce(l)):ce(l),a}function lt(t,e){var n=t.c*t.i,r=n+e+16*e;return Mi(t.c<=e),t.V=Di(r),null==t.V?(t.Ta=null,t.Ua=0,t.a=1,0):(t.Ta=t.V,t.Ua=t.Ba+n+e,1)}function ct(t,e){var n=t.C,r=e-n,i=t.V,a=t.Ba+t.c*n;for(Mi(e<=t.l.o);0<r;){var o=16<r?16:r,s=t.l.ma,u=t.l.width,l=u*o,c=s.ca,h=s.tb+u*n,f=t.Ta,d=t.Ua;rt(t,o,i,a),yr(f,d,c,h,l),ot(s,n,n+o,c,h,u),r-=o,i+=o*t.c,n+=o}Mi(n==e),t.C=t.Ma=e}function ht(){this.ub=this.yd=this.td=this.Rb=0}function ft(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function dt(){this.Fb=this.Bb=this.Cb=0,this.Zb=Di(4),this.Lb=Di(4)}function pt(){this.Yb=function(){var t=[];return function t(e,n,r){for(var i=r[n],a=0;a<i&&(e.push(r.length>n+1?[]:0),!(r.length<n+1));a++)t(e[a],n+1,r)}(t,0,[3,11]),t}()}function gt(){this.jb=Di(3),this.Wc=zi([4,8],pt),this.Xc=zi([4,17],pt)}function mt(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new Di(4),this.od=new Di(4)}function bt(){this.ld=this.La=this.dd=this.tc=0}function vt(){this.Na=this.la=0}function yt(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function wt(){this.ad=Di(384),this.Za=0,this.Ob=Di(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function xt(){this.uc=this.M=this.Nb=0,this.wa=Array(new bt),this.Y=0,this.ya=Array(new wt),this.aa=0,this.l=new _t}function Nt(){this.y=Di(16),this.f=Di(8),this.ea=Di(8)}function Lt(){this.cb=this.a=0,this.sc="",this.m=new e,this.Od=new ht,this.Kc=new ft,this.ed=new mt,this.Qa=new dt,this.Ic=this.$c=this.Aa=0,this.D=new xt,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=Ui(8,e),this.ia=0,this.pb=Ui(4,yt),this.Pa=new gt,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new Nt),this.Hd=0,this.rb=Array(new vt),this.sb=0,this.wa=Array(new bt),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new wt),this.L=this.aa=0,this.gd=zi([4,2],bt),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function At(t,e){return t<0?0:e<t?e:t}function _t(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function St(){var t=new Lt;return null!=t&&(t.a=0,t.sc="OK",t.cb=0,t.Xb=0,Kr||(Kr=Ct)),t}function Pt(t,e,n){return 0==t.a&&(t.a=e,t.sc=n,t.cb=0),0}function kt(t,e,n){return 3<=n&&157==t[e+0]&&1==t[e+1]&&42==t[e+2]}function Ft(t,e){if(null==t)return 0;if(t.a=0,t.sc="OK",null==e)return Pt(t,2,"null VP8Io passed to VP8GetHeaders()");var n=e.data,r=e.w,i=e.ha;if(i<4)return Pt(t,7,"Truncated header.");var a=n[r+0]|n[r+1]<<8|n[r+2]<<16,o=t.Od;if(o.Rb=!(1&a),o.td=a>>1&7,o.yd=a>>4&1,o.ub=a>>5,3<o.td)return Pt(t,3,"Incorrect keyframe parameters.");if(!o.yd)return Pt(t,4,"Frame not displayable.");r+=3,i-=3;var s=t.Kc;if(o.Rb){if(i<7)return Pt(t,7,"cannot parse picture header");if(!kt(n,r,i))return Pt(t,3,"Bad code word");s.c=16383&(n[r+4]<<8|n[r+3]),s.Td=n[r+4]>>6,s.i=16383&(n[r+6]<<8|n[r+5]),s.Ud=n[r+6]>>6,r+=7,i-=7,t.za=s.c+15>>4,t.Ub=s.i+15>>4,e.width=s.c,e.height=s.i,e.Da=0,e.j=0,e.v=0,e.va=e.width,e.o=e.height,e.da=0,e.ib=e.width,e.hb=e.height,e.U=e.width,e.T=e.height,Ri((a=t.Pa).jb,0,255,a.jb.length),Mi(null!=(a=t.Qa)),a.Cb=0,a.Bb=0,a.Fb=1,Ri(a.Zb,0,0,a.Zb.length),Ri(a.Lb,0,0,a.Lb)}if(o.ub>i)return Pt(t,7,"bad partition length");p(a=t.m,n,r,o.ub),r+=o.ub,i-=o.ub,o.Rb&&(s.Ld=x(a),s.Kd=x(a)),s=t.Qa;var u,l=t.Pa;if(Mi(null!=a),Mi(null!=s),s.Cb=x(a),s.Cb){if(s.Bb=x(a),x(a)){for(s.Fb=x(a),u=0;u<4;++u)s.Zb[u]=x(a)?b(a,7):0;for(u=0;u<4;++u)s.Lb[u]=x(a)?b(a,6):0}if(s.Bb)for(u=0;u<3;++u)l.jb[u]=x(a)?g(a,8):255}else s.Bb=0;if(a.Ka)return Pt(t,3,"cannot parse segment header");if((s=t.ed).zd=x(a),s.Tb=g(a,6),s.wb=g(a,3),s.Pc=x(a),s.Pc&&x(a)){for(l=0;l<4;++l)x(a)&&(s.vd[l]=b(a,6));for(l=0;l<4;++l)x(a)&&(s.od[l]=b(a,6))}if(t.L=0==s.Tb?0:s.zd?1:2,a.Ka)return Pt(t,3,"cannot parse filter header");var c=i;if(r=(i=u=r)+c,s=c,t.Xb=(1<<g(t.m,2))-1,c<3*(l=t.Xb))n=7;else{for(u+=3*l,s-=3*l,c=0;c<l;++c){var h=n[i+0]|n[i+1]<<8|n[i+2]<<16;s<h&&(h=s),p(t.Jc[+c],n,u,h),u+=h,s-=h,i+=3}p(t.Jc[+l],n,u,s),n=u<r?0:5}if(0!=n)return Pt(t,n,"cannot parse partitions");for(n=g(u=t.m,7),i=x(u)?b(u,4):0,r=x(u)?b(u,4):0,s=x(u)?b(u,4):0,l=x(u)?b(u,4):0,u=x(u)?b(u,4):0,c=t.Qa,h=0;h<4;++h){if(c.Cb){var f=c.Zb[h];c.Fb||(f+=n)}else{if(0<h){t.pb[h]=t.pb[0];continue}f=n}var d=t.pb[h];d.Sc[0]=Jr[At(f+i,127)],d.Sc[1]=Xr[At(f+0,127)],d.Eb[0]=2*Jr[At(f+r,127)],d.Eb[1]=101581*Xr[At(f+s,127)]>>16,d.Eb[1]<8&&(d.Eb[1]=8),d.Qc[0]=Jr[At(f+l,117)],d.Qc[1]=Xr[At(f+u,127)],d.lc=f+u}if(!o.Rb)return Pt(t,4,"Not a key frame.");for(x(a),o=t.Pa,n=0;n<4;++n){for(i=0;i<8;++i)for(r=0;r<3;++r)for(s=0;s<11;++s)l=T(a,ni[n][i][r][s])?g(a,8):ti[n][i][r][s],o.Wc[n][i].Yb[r][s]=l;for(i=0;i<17;++i)o.Xc[n][i]=o.Wc[n][ri[i]]}return t.kc=x(a),t.kc&&(t.Bd=g(a,8)),t.cb=1}function Ct(t,e,n,r,i,a,o){var s=e[i].Yb[n];for(n=0;i<16;++i){if(!T(t,s[n+0]))return i;for(;!T(t,s[n+1]);)if(s=e[++i].Yb[0],n=0,16==i)return 16;var u=e[i+1].Yb;if(T(t,s[n+2])){var l=t,c=0;if(T(l,(f=s)[(h=n)+3]))if(T(l,f[h+6])){for(s=0,h=2*(c=T(l,f[h+8]))+(f=T(l,f[h+9+c])),c=0,f=Zr[h];f[s];++s)c+=c+T(l,f[s]);c+=3+(8<<h)}else T(l,f[h+7])?(c=7+2*T(l,165),c+=T(l,145)):c=5+T(l,159);else c=T(l,f[h+4])?3+T(l,f[h+5]):2;s=u[2]}else s=u[c=1];u=o+$r[i],(l=t).b<0&&v(l);var h,f=l.b,d=(h=l.Ca>>1)-(l.I>>f)>>31;--l.b,l.Ca+=d,l.Ca|=1,l.I-=(h+1&d)<<f,a[u]=((c^d)-d)*r[(0<i)+0]}return 16}function It(t){var e=t.rb[t.sb-1];e.la=0,e.Na=0,Ri(t.zc,0,0,t.zc.length),t.ja=0}function jt(t,e){if(null==t)return 0;if(null==e)return Pt(t,2,"NULL VP8Io parameter in VP8Decode().");if(!t.cb&&!Ft(t,e))return 0;if(Mi(t.cb),null==e.ac||e.ac(e)){e.ob&&(t.L=0);var n=ji[t.L];if(2==t.L?(t.yb=0,t.zb=0):(t.yb=e.v-n>>4,t.zb=e.j-n>>4,t.yb<0&&(t.yb=0),t.zb<0&&(t.zb=0)),t.Va=e.o+15+n>>4,t.Hb=e.va+15+n>>4,t.Hb>t.za&&(t.Hb=t.za),t.Va>t.Ub&&(t.Va=t.Ub),0<t.L){var r=t.ed;for(n=0;n<4;++n){var i;if(t.Qa.Cb){var a=t.Qa.Lb[n];t.Qa.Fb||(a+=r.Tb)}else a=r.Tb;for(i=0;i<=1;++i){var o=t.gd[n][i],s=a;if(r.Pc&&(s+=r.vd[0],i&&(s+=r.od[0])),0<(s=s<0?0:63<s?63:s)){var u=s;0<r.wb&&((u=4<r.wb?u>>2:u>>1)>9-r.wb&&(u=9-r.wb)),u<1&&(u=1),o.dd=u,o.tc=2*s+u,o.ld=40<=s?2:15<=s?1:0}else o.tc=0;o.La=i}}}n=0}else Pt(t,6,"Frame setup failed"),n=t.a;if(n=0==n){if(n){(t.$c=0)<t.Aa||(t.Ic=Oi);t:{n=t.Ic;r=4*(u=t.za);var l=32*u,c=u+1,h=0<t.L?u*(0<t.Aa?2:1):0,f=(2==t.Aa?2:1)*u;if((o=r+832+(i=3*(16*n+ji[t.L])/2*l)+(a=null!=t.Fa&&0<t.Fa.length?t.Kc.c*t.Kc.i:0))!=o)n=0;else{if(o>t.Vb){if(t.Vb=0,t.Ec=Di(o),t.Fc=0,null==t.Ec){n=Pt(t,1,"no memory during frame initialization.");break t}t.Vb=o}o=t.Ec,s=t.Fc,t.Ac=o,t.Bc=s,s+=r,t.Gd=Ui(l,Nt),t.Hd=0,t.rb=Ui(c+1,vt),t.sb=1,t.wa=h?Ui(h,bt):null,t.Y=0,t.D.Nb=0,t.D.wa=t.wa,t.D.Y=t.Y,0<t.Aa&&(t.D.Y+=u),Mi(!0),t.oc=o,t.pc=s,s+=832,t.ya=Ui(f,wt),t.aa=0,t.D.ya=t.ya,t.D.aa=t.aa,2==t.Aa&&(t.D.aa+=u),t.R=16*u,t.B=8*u,u=(l=ji[t.L])*t.R,l=l/2*t.B,t.sa=o,t.ta=s+u,t.qa=t.sa,t.ra=t.ta+16*n*t.R+l,t.Ha=t.qa,t.Ia=t.ra+8*n*t.B+l,t.$c=0,s+=i,t.mb=a?o:null,t.nb=a?s:null,Mi(s+a<=t.Fc+t.Vb),It(t),Ri(t.Ac,t.Bc,0,r),n=1}}if(n){if(e.ka=0,e.y=t.sa,e.O=t.ta,e.f=t.qa,e.N=t.ra,e.ea=t.Ha,e.Vd=t.Ia,e.fa=t.R,e.Rc=t.B,e.F=null,e.J=0,!Ar){for(n=-255;n<=255;++n)wr[255+n]=n<0?-n:n;for(n=-1020;n<=1020;++n)xr[1020+n]=n<-128?-128:127<n?127:n;for(n=-112;n<=112;++n)Nr[112+n]=n<-16?-16:15<n?15:n;for(n=-255;n<=510;++n)Lr[255+n]=n<0?0:255<n?255:n;Ar=1}$n=zt,Qn=qt,er=Rt,nr=Dt,rr=Ut,tr=Tt,ir=Ue,ar=ze,or=Ve,sr=Ge,ur=He,lr=We,cr=Ye,hr=Je,fr=Ee,dr=Me,pr=Te,gr=qe,ai[0]=be,ai[1]=Wt,ai[2]=ge,ai[3]=me,ai[4]=ve,ai[5]=we,ai[6]=ye,ai[7]=xe,ai[8]=Le,ai[9]=Ne,ii[0]=Kt,ii[1]=Gt,ii[2]=Yt,ii[3]=Jt,ii[4]=Zt,ii[5]=fe,ii[6]=de,oi[0]=Pe,oi[1]=Vt,oi[2]=Ae,oi[3]=_e,oi[4]=Fe,oi[5]=ke,oi[6]=Ce,n=1}else n=0}n&&(n=function(t,e){for(t.M=0;t.M<t.Va;++t.M){var n,r=t.Jc[t.M&t.Xb],i=t.m,a=t;for(n=0;n<a.za;++n){var o=i,s=a,u=s.Ac,l=s.Bc+4*n,c=s.zc,h=s.ya[s.aa+n];if(s.Qa.Bb?h.$b=T(o,s.Pa.jb[0])?2+T(o,s.Pa.jb[2]):T(o,s.Pa.jb[1]):h.$b=0,s.kc&&(h.Ad=T(o,s.Bd)),h.Za=!T(o,145)+0,h.Za){var f=h.Ob,d=0;for(s=0;s<4;++s){var p,g=c[0+s];for(p=0;p<4;++p){g=ei[u[l+p]][g];for(var m=Qr[T(o,g[0])];0<m;)m=Qr[2*m+T(o,g[m])];g=-m,u[l+p]=g}qi(f,d,u,l,4),d+=4,c[0+s]=g}}else g=T(o,156)?T(o,128)?1:3:T(o,163)?2:0,Ri(u,l,h.Ob[0]=g,4),Ri(c,0,g,4);h.Dd=T(o,142)?T(o,114)?T(o,183)?1:3:2:0}if(a.m.Ka)return Pt(t,7,"Premature end-of-partition0 encountered.");for(;t.ja<t.za;++t.ja){if(a=r,o=(i=t).rb[i.sb-1],u=i.rb[i.sb+i.ja],n=i.ya[i.aa+i.ja],l=i.kc?n.Ad:0)o.la=u.la=0,n.Za||(o.Na=u.Na=0),n.Hc=0,n.Gc=0,n.ia=0;else{var b,v;if(o=u,u=a,l=i.Pa.Xc,c=i.ya[i.aa+i.ja],h=i.pb[c.$b],s=c.ad,f=0,d=i.rb[i.sb-1],g=p=0,Ri(s,f,0,384),c.Za)var y=0,w=l[3];else{m=Di(16);var x=o.Na+d.Na;if(x=Kr(u,l[1],x,h.Eb,0,m,0),o.Na=d.Na=(0<x)+0,1<x)$n(m,0,s,f);else{var N=m[0]+3>>3;for(m=0;m<256;m+=16)s[f+m]=N}y=1,w=l[0]}var L=15&o.la,A=15&d.la;for(m=0;m<4;++m){var _=1&A;for(N=v=0;N<4;++N)L=L>>1|(_=y<(x=Kr(u,w,x=_+(1&L),h.Sc,y,s,f)))<<7,v=v<<2|(3<x?3:1<x?2:0!=s[f+0]),f+=16;L>>=4,A=A>>1|_<<7,p=(p<<8|v)>>>0}for(w=L,y=A>>4,b=0;b<4;b+=2){for(v=0,L=o.la>>4+b,A=d.la>>4+b,m=0;m<2;++m){for(_=1&A,N=0;N<2;++N)x=_+(1&L),L=L>>1|(_=0<(x=Kr(u,l[2],x,h.Qc,0,s,f)))<<3,v=v<<2|(3<x?3:1<x?2:0!=s[f+0]),f+=16;L>>=2,A=A>>1|_<<5}g|=v<<4*b,w|=L<<4<<b,y|=(240&A)<<b}o.la=w,d.la=y,c.Hc=p,c.Gc=g,c.ia=43690&g?0:h.ia,l=!(p|g)}if(0<i.L&&(i.wa[i.Y+i.ja]=i.gd[n.$b][n.Za],i.wa[i.Y+i.ja].La|=!l),a.Ka)return Pt(t,7,"Premature end-of-file encountered.")}if(It(t),i=e,a=1,n=(r=t).D,o=0<r.L&&r.M>=r.zb&&r.M<=r.Va,0==r.Aa)t:{if(n.M=r.M,n.uc=o,Pn(r,n),a=1,n=(v=r.D).Nb,o=(g=ji[r.L])*r.R,u=g/2*r.B,m=16*n*r.R,N=8*n*r.B,l=r.sa,c=r.ta-o+m,h=r.qa,s=r.ra-u+N,f=r.Ha,d=r.Ia-u+N,A=0==(L=v.M),p=L>=r.Va-1,2==r.Aa&&Pn(r,v),v.uc)for(_=(x=r).D.M,Mi(x.D.uc),v=x.yb;v<x.Hb;++v){y=v,w=_;var S=(P=(M=x).D).Nb;b=M.R;var P=P.wa[P.Y+y],k=M.sa,F=M.ta+16*S*b+16*y,C=P.dd,I=P.tc;if(0!=I)if(Mi(3<=I),1==M.L)0<y&&dr(k,F,b,I+4),P.La&&gr(k,F,b,I),0<w&&fr(k,F,b,I+4),P.La&&pr(k,F,b,I);else{var j=M.B,B=M.qa,O=M.ra+8*S*j+8*y,E=M.Ha,M=M.Ia+8*S*j+8*y;S=P.ld,0<y&&(ar(k,F,b,I+4,C,S),sr(B,O,E,M,j,I+4,C,S)),P.La&&(lr(k,F,b,I,C,S),hr(B,O,E,M,j,I,C,S)),0<w&&(ir(k,F,b,I+4,C,S),or(B,O,E,M,j,I+4,C,S)),P.La&&(ur(k,F,b,I,C,S),cr(B,O,E,M,j,I,C,S))}}if(r.ia&&alert("todo:DitherRow"),null!=i.put){if(v=16*L,L=16*(L+1),A?(i.y=r.sa,i.O=r.ta+m,i.f=r.qa,i.N=r.ra+N,i.ea=r.Ha,i.W=r.Ia+N):(v-=g,i.y=l,i.O=c,i.f=h,i.N=s,i.ea=f,i.W=d),p||(L-=g),L>i.o&&(L=i.o),i.F=null,(i.J=null)!=r.Fa&&0<r.Fa.length&&v<L&&(i.J=an(r,i,v,L-v),i.F=r.mb,null==i.F&&0==i.F.length)){a=Pt(r,3,"Could not decode alpha data.");break t}v<i.j&&(g=i.j-v,v=i.j,Mi(!(1&g)),i.O+=r.R*g,i.N+=r.B*(g>>1),i.W+=r.B*(g>>1),null!=i.F&&(i.J+=i.width*g)),v<L&&(i.O+=i.v,i.N+=i.v>>1,i.W+=i.v>>1,null!=i.F&&(i.J+=i.v),i.ka=v-i.j,i.U=i.va-i.v,i.T=L-v,a=i.put(i))}n+1!=r.Ic||p||(qi(r.sa,r.ta-o,l,c+16*r.R,o),qi(r.qa,r.ra-u,h,s+8*r.B,u),qi(r.Ha,r.Ia-u,f,d+8*r.B,u))}if(!a)return Pt(t,6,"Output aborted.")}return 1}(t,e)),null!=e.bc&&e.bc(e),n&=1}return n?(t.cb=0,n):0}function Bt(t,e,n,r,i){i=t[e+n+32*r]+(i>>3),t[e+n+32*r]=-256&i?i<0?0:255:i}function Ot(t,e,n,r,i,a){Bt(t,e,0,n,r+i),Bt(t,e,1,n,r+a),Bt(t,e,2,n,r-a),Bt(t,e,3,n,r-i)}function Et(t){return(20091*t>>16)+t}function Mt(t,e,n,r){var i,a=0,o=Di(16);for(i=0;i<4;++i){var s=t[e+0]+t[e+8],u=t[e+0]-t[e+8],l=(35468*t[e+4]>>16)-Et(t[e+12]),c=Et(t[e+4])+(35468*t[e+12]>>16);o[a+0]=s+c,o[a+1]=u+l,o[a+2]=u-l,o[a+3]=s-c,a+=4,e++}for(i=a=0;i<4;++i)s=(t=o[a+0]+4)+o[a+8],u=t-o[a+8],l=(35468*o[a+4]>>16)-Et(o[a+12]),Bt(n,r,0,0,s+(c=Et(o[a+4])+(35468*o[a+12]>>16))),Bt(n,r,1,0,u+l),Bt(n,r,2,0,u-l),Bt(n,r,3,0,s-c),a++,r+=32}function Tt(t,e,n,r){var i=t[e+0]+4,a=35468*t[e+4]>>16,o=Et(t[e+4]),s=35468*t[e+1]>>16;Ot(n,r,0,i+o,t=Et(t[e+1]),s),Ot(n,r,1,i+a,t,s),Ot(n,r,2,i-a,t,s),Ot(n,r,3,i-o,t,s)}function qt(t,e,n,r,i){Mt(t,e,n,r),i&&Mt(t,e+16,n,r+4)}function Rt(t,e,n,r){Qn(t,e+0,n,r,1),Qn(t,e+32,n,r+128,1)}function Dt(t,e,n,r){var i;for(t=t[e+0]+4,i=0;i<4;++i)for(e=0;e<4;++e)Bt(n,r,e,i,t)}function Ut(t,e,n,r){t[e+0]&&nr(t,e+0,n,r),t[e+16]&&nr(t,e+16,n,r+4),t[e+32]&&nr(t,e+32,n,r+128),t[e+48]&&nr(t,e+48,n,r+128+4)}function zt(t,e,n,r){var i,a=Di(16);for(i=0;i<4;++i){var o=t[e+0+i]+t[e+12+i],s=t[e+4+i]+t[e+8+i],u=t[e+4+i]-t[e+8+i],l=t[e+0+i]-t[e+12+i];a[0+i]=o+s,a[8+i]=o-s,a[4+i]=l+u,a[12+i]=l-u}for(i=0;i<4;++i)o=(t=a[0+4*i]+3)+a[3+4*i],s=a[1+4*i]+a[2+4*i],u=a[1+4*i]-a[2+4*i],l=t-a[3+4*i],n[r+0]=o+s>>3,n[r+16]=l+u>>3,n[r+32]=o-s>>3,n[r+48]=l-u>>3,r+=64}function Ht(t,e,n){var r,i=e-32,a=Pr,o=255-t[i-1];for(r=0;r<n;++r){var s,u=a,l=o+t[e-1];for(s=0;s<n;++s)t[e+s]=u[l+t[i+s]];e+=32}}function Wt(t,e){Ht(t,e,4)}function Vt(t,e){Ht(t,e,8)}function Gt(t,e){Ht(t,e,16)}function Yt(t,e){var n;for(n=0;n<16;++n)qi(t,e+32*n,t,e-32,16)}function Jt(t,e){var n;for(n=16;0<n;--n)Ri(t,e,t[e-1],16),e+=32}function Xt(t,e,n){var r;for(r=0;r<16;++r)Ri(e,n+32*r,t,16)}function Kt(t,e){var n,r=16;for(n=0;n<16;++n)r+=t[e-1+32*n]+t[e+n-32];Xt(r>>5,t,e)}function Zt(t,e){var n,r=8;for(n=0;n<16;++n)r+=t[e-1+32*n];Xt(r>>4,t,e)}function fe(t,e){var n,r=8;for(n=0;n<16;++n)r+=t[e+n-32];Xt(r>>4,t,e)}function de(t,e){Xt(128,t,e)}function pe(t,e,n){return t+2*e+n+2>>2}function ge(t,e){var n,r=e-32;r=new Uint8Array([pe(t[r-1],t[r+0],t[r+1]),pe(t[r+0],t[r+1],t[r+2]),pe(t[r+1],t[r+2],t[r+3]),pe(t[r+2],t[r+3],t[r+4])]);for(n=0;n<4;++n)qi(t,e+32*n,r,0,r.length)}function me(t,e){var n=t[e-1],r=t[e-1+32],i=t[e-1+64],a=t[e-1+96];o(t,e+0,16843009*pe(t[e-1-32],n,r)),o(t,e+32,16843009*pe(n,r,i)),o(t,e+64,16843009*pe(r,i,a)),o(t,e+96,16843009*pe(i,a,a))}function be(t,e){var n,r=4;for(n=0;n<4;++n)r+=t[e+n-32]+t[e-1+32*n];for(r>>=3,n=0;n<4;++n)Ri(t,e+32*n,r,4)}function ve(t,e){var n=t[e-1+0],r=t[e-1+32],i=t[e-1+64],a=t[e-1-32],o=t[e+0-32],s=t[e+1-32],u=t[e+2-32],l=t[e+3-32];t[e+0+96]=pe(r,i,t[e-1+96]),t[e+1+96]=t[e+0+64]=pe(n,r,i),t[e+2+96]=t[e+1+64]=t[e+0+32]=pe(a,n,r),t[e+3+96]=t[e+2+64]=t[e+1+32]=t[e+0+0]=pe(o,a,n),t[e+3+64]=t[e+2+32]=t[e+1+0]=pe(s,o,a),t[e+3+32]=t[e+2+0]=pe(u,s,o),t[e+3+0]=pe(l,u,s)}function ye(t,e){var n=t[e+1-32],r=t[e+2-32],i=t[e+3-32],a=t[e+4-32],o=t[e+5-32],s=t[e+6-32],u=t[e+7-32];t[e+0+0]=pe(t[e+0-32],n,r),t[e+1+0]=t[e+0+32]=pe(n,r,i),t[e+2+0]=t[e+1+32]=t[e+0+64]=pe(r,i,a),t[e+3+0]=t[e+2+32]=t[e+1+64]=t[e+0+96]=pe(i,a,o),t[e+3+32]=t[e+2+64]=t[e+1+96]=pe(a,o,s),t[e+3+64]=t[e+2+96]=pe(o,s,u),t[e+3+96]=pe(s,u,u)}function we(t,e){var n=t[e-1+0],r=t[e-1+32],i=t[e-1+64],a=t[e-1-32],o=t[e+0-32],s=t[e+1-32],u=t[e+2-32],l=t[e+3-32];t[e+0+0]=t[e+1+64]=a+o+1>>1,t[e+1+0]=t[e+2+64]=o+s+1>>1,t[e+2+0]=t[e+3+64]=s+u+1>>1,t[e+3+0]=u+l+1>>1,t[e+0+96]=pe(i,r,n),t[e+0+64]=pe(r,n,a),t[e+0+32]=t[e+1+96]=pe(n,a,o),t[e+1+32]=t[e+2+96]=pe(a,o,s),t[e+2+32]=t[e+3+96]=pe(o,s,u),t[e+3+32]=pe(s,u,l)}function xe(t,e){var n=t[e+0-32],r=t[e+1-32],i=t[e+2-32],a=t[e+3-32],o=t[e+4-32],s=t[e+5-32],u=t[e+6-32],l=t[e+7-32];t[e+0+0]=n+r+1>>1,t[e+1+0]=t[e+0+64]=r+i+1>>1,t[e+2+0]=t[e+1+64]=i+a+1>>1,t[e+3+0]=t[e+2+64]=a+o+1>>1,t[e+0+32]=pe(n,r,i),t[e+1+32]=t[e+0+96]=pe(r,i,a),t[e+2+32]=t[e+1+96]=pe(i,a,o),t[e+3+32]=t[e+2+96]=pe(a,o,s),t[e+3+64]=pe(o,s,u),t[e+3+96]=pe(s,u,l)}function Ne(t,e){var n=t[e-1+0],r=t[e-1+32],i=t[e-1+64],a=t[e-1+96];t[e+0+0]=n+r+1>>1,t[e+2+0]=t[e+0+32]=r+i+1>>1,t[e+2+32]=t[e+0+64]=i+a+1>>1,t[e+1+0]=pe(n,r,i),t[e+3+0]=t[e+1+32]=pe(r,i,a),t[e+3+32]=t[e+1+64]=pe(i,a,a),t[e+3+64]=t[e+2+64]=t[e+0+96]=t[e+1+96]=t[e+2+96]=t[e+3+96]=a}function Le(t,e){var n=t[e-1+0],r=t[e-1+32],i=t[e-1+64],a=t[e-1+96],o=t[e-1-32],s=t[e+0-32],u=t[e+1-32],l=t[e+2-32];t[e+0+0]=t[e+2+32]=n+o+1>>1,t[e+0+32]=t[e+2+64]=r+n+1>>1,t[e+0+64]=t[e+2+96]=i+r+1>>1,t[e+0+96]=a+i+1>>1,t[e+3+0]=pe(s,u,l),t[e+2+0]=pe(o,s,u),t[e+1+0]=t[e+3+32]=pe(n,o,s),t[e+1+32]=t[e+3+64]=pe(r,n,o),t[e+1+64]=t[e+3+96]=pe(i,r,n),t[e+1+96]=pe(a,i,r)}function Ae(t,e){var n;for(n=0;n<8;++n)qi(t,e+32*n,t,e-32,8)}function _e(t,e){var n;for(n=0;n<8;++n)Ri(t,e,t[e-1],8),e+=32}function Se(t,e,n){var r;for(r=0;r<8;++r)Ri(e,n+32*r,t,8)}function Pe(t,e){var n,r=8;for(n=0;n<8;++n)r+=t[e+n-32]+t[e-1+32*n];Se(r>>4,t,e)}function ke(t,e){var n,r=4;for(n=0;n<8;++n)r+=t[e+n-32];Se(r>>3,t,e)}function Fe(t,e){var n,r=4;for(n=0;n<8;++n)r+=t[e-1+32*n];Se(r>>3,t,e)}function Ce(t,e){Se(128,t,e)}function Ie(t,e,n){var r=t[e-n],i=t[e+0],a=3*(i-r)+_r[1020+t[e-2*n]-t[e+n]],o=Sr[112+(a+4>>3)];t[e-n]=Pr[255+r+Sr[112+(a+3>>3)]],t[e+0]=Pr[255+i-o]}function je(t,e,n,r){var i=t[e+0],a=t[e+n];return kr[255+t[e-2*n]-t[e-n]]>r||kr[255+a-i]>r}function Be(t,e,n,r){return 4*kr[255+t[e-n]-t[e+0]]+kr[255+t[e-2*n]-t[e+n]]<=r}function Oe(t,e,n,r,i){var a=t[e-3*n],o=t[e-2*n],s=t[e-n],u=t[e+0],l=t[e+n],c=t[e+2*n],h=t[e+3*n];return 4*kr[255+s-u]+kr[255+o-l]>r?0:kr[255+t[e-4*n]-a]<=i&&kr[255+a-o]<=i&&kr[255+o-s]<=i&&kr[255+h-c]<=i&&kr[255+c-l]<=i&&kr[255+l-u]<=i}function Ee(t,e,n,r){var i=2*r+1;for(r=0;r<16;++r)Be(t,e+r,n,i)&&Ie(t,e+r,n)}function Me(t,e,n,r){var i=2*r+1;for(r=0;r<16;++r)Be(t,e+r*n,1,i)&&Ie(t,e+r*n,1)}function Te(t,e,n,r){var i;for(i=3;0<i;--i)Ee(t,e+=4*n,n,r)}function qe(t,e,n,r){var i;for(i=3;0<i;--i)Me(t,e+=4,n,r)}function Re(t,e,n,r,i,a,o,s){for(a=2*a+1;0<i--;){if(Oe(t,e,n,a,o))if(je(t,e,n,s))Ie(t,e,n);else{var u=t,l=e,c=n,h=u[l-2*c],f=u[l-c],d=u[l+0],p=u[l+c],g=u[l+2*c],m=27*(v=_r[1020+3*(d-f)+_r[1020+h-p]])+63>>7,b=18*v+63>>7,v=9*v+63>>7;u[l-3*c]=Pr[255+u[l-3*c]+v],u[l-2*c]=Pr[255+h+b],u[l-c]=Pr[255+f+m],u[l+0]=Pr[255+d-m],u[l+c]=Pr[255+p-b],u[l+2*c]=Pr[255+g-v]}e+=r}}function De(t,e,n,r,i,a,o,s){for(a=2*a+1;0<i--;){if(Oe(t,e,n,a,o))if(je(t,e,n,s))Ie(t,e,n);else{var u=t,l=e,c=n,h=u[l-c],f=u[l+0],d=u[l+c],p=Sr[112+((g=3*(f-h))+4>>3)],g=Sr[112+(g+3>>3)],m=p+1>>1;u[l-2*c]=Pr[255+u[l-2*c]+m],u[l-c]=Pr[255+h+g],u[l+0]=Pr[255+f-p],u[l+c]=Pr[255+d-m]}e+=r}}function Ue(t,e,n,r,i,a){Re(t,e,n,1,16,r,i,a)}function ze(t,e,n,r,i,a){Re(t,e,1,n,16,r,i,a)}function He(t,e,n,r,i,a){var o;for(o=3;0<o;--o)De(t,e+=4*n,n,1,16,r,i,a)}function We(t,e,n,r,i,a){var o;for(o=3;0<o;--o)De(t,e+=4,1,n,16,r,i,a)}function Ve(t,e,n,r,i,a,o,s){Re(t,e,i,1,8,a,o,s),Re(n,r,i,1,8,a,o,s)}function Ge(t,e,n,r,i,a,o,s){Re(t,e,1,i,8,a,o,s),Re(n,r,1,i,8,a,o,s)}function Ye(t,e,n,r,i,a,o,s){De(t,e+4*i,i,1,8,a,o,s),De(n,r+4*i,i,1,8,a,o,s)}function Je(t,e,n,r,i,a,o,s){De(t,e+4,1,i,8,a,o,s),De(n,r+4,1,i,8,a,o,s)}function Xe(){this.ba=new H,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new V,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function Ke(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function Ze(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function $e(){this.ua=0,this.Wa=new i,this.vb=new i,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new te,this.yc=new Qt}function Qe(){this.xb=this.a=0,this.l=new _t,this.ca=new H,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new F,this.Pb=0,this.wd=new F,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new $e,this.ab=0,this.gc=Ui(4,Ze),this.Oc=0}function tn(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new _t,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function en(t,e,n,r,i,a,o){for(t=null==t?0:t[e+0],e=0;e<o;++e)i[a+e]=t+n[r+e]&255,t=i[a+e]}function nn(t,e,n,r,i,a,o){var s;if(null==t)en(null,null,n,r,i,a,o);else for(s=0;s<o;++s)i[a+s]=t[e+s]+n[r+s]&255}function rn(t,e,n,r,i,a,o){if(null==t)en(null,null,n,r,i,a,o);else{var s,u=t[e+0],l=u,c=u;for(s=0;s<o;++s)l=c+(u=t[e+s])-l,c=n[r+s]+(-256&l?l<0?0:255:l)&255,l=u,i[a+s]=c}}function an(t,e,n,r){var i=e.width,a=e.o;if(Mi(null!=t&&null!=e),n<0||r<=0||a<n+r)return null;if(!t.Cc){if(null==t.ga){var o;if(t.ga=new tn,(o=null==t.ga)||(o=e.width*e.o,Mi(0==t.Gb.length),t.Gb=Di(o),t.Uc=0,o=!(o=null==t.Gb?0:(t.mb=t.Gb,t.nb=t.Uc,t.rc=null,1))),!o){o=t.ga;var s=t.Fa,u=t.P,l=t.qc,c=t.mb,h=t.nb,f=u+1,d=l-1,p=o.l;if(Mi(null!=s&&null!=c&&null!=e),li[0]=null,li[1]=en,li[2]=nn,li[3]=rn,o.ca=c,o.tb=h,o.c=e.width,o.i=e.height,Mi(0<o.c&&0<o.i),l<=1)e=0;else if(o.$a=s[u+0]>>0&3,o.Z=s[u+0]>>2&3,o.Lc=s[u+0]>>4&3,u=s[u+0]>>6&3,o.$a<0||1<o.$a||4<=o.Z||1<o.Lc||u)e=0;else if(p.put=K,p.ac=X,p.bc=Z,p.ma=o,p.width=e.width,p.height=e.height,p.Da=e.Da,p.v=e.v,p.va=e.va,p.j=e.j,p.o=e.o,o.$a)t:{Mi(1==o.$a),e=ut();e:for(;;){if(null==e){e=0;break t}if(Mi(null!=o),(o.mc=e).c=o.c,e.i=o.i,e.l=o.l,e.l.ma=o,e.l.width=o.c,e.l.height=o.i,e.a=0,k(e.m,s,f,d),!he(o.c,o.i,1,e,null))break e;if(!(e=1==e.ab&&3==e.gc[0].hc&&at(e.s)?(o.ic=1,s=e.c*e.i,e.Ta=null,e.Ua=0,e.V=Di(s),e.Ba=0,null==e.V?(e.a=1,0):1):(o.ic=0,lt(e,o.c))))break e;e=1;break t}o.mc=null,e=0}else e=d>=o.c*o.i;o=!e}if(o)return null;1!=t.ga.Lc?t.Ga=0:r=a-n}Mi(null!=t.ga),Mi(n+r<=a);t:{if(e=(s=t.ga).c,a=s.l.o,0==s.$a){if(f=t.rc,d=t.Vc,p=t.Fa,u=t.P+1+n*e,l=t.mb,c=t.nb+n*e,Mi(u<=t.P+t.qc),0!=s.Z)for(Mi(null!=li[s.Z]),o=0;o<r;++o)li[s.Z](f,d,p,u,l,c,e),f=l,d=c,c+=e,u+=e;else for(o=0;o<r;++o)qi(l,c,p,u,e),f=l,d=c,c+=e,u+=e;t.rc=f,t.Vc=d}else{if(Mi(null!=s.mc),e=n+r,Mi(null!=(o=s.mc)),Mi(e<=o.i),o.C>=e)e=1;else if(s.ic||cn(),s.ic){s=o.V,f=o.Ba,d=o.c;var g=o.i,m=(p=1,u=o.$/d,l=o.$%d,c=o.m,h=o.s,o.$),b=d*g,v=d*e,y=h.wc,w=m<v?nt(h,l,u):null;Mi(m<=b),Mi(e<=g),Mi(at(h));e:for(;;){for(;!c.h&&m<v;){if(l&y||(w=nt(h,l,u)),Mi(null!=w),ie(c),(g=et(w.G[0],w.H[0],c))<256)s[f+m]=g,++m,d<=++l&&(l=0,++u<=e&&!(u%16)&&st(o,u));else{if(!(g<280)){p=0;break e}g=Q(g-256,c);var x,N=et(w.G[4],w.H[4],c);if(ie(c),!((N=tt(d,N=Q(N,c)))<=m&&g<=b-m)){p=0;break e}for(x=0;x<g;++x)s[f+m+x]=s[f+m+x-N];for(m+=g,l+=g;d<=l;)l-=d,++u<=e&&!(u%16)&&st(o,u);m<v&&l&y&&(w=nt(h,l,u))}Mi(c.h==A(c))}st(o,e<u?e:u);break e}!p||c.h&&m<b?(p=0,o.a=c.h?5:3):o.$=m,e=p}else e=le(o,o.V,o.Ba,o.c,o.i,e,ct);if(!e){r=0;break t}}a<=n+r&&(t.Cc=1),r=1}if(!r)return null;if(t.Cc&&(null!=(r=t.ga)&&(r.mc=null),t.ga=null,0<t.Ga))return alert("todo:WebPDequantizeLevels"),null}return t.nb+n*i}function on(t,e,n,r,i,a){for(;0<i--;){var o,s=t,u=e+(n?1:0),l=t,c=e+(n?0:3);for(o=0;o<r;++o){var h=l[c+4*o];255!=h&&(h*=32897,s[u+4*o+0]=s[u+4*o+0]*h>>23,s[u+4*o+1]=s[u+4*o+1]*h>>23,s[u+4*o+2]=s[u+4*o+2]*h>>23)}e+=a}}function sn(t,e,n,r,i){for(;0<r--;){var a;for(a=0;a<n;++a){var o=t[e+2*a+0],s=15&(l=t[e+2*a+1]),u=4369*s,l=(240&l|l>>4)*u>>16;t[e+2*a+0]=(240&o|o>>4)*u>>16&240|(15&o|o<<4)*u>>16>>4&15,t[e+2*a+1]=240&l|s}e+=i}}function un(t,e,n,r,i,a,o,s){var u,l,c=255;for(l=0;l<i;++l){for(u=0;u<r;++u){var h=t[e+u];c&=a[o+4*u]=h}e+=n,o+=s}return 255!=c}function ln(t,e,n,r,i){var a;for(a=0;a<i;++a)n[r+a]=t[e+a]>>8}function cn(){mr=on,br=sn,vr=un,yr=ln}function hn(t,S,P){m[t]=function(t,e,n,r,i,a,o,s,u,l,c,h,f,d,p,g,m){var b,v=m-1>>1,y=i[a+0]|o[s+0]<<16,w=u[l+0]|c[h+0]<<16;Mi(null!=t);var x=3*y+w+131074>>2;for(S(t[e+0],255&x,x>>16,f,d),null!=n&&(x=3*w+y+131074>>2,S(n[r+0],255&x,x>>16,p,g)),b=1;b<=v;++b){var N=i[a+b]|o[s+b]<<16,L=u[l+b]|c[h+b]<<16,A=y+N+w+L+524296,_=A+2*(N+w)>>3;x=_+y>>1,y=(A=A+2*(y+L)>>3)+N>>1,S(t[e+2*b-1],255&x,x>>16,f,d+(2*b-1)*P),S(t[e+2*b-0],255&y,y>>16,f,d+(2*b-0)*P),null!=n&&(x=A+w>>1,y=_+L>>1,S(n[r+2*b-1],255&x,x>>16,p,g+(2*b-1)*P),S(n[r+2*b+0],255&y,y>>16,p,g+(2*b+0)*P)),y=N,w=L}1&m||(x=3*y+w+131074>>2,S(t[e+m-1],255&x,x>>16,f,d+(m-1)*P),null!=n&&(x=3*w+y+131074>>2,S(n[r+m-1],255&x,x>>16,p,g+(m-1)*P)))}}function fn(){ci[Fr]=hi,ci[Cr]=di,ci[Ir]=fi,ci[jr]=pi,ci[Br]=gi,ci[Or]=mi,ci[Er]=bi,ci[Mr]=di,ci[Tr]=pi,ci[qr]=gi,ci[Rr]=mi}function dn(t){return t&~Li?t<0?0:255:t>>Ni}function pn(t,e){return dn((19077*t>>8)+(26149*e>>8)-14234)}function gn(t,e,n){return dn((19077*t>>8)-(6419*e>>8)-(13320*n>>8)+8708)}function mn(t,e){return dn((19077*t>>8)+(33050*e>>8)-17685)}function bn(t,e,n,r,i){r[i+0]=pn(t,n),r[i+1]=gn(t,e,n),r[i+2]=mn(t,e)}function vn(t,e,n,r,i){r[i+0]=mn(t,e),r[i+1]=gn(t,e,n),r[i+2]=pn(t,n)}function yn(t,e,n,r,i){var a=gn(t,e,n);e=a<<3&224|mn(t,e)>>3,r[i+0]=248&pn(t,n)|a>>5,r[i+1]=e}function wn(t,e,n,r,i){var a=240&mn(t,e)|15;r[i+0]=240&pn(t,n)|gn(t,e,n)>>4,r[i+1]=a}function xn(t,e,n,r,i){r[i+0]=255,bn(t,e,n,r,i+1)}function Nn(t,e,n,r,i){vn(t,e,n,r,i),r[i+3]=255}function Ln(t,e,n,r,i){bn(t,e,n,r,i),r[i+3]=255}function At(t,e){return t<0?0:e<t?e:t}function An(t,c,h){m[t]=function(t,e,n,r,i,a,o,s,u){for(var l=s+(-2&u)*h;s!=l;)c(t[e+0],n[r+0],i[a+0],o,s),c(t[e+1],n[r+0],i[a+0],o,s+h),e+=2,++r,++a,s+=2*h;1&u&&c(t[e+0],n[r+0],i[a+0],o,s)}}function _n(t,e,n){return 0==n?0==t?0==e?6:5:0==e?4:0:n}function Sn(t,e,n,r,i){switch(t>>>30){case 3:Qn(e,n,r,i,0);break;case 2:tr(e,n,r,i);break;case 1:nr(e,n,r,i)}}function Pn(t,e){var n,r,i=e.M,a=e.Nb,o=t.oc,s=t.pc+40,u=t.oc,l=t.pc+584,c=t.oc,h=t.pc+600;for(n=0;n<16;++n)o[s+32*n-1]=129;for(n=0;n<8;++n)u[l+32*n-1]=129,c[h+32*n-1]=129;for(0<i?o[s-1-32]=u[l-1-32]=c[h-1-32]=129:(Ri(o,s-32-1,127,21),Ri(u,l-32-1,127,9),Ri(c,h-32-1,127,9)),r=0;r<t.za;++r){var f=e.ya[e.aa+r];if(0<r){for(n=-1;n<16;++n)qi(o,s+32*n-4,o,s+32*n+12,4);for(n=-1;n<8;++n)qi(u,l+32*n-4,u,l+32*n+4,4),qi(c,h+32*n-4,c,h+32*n+4,4)}var d=t.Gd,p=t.Hd+r,g=f.ad,m=f.Hc;if(0<i&&(qi(o,s-32,d[p].y,0,16),qi(u,l-32,d[p].f,0,8),qi(c,h-32,d[p].ea,0,8)),f.Za){var b=o,v=s-32+16;for(0<i&&(r>=t.za-1?Ri(b,v,d[p].y[15],4):qi(b,v,d[p+1].y,0,4)),n=0;n<4;n++)b[v+128+n]=b[v+256+n]=b[v+384+n]=b[v+0+n];for(n=0;n<16;++n,m<<=2)b=o,v=s+Ii[n],ai[f.Ob[n]](b,v),Sn(m,g,16*+n,b,v)}else if(b=_n(r,i,f.Ob[0]),ii[b](o,s),0!=m)for(n=0;n<16;++n,m<<=2)Sn(m,g,16*+n,o,s+Ii[n]);for(n=f.Gc,b=_n(r,i,f.Dd),oi[b](u,l),oi[b](c,h),m=g,b=u,v=l,255&(f=n>>0)&&(170&f?er(m,256,b,v):rr(m,256,b,v)),f=c,m=h,255&(n>>=8)&&(170&n?er(g,320,f,m):rr(g,320,f,m)),i<t.Ub-1&&(qi(d[p].y,0,o,s+480,16),qi(d[p].f,0,u,l+224,8),qi(d[p].ea,0,c,h+224,8)),n=8*a*t.B,d=t.sa,p=t.ta+16*r+16*a*t.R,g=t.qa,f=t.ra+8*r+n,m=t.Ha,b=t.Ia+8*r+n,n=0;n<16;++n)qi(d,p+n*t.R,o,s+32*n,16);for(n=0;n<8;++n)qi(g,f+n*t.B,u,l+32*n,8),qi(m,b+n*t.B,c,h+32*n,8)}}function kn(t,e,n,r,i,a,o,s,u){var l=[0],c=[0],h=0,f=null!=u?u.kd:0,d=null!=u?u:new Ke;if(null==t||n<12)return 7;d.data=t,e=[d.w=e],n=[d.ha=n],d.gb=[d.gb];t:{var p=e,g=n,m=d.gb;if(Mi(null!=t),Mi(null!=g),Mi(null!=m),12<=g[m[0]=0]&&!Ti(t,p[0],"RIFF")){if(Ti(t,p[0]+8,"WEBP")){m=3;break t}var b=I(t,p[0]+4);if(b<12||4294967286<b){m=3;break t}if(f&&b>g[0]-8){m=7;break t}m[0]=b,p[0]+=12,g[0]-=12}m=0}if(0!=m)return m;for(b=0<d.gb[0],n=n[0];;){t:{var v=t;g=e;var y=l,w=c,x=p=[0];if((m=n)[(A=h=[h])[0]=0]<8)m=7;else{if(!Ti(v,g[0],"VP8X")){if(10!=I(v,g[0]+4)){m=3;break t}if(m[0]<18){m=7;break t}var N=I(v,g[0]+8),L=1+C(v,g[0]+12);if(2147483648<=L*(v=1+C(v,g[0]+15))){m=3;break t}null!=x&&(x[0]=N),null!=y&&(y[0]=L),null!=w&&(w[0]=v),g[0]+=18,m[0]-=18,A[0]=1}m=0}}if(h=h[0],p=p[0],0!=m)return m;if(g=!!(2&p),!b&&h)return 3;if(null!=a&&(a[0]=!!(16&p)),null!=o&&(o[0]=g),null!=s&&(s[0]=0),o=l[0],p=c[0],h&&g&&null==u){m=0;break}if(n<4){m=7;break}if(b&&h||!b&&!h&&!Ti(t,e[0],"ALPH")){n=[n],d.na=[d.na],d.P=[d.P],d.Sa=[d.Sa];t:{N=t,m=e,b=n;var A=d.gb;y=d.na,w=d.P,x=d.Sa;L=22,Mi(null!=N),Mi(null!=b),v=m[0];var _=b[0];for(Mi(null!=y),Mi(null!=x),y[0]=null,w[0]=null,x[0]=0;;){if(m[0]=v,(b[0]=_)<8){m=7;break t}var S=I(N,v+4);if(4294967286<S){m=3;break t}var P=8+S+1&-2;if(L+=P,0<A&&A<L){m=3;break t}if(!Ti(N,v,"VP8 ")||!Ti(N,v,"VP8L")){m=0;break t}if(_[0]<P){m=7;break t}Ti(N,v,"ALPH")||(y[0]=N,w[0]=v+8,x[0]=S),v+=P,_-=P}}if(n=n[0],d.na=d.na[0],d.P=d.P[0],d.Sa=d.Sa[0],0!=m)break}n=[n],d.Ja=[d.Ja],d.xa=[d.xa];t:if(A=t,m=e,b=n,y=d.gb[0],w=d.Ja,x=d.xa,N=m[0],v=!Ti(A,N,"VP8 "),L=!Ti(A,N,"VP8L"),Mi(null!=A),Mi(null!=b),Mi(null!=w),Mi(null!=x),b[0]<8)m=7;else{if(v||L){if(A=I(A,N+4),12<=y&&y-12<A){m=3;break t}if(f&&A>b[0]-8){m=7;break t}w[0]=A,m[0]+=8,b[0]-=8,x[0]=L}else x[0]=5<=b[0]&&47==A[N+0]&&!(A[N+4]>>5),w[0]=b[0];m=0}if(n=n[0],d.Ja=d.Ja[0],d.xa=d.xa[0],e=e[0],0!=m)break;if(4294967286<d.Ja)return 3;if(null==s||g||(s[0]=d.xa?2:1),o=[o],p=[p],d.xa){if(n<5){m=7;break}s=o,f=p,g=a,t=null==t||n<5?0:5<=n&&47==t[e+0]&&!(t[e+4]>>5)?(b=[0],A=[0],y=[0],k(w=new F,t,e,n),$(w,b,A,y)?(null!=s&&(s[0]=b[0]),null!=f&&(f[0]=A[0]),null!=g&&(g[0]=y[0]),1):0):0}else{if(n<10){m=7;break}s=p,t=null==t||n<10||!kt(t,e+3,n-3)?0:(f=t[e+0]|t[e+1]<<8|t[e+2]<<16,g=16383&(t[e+7]<<8|t[e+6]),t=16383&(t[e+9]<<8|t[e+8]),1&f||3<(f>>1&7)||!(f>>4&1)||f>>5>=d.Ja||!g||!t?0:(o&&(o[0]=g),s&&(s[0]=t),1))}if(!t)return 3;if(o=o[0],p=p[0],h&&(l[0]!=o||c[0]!=p))return 3;null!=u&&(u[0]=d,u.offset=e-u.w,Mi(e-u.w<4294967286),Mi(u.offset==u.ha-n));break}return 0==m||7==m&&h&&null==u?(null!=a&&(a[0]|=null!=d.na&&0<d.na.length),null!=r&&(r[0]=o),null!=i&&(i[0]=p),0):m}function Fn(t,e,n){var r=e.width,i=e.height,a=0,o=0,s=r,u=i;if(e.Da=null!=t&&0<t.Da,e.Da&&(s=t.cd,u=t.bd,a=t.v,o=t.j,n<11||(a&=-2,o&=-2),a<0||o<0||s<=0||u<=0||r<a+s||i<o+u))return 0;if(e.v=a,e.j=o,e.va=a+s,e.o=o+u,e.U=s,e.T=u,e.da=null!=t&&0<t.da,e.da){if(!N(s,u,n=[t.ib],a=[t.hb]))return 0;e.ib=n[0],e.hb=a[0]}return e.ob=null!=t&&t.ob,e.Kb=null==t||!t.Sd,e.da&&(e.ob=e.ib<3*r/4&&e.hb<3*i/4,e.Kb=0),1}function Cn(t){if(null==t)return 2;if(t.S<11){var e=t.f.RGBA;e.fb+=(t.height-1)*e.A,e.A=-e.A}else e=t.f.kb,t=t.height,e.O+=(t-1)*e.fa,e.fa=-e.fa,e.N+=(t-1>>1)*e.Ab,e.Ab=-e.Ab,e.W+=(t-1>>1)*e.Db,e.Db=-e.Db,null!=e.F&&(e.J+=(t-1)*e.lb,e.lb=-e.lb);return 0}function In(t,e,n,r){if(null==r||t<=0||e<=0)return 2;if(null!=n){if(n.Da){var i=n.cd,a=n.bd,o=-2&n.v,s=-2&n.j;if(o<0||s<0||i<=0||a<=0||t<o+i||e<s+a)return 2;t=i,e=a}if(n.da){if(!N(t,e,i=[n.ib],a=[n.hb]))return 2;t=i[0],e=a[0]}}r.width=t,r.height=e;t:{var u=r.width,l=r.height;if(t=r.S,u<=0||l<=0||!(Fr<=t&&t<13))t=2;else{if(r.Rd<=0&&null==r.sd){o=a=i=e=0;var c=(s=u*Ei[t])*l;if(t<11||(a=(l+1)/2*(e=(u+1)/2),12==t&&(o=(i=u)*l)),null==(l=Di(c+2*a+o))){t=1;break t}r.sd=l,t<11?((u=r.f.RGBA).eb=l,u.fb=0,u.A=s,u.size=c):((u=r.f.kb).y=l,u.O=0,u.fa=s,u.Fd=c,u.f=l,u.N=0+c,u.Ab=e,u.Cd=a,u.ea=l,u.W=0+c+a,u.Db=e,u.Ed=a,12==t&&(u.F=l,u.J=0+c+2*a),u.Tc=o,u.lb=i)}if(e=1,i=r.S,a=r.width,o=r.height,Fr<=i&&i<13)if(i<11)t=r.f.RGBA,e&=(s=Math.abs(t.A))*(o-1)+a<=t.size,e&=s>=a*Ei[i],e&=null!=t.eb;else{t=r.f.kb,s=(a+1)/2,c=(o+1)/2,u=Math.abs(t.fa);l=Math.abs(t.Ab);var h=Math.abs(t.Db),f=Math.abs(t.lb),d=f*(o-1)+a;e&=u*(o-1)+a<=t.Fd,e&=l*(c-1)+s<=t.Cd,e=(e&=h*(c-1)+s<=t.Ed)&a<=u&s<=l&s<=h,e&=null!=t.y,e&=null!=t.f,e&=null!=t.ea,12==i&&(e&=a<=f,e&=d<=t.Tc,e&=null!=t.F)}else e=0;t=e?0:2}}return 0!=t||null!=n&&n.fd&&(t=Cn(r)),t}var jn=64,Bn=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,16777215],On=24,En=32,Mn=8,Tn=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];a("Predictor0","PredictorAdd0"),m.Predictor0=function(){return 4278190080},m.Predictor1=function(t){return t},m.Predictor2=function(t,e,n){return e[n+0]},m.Predictor3=function(t,e,n){return e[n+1]},m.Predictor4=function(t,e,n){return e[n-1]},m.Predictor5=function(t,e,n){return s(s(t,e[n+1]),e[n+0])},m.Predictor6=function(t,e,n){return s(t,e[n-1])},m.Predictor7=function(t,e,n){return s(t,e[n+0])},m.Predictor8=function(t,e,n){return s(e[n-1],e[n+0])},m.Predictor9=function(t,e,n){return s(e[n+0],e[n+1])},m.Predictor10=function(t,e,n){return s(s(t,e[n-1]),s(e[n+0],e[n+1]))},m.Predictor11=function(t,e,n){var r=e[n+0];return c(r>>24&255,t>>24&255,(e=e[n-1])>>24&255)+c(r>>16&255,t>>16&255,e>>16&255)+c(r>>8&255,t>>8&255,e>>8&255)+c(255&r,255&t,255&e)<=0?r:t},m.Predictor12=function(t,e,n){var r=e[n+0];return(u((t>>24&255)+(r>>24&255)-((e=e[n-1])>>24&255))<<24|u((t>>16&255)+(r>>16&255)-(e>>16&255))<<16|u((t>>8&255)+(r>>8&255)-(e>>8&255))<<8|u((255&t)+(255&r)-(255&e)))>>>0},m.Predictor13=function(t,e,n){var r=e[n-1];return(l((t=s(t,e[n+0]))>>24&255,r>>24&255)<<24|l(t>>16&255,r>>16&255)<<16|l(t>>8&255,r>>8&255)<<8|l(t>>0&255,r>>0&255))>>>0};var qn=m.PredictorAdd0;m.PredictorAdd1=B,a("Predictor2","PredictorAdd2"),a("Predictor3","PredictorAdd3"),a("Predictor4","PredictorAdd4"),a("Predictor5","PredictorAdd5"),a("Predictor6","PredictorAdd6"),a("Predictor7","PredictorAdd7"),a("Predictor8","PredictorAdd8"),a("Predictor9","PredictorAdd9"),a("Predictor10","PredictorAdd10"),a("Predictor11","PredictorAdd11"),a("Predictor12","PredictorAdd12"),a("Predictor13","PredictorAdd13");var Rn=m.PredictorAdd2;d("ColorIndexInverseTransform","MapARGB","32b",function(t){return t>>8&255},function(t){return t}),d("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",function(t){return t},function(t){return t>>8&255});var Dn,Un=m.ColorIndexInverseTransform,zn=m.MapARGB,Hn=m.VP8LColorIndexInverseTransformAlpha,Wn=m.MapAlpha,Vn=m.VP8LPredictorsAdd=[];Vn.length=16,(m.VP8LPredictors=[]).length=16,(m.VP8LPredictorsAdd_C=[]).length=16,(m.VP8LPredictors_C=[]).length=16;var Gn,Yn,Jn,Xn,Kn,Zn,$n,Qn,tr,er,nr,rr,ir,ar,or,sr,ur,lr,cr,hr,fr,dr,pr,gr,mr,br,vr,yr,wr=Di(511),xr=Di(2041),Nr=Di(225),Lr=Di(767),Ar=0,_r=xr,Sr=Nr,Pr=Lr,kr=wr,Fr=0,Cr=1,Ir=2,jr=3,Br=4,Or=5,Er=6,Mr=7,Tr=8,qr=9,Rr=10,Dr=[2,3,7],Ur=[3,3,11],zr=[280,256,256,256,40],Hr=[0,1,1,1,0],Wr=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],Vr=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],Gr=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],Yr=8,Jr=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],Xr=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],Kr=null,Zr=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],$r=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],Qr=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],ti=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],ei=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],ni=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],ri=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],ii=[],ai=[],oi=[],si=1,ui=2,li=[],ci=[];hn("UpsampleRgbLinePair",bn,3),hn("UpsampleBgrLinePair",vn,3),hn("UpsampleRgbaLinePair",Ln,4),hn("UpsampleBgraLinePair",Nn,4),hn("UpsampleArgbLinePair",xn,4),hn("UpsampleRgba4444LinePair",wn,2),hn("UpsampleRgb565LinePair",yn,2);var hi=void 0,fi=void 0,di=void 0,pi=void 0,gi=void 0,mi=void 0,bi=void 0,vi=16,yi=1<<vi-1,wi=-227,xi=482,Ni=6,Li=(256<<Ni)-1,Ai=0,_i=Di(256),Si=Di(256),Pi=Di(256),ki=Di(256),Fi=Di(xi-wi),Ci=Di(xi-wi);An("YuvToRgbRow",bn,3),An("YuvToBgrRow",vn,3),An("YuvToRgbaRow",Ln,4),An("YuvToBgraRow",Nn,4),An("YuvToArgbRow",xn,4),An("YuvToRgba4444Row",wn,2),An("YuvToRgb565Row",yn,2);var Ii=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],ji=[0,2,8],Bi=[8,7,6,4,4,2,2,2,1,1,1,1],Oi=1;this.WebPDecodeRGBA=function(t,e,n,r,i){var a=Cr,o=new Xe,s=new H;(o.ba=s).S=a,s.width=[s.width],s.height=[s.height];var u=s.width,l=s.height,c=new W;if(null==c||null==t)var h=2;else Mi(null!=c),h=kn(t,e,n,c.width,c.height,c.Pd,c.Qd,c.format,null);if(u=0!=h?0:(null!=u&&(u[0]=c.width[0]),null!=l&&(l[0]=c.height[0]),1)){s.width=s.width[0],s.height=s.height[0],null!=r&&(r[0]=s.width),null!=i&&(i[0]=s.height);t:{if(r=new _t,(i=new Ke).data=t,i.w=e,i.ha=n,i.kd=1,e=[0],Mi(null!=i),0!=(t=kn(i.data,i.w,i.ha,null,null,null,e,null,i))&&7!=t||!e[0]||(t=4),0==(e=t)){if(Mi(null!=o),r.data=i.data,r.w=i.w+i.offset,r.ha=i.ha-i.offset,r.put=K,r.ac=X,r.bc=Z,r.ma=o,i.xa){if(null==(t=ut())){o=1;break t}if(function(t,e){var n=[0],r=[0],i=[0];e:for(;;){if(null==t)return 0;if(null==e)return t.a=2,0;if(t.l=e,t.a=0,k(t.m,e.data,e.w,e.ha),!$(t.m,n,r,i)){t.a=3;break e}if(t.xb=ui,e.width=n[0],e.height=r[0],!he(n[0],r[0],1,t,null))break e;return 1}return Mi(0!=t.a),0}(t,r)){if(r=0==(e=In(r.width,r.height,o.Oa,o.ba))){e:{r=t;n:for(;;){if(null==r){r=0;break e}if(Mi(null!=r.s.yc),Mi(null!=r.s.Ya),Mi(0<r.s.Wb),Mi(null!=(n=r.l)),Mi(null!=(i=n.ma)),0!=r.xb){if(r.ca=i.ba,r.tb=i.tb,Mi(null!=r.ca),!Fn(i.Oa,n,jr)){r.a=2;break n}if(!lt(r,n.width))break n;if(n.da)break n;if((n.da||D(r.ca.S))&&cn(),r.ca.S<11||(alert("todo:WebPInitConvertARGBToYUV"),null!=r.ca.f.kb.F&&cn()),r.Pb&&0<r.s.ua&&null==r.s.vb.X&&!ae(r.s.vb,r.s.Wa.Xa)){r.a=1;break n}r.xb=0}if(!le(r,r.V,r.Ba,r.c,r.i,n.o,it))break n;i.Dc=r.Ma,r=1;break e}Mi(0!=r.a),r=0}r=!r}r&&(e=t.a)}else e=t.a}else{if(null==(t=new St)){o=1;break t}if(t.Fa=i.na,t.P=i.P,t.qc=i.Sa,Ft(t,r)){if(0==(e=In(r.width,r.height,o.Oa,o.ba))){if(t.Aa=0,n=o.Oa,Mi(null!=(i=t)),null!=n){if(0<(u=(u=n.Md)<0?0:100<u?255:255*u/100)){for(l=c=0;l<4;++l)(h=i.pb[l]).lc<12&&(h.ia=u*Bi[h.lc<0?0:h.lc]>>3),c|=h.ia;c&&(alert("todo:VP8InitRandom"),i.ia=1)}i.Ga=n.Id,100<i.Ga?i.Ga=100:i.Ga<0&&(i.Ga=0)}jt(t,r)||(e=t.a)}}else e=t.a}0==e&&null!=o.Oa&&o.Oa.fd&&(e=Cn(o.ba))}o=e}a=0!=o?null:a<11?s.f.RGBA.eb:s.f.kb.y}else a=null;return a};var Ei=[3,4,3,4,4,2,2,4,4,4,2,1,1]});var e=[0],n=[0],r=[],i=new ot,a=t,o=function(t,e){var n={},r=0,i=!1,a=0,o=0;if(n.frames=[],!
/*Copyright (c) 2017 Dominik Homberger
    
    https://webpjs.appspot.com
    WebPRiffParser <EMAIL>
    */
function(t,e,n,r){for(var i=0;i<r;i++)if(t[e+i]!=n.charCodeAt(i))return!0;return!1}(t,e,"RIFF",4)){var s,u;for(b(t,e+=4),e+=8;e<t.length;){var l=g(t,e),c=b(t,e+=4);e+=4;var h=c+(1&c);switch(l){case"VP8 ":case"VP8L":void 0===n.frames[r]&&(n.frames[r]={}),(p=n.frames[r]).src_off=i?o:e-8,p.src_size=a+c+8,r++,i&&(i=!1,o=a=0);break;case"VP8X":(p=n.header={}).feature_flags=t[e];var f=e+4;p.canvas_width=1+m(t,f),f+=3,p.canvas_height=1+m(t,f),f+=3;break;case"ALPH":i=!0,a=h+8,o=e-8;break;case"ANIM":(p=n.header).bgcolor=b(t,e),f=e+4,p.loop_count=(s=t)[(u=f)+0]<<0|s[u+1]<<8,f+=2;break;case"ANMF":var d,p;(p=n.frames[r]={}).offset_x=2*m(t,e),e+=3,p.offset_y=2*m(t,e),e+=3,p.width=1+m(t,e),e+=3,p.height=1+m(t,e),e+=3,p.duration=m(t,e),e+=3,d=t[e++],p.dispose=1&d,p.blend=d>>1&1}"ANMF"!=l&&(e+=h)}return n}}(a,0);o.response=a,o.rgbaoutput=!0,o.dataurl=!1;var s=o.header?o.header:null,u=o.frames?o.frames:null;if(s){s.loop_counter=s.loop_count,e=s.canvas_height,n=s.canvas_width;for(var l=0;l<u.length&&0!=u[l].blend;l++);}var c=u[0],h=i.WebPDecodeRGBA(a,c.src_off,c.src_size,n,e);c.rgba=h,c.imgwidth=n[0],c.imgheight=e[0];for(var f=0;f<n[0]*e[0]*4;f++)r[f]=h[f];return this.width=n,this.height=e,this.data=r,this}ot.prototype.getData=function(){return this.data};try{exports.WebPDecoder=ot}catch(t){}
/*
   Copyright (c) 2013 Gildas Lormeau. All rights reserved.

   Redistribution and use in source and binary forms, with or without
   modification, are permitted provided that the following conditions are met:

   1. Redistributions of source code must retain the above copyright notice,
   this list of conditions and the following disclaimer.

   2. Redistributions in binary form must reproduce the above copyright 
   notice, this list of conditions and the following disclaimer in 
   the documentation and/or other materials provided with the distribution.

   3. The names of the authors may not be used to endorse or promote products
   derived from this software without specific prior written permission.

   THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED WARRANTIES,
   INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
   FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL JCRAFT,
   INC. OR ANY CONTRIBUTORS TO THIS SOFTWARE BE LIABLE FOR ANY DIRECT, INDIRECT,
   INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
   LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
   OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
   LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
   NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
   EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
   */
/*
   Copyright (c) 2013 Gildas Lormeau. All rights reserved.

   Redistribution and use in source and binary forms, with or without
   modification, are permitted provided that the following conditions are met:

   1. Redistributions of source code must retain the above copyright notice,
   this list of conditions and the following disclaimer.

   2. Redistributions in binary form must reproduce the above copyright 
   notice, this list of conditions and the following disclaimer in 
   the documentation and/or other materials provided with the distribution.

   3. The names of the authors may not be used to endorse or promote products
   derived from this software without specific prior written permission.

   THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED WARRANTIES,
   INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
   FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL JCRAFT,
   INC. OR ANY CONTRIBUTORS TO THIS SOFTWARE BE LIABLE FOR ANY DIRECT, INDIRECT,
   INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
   LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
   OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
   LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
   NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
   EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
   */
!function(t){var p=15,g=573,e=[0,1,2,3,4,4,5,5,6,6,6,6,7,7,7,7,8,8,8,8,8,8,8,8,9,9,9,9,9,9,9,9,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,0,0,16,17,18,18,19,19,20,20,20,20,21,21,21,21,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29];function ht(){var d=this;function u(t,e){for(var n=0;n|=1&t,t>>>=1,n<<=1,0<--e;);return n>>>1}d.build_tree=function(t){var e,n,r,i=d.dyn_tree,a=d.stat_desc.static_tree,o=d.stat_desc.elems,s=-1;for(t.heap_len=0,t.heap_max=g,e=0;e<o;e++)0!==i[2*e]?(t.heap[++t.heap_len]=s=e,t.depth[e]=0):i[2*e+1]=0;for(;t.heap_len<2;)i[2*(r=t.heap[++t.heap_len]=s<2?++s:0)]=1,t.depth[r]=0,t.opt_len--,a&&(t.static_len-=a[2*r+1]);for(d.max_code=s,e=Math.floor(t.heap_len/2);1<=e;e--)t.pqdownheap(i,e);for(r=o;e=t.heap[1],t.heap[1]=t.heap[t.heap_len--],t.pqdownheap(i,1),n=t.heap[1],t.heap[--t.heap_max]=e,t.heap[--t.heap_max]=n,i[2*r]=i[2*e]+i[2*n],t.depth[r]=Math.max(t.depth[e],t.depth[n])+1,i[2*e+1]=i[2*n+1]=r,t.heap[1]=r++,t.pqdownheap(i,1),2<=t.heap_len;);t.heap[--t.heap_max]=t.heap[1],function(t){var e,n,r,i,a,o,s=d.dyn_tree,u=d.stat_desc.static_tree,l=d.stat_desc.extra_bits,c=d.stat_desc.extra_base,h=d.stat_desc.max_length,f=0;for(i=0;i<=p;i++)t.bl_count[i]=0;for(s[2*t.heap[t.heap_max]+1]=0,e=t.heap_max+1;e<g;e++)h<(i=s[2*s[2*(n=t.heap[e])+1]+1]+1)&&(i=h,f++),s[2*n+1]=i,n>d.max_code||(t.bl_count[i]++,a=0,c<=n&&(a=l[n-c]),o=s[2*n],t.opt_len+=o*(i+a),u&&(t.static_len+=o*(u[2*n+1]+a)));if(0!==f){do{for(i=h-1;0===t.bl_count[i];)i--;t.bl_count[i]--,t.bl_count[i+1]+=2,t.bl_count[h]--,f-=2}while(0<f);for(i=h;0!==i;i--)for(n=t.bl_count[i];0!==n;)(r=t.heap[--e])>d.max_code||(s[2*r+1]!==i&&(t.opt_len+=(i-s[2*r+1])*s[2*r],s[2*r+1]=i),n--)}}(t),function(t,e,n){var r,i,a,o=[],s=0;for(r=1;r<=p;r++)o[r]=s=s+n[r-1]<<1;for(i=0;i<=e;i++)0!==(a=t[2*i+1])&&(t[2*i]=u(o[a]++,a))}(i,d.max_code,t.bl_count)}}function ft(t,e,n,r,i){this.static_tree=t,this.extra_bits=e,this.extra_base=n,this.elems=r,this.max_length=i}ht._length_code=[0,1,2,3,4,5,6,7,8,8,9,9,10,10,11,11,12,12,12,12,13,13,13,13,14,14,14,14,15,15,15,15,16,16,16,16,16,16,16,16,17,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,28],ht.base_length=[0,1,2,3,4,5,6,7,8,10,12,14,16,20,24,28,32,40,48,56,64,80,96,112,128,160,192,224,0],ht.base_dist=[0,1,2,3,4,6,8,12,16,24,32,48,64,96,128,192,256,384,512,768,1024,1536,2048,3072,4096,6144,8192,12288,16384,24576],ht.d_code=function(t){return t<256?e[t]:e[256+(t>>>7)]},ht.extra_lbits=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],ht.extra_dbits=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],ht.extra_blbits=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],ht.bl_order=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],ft.static_ltree=[12,8,140,8,76,8,204,8,44,8,172,8,108,8,236,8,28,8,156,8,92,8,220,8,60,8,188,8,124,8,252,8,2,8,130,8,66,8,194,8,34,8,162,8,98,8,226,8,18,8,146,8,82,8,210,8,50,8,178,8,114,8,242,8,10,8,138,8,74,8,202,8,42,8,170,8,106,8,234,8,26,8,154,8,90,8,218,8,58,8,186,8,122,8,250,8,6,8,134,8,70,8,198,8,38,8,166,8,102,8,230,8,22,8,150,8,86,8,214,8,54,8,182,8,118,8,246,8,14,8,142,8,78,8,206,8,46,8,174,8,110,8,238,8,30,8,158,8,94,8,222,8,62,8,190,8,126,8,254,8,1,8,129,8,65,8,193,8,33,8,161,8,97,8,225,8,17,8,145,8,81,8,209,8,49,8,177,8,113,8,241,8,9,8,137,8,73,8,201,8,41,8,169,8,105,8,233,8,25,8,153,8,89,8,217,8,57,8,185,8,121,8,249,8,5,8,133,8,69,8,197,8,37,8,165,8,101,8,229,8,21,8,149,8,85,8,213,8,53,8,181,8,117,8,245,8,13,8,141,8,77,8,205,8,45,8,173,8,109,8,237,8,29,8,157,8,93,8,221,8,61,8,189,8,125,8,253,8,19,9,275,9,147,9,403,9,83,9,339,9,211,9,467,9,51,9,307,9,179,9,435,9,115,9,371,9,243,9,499,9,11,9,267,9,139,9,395,9,75,9,331,9,203,9,459,9,43,9,299,9,171,9,427,9,107,9,363,9,235,9,491,9,27,9,283,9,155,9,411,9,91,9,347,9,219,9,475,9,59,9,315,9,187,9,443,9,123,9,379,9,251,9,507,9,7,9,263,9,135,9,391,9,71,9,327,9,199,9,455,9,39,9,295,9,167,9,423,9,103,9,359,9,231,9,487,9,23,9,279,9,151,9,407,9,87,9,343,9,215,9,471,9,55,9,311,9,183,9,439,9,119,9,375,9,247,9,503,9,15,9,271,9,143,9,399,9,79,9,335,9,207,9,463,9,47,9,303,9,175,9,431,9,111,9,367,9,239,9,495,9,31,9,287,9,159,9,415,9,95,9,351,9,223,9,479,9,63,9,319,9,191,9,447,9,127,9,383,9,255,9,511,9,0,7,64,7,32,7,96,7,16,7,80,7,48,7,112,7,8,7,72,7,40,7,104,7,24,7,88,7,56,7,120,7,4,7,68,7,36,7,100,7,20,7,84,7,52,7,116,7,3,8,131,8,67,8,195,8,35,8,163,8,99,8,227,8],ft.static_dtree=[0,5,16,5,8,5,24,5,4,5,20,5,12,5,28,5,2,5,18,5,10,5,26,5,6,5,22,5,14,5,30,5,1,5,17,5,9,5,25,5,5,5,21,5,13,5,29,5,3,5,19,5,11,5,27,5,7,5,23,5],ft.static_l_desc=new ft(ft.static_ltree,ht.extra_lbits,257,286,p),ft.static_d_desc=new ft(ft.static_dtree,ht.extra_dbits,0,30,p),ft.static_bl_desc=new ft(null,ht.extra_blbits,0,19,7);function n(t,e,n,r,i){this.good_length=t,this.max_lazy=e,this.nice_length=n,this.max_chain=r,this.func=i}var dt=[new n(0,0,0,0,0),new n(4,4,8,4,1),new n(4,5,16,8,1),new n(4,6,32,32,1),new n(4,4,16,16,2),new n(8,16,32,32,2),new n(8,16,128,128,2),new n(8,32,128,256,2),new n(32,128,258,1024,2),new n(32,258,258,4096,2)],pt=["need dictionary","stream end","","","stream error","data error","","buffer error","",""];function gt(t,e,n,r){var i=t[2*e],a=t[2*n];return i<a||i===a&&r[e]<=r[n]}function r(){var s,u,l,c,f,h,d,p,i,g,m,b,v,o,y,w,x,N,L,A,_,S,P,k,F,C,I,j,B,O,E,M,T,q,R,D,U,a,z,H,W,V=this,G=new ht,Y=new ht,J=new ht;function X(){var t;for(t=0;t<286;t++)E[2*t]=0;for(t=0;t<30;t++)M[2*t]=0;for(t=0;t<19;t++)T[2*t]=0;E[512]=1,V.opt_len=V.static_len=0,D=a=0}function K(t,e){var n,r,i=-1,a=t[1],o=0,s=7,u=4;for(0===a&&(s=138,u=3),t[2*(e+1)+1]=65535,n=0;n<=e;n++)r=a,a=t[2*(n+1)+1],++o<s&&r===a||(o<u?T[2*r]+=o:0!==r?(r!==i&&T[2*r]++,T[32]++):o<=10?T[34]++:T[36]++,i=r,u=(o=0)===a?(s=138,3):r===a?(s=6,3):(s=7,4))}function Z(t){V.pending_buf[V.pending++]=t}function $(t){Z(255&t),Z(t>>>8&255)}function Q(t,e){var n,r=e;16-r<W?($(H|=(n=t)<<W&65535),H=n>>>16-W,W+=r-16):(H|=t<<W&65535,W+=r)}function tt(t,e){var n=2*t;Q(65535&e[n],65535&e[1+n])}function et(t,e){var n,r,i=-1,a=t[1],o=0,s=7,u=4;for(0===a&&(s=138,u=3),n=0;n<=e;n++)if(r=a,a=t[2*(n+1)+1],!(++o<s&&r===a)){if(o<u)for(;tt(r,T),0!=--o;);else 0!==r?(r!==i&&(tt(r,T),o--),tt(16,T),Q(o-3,2)):o<=10?(tt(17,T),Q(o-3,3)):(tt(18,T),Q(o-11,7));i=r,u=(o=0)===a?(s=138,3):r===a?(s=6,3):(s=7,4)}}function nt(){16===W?($(H),W=H=0):8<=W&&(Z(255&H),H>>>=8,W-=8)}function rt(t,e){var n,r,i;if(V.pending_buf[U+2*D]=t>>>8&255,V.pending_buf[U+2*D+1]=255&t,V.pending_buf[q+D]=255&e,D++,0===t?E[2*e]++:(a++,t--,E[2*(ht._length_code[e]+256+1)]++,M[2*ht.d_code(t)]++),0==(8191&D)&&2<I){for(n=8*D,r=_-x,i=0;i<30;i++)n+=M[2*i]*(5+ht.extra_dbits[i]);if(n>>>=3,a<Math.floor(D/2)&&n<Math.floor(r/2))return!0}return D===R-1}function it(t,e){var n,r,i,a,o=0;if(0!==D)for(;n=V.pending_buf[U+2*o]<<8&65280|255&V.pending_buf[U+2*o+1],r=255&V.pending_buf[q+o],o++,0===n?tt(r,t):(tt((i=ht._length_code[r])+256+1,t),0!==(a=ht.extra_lbits[i])&&Q(r-=ht.base_length[i],a),tt(i=ht.d_code(--n),e),0!==(a=ht.extra_dbits[i])&&Q(n-=ht.base_dist[i],a)),o<D;);tt(256,t),z=t[513]}function at(){8<W?$(H):0<W&&Z(255&H),W=H=0}function ot(t,e,n){Q(0+(n?1:0),3),function(t,e,n){at(),z=8,n&&($(e),$(~e)),V.pending_buf.set(p.subarray(t,t+e),V.pending),V.pending+=e}(t,e,!0)}function e(t,e,n){var r,i,a=0;0<I?(G.build_tree(V),Y.build_tree(V),a=function(){var t;for(K(E,G.max_code),K(M,Y.max_code),J.build_tree(V),t=18;3<=t&&0===T[2*ht.bl_order[t]+1];t--);return V.opt_len+=3*(t+1)+5+5+4,t}(),r=V.opt_len+3+7>>>3,(i=V.static_len+3+7>>>3)<=r&&(r=i)):r=i=e+5,e+4<=r&&-1!==t?ot(t,e,n):i===r?(Q(2+(n?1:0),3),it(ft.static_ltree,ft.static_dtree)):(Q(4+(n?1:0),3),function(t,e,n){var r;for(Q(t-257,5),Q(e-1,5),Q(n-4,4),r=0;r<n;r++)Q(T[2*ht.bl_order[r]+1],3);et(E,t-1),et(M,e-1)}(G.max_code+1,Y.max_code+1,a+1),it(E,M)),X(),n&&at()}function st(t){e(0<=x?x:-1,_-x,t),x=_,s.flush_pending()}function ut(){var t,e,n,r;do{if(0===(r=i-P-_)&&0===_&&0===P)r=f;else if(-1===r)r--;else if(f+f-262<=_){for(p.set(p.subarray(f,f+f),0),S-=f,_-=f,x-=f,n=t=v;e=65535&m[--n],m[n]=f<=e?e-f:0,0!=--t;);for(n=t=f;e=65535&g[--n],g[n]=f<=e?e-f:0,0!=--t;);r+=f}if(0===s.avail_in)return;t=s.read_buf(p,_+P,r),3<=(P+=t)&&(b=((b=255&p[_])<<w^255&p[_+1])&y)}while(P<262&&0!==s.avail_in)}function lt(t){var e,n,r=F,i=_,a=k,o=f-262<_?_-(f-262):0,s=O,u=d,l=_+258,c=p[i+a-1],h=p[i+a];B<=k&&(r>>=2),P<s&&(s=P);do{if(p[(e=t)+a]===h&&p[e+a-1]===c&&p[e]===p[i]&&p[++e]===p[i+1]){i+=2,e++;do{}while(p[++i]===p[++e]&&p[++i]===p[++e]&&p[++i]===p[++e]&&p[++i]===p[++e]&&p[++i]===p[++e]&&p[++i]===p[++e]&&p[++i]===p[++e]&&p[++i]===p[++e]&&i<l);if(n=258-(l-i),i=l-258,a<n){if(S=t,s<=(a=n))break;c=p[i+a-1],h=p[i+a]}}}while((t=65535&g[t&u])>o&&0!=--r);return a<=P?a:P}function ct(t){return t.total_in=t.total_out=0,t.msg=null,V.pending=0,V.pending_out=0,u=113,c=0,G.dyn_tree=E,G.stat_desc=ft.static_l_desc,Y.dyn_tree=M,Y.stat_desc=ft.static_d_desc,J.dyn_tree=T,J.stat_desc=ft.static_bl_desc,W=H=0,z=8,X(),function(){var t;for(i=2*f,t=m[v-1]=0;t<v-1;t++)m[t]=0;C=dt[I].max_lazy,B=dt[I].good_length,O=dt[I].nice_length,F=dt[I].max_chain,N=k=2,b=A=P=x=_=0}(),0}V.depth=[],V.bl_count=[],V.heap=[],E=[],M=[],T=[],V.pqdownheap=function(t,e){for(var n=V.heap,r=n[e],i=e<<1;i<=V.heap_len&&(i<V.heap_len&&gt(t,n[i+1],n[i],V.depth)&&i++,!gt(t,r,n[i],V.depth));)n[e]=n[i],e=i,i<<=1;n[e]=r},V.deflateInit=function(t,e,n,r,i,a){return r||(r=8),i||(i=8),a||(a=0),t.msg=null,-1===e&&(e=6),i<1||9<i||8!==r||n<9||15<n||e<0||9<e||a<0||2<a?-2:(t.dstate=V,d=(f=1<<(h=n))-1,y=(v=1<<(o=i+7))-1,w=Math.floor((o+3-1)/3),p=new Uint8Array(2*f),g=[],m=[],R=1<<i+6,V.pending_buf=new Uint8Array(4*R),l=4*R,U=Math.floor(R/2),q=3*R,I=e,j=a,ct(t))},V.deflateEnd=function(){return 42!==u&&113!==u&&666!==u?-2:(V.pending_buf=null,p=g=m=null,V.dstate=null,113===u?-3:0)},V.deflateParams=function(t,e,n){var r=0;return-1===e&&(e=6),e<0||9<e||n<0||2<n?-2:(dt[I].func!==dt[e].func&&0!==t.total_in&&(r=t.deflate(1)),I!==e&&(C=dt[I=e].max_lazy,B=dt[I].good_length,O=dt[I].nice_length,F=dt[I].max_chain),j=n,r)},V.deflateSetDictionary=function(t,e,n){var r,i=n,a=0;if(!e||42!==u)return-2;if(i<3)return 0;for(f-262<i&&(a=n-(i=f-262)),p.set(e.subarray(a,a+i),0),x=_=i,b=((b=255&p[0])<<w^255&p[1])&y,r=0;r<=i-3;r++)b=(b<<w^255&p[r+2])&y,g[r&d]=m[b],m[b]=r;return 0},V.deflate=function(t,e){var n,r,i,a,o;if(4<e||e<0)return-2;if(!t.next_out||!t.next_in&&0!==t.avail_in||666===u&&4!==e)return t.msg=pt[4],-2;if(0===t.avail_out)return t.msg=pt[7],-5;if(s=t,a=c,c=e,42===u&&(r=8+(h-8<<4)<<8,3<(i=(I-1&255)>>1)&&(i=3),r|=i<<6,0!==_&&(r|=32),u=113,function(t){Z(t>>8&255),Z(255&t)}(r+=31-r%31)),0!==V.pending){if(s.flush_pending(),0===s.avail_out)return c=-1,0}else if(0===s.avail_in&&e<=a&&4!==e)return s.msg=pt[7],-5;if(666===u&&0!==s.avail_in)return t.msg=pt[7],-5;if(0!==s.avail_in||0!==P||0!==e&&666!==u){switch(o=-1,dt[I].func){case 0:o=function(t){var e,n=65535;for(l-5<n&&(n=l-5);;){if(P<=1){if(ut(),0===P&&0===t)return 0;if(0===P)break}if(_+=P,e=x+n,((P=0)===_||e<=_)&&(P=_-e,_=e,st(!1),0===s.avail_out))return 0;if(f-262<=_-x&&(st(!1),0===s.avail_out))return 0}return st(4===t),0===s.avail_out?4===t?2:0:4===t?3:1}(e);break;case 1:o=function(t){for(var e,n=0;;){if(P<262){if(ut(),P<262&&0===t)return 0;if(0===P)break}if(3<=P&&(b=(b<<w^255&p[_+2])&y,n=65535&m[b],g[_&d]=m[b],m[b]=_),0!==n&&(_-n&65535)<=f-262&&2!==j&&(N=lt(n)),3<=N)if(e=rt(_-S,N-3),P-=N,N<=C&&3<=P){for(N--;b=(b<<w^255&p[++_+2])&y,n=65535&m[b],g[_&d]=m[b],m[b]=_,0!=--N;);_++}else _+=N,N=0,b=((b=255&p[_])<<w^255&p[_+1])&y;else e=rt(0,255&p[_]),P--,_++;if(e&&(st(!1),0===s.avail_out))return 0}return st(4===t),0===s.avail_out?4===t?2:0:4===t?3:1}(e);break;case 2:o=function(t){for(var e,n,r=0;;){if(P<262){if(ut(),P<262&&0===t)return 0;if(0===P)break}if(3<=P&&(b=(b<<w^255&p[_+2])&y,r=65535&m[b],g[_&d]=m[b],m[b]=_),k=N,L=S,N=2,0!==r&&k<C&&(_-r&65535)<=f-262&&(2!==j&&(N=lt(r)),N<=5&&(1===j||3===N&&4096<_-S)&&(N=2)),3<=k&&N<=k){for(n=_+P-3,e=rt(_-1-L,k-3),P-=k-1,k-=2;++_<=n&&(b=(b<<w^255&p[_+2])&y,r=65535&m[b],g[_&d]=m[b],m[b]=_),0!=--k;);if(A=0,N=2,_++,e&&(st(!1),0===s.avail_out))return 0}else if(0!==A){if((e=rt(0,255&p[_-1]))&&st(!1),_++,P--,0===s.avail_out)return 0}else A=1,_++,P--}return 0!==A&&(e=rt(0,255&p[_-1]),A=0),st(4===t),0===s.avail_out?4===t?2:0:4===t?3:1}(e)}if(2!==o&&3!==o||(u=666),0===o||2===o)return 0===s.avail_out&&(c=-1),0;if(1===o){if(1===e)Q(2,3),tt(256,ft.static_ltree),nt(),1+z+10-W<9&&(Q(2,3),tt(256,ft.static_ltree),nt()),z=7;else if(ot(0,0,!1),3===e)for(n=0;n<v;n++)m[n]=0;if(s.flush_pending(),0===s.avail_out)return c=-1,0}}return 4!==e?0:1}}function i(){this.next_in_index=0,this.next_out_index=0,this.avail_in=0,this.total_in=0,this.avail_out=0,this.total_out=0}i.prototype={deflateInit:function(t,e){return this.dstate=new r,e||(e=p),this.dstate.deflateInit(this,t,e)},deflate:function(t){return this.dstate?this.dstate.deflate(this,t):-2},deflateEnd:function(){if(!this.dstate)return-2;var t=this.dstate.deflateEnd();return this.dstate=null,t},deflateParams:function(t,e){return this.dstate?this.dstate.deflateParams(this,t,e):-2},deflateSetDictionary:function(t,e){return this.dstate?this.dstate.deflateSetDictionary(this,t,e):-2},read_buf:function(t,e,n){var r=this.avail_in;return n<r&&(r=n),0===r?0:(this.avail_in-=r,t.set(this.next_in.subarray(this.next_in_index,this.next_in_index+r),e),this.next_in_index+=r,this.total_in+=r,r)},flush_pending:function(){var t=this,e=t.dstate.pending;e>t.avail_out&&(e=t.avail_out),0!==e&&(t.next_out.set(t.dstate.pending_buf.subarray(t.dstate.pending_out,t.dstate.pending_out+e),t.next_out_index),t.next_out_index+=e,t.dstate.pending_out+=e,t.total_out+=e,t.avail_out-=e,t.dstate.pending-=e,0===t.dstate.pending&&(t.dstate.pending_out=0))}};var a=t.zip||t;a.Deflater=a._jzlib_Deflater=function(t){var s=new i,u=new Uint8Array(512),e=t?t.level:-1;void 0===e&&(e=-1),s.deflateInit(e),s.next_out=u,this.append=function(t,e){var n,r=[],i=0,a=0,o=0;if(t.length){s.next_in_index=0,s.next_in=t,s.avail_in=t.length;do{if(s.next_out_index=0,s.avail_out=512,0!==s.deflate(0))throw new Error("deflating: "+s.msg);s.next_out_index&&(512===s.next_out_index?r.push(new Uint8Array(u)):r.push(new Uint8Array(u.subarray(0,s.next_out_index)))),o+=s.next_out_index,e&&0<s.next_in_index&&s.next_in_index!==i&&(e(s.next_in_index),i=s.next_in_index)}while(0<s.avail_in||0===s.avail_out);return n=new Uint8Array(o),r.forEach(function(t){n.set(t,a),a+=t.length}),n}},this.flush=function(){var t,e,n=[],r=0,i=0;do{if(s.next_out_index=0,s.avail_out=512,1!==(t=s.deflate(4))&&0!==t)throw new Error("deflating: "+s.msg);0<512-s.avail_out&&n.push(new Uint8Array(u.subarray(0,s.next_out_index))),i+=s.next_out_index}while(0<s.avail_in||0===s.avail_out);return s.deflateEnd(),e=new Uint8Array(i),n.forEach(function(t){e.set(t,r),r+=t.length}),e}}}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||"undefined"!=typeof global&&global||Function('return typeof this === "object" && this.content')()||Function("return this")()),("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||"undefined"!=typeof global&&global||Function('return typeof this === "object" && this.content')()||Function("return this")()).RGBColor=function(t){var e;t=t||"",this.ok=!1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"}[t=(t=t.replace(/ /g,"")).toLowerCase()]||t;for(var n=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],r=0;r<n.length;r++){var i=n[r].re,a=n[r].process,o=i.exec(t);o&&(e=a(o),this.r=e[0],this.g=e[1],this.b=e[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:255<this.r?255:this.r,this.g=this.g<0||isNaN(this.g)?0:255<this.g?255:this.g,this.b=this.b<0||isNaN(this.b)?0:255<this.b?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),n=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==n.length&&(n="0"+n),"#"+t+e+n}},function(t){function e(t){var e;if(this.rawData=t,e=this.contents=new J(t),this.contents.pos=4,"ttcf"===e.readString(4))throw new Error("TTCF not supported.");e.pos=0,this.parse(),this.subset=new R(this),this.registerTTF()}t.API.TTFFont=(e.open=function(t){return new e(t)},e.prototype.parse=function(){return this.directory=new r(this.contents),this.head=new l(this),this.name=new L(this),this.cmap=new b(this),this.toUnicode={},this.hhea=new g(this),this.maxp=new _(this),this.hmtx=new P(this),this.post=new w(this),this.os2=new v(this),this.loca=new M(this),this.glyf=new C(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},e.prototype.registerTTF=function(){var i,t,e,n,r;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=function(){var t,e,n,r;for(r=[],t=0,e=(n=this.bbox).length;t<e;t++)i=n[t],r.push(Math.round(i*this.scaleFactor));return r}.call(this),this.stemV=0,this.post.exists?(e=255&(n=this.post.italic_angle),0!=(32768&(t=n>>16))&&(t=-(1+(65535^t))),this.italicAngle=+(t+"."+e)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=1===(r=this.familyClass)||2===r||3===r||4===r||5===r||7===r,this.isScript=10===this.familyClass,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),0!==this.italicAngle&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw new Error("No unicode cmap for font")},e.prototype.characterToGlyph=function(t){var e;return(null!=(e=this.cmap.unicode)?e.codeMap[t]:void 0)||0},e.prototype.widthOfGlyph=function(t){var e;return e=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(t).advance*e},e.prototype.widthOfString=function(t,e,n){var r,i,a,o;for(i=a=0,o=(t=""+t).length;0<=o?i<o:o<i;i=0<=o?++i:--i)r=t.charCodeAt(i),a+=this.widthOfGlyph(this.characterToGlyph(r))+n*(1e3/e)||0;return a*(e/1e3)},e.prototype.lineHeight=function(t,e){var n;return null==e&&(e=!1),n=e?this.lineGap:0,(this.ascender+n-this.decender)/1e3*t},e);var J=(n.prototype.readByte=function(){return this.data[this.pos++]},n.prototype.writeByte=function(t){return this.data[this.pos++]=t},n.prototype.readUInt32=function(){return 16777216*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},n.prototype.writeUInt32=function(t){return this.writeByte(t>>>24&255),this.writeByte(t>>16&255),this.writeByte(t>>8&255),this.writeByte(255&t)},n.prototype.readInt32=function(){var t;return 2147483648<=(t=this.readUInt32())?t-4294967296:t},n.prototype.writeInt32=function(t){return t<0&&(t+=4294967296),this.writeUInt32(t)},n.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},n.prototype.writeUInt16=function(t){return this.writeByte(t>>8&255),this.writeByte(255&t)},n.prototype.readInt16=function(){var t;return 32768<=(t=this.readUInt16())?t-65536:t},n.prototype.writeInt16=function(t){return t<0&&(t+=65536),this.writeUInt16(t)},n.prototype.readString=function(t){var e,n;for(n=[],e=0;0<=t?e<t:t<e;e=0<=t?++e:--e)n[e]=String.fromCharCode(this.readByte());return n.join("")},n.prototype.writeString=function(t){var e,n,r;for(r=[],e=0,n=t.length;0<=n?e<n:n<e;e=0<=n?++e:--e)r.push(this.writeByte(t.charCodeAt(e)));return r},n.prototype.readShort=function(){return this.readInt16()},n.prototype.writeShort=function(t){return this.writeInt16(t)},n.prototype.readLongLong=function(){var t,e,n,r,i,a,o,s;return t=this.readByte(),e=this.readByte(),n=this.readByte(),r=this.readByte(),i=this.readByte(),a=this.readByte(),o=this.readByte(),s=this.readByte(),128&t?-1*(72057594037927940*(255^t)+281474976710656*(255^e)+1099511627776*(255^n)+4294967296*(255^r)+16777216*(255^i)+65536*(255^a)+256*(255^o)+(255^s)+1):72057594037927940*t+281474976710656*e+1099511627776*n+4294967296*r+16777216*i+65536*a+256*o+s},n.prototype.writeLongLong=function(t){var e,n;return e=Math.floor(t/4294967296),n=4294967295&t,this.writeByte(e>>24&255),this.writeByte(e>>16&255),this.writeByte(e>>8&255),this.writeByte(255&e),this.writeByte(n>>24&255),this.writeByte(n>>16&255),this.writeByte(n>>8&255),this.writeByte(255&n)},n.prototype.readInt=function(){return this.readInt32()},n.prototype.writeInt=function(t){return this.writeInt32(t)},n.prototype.read=function(t){var e,n;for(e=[],n=0;0<=t?n<t:t<n;n=0<=t?++n:--n)e.push(this.readByte());return e},n.prototype.write=function(t){var e,n,r,i;for(i=[],n=0,r=t.length;n<r;n++)e=t[n],i.push(this.writeByte(e));return i},n);function n(t){this.data=null!=t?t:[],this.pos=0,this.length=this.data.length}var p,r=(i.prototype.encode=function(t){var e,n,r,i,a,o,s,u,l,c,h,f,d;for(d in h=Object.keys(t).length,o=Math.log(2),l=16*Math.floor(Math.log(h)/o),i=Math.floor(l/o),u=16*h-l,(n=new J).writeInt(this.scalarType),n.writeShort(h),n.writeShort(l),n.writeShort(i),n.writeShort(u),r=16*h,s=n.pos+r,a=null,f=[],t)for(c=t[d],n.writeString(d),n.writeInt(p(c)),n.writeInt(s),n.writeInt(c.length),f=f.concat(c),"head"===d&&(a=s),s+=c.length;s%4;)f.push(0),s++;return n.write(f),e=2981146554-p(n.data),n.pos=a+8,n.writeUInt32(e),n.data},p=function(t){var e,n,r,i;for(t=F.call(t);t.length%4;)t.push(0);for(r=new J(t),e=n=0,i=t.length;e<i;e=e+=4)n+=r.readUInt32();return 4294967295&n},i);function i(t){var e,n,r;for(this.scalarType=t.readInt(),this.tableCount=t.readShort(),this.searchRange=t.readShort(),this.entrySelector=t.readShort(),this.rangeShift=t.readShort(),this.tables={},n=0,r=this.tableCount;0<=r?n<r:r<n;n=0<=r?++n:--n)e={tag:t.readString(4),checksum:t.readInt(),offset:t.readInt(),length:t.readInt()},this.tables[e.tag]=e}function a(t,e){for(var n in e)s.call(e,n)&&(t[n]=e[n]);function r(){this.constructor=t}return r.prototype=e.prototype,t.prototype=new r,t.__super__=e.prototype,t}var o,s={}.hasOwnProperty;function u(t){var e;this.file=t,e=this.file.directory.tables[this.tag],this.exists=!!e,e&&(this.offset=e.offset,this.length=e.length,this.parse(this.file.contents))}u.prototype.parse=function(){},u.prototype.encode=function(){},u.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null};var l=(a(c,o=u),c.prototype.tag="head",c.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.revision=t.readInt(),this.checkSumAdjustment=t.readInt(),this.magicNumber=t.readInt(),this.flags=t.readShort(),this.unitsPerEm=t.readShort(),this.created=t.readLongLong(),this.modified=t.readLongLong(),this.xMin=t.readShort(),this.yMin=t.readShort(),this.xMax=t.readShort(),this.yMax=t.readShort(),this.macStyle=t.readShort(),this.lowestRecPPEM=t.readShort(),this.fontDirectionHint=t.readShort(),this.indexToLocFormat=t.readShort(),this.glyphDataFormat=t.readShort()},c.prototype.encode=function(t){var e;return(e=new J).writeInt(this.version),e.writeInt(this.revision),e.writeInt(this.checkSumAdjustment),e.writeInt(this.magicNumber),e.writeShort(this.flags),e.writeShort(this.unitsPerEm),e.writeLongLong(this.created),e.writeLongLong(this.modified),e.writeShort(this.xMin),e.writeShort(this.yMin),e.writeShort(this.xMax),e.writeShort(this.yMax),e.writeShort(this.macStyle),e.writeShort(this.lowestRecPPEM),e.writeShort(this.fontDirectionHint),e.writeShort(t),e.writeShort(this.glyphDataFormat),e.data},c);function c(){return c.__super__.constructor.apply(this,arguments)}var h=(f.encode=function(t,e){var n,r,i,a,o,s,u,l,c,h,f,d,p,g,m,b,v,y,w,x,N,L,A,_,S,P,k,F,C,I,j,B,O,E,M,T,q,R,D,U,z,H,W,V,G,Y;switch(F=new J,a=Object.keys(t).sort(function(t,e){return t-e}),e){case"macroman":for(p=0,g=function(){var t=[];for(d=0;d<256;++d)t.push(0);return t}(),b={0:0},i={},C=0,O=a.length;C<O;C++)null==b[W=t[r=a[C]]]&&(b[W]=++p),i[r]={old:t[r],new:b[t[r]]},g[r]=b[t[r]];return F.writeUInt16(1),F.writeUInt16(0),F.writeUInt32(12),F.writeUInt16(0),F.writeUInt16(262),F.writeUInt16(0),F.write(g),{charMap:i,subtable:F.data,maxGlyphID:p+1};case"unicode":for(P=[],c=[],b={},n={},m=u=null,I=v=0,E=a.length;I<E;I++)null==b[w=t[r=a[I]]]&&(b[w]=++v),n[r]={old:w,new:b[w]},o=b[w]-r,null!=m&&o===u||(m&&c.push(m),P.push(r),u=o),m=r;for(m&&c.push(m),c.push(65535),P.push(65535),_=2*(A=P.length),L=2*Math.pow(Math.log(A)/Math.LN2,2),h=Math.log(L/2)/Math.LN2,N=2*A-L,s=[],x=[],f=[],d=j=0,M=P.length;j<M;d=++j){if(S=P[d],l=c[d],65535===S){s.push(0),x.push(0);break}if(32768<=S-(k=n[S].new))for(s.push(0),x.push(2*(f.length+A-d)),r=B=S;S<=l?B<=l:l<=B;r=S<=l?++B:--B)f.push(n[r].new);else s.push(k-S),x.push(0)}for(F.writeUInt16(3),F.writeUInt16(1),F.writeUInt32(12),F.writeUInt16(4),F.writeUInt16(16+8*A+2*f.length),F.writeUInt16(0),F.writeUInt16(_),F.writeUInt16(L),F.writeUInt16(h),F.writeUInt16(N),z=0,T=c.length;z<T;z++)r=c[z],F.writeUInt16(r);for(F.writeUInt16(0),H=0,q=P.length;H<q;H++)r=P[H],F.writeUInt16(r);for(V=0,R=s.length;V<R;V++)o=s[V],F.writeUInt16(o);for(G=0,D=x.length;G<D;G++)y=x[G],F.writeUInt16(y);for(Y=0,U=f.length;Y<U;Y++)p=f[Y],F.writeUInt16(p);return{charMap:n,subtable:F.data,maxGlyphID:v+1}}},f);function f(n,t){var e,r,i,a,o,s,u,l,c,h,f,d,p,g,m,b,v;switch(this.platformID=n.readUInt16(),this.encodingID=n.readShort(),this.offset=t+n.readInt(),c=n.pos,n.pos=this.offset,this.format=n.readUInt16(),this.length=n.readUInt16(),this.language=n.readUInt16(),this.isUnicode=3===this.platformID&&1===this.encodingID&&4===this.format||0===this.platformID&&4===this.format,this.codeMap={},this.format){case 0:for(s=0;s<256;++s)this.codeMap[s]=n.readByte();break;case 4:for(f=n.readUInt16(),h=f/2,n.pos+=6,i=function(){var t,e;for(e=[],s=t=0;0<=h?t<h:h<t;s=0<=h?++t:--t)e.push(n.readUInt16());return e}(),n.pos+=2,p=function(){var t,e;for(e=[],s=t=0;0<=h?t<h:h<t;s=0<=h?++t:--t)e.push(n.readUInt16());return e}(),u=function(){var t,e;for(e=[],s=t=0;0<=h?t<h:h<t;s=0<=h?++t:--t)e.push(n.readUInt16());return e}(),l=function(){var t,e;for(e=[],s=t=0;0<=h?t<h:h<t;s=0<=h?++t:--t)e.push(n.readUInt16());return e}(),r=(this.length-n.pos+this.offset)/2,o=function(){var t,e;for(e=[],s=t=0;0<=r?t<r:r<t;s=0<=r?++t:--t)e.push(n.readUInt16());return e}(),s=m=0,v=i.length;m<v;s=++m)for(g=i[s],e=b=d=p[s];d<=g?b<=g:g<=b;e=d<=g?++b:--b)0===l[s]?a=e+u[s]:0!==(a=o[l[s]/2+(e-d)-(h-s)]||0)&&(a+=u[s]),this.codeMap[e]=65535&a}n.pos=c}var b=(a(d,o),d.prototype.tag="cmap",d.prototype.parse=function(t){var e,n,r;for(t.pos=this.offset,this.version=t.readUInt16(),r=t.readUInt16(),this.tables=[],this.unicode=null,n=0;0<=r?n<r:r<n;n=0<=r?++n:--n)e=new h(t,this.offset),this.tables.push(e),!e.isUnicode||null==this.unicode&&(this.unicode=e);return!0},d.encode=function(t,e){var n,r;return null==e&&(e="macroman"),n=h.encode(t,e),(r=new J).writeUInt16(0),r.writeUInt16(1),n.table=r.data.concat(n.subtable),n},d);function d(){return d.__super__.constructor.apply(this,arguments)}var g=(a(m,o),m.prototype.tag="hhea",m.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.ascender=t.readShort(),this.decender=t.readShort(),this.lineGap=t.readShort(),this.advanceWidthMax=t.readShort(),this.minLeftSideBearing=t.readShort(),this.minRightSideBearing=t.readShort(),this.xMaxExtent=t.readShort(),this.caretSlopeRise=t.readShort(),this.caretSlopeRun=t.readShort(),this.caretOffset=t.readShort(),t.pos+=8,this.metricDataFormat=t.readShort(),this.numberOfMetrics=t.readUInt16()},m);function m(){return m.__super__.constructor.apply(this,arguments)}var v=(a(y,o),y.prototype.tag="OS/2",y.prototype.parse=function(n){if(n.pos=this.offset,this.version=n.readUInt16(),this.averageCharWidth=n.readShort(),this.weightClass=n.readUInt16(),this.widthClass=n.readUInt16(),this.type=n.readShort(),this.ySubscriptXSize=n.readShort(),this.ySubscriptYSize=n.readShort(),this.ySubscriptXOffset=n.readShort(),this.ySubscriptYOffset=n.readShort(),this.ySuperscriptXSize=n.readShort(),this.ySuperscriptYSize=n.readShort(),this.ySuperscriptXOffset=n.readShort(),this.ySuperscriptYOffset=n.readShort(),this.yStrikeoutSize=n.readShort(),this.yStrikeoutPosition=n.readShort(),this.familyClass=n.readShort(),this.panose=function(){var t,e;for(e=[],t=0;t<10;++t)e.push(n.readByte());return e}(),this.charRange=function(){var t,e;for(e=[],t=0;t<4;++t)e.push(n.readInt());return e}(),this.vendorID=n.readString(4),this.selection=n.readShort(),this.firstCharIndex=n.readShort(),this.lastCharIndex=n.readShort(),0<this.version&&(this.ascent=n.readShort(),this.descent=n.readShort(),this.lineGap=n.readShort(),this.winAscent=n.readShort(),this.winDescent=n.readShort(),this.codePageRange=function(){var t,e;for(e=[],t=0;t<2;t=++t)e.push(n.readInt());return e}(),1<this.version))return this.xHeight=n.readShort(),this.capHeight=n.readShort(),this.defaultChar=n.readShort(),this.breakChar=n.readShort(),this.maxContext=n.readShort()},y);function y(){return y.__super__.constructor.apply(this,arguments)}var w=(a(x,o),x.prototype.tag="post",x.prototype.parse=function(r){var t,e,n;switch(r.pos=this.offset,this.format=r.readInt(),this.italicAngle=r.readInt(),this.underlinePosition=r.readShort(),this.underlineThickness=r.readShort(),this.isFixedPitch=r.readInt(),this.minMemType42=r.readInt(),this.maxMemType42=r.readInt(),this.minMemType1=r.readInt(),this.maxMemType1=r.readInt(),this.format){case 65536:break;case 131072:var i;for(e=r.readUInt16(),this.glyphNameIndex=[],i=0;0<=e?i<e:e<i;i=0<=e?++i:--i)this.glyphNameIndex.push(r.readUInt16());for(this.names=[],n=[];r.pos<this.offset+this.length;)t=r.readByte(),n.push(this.names.push(r.readString(t)));return n;case 151552:return e=r.readUInt16(),this.offsets=r.read(e);case 196608:break;case 262144:return this.map=function(){var t,e,n;for(n=[],i=t=0,e=this.file.maxp.numGlyphs;0<=e?t<e:e<t;i=0<=e?++t:--t)n.push(r.readUInt32());return n}.call(this)}},x);
/* comment : Store copyright information, platformID, encodingID, and languageID in the NameEntry object.*/function x(){return x.__super__.constructor.apply(this,arguments)}var N=function(t,e){this.raw=t,this.length=t.length,this.platformID=e.platformID,this.encodingID=e.encodingID,this.languageID=e.languageID},L=(a(A,o),A.prototype.tag="name",A.prototype.parse=function(t){var e,n,r,i,a,o,s,u,l,c,h;for(t.pos=this.offset,t.readShort(),e=t.readShort(),o=t.readShort(),n=[],i=0;0<=e?i<e:e<i;i=0<=e?++i:--i)n.push({platformID:t.readShort(),encodingID:t.readShort(),languageID:t.readShort(),nameID:t.readShort(),length:t.readShort(),offset:this.offset+o+t.readShort()});for(s={},i=l=0,c=n.length;l<c;i=++l)r=n[i],t.pos=r.offset,u=t.readString(r.length),a=new N(u,r),null==s[h=r.nameID]&&(s[h]=[]),s[r.nameID].push(a);this.strings=s,this.copyright=s[0],this.fontFamily=s[1],this.fontSubfamily=s[2],this.uniqueSubfamily=s[3],this.fontName=s[4],this.version=s[5];try{this.postscriptName=s[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch(t){this.postscriptName=s[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=s[7],this.manufacturer=s[8],this.designer=s[9],this.description=s[10],this.vendorUrl=s[11],this.designerUrl=s[12],this.license=s[13],this.licenseUrl=s[14],this.preferredFamily=s[15],this.preferredSubfamily=s[17],this.compatibleFull=s[18],this.sampleText=s[19]},A);function A(){return A.__super__.constructor.apply(this,arguments)}var _=(a(S,o),S.prototype.tag="maxp",S.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.numGlyphs=t.readUInt16(),this.maxPoints=t.readUInt16(),this.maxContours=t.readUInt16(),this.maxCompositePoints=t.readUInt16(),this.maxComponentContours=t.readUInt16(),this.maxZones=t.readUInt16(),this.maxTwilightPoints=t.readUInt16(),this.maxStorage=t.readUInt16(),this.maxFunctionDefs=t.readUInt16(),this.maxInstructionDefs=t.readUInt16(),this.maxStackElements=t.readUInt16(),this.maxSizeOfInstructions=t.readUInt16(),this.maxComponentElements=t.readUInt16(),this.maxComponentDepth=t.readUInt16()},S);function S(){return S.__super__.constructor.apply(this,arguments)}var P=(a(k,o),k.prototype.tag="hmtx",k.prototype.parse=function(n){var r,t,i,a,e,o,s;for(n.pos=this.offset,this.metrics=[],r=0,o=this.file.hhea.numberOfMetrics;0<=o?r<o:o<r;r=0<=o?++r:--r)this.metrics.push({advance:n.readUInt16(),lsb:n.readInt16()});for(i=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var t,e;for(e=[],r=t=0;0<=i?t<i:i<t;r=0<=i?++t:--t)e.push(n.readInt16());return e}(),this.widths=function(){var t,e,n,r;for(r=[],t=0,e=(n=this.metrics).length;t<e;t++)a=n[t],r.push(a.advance);return r}.call(this),t=this.widths[this.widths.length-1],s=[],r=e=0;0<=i?e<i:i<e;r=0<=i?++e:--e)s.push(this.widths.push(t));return s},k.prototype.forGlyph=function(t){return t in this.metrics?this.metrics[t]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[t-this.metrics.length]}},k);function k(){return k.__super__.constructor.apply(this,arguments)}var F=[].slice,C=(a(I,o),I.prototype.tag="glyf",I.prototype.parse=function(){return this.cache={}},I.prototype.glyphFor=function(t){var e,n,r,i,a,o,s,u,l,c;return t in this.cache?this.cache[t]:(i=this.file.loca,e=this.file.contents,n=i.indexOf(t),0===(r=i.lengthOf(t))?this.cache[t]=null:(e.pos=this.offset+n,a=(o=new J(e.read(r))).readShort(),u=o.readShort(),c=o.readShort(),s=o.readShort(),l=o.readShort(),this.cache[t]=-1===a?new O(o,u,c,s,l):new j(o,a,u,c,s,l),this.cache[t]))},I.prototype.encode=function(t,e,n){var r,i,a,o,s;for(a=[],i=[],o=0,s=e.length;o<s;o++)r=t[e[o]],i.push(a.length),r&&(a=a.concat(r.encode(n)));return i.push(a.length),{table:a,offsets:i}},I);function I(){return I.__super__.constructor.apply(this,arguments)}var j=(B.prototype.encode=function(){return this.raw.data},B);function B(t,e,n,r,i,a){this.raw=t,this.numberOfContours=e,this.xMin=n,this.yMin=r,this.xMax=i,this.yMax=a,this.compound=!1}var O=(E.prototype.encode=function(){var t,e,n;for(e=new J(F.call(this.raw.data)),t=0,n=this.glyphIDs.length;t<n;++t)e.pos=this.glyphOffsets[t];return e.data},E);function E(t,e,n,r,i){var a,o;for(this.raw=t,this.xMin=e,this.yMin=n,this.xMax=r,this.yMax=i,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],a=this.raw;o=a.readShort(),this.glyphOffsets.push(a.pos),this.glyphIDs.push(a.readShort()),32&o;)a.pos+=1&o?4:2,128&o?a.pos+=8:64&o?a.pos+=4:8&o&&(a.pos+=2)}var M=(a(T,o),T.prototype.tag="loca",T.prototype.parse=function(n){var t,r;return n.pos=this.offset,t=this.file.head.indexToLocFormat,this.offsets=0===t?function(){var t,e;for(e=[],r=0,t=this.length;r<t;r+=2)e.push(2*n.readUInt16());return e}.call(this):function(){var t,e;for(e=[],r=0,t=this.length;r<t;r+=4)e.push(n.readUInt32());return e}.call(this)},T.prototype.indexOf=function(t){return this.offsets[t]},T.prototype.lengthOf=function(t){return this.offsets[t+1]-this.offsets[t]},T.prototype.encode=function(t,e){for(var n=new Uint32Array(this.offsets.length),r=0,i=0,a=0;a<n.length;++a)if(n[a]=r,i<e.length&&e[i]==a){++i,n[a]=r;var o=this.offsets[a],s=this.offsets[a+1]-o;0<s&&(r+=s)}for(var u=new Array(4*n.length),l=0;l<n.length;++l)u[4*l+3]=255&n[l],u[4*l+2]=(65280&n[l])>>8,u[4*l+1]=(16711680&n[l])>>16,u[4*l]=(4278190080&n[l])>>24;return u},T);function T(){return T.__super__.constructor.apply(this,arguments)}var q,R=(D.prototype.generateCmap=function(){var t,e,n,r,i;for(e in r=this.font.cmap.tables[0].codeMap,t={},i=this.subset)n=i[e],t[e]=r[n];return t},D.prototype.glyphsFor=function(t){var e,n,r,i,a,o,s;for(r={},a=0,o=t.length;a<o;a++)r[i=t[a]]=this.font.glyf.glyphFor(i);for(i in e=[],r)(null!=(n=r[i])?n.compound:void 0)&&e.push.apply(e,n.glyphIDs);if(0<e.length)for(i in s=this.glyphsFor(e))n=s[i],r[i]=n;return r},D.prototype.encode=function(t,e){var n,r,i,a,o,s,u,l,c,h,f,d,p,g,m;for(r in n=b.encode(this.generateCmap(),"unicode"),a=this.glyphsFor(t),f={0:0},m=n.charMap)f[(s=m[r]).old]=s.new;for(d in h=n.maxGlyphID,a)d in f||(f[d]=h++);return l=function(t){var e,n;for(e in n={},t)n[t[e]]=e;return n}(f),c=Object.keys(l).sort(function(t,e){return t-e}),p=function(){var t,e,n;for(n=[],t=0,e=c.length;t<e;t++)o=c[t],n.push(l[o]);return n}(),i=this.font.glyf.encode(a,p,f),u=this.font.loca.encode(i.offsets,p),g={cmap:this.font.cmap.raw(),glyf:i.table,loca:u,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(e)},this.font.os2.exists&&(g["OS/2"]=this.font.os2.raw()),this.font.directory.encode(g)},D);function D(t){this.font=t,this.subset={},this.unicodes={},this.next=33}function U(){}t.API.PDFObject=(q=function(t,e){return(Array(e+1).join("0")+t).slice(-e)},U.convert=function(r){var i,t,e,n;if(Array.isArray(r))return"["+function(){var t,e,n;for(n=[],t=0,e=r.length;t<e;t++)i=r[t],n.push(U.convert(i));return n}().join(" ")+"]";if("string"==typeof r)return"/"+r;if(null!=r?r.isString:void 0)return"("+r+")";if(r instanceof Date)return"(D:"+q(r.getUTCFullYear(),4)+q(r.getUTCMonth(),2)+q(r.getUTCDate(),2)+q(r.getUTCHours(),2)+q(r.getUTCMinutes(),2)+q(r.getUTCSeconds(),2)+"Z)";if("[object Object]"!=={}.toString.call(r))return""+r;for(t in e=["<<"],r)n=r[t],e.push("/"+t+" "+U.convert(n));return e.push(">>"),e.join("\n")},U)}(U),(
/*
  # PNG.js
  # Copyright (c) 2011 Devon Govett
  # MIT LICENSE
  # 
  # 
  */
D="undefined"!=typeof self&&self||"undefined"!=typeof window&&window||"undefined"!=typeof global&&global||Function('return typeof this === "object" && this.content')()||Function("return this")()).PNG=function(){var l,n,r;function t(t){var e,n,r,i,a,o,s,u,l,c,h,f,d,p;for(this.data=t,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},o=null;;){switch(e=this.readUInt32(),l=function(){var t,e;for(e=[],t=0;t<4;++t)e.push(String.fromCharCode(this.data[this.pos++]));return e}.call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(e);break;case"fcTL":o&&this.animation.frames.push(o),this.pos+=4,o={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},a=this.readUInt16(),i=this.readUInt16()||100,o.delay=1e3*a/i,o.disposeOp=this.data[this.pos++],o.blendOp=this.data[this.pos++],o.data=[];break;case"IDAT":case"fdAT":for("fdAT"===l&&(this.pos+=4,e-=4),t=(null!=o?o.data:void 0)||this.imgData,f=0;0<=e?f<e:e<f;0<=e?++f:--f)t.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(r=this.palette.length/3,this.transparency.indexed=this.read(e),this.transparency.indexed.length>r)throw new Error("More transparent colors than palette size");if(0<(c=r-this.transparency.indexed.length))for(d=0;0<=c?d<c:c<d;0<=c?++d:--d)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(e)[0];break;case 2:this.transparency.rgb=this.read(e)}break;case"tEXt":s=(h=this.read(e)).indexOf(0),u=String.fromCharCode.apply(String,h.slice(0,s)),this.text[u]=String.fromCharCode.apply(String,h.slice(s+1));break;case"IEND":return o&&this.animation.frames.push(o),this.colors=function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}.call(this),this.hasAlphaChannel=4===(p=this.colorType)||6===p,n=this.colors+(this.hasAlphaChannel?1:0),this.pixelBitlength=this.bits*n,this.colorSpace=function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}.call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=e}if(this.pos+=4,this.pos>this.data.length)throw new Error("Incomplete or corrupt PNG file")}}function i(){if("[object Window]"!==Object.prototype.toString.call(D))return!1;try{n=D.document.createElement("canvas"),r=n.getContext("2d")}catch(t){return!1}return!0}return t.prototype.read=function(t){var e,n;for(n=[],e=0;0<=t?e<t:t<e;0<=t?++e:--e)n.push(this.data[this.pos++]);return n},t.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},t.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},t.prototype.decodePixels=function(I){var j=this.pixelBitlength/8,B=new Uint8Array(this.width*this.height*j),O=0,E=this;if(null==I&&(I=this.imgData),0===I.length)return new Uint8Array(0);function t(t,e,n,r){var i,a,o,s,u,l,c,h,f,d,p,g,m,b,v,y,w,x,N,L,A,_=Math.ceil((E.width-t)/n),S=Math.ceil((E.height-e)/r),P=E.width==_&&E.height==S;for(b=j*_,g=P?B:new Uint8Array(b*S),l=I.length,a=m=0;m<S&&O<l;){switch(I[O++]){case 0:for(s=w=0;w<b;s=w+=1)g[a++]=I[O++];break;case 1:for(s=x=0;x<b;s=x+=1)i=I[O++],u=s<j?0:g[a-j],g[a++]=(i+u)%256;break;case 2:for(s=N=0;N<b;s=N+=1)i=I[O++],o=(s-s%j)/j,v=m&&g[(m-1)*b+o*j+s%j],g[a++]=(v+i)%256;break;case 3:for(s=L=0;L<b;s=L+=1)i=I[O++],o=(s-s%j)/j,u=s<j?0:g[a-j],v=m&&g[(m-1)*b+o*j+s%j],g[a++]=(i+Math.floor((u+v)/2))%256;break;case 4:for(s=A=0;A<b;s=A+=1)i=I[O++],o=(s-s%j)/j,u=s<j?0:g[a-j],0===m?v=y=0:(v=g[(m-1)*b+o*j+s%j],y=o&&g[(m-1)*b+(o-1)*j+s%j]),c=u+v-y,h=Math.abs(c-u),d=Math.abs(c-v),p=Math.abs(c-y),f=h<=d&&h<=p?u:d<=p?v:y,g[a++]=(i+f)%256;break;default:throw new Error("Invalid filter algorithm: "+I[O-1])}if(!P){var k=((e+m*r)*E.width+t)*j,F=m*b;for(s=0;s<_;s+=1){for(var C=0;C<j;C+=1)B[k++]=g[F++];k+=(n-1)*j}}m++}}return I=(I=new lt(I)).getBytes(),1==E.interlaceMethod?(t(0,0,8,8),t(4,0,8,8),t(0,4,4,8),t(2,0,4,4),t(0,2,2,4),t(1,0,2,2),t(0,1,1,2)):t(0,0,1,1),B},t.prototype.decodePalette=function(){var t,e,n,r,i,a,o,s,u;for(n=this.palette,a=this.transparency.indexed||[],i=new Uint8Array((a.length||0)+n.length),e=o=t=r=0,s=n.length;o<s;e=o+=3)i[r++]=n[e],i[r++]=n[e+1],i[r++]=n[e+2],i[r++]=null!=(u=a[t++])?u:255;return i},t.prototype.copyToImageData=function(t,e){var n,r,i,a,o,s,u,l,c,h,f;if(r=this.colors,c=null,n=this.hasAlphaChannel,this.palette.length&&(c=null!=(f=this._decodedPalette)?f:this._decodedPalette=this.decodePalette(),r=4,n=!0),l=(i=t.data||t).length,o=c||e,a=s=0,1===r)for(;a<l;)u=c?4*e[a/4]:s,h=o[u++],i[a++]=h,i[a++]=h,i[a++]=h,i[a++]=n?o[u++]:255,s=u;else for(;a<l;)u=c?4*e[a/4]:s,i[a++]=o[u++],i[a++]=o[u++],i[a++]=o[u++],i[a++]=n?o[u++]:255,s=u},t.prototype.decode=function(){var t;return t=new Uint8Array(this.width*this.height*4),this.copyToImageData(t,this.decodePixels()),t},i(),l=function(t){if(!0!==i())throw new Error("This method requires a Browser with Canvas-capability.");var e;return r.width=t.width,r.height=t.height,r.clearRect(0,0,t.width,t.height),r.putImageData(t,0,0),(e=new Image).src=n.toDataURL(),e},t.prototype.decodeFrames=function(t){var e,n,r,i,a,o,s,u;if(this.animation){for(u=[],n=a=0,o=(s=this.animation.frames).length;a<o;n=++a)e=s[n],r=t.createImageData(e.width,e.height),i=this.decodePixels(new Uint8Array(e.data)),this.copyToImageData(r,i),e.imageData=r,u.push(e.image=l(r));return u}},t.prototype.renderFrame=function(t,e){var n,r,i;return n=(r=this.animation.frames)[e],i=r[e-1],0===e&&t.clearRect(0,0,this.width,this.height),1===(null!=i?i.disposeOp:void 0)?t.clearRect(i.xOffset,i.yOffset,i.width,i.height):2===(null!=i?i.disposeOp:void 0)&&t.putImageData(i.imageData,i.xOffset,i.yOffset),0===n.blendOp&&t.clearRect(n.xOffset,n.yOffset,n.width,n.height),t.drawImage(n.image,n.xOffset,n.yOffset)},t.prototype.animate=function(n){var r,i,a,o,s,t,u=this;return i=0,t=this.animation,o=t.numFrames,a=t.frames,s=t.numPlays,(r=function(){var t,e;if(t=i++%o,e=a[t],u.renderFrame(n,t),1<o&&i/o<s)return u.animation._timeout=setTimeout(r,e.delay)})()},t.prototype.stopAnimation=function(){var t;return clearTimeout(null!=(t=this.animation)?t._timeout:void 0)},t.prototype.render=function(t){var e,n;return t._png&&t._png.stopAnimation(),t._png=this,t.width=this.width,t.height=this.height,e=t.getContext("2d"),this.animation?(this.decodeFrames(e),this.animate(e)):(n=e.createImageData(this.width,this.height),this.copyToImageData(n,this.decodePixels()),e.putImageData(n,0,0))},t}();
/*
   * Extracted from pdf.js
   * https://github.com/andreasgal/pdf.js
   *
   * Copyright (c) 2011 Mozilla Foundation
   *
   * Contributors: Andreas Gal <<EMAIL>>
   *               Chris G Jones <<EMAIL>>
   *               Shaon Barman <<EMAIL>>
   *               Vivien Nicolas <<EMAIL>>
   *               Justin D'Arcangelo <<EMAIL>>
   *               Yury Delendik
   *
   * 
   */
var st=(ut.prototype={ensureBuffer:function(t){var e=this.buffer,n=e?e.byteLength:0;if(t<n)return e;for(var r=512;r<t;)r<<=1;for(var i=new Uint8Array(r),a=0;a<n;++a)i[a]=e[a];return this.buffer=i},getByte:function(){for(var t=this.pos;this.bufferLength<=t;){if(this.eof)return null;this.readBlock()}return this.buffer[this.pos++]},getBytes:function(t){var e=this.pos;if(t){this.ensureBuffer(e+t);for(var n=e+t;!this.eof&&this.bufferLength<n;)this.readBlock();var r=this.bufferLength;r<n&&(n=r)}else{for(;!this.eof;)this.readBlock();n=this.bufferLength}return this.pos=n,this.buffer.subarray(e,n)},lookChar:function(){for(var t=this.pos;this.bufferLength<=t;){if(this.eof)return null;this.readBlock()}return String.fromCharCode(this.buffer[this.pos])},getChar:function(){for(var t=this.pos;this.bufferLength<=t;){if(this.eof)return null;this.readBlock()}return String.fromCharCode(this.buffer[this.pos++])},makeSubStream:function(t,e,n){for(var r=t+e;this.bufferLength<=r&&!this.eof;)this.readBlock();return new Stream(this.buffer,t,e,n)},skip:function(t){t||(t=1),this.pos+=t},reset:function(){this.pos=0}},ut);function ut(){this.pos=0,this.bufferLength=0,this.eof=!1,this.buffer=null}var lt=("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||"undefined"!=typeof global&&global||Function('return typeof this === "object" && this.content')()||Function("return this")()).FlateStream=function(){if("undefined"!=typeof Uint32Array){var F=new Uint32Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),C=new Uint32Array([3,4,5,6,7,8,9,10,65547,65549,65551,65553,131091,131095,131099,131103,196643,196651,196659,196667,262211,262227,262243,262259,327811,327843,327875,327907,258,258,258]),I=new Uint32Array([1,2,3,4,65541,65543,131081,131085,196625,196633,262177,262193,327745,327777,393345,393409,459009,459137,524801,525057,590849,591361,657409,658433,724993,727041,794625,798721,868353,876545]),j=[new Uint32Array([459008,524368,524304,524568,459024,524400,524336,590016,459016,524384,524320,589984,524288,524416,524352,590048,459012,524376,524312,589968,459028,524408,524344,590032,459020,524392,524328,59e4,524296,524424,524360,590064,459010,524372,524308,524572,459026,524404,524340,590024,459018,524388,524324,589992,524292,524420,524356,590056,459014,524380,524316,589976,459030,524412,524348,590040,459022,524396,524332,590008,524300,524428,524364,590072,459009,524370,524306,524570,459025,524402,524338,590020,459017,524386,524322,589988,524290,524418,524354,590052,459013,524378,524314,589972,459029,524410,524346,590036,459021,524394,524330,590004,524298,524426,524362,590068,459011,524374,524310,524574,459027,524406,524342,590028,459019,524390,524326,589996,524294,524422,524358,590060,459015,524382,524318,589980,459031,524414,524350,590044,459023,524398,524334,590012,524302,524430,524366,590076,459008,524369,524305,524569,459024,524401,524337,590018,459016,524385,524321,589986,524289,524417,524353,590050,459012,524377,524313,589970,459028,524409,524345,590034,459020,524393,524329,590002,524297,524425,524361,590066,459010,524373,524309,524573,459026,524405,524341,590026,459018,524389,524325,589994,524293,524421,524357,590058,459014,524381,524317,589978,459030,524413,524349,590042,459022,524397,524333,590010,524301,524429,524365,590074,459009,524371,524307,524571,459025,524403,524339,590022,459017,524387,524323,589990,524291,524419,524355,590054,459013,524379,524315,589974,459029,524411,524347,590038,459021,524395,524331,590006,524299,524427,524363,590070,459011,524375,524311,524575,459027,524407,524343,590030,459019,524391,524327,589998,524295,524423,524359,590062,459015,524383,524319,589982,459031,524415,524351,590046,459023,524399,524335,590014,524303,524431,524367,590078,459008,524368,524304,524568,459024,524400,524336,590017,459016,524384,524320,589985,524288,524416,524352,590049,459012,524376,524312,589969,459028,524408,524344,590033,459020,524392,524328,590001,524296,524424,524360,590065,459010,524372,524308,524572,459026,524404,524340,590025,459018,524388,524324,589993,524292,524420,524356,590057,459014,524380,524316,589977,459030,524412,524348,590041,459022,524396,524332,590009,524300,524428,524364,590073,459009,524370,524306,524570,459025,524402,524338,590021,459017,524386,524322,589989,524290,524418,524354,590053,459013,524378,524314,589973,459029,524410,524346,590037,459021,524394,524330,590005,524298,524426,524362,590069,459011,524374,524310,524574,459027,524406,524342,590029,459019,524390,524326,589997,524294,524422,524358,590061,459015,524382,524318,589981,459031,524414,524350,590045,459023,524398,524334,590013,524302,524430,524366,590077,459008,524369,524305,524569,459024,524401,524337,590019,459016,524385,524321,589987,524289,524417,524353,590051,459012,524377,524313,589971,459028,524409,524345,590035,459020,524393,524329,590003,524297,524425,524361,590067,459010,524373,524309,524573,459026,524405,524341,590027,459018,524389,524325,589995,524293,524421,524357,590059,459014,524381,524317,589979,459030,524413,524349,590043,459022,524397,524333,590011,524301,524429,524365,590075,459009,524371,524307,524571,459025,524403,524339,590023,459017,524387,524323,589991,524291,524419,524355,590055,459013,524379,524315,589975,459029,524411,524347,590039,459021,524395,524331,590007,524299,524427,524363,590071,459011,524375,524311,524575,459027,524407,524343,590031,459019,524391,524327,589999,524295,524423,524359,590063,459015,524383,524319,589983,459031,524415,524351,590047,459023,524399,524335,590015,524303,524431,524367,590079]),9],B=[new Uint32Array([327680,327696,327688,327704,327684,327700,327692,327708,327682,327698,327690,327706,327686,327702,327694,0,327681,327697,327689,327705,327685,327701,327693,327709,327683,327699,327691,327707,327687,327703,327695,0]),5];return(t.prototype=Object.create(st.prototype)).getBits=function(t){for(var e,n=this.codeSize,r=this.codeBuf,i=this.bytes,a=this.bytesPos;n<t;)void 0===(e=i[a++])&&O("Bad encoding in flate stream"),r|=e<<n,n+=8;return e=r&(1<<t)-1,this.codeBuf=r>>t,this.codeSize=n-=t,this.bytesPos=a,e},t.prototype.getCode=function(t){for(var e=t[0],n=t[1],r=this.codeSize,i=this.codeBuf,a=this.bytes,o=this.bytesPos;r<n;){var s;void 0===(s=a[o++])&&O("Bad encoding in flate stream"),i|=s<<r,r+=8}var u=e[i&(1<<n)-1],l=u>>16,c=65535&u;return(0==r||r<l||0==l)&&O("Bad encoding in flate stream"),this.codeBuf=i>>l,this.codeSize=r-l,this.bytesPos=o,c},t.prototype.generateHuffmanTable=function(t){for(var e=t.length,n=0,r=0;r<e;++r)t[r]>n&&(n=t[r]);for(var i=1<<n,a=new Uint32Array(i),o=1,s=0,u=2;o<=n;++o,s<<=1,u<<=1)for(var l=0;l<e;++l)if(t[l]==o){var c=0,h=s;for(r=0;r<o;++r)c=c<<1|1&h,h>>=1;for(r=c;r<i;r+=u)a[r]=o<<16|l;++s}return[a,n]},t.prototype.readBlock=function(){function t(t,e,n,r,i){for(var a=t.getBits(n)+r;0<a--;)e[u++]=i}var e=this.getBits(3);if(1&e&&(this.eof=!0),0!=(e>>=1)){var n,r;if(1==e)n=j,r=B;else if(2==e){for(var i=this.getBits(5)+257,a=this.getBits(5)+1,o=this.getBits(4)+4,s=Array(F.length),u=0;u<o;)s[F[u++]]=this.getBits(3);for(var l=this.generateHuffmanTable(s),c=0,h=(u=0,i+a),f=new Array(h);u<h;){var d=this.getCode(l);16==d?t(this,f,2,3,c):17==d?t(this,f,3,3,c=0):18==d?t(this,f,7,11,c=0):f[u++]=c=d}n=this.generateHuffmanTable(f.slice(0,i)),r=this.generateHuffmanTable(f.slice(i,h))}else O("Unknown block type in flate stream");for(var p=(S=this.buffer)?S.length:0,g=this.bufferLength;;){var m=this.getCode(n);if(m<256)p<=g+1&&(p=(S=this.ensureBuffer(g+1)).length),S[g++]=m;else{if(256==m)return void(this.bufferLength=g);var b=(m=C[m-=257])>>16;0<b&&(b=this.getBits(b));c=(65535&m)+b;m=this.getCode(r),0<(b=(m=I[m])>>16)&&(b=this.getBits(b));var v=(65535&m)+b;p<=g+c&&(p=(S=this.ensureBuffer(g+c)).length);for(var y=0;y<c;++y,++g)S[g]=S[g-v]}}}else{var w,x=this.bytes,N=this.bytesPos;void 0===(w=x[N++])&&O("Bad block header in flate stream");var L=w;void 0===(w=x[N++])&&O("Bad block header in flate stream"),L|=w<<8,void 0===(w=x[N++])&&O("Bad block header in flate stream");var A=w;void 0===(w=x[N++])&&O("Bad block header in flate stream"),(A|=w<<8)!=(65535&~L)&&O("Bad uncompressed block length in flate stream"),this.codeBuf=0,this.codeSize=0;var _=this.bufferLength,S=this.ensureBuffer(_+L),P=_+L;this.bufferLength=P;for(var k=_;k<P;++k){if(void 0===(w=x[N++])){this.eof=!0;break}S[k]=w}this.bytesPos=N}},t}function O(t){throw new Error(t)}function t(t){var e=0,n=t[e++],r=t[e++];-1!=n&&-1!=r||O("Invalid header in flate stream"),8!=(15&n)&&O("Unknown compression method in flate stream"),((n<<8)+r)%31!=0&&O("Bad FCHECK in flate stream"),32&r&&O("FDICT bit set in flate stream"),this.bytes=t,this.bytesPos=2,this.codeSize=0,this.codeBuf=0,st.call(this)}}();return function(t,e){if("function"==typeof define&&define.amd)define([],e);else if("undefined"!=typeof exports)e();else{e(),t.FileSaver={}}}(window,function(){var c="object"===("undefined"==typeof window?"undefined":on(window))&&window.window===window?window:"object"===("undefined"==typeof self?"undefined":on(self))&&self.self===self?self:"object"===("undefined"==typeof global?"undefined":on(global))&&global.global===global?global:void 0;function h(t,e,n){var r=new XMLHttpRequest;r.open("GET",t),r.responseType="blob",r.onload=function(){i(r.response,e,n)},r.onerror=function(){console.error("could not download file")},r.send()}function a(t){var e=new XMLHttpRequest;return e.open("HEAD",t,!1),e.send(),200<=e.status&&e.status<=299}function o(e){try{e.dispatchEvent(new MouseEvent("click"))}catch(t){var n=document.createEvent("MouseEvents");n.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(n)}}var i=c.saveAs||("object"!==("undefined"==typeof window?"undefined":on(window))||window!==c?function(){}:"download"in HTMLAnchorElement.prototype?function(t,e,n){var r=c.URL||c.webkitURL,i=document.createElement("a");e=e||t.name||"download",i.download=e,i.rel="noopener","string"==typeof t?(i.href=t,i.origin!==location.origin?a(i.href)?h(t,e,n):o(i,i.target="_blank"):o(i)):(i.href=r.createObjectURL(t),setTimeout(function(){r.revokeObjectURL(i.href)},4e4),setTimeout(function(){o(i)},0))}:"msSaveOrOpenBlob"in navigator?function(t,e,n){if(e=e||t.name||"download","string"==typeof t)if(a(t))h(t,e,n);else{var r=document.createElement("a");r.href=t,r.target="_blank",setTimeout(function(){o(r)})}else navigator.msSaveOrOpenBlob(function(t,e){return void 0===e?e={autoBom:!1}:"object"!==on(e)&&(console.warn("Deprecated: Expected third argument to be a object"),e={autoBom:!e}),e.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(t.type)?new Blob([String.fromCharCode(65279),t],{type:t.type}):t}(t,n),e)}:function(t,e,n,r){if((r=r||open("","_blank"))&&(r.document.title=r.document.body.innerText="downloading..."),"string"==typeof t)return h(t,e,n);var i="application/octet-stream"===t.type,a=/constructor/i.test(c.HTMLElement)||c.safari,o=/CriOS\/[\d]+/.test(navigator.userAgent);if((o||i&&a)&&"object"===("undefined"==typeof FileReader?"undefined":on(FileReader))){var s=new FileReader;s.onloadend=function(){var t=s.result;t=o?t:t.replace(/^data:[^;]*;/,"data:attachment/file;"),r?r.location.href=t:location=t,r=null},s.readAsDataURL(t)}else{var u=c.URL||c.webkitURL,l=u.createObjectURL(t);r?r.location=l:location.href=l,r=null,setTimeout(function(){u.revokeObjectURL(l)},4e4)}});c.saveAs=i.saveAs=i,"undefined"!=typeof module&&(module.exports=i)}),U});try{module.exports=jsPDF}catch(t){}