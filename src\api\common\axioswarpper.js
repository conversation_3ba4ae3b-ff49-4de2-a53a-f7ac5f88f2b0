/*
 * @Desc:
 * @Date: 2019-12-28 20:55:00
 * @Author:Nice
 * @FilePath: /src/api/common/axioswarpper.js
 * http配置
 */
import axios from "axios";
import { uid, Notify } from "quasar";
import { LocalStorage, SessionStorage } from "quasar";
import lodash from "lodash";
const FOR_INTERNAL=  process.env.FOR_INTERNAL
const FOR_EXTERNAL=  process. env.FOR_EXTERNAL
/// 此段代码 目的 输出 HTTP 根域名 + WS 根域名  （两个都不带最后的斜线）
//  HTTP根域名（本地会走proxy代理，再转API域名）： axios.defaults.baseURL
//  HTTP根域名（本地不走proxy代理，直接API域名）： axios.prototype.HTTP_ROOT_DOMAIN
//  WS 根域名 ：axios.prototype.WS_ROOT_DOMAIN
console.error("----CURRENT_REQUEST_DOMAIN---", process.env.CURRENT_REQUEST_DOMAIN);
console.error("----process.env.NODE_ENV---", process.env.NODE_ENV, process.env.FRONT_WEB_ENV);
if (process.env.FRONT_WEB_ENV=='idc_online' && process.env.NODE_ENV != "development") {
  // 生产环境 走   axios.defaults.baseURL 请求
  let api_prefix = process.env.API_DOMAIN_PREFIX;
  let arr = location.host.split(".");
  // console.log(location)
  // console.log(arr)
  let api_domain = `${location.protocol}//${api_prefix}.${
    arr[arr.length - 2]
  }.${arr[arr.length - 1]}`;
  axios.defaults.baseURL = api_domain;
  axios.prototype.HTTP_ROOT_DOMAIN = api_domain;
  axios.prototype.WS_ROOT_DOMAIN = api_domain.replace("http", "ws");
  console.error("-----1111---", api_domain);
} else {
  // 本地开发  走当前域名 + proxy 代理  axios.defaults.baseURL 使用默认配置
  axios.defaults.baseURL = process.env.CURRENT_REQUEST_DOMAIN;
  axios.prototype.HTTP_ROOT_DOMAIN = process.env.CURRENT_REQUEST_DOMAIN;
  axios.prototype.WS_ROOT_DOMAIN = process.env.CURRENT_REQUEST_DOMAIN.replace(
    "http",
    "ws"
  );
  console.error("--2222--", axios.defaults.baseURL);
}

/**
 * 在目前的所有场景下 以上代码输出 :
 *  axios.defaults.baseURL    :   http://api.aaa.com   或者  https://api.aaa.com
 * axios.prototype.WS_ROOT_DOMAIN   :  ws://api.aaa.com   或者  wss://api.aaa.com
 */
if(FOR_INTERNAL){
axios.prototype.API_PREFIX_FILE_REAL_YEWU = axios.prototype.HTTP_ROOT_DOMAIN + "/" + "yewu9" + "/manage/merchant/file/download?fileName=";
axios.prototype.API_PREFIX_FILE_FINANCE = axios.prototype.HTTP_ROOT_DOMAIN + "/" + "yewu8" + "/order/financeMonth/financeDayExport?financeDayId=";
}
if(FOR_EXTERNAL){
axios.prototype.API_PREFIX_FILE_REAL_YEWU = axios.prototype.HTTP_ROOT_DOMAIN+'/' +  'yewu17'+ "/admin/merchant/file/download?fileName=";
axios.prototype.API_PREFIX_FILE_FINANCE = axios.prototype.HTTP_ROOT_DOMAIN+'/' +  'yewu17'+ "/admin/financeMonth/financeDayExport?financeDayId=";
}

// 后台项目的 登录谷歌身份验证器 APP 下载
// axios.prototype.API_PREFIX_APP_PREFIX =axios.defaults.baseURL+'/' + "yewu5"+'/v1/admin'
////  console.log(axios.defaults.baseURL, "aaa");
////  console.log(axios.prototype.HTTP_ROOT_DOMAIN, "bbb");
/**
 *  WS使用方式：
 *  参考这个示例
 *  import axios from 'src/api/common/axioswarpper.js'
 *  url: axios.prototype.WS_DOMAIN_FRNGKONG_1
 *
 *
 *  文件相关服务使用方式：
 *  非API 相关的JS  文件内---使用：
 *  import axios from 'src/api/common/axioswarpper.js'
 *  url: axios.prototype.API_PREFIX_FILE_REAL
 *  必须是api JS 文件内----使用：
 *  文件服务的axios 请求  不用区分 开发环境 以前的写法需要去掉 （注意必须是api JS 文件内）
 *  import axios from 'src/api/common/axioswarpper.js'
 *  api文件内之前写法（必须删除）： let prefix= process.env.NODE_ENV ==
 *                        "development" ?process.env.API_PREFIX_FILE :process.env.API_PREFIX_FILE_REAL
 *  api文件内现在写法：
 *  'file'没有写死： let prefix= process.env.API_PREFIX_FILE
 *  'file'写死写法： let prefix=  axios.prototype.API_PREFIX_FILE_REAL
 *
 *
 *  谷歌身份APP验证器 本质是也是 文件服务 只是代理不同 使用方式：
 *  import axios from 'src/api/common/axioswarpper.js'
 *  url: axios.prototype.API_PREFIX_APP_PREFIX
 *  其他常规HTTP 请求使用规则不变，同以前
 */
 // 节流
const Throttle = (fn, t) => {
  let last;
  let timer;
  let interval = t || 500;
  return function() {
    let args = arguments;
    let now = Date.now();
    if (last && now - last < interval) {
      clearTimeout(timer);
      timer = null;
      timer = setTimeout(() => {
        last = now;
        fn.apply(this, args);
      }, interval);
    } else {
      last = now;
      fn.apply(this, args);
    }
  };
};
let dismiss = null;
function handle(message) {
  dismiss= Notify.create({
    icon: "warning",
    color: "negative",
    message
  });
}
let handleSuccuss = Throttle(handle, 800);
let uuid = uid();
// 超时时间
axios.defaults.timeout = 1500000;
// http请求拦截器
// 在向后端请求时，如果上传的数据里存在file文件对象，需要用到表单提交，这时候我们需要将JSON对象，转成formData对象，具体见代码
// const formData = new FormData();
// Object.keys(params).forEach((key) => {
// formData.append(key, params[key]);
// });
// 下面也有可能需要formData转JSON，代码如下：
// var jsonData = {};
// formData.forEach((value, key) => jsonData[key] = value);
axios.interceptors.request.use(
  config => {
    ////  console.log(
    //   "=====================================axios.interceptors.request.use============================================"
    // );
  //   //  console.log(config);
    config.params = config.params || {};
    config.data = config.data || {};
    // {type:1}
    // 自定义参数 说明  type  1 formdata 2 json 3 file   默认 json
    switch (config.type) {
      case 1:
        {
          config.params = {
                ...config.params,
                 ...config.data
              };
          config.headers["Content-Type"] =
            "application/x-www-form-urlencoded; charset=UTF-8";
        }
        break;
      case 3:
        {
          config.headers["Content-Type"] = "multipart/form-data";
        }
        break;
      case 4:
        {
          config.headers["Content-Disposition"] = "attachment";
          config.headers["Content-Type"] = "application/octet-stream";
        }
        break;
      case 5:
        {
          let { data } = config;
          if (data && typeof data === "object") {
            let { id, adminNewPassword } = data;
            if (id !== undefined && adminNewPassword !== undefined) {
              let url = new URLSearchParams({ id, adminNewPassword });
              config.params = {
                ...config.params,
                 ...config.data
              };
            }
          }
        }
        break;
      case 6:
        {
          config.params = {
            ...config.params,
              ...config.data
          };
        }
        break;
      case 8:
        {
          let { data } = config;
         //  console.log(data);
        }
        break;
      case 11:
        {
          console.log(" config.type  11   --",config.data );
          // post 请求 的 params 拼接到   URL 后面
          config.headers["Content-Type"] = "application/json";
          let { data } = config;
          if (data && typeof data === "object") {
            if (data !== undefined) {
              config.params = {
                ...config.params,
                ...data,
                flag:0
              };
            }
          }
        }
        break;
      default:
        {
          config.headers["Content-Type"] = "application/json";
        }
        break;
    }

if(FOR_INTERNAL){
    //国际化
    let i18n = LocalStorage.getItem("language") || "zs";
    if (i18n == "zh") {
      i18n = "zs";
    }
    config.headers["language"] = i18n;
    let merchantName = lodash.get(
      SessionStorage.getItem("userInfo"),
      "userCode"
    );
    let userId = lodash.get(SessionStorage.getItem("userInfo"), "userId");
    let appId = lodash.get(SessionStorage.getItem("userInfo"), "appId");
    // 商户code
    if (merchantName && config.type != 11)
      config.headers["merchantName"] = encodeURIComponent(merchantName);
    // 用户id
    if (userId) config.headers["user-id"] = userId;
    if (appId) config.headers["app-id"] = appId;
    // uuid - 时间戳
    config.headers["request-id"] = `${uuid.replace(/-/g, "")}-${Date.now()}`;
    // 用户token
    let token = SessionStorage.getItem("token");
    if (token) {
      config.headers["token"] = token;
    }
    config.params = {
      ...config.params,
      rnd_str_st: new Date().getTime()
    };
}

if(FOR_EXTERNAL){
      let token = SessionStorage.getItem('token')
    let userId = lodash.get(SessionStorage.getItem('userInfo'),'merchantId')
    let merchantName = lodash.get(SessionStorage.getItem('userInfo'),'merchantName')
    let i18n = LocalStorage.getItem('language') || 'zs'
    if(i18n=='zh'){ i18n='zs' }
    // 用户id
    if(userId) config.headers['user-id'] = userId;
    // 商户code
    if(merchantName&&config.type!=11)  config.headers['merchantName'] = encodeURIComponent(merchantName);
    // 用户token
    if(token) config.headers['Authorization'] = token;
    // 语种选择
    config.headers['language'] = i18n;
    config.headers['request-id'] = `${uuid.replace(/-/g, '')}-${Date.now()}`
    config.params=config.params ||{}
    config.params={
      ...config.params,
      rnd_str_st: new Date().getTime()
    }
}
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);
// http响应拦截器
axios.interceptors.response.use(
  res => {
    // session 过期处理
    let code = res.data.code;
    if(FOR_INTERNAL){
    // 00400800
    if (code == "00400800" || code == "00400111") {
      // 处理函数
      // 节流函数，防止多次出现提示框！
      handleSuccuss("会话过期或已在其它地方登录，请重新登录！");
      SessionStorage.remove("userInfo");
      // 2020-1-28  注释
      setTimeout(() => {
        window.location.reload();
      }, 3000);
    } else if (code == "00400430") {
      // handleSuccuss('');
      window.$router.push("/no_auth");
    }
    }
if(FOR_EXTERNAL){
     if(dismiss){dismiss()}
     // 00400800
     if(code == '0400035'){
       // 处理函数
       // 节流函数，防止多次出现提示框！
      handleSuccuss("未登录或已过期，请重新登录!")
      SessionStorage.remove('userInfo');
      SessionStorage.remove('token')
      // 2020-1-28  注释
      setTimeout(() => {
        window.location.reload();
      },3000)
     }else if(code=="0400403"){
      window.$router.push('/no_auth');
    }
}
    return res;
  },
  error => {
    if(error.response){
      if(error.response.status==413){
        handleSuccuss("文章大小超过2M，请调整！");
      }
    }
    console.log(error.response.status);
    return Promise.reject(error);
  }
);
export default axios;
