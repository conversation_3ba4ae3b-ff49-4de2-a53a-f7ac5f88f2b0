/*
 * @Desc:  赛事文章接口
 * @Date: 2019-12-28 20:59:34
 * @Author:cable
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_4;


// 采集管理列表
export const article_list = (
  params,
  url = "/manage/sArticle/list"
) => axios.post(`${prefix}${url}`, params );

// 采集管理列表-删除/批量删除
export const del_article_list = (
  params,
  url = "/manage/sArticle/batchDel"
) => axios.post(`${prefix}${url}`, params );

// 采集管理列表-栏目下拉选
export const category_List = (
  params,
  url = "/manage/sArticle/categoryList"
) => axios.get(`${prefix}${url}`, params );

// 采集管理列表-导出/批量导出文章
export const batch_Export = (
  params,
  url = "/manage/sArticle/batchExport"
) => axios.post(`${prefix}${url}`, params );

// 采集管理列表-作者下拉选
export const author_List = (
  params,
  url = "/manage/articleAuthor/authorList"
) => axios.get(`${prefix}${url}`, params );


// 内容管理-文章数据列表
export const articleStatistics_list = (
  params,
  url = "/manage/articleStatistics/list"
) => axios.post(`${prefix}${url}`, params );
