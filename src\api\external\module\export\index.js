/*
 * @Desc:
 * @Author:Nice
 * @Date:
 * @FilePath:
 */
import axios from "src/api/common/axioswarpper.js";

let prefix1 = axios.prototype.HTTP_ROOT_DOMAIN + '/'+ process.env.API_PREFIX_17
let prefix3 = axios.prototype.HTTP_ROOT_DOMAIN + '/'+ process.env.API_PREFIX_4

let prefix = process.env.API_PREFIX_17;
import {LocalStorage } from "quasar";
export const post_excel_export = (params, which) => {
  console.log(
    "=====================================ALAIR============================================"
  );
  console.log(params,which);
  console.log(process.env);
  let prefix = "";
  if (!which) {
    prefix = prefix3;
  } else if (which) {
    prefix = prefix1;
  }
  params={
    ...params,
    language: LocalStorage.getItem('language')
  }

// window.open

  let urlp = `${prefix}${params.url}?`;
  let keys = Object.keys(params);
  keys.map(x => {
    if (x != "url") {
      if (params[x]) {
        urlp += `${x}=${params[x]}&`;
      }
      if (params[x] === 0) {
        urlp += `${x}=${params[x]}&`;
      }
    }
  });
  urlp = urlp.substring(0, urlp.length - 1);
  console.log(keys);
  console.log(urlp);
  console.log(
    "=====================================ALAIR============================================"
  );
  // return false
  console.log(urlp)
  // window.open(urlp, "_self");
  window.open(urlp,  "_blank");
  // window.open('http://172.18.178.165:10711/report/merchantFileExport?pageNum=1&pageSize=50&dateType=day&filter=1', "_self");
};


export const post_exportTicketList_post= (params, url, which) => {
  let prefix = which? prefix3: prefix1;
  return axios.post(`${prefix}${url}`, params, {type: 2});
}
export const post_exportTicketList = (params, url) => {
  return axios.post(`${prefix}${url}`, params, { responseType:"blob" })
}
export const post_exportTicketList_json = (params, url, which) => {
  let prefix = which? prefix3: prefix1;
  return axios.post(`${prefix}${url}`, params);
}



//列表查询
export const post_find_file_list = (params, url = "/admin/file/findList") =>
  axios.post(`${prefix}${url}`, params, {type: 2});

//我的消息列表删除
export const post_find_file_delete = (params, url = "/admin/file/deleteFile") =>
axios.post(`${prefix}${url}`, params, {type:2});

//列表查询
export const post_find_file_rate = (params, url = "/admin/file/queryFileRate") =>
  axios.post(`${prefix}${url}`, params, {type: 2});

//查询正在生成中
export const post_find_new_file_id = (params, url = "/admin/file/queryExecuteFile") =>
axios.post(`${prefix}${url}`, params, {type: 2});

//重新生成
export const post_find_execute = (params, url = "/admin/file/execute") =>
axios.post(`${prefix}${url}`, params, {type: 2});