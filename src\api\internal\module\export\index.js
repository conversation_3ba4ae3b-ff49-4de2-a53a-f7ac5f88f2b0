/*
 * @Desc:
 * @Date: 2020-02-08 20:48:35
 * @Author:Nice
 * @FilePath: /src/api/internal/module/export/index.js
 */
import axios from "src/api/common/axioswarpper.js";
axios.prototype.HTTP_ROOT_DOMAIN
import { LocalStorage } from 'quasar'


let prefix1 =
 process.env.NODE_ENV != "development"? axios.defaults.baseURL+  '/'+ process.env.API_PREFIX_4 : process.env.CURRENT_REQUEST_DOMAIN+'/'+ process.env.API_PREFIX_4;
let prefix3 =
 process.env.NODE_ENV != "development"? axios.defaults.baseURL+  '/'+ process.env.API_PREFIX_3 : process.env.CURRENT_REQUEST_DOMAIN+'/'+ process.env.API_PREFIX_3;
 let prefix = process.env.API_PREFIX_3;
 
let prefix4 =
process.env.NODE_ENV != "development"? axios.defaults.baseURL+  '/'+ process.env.API_PREFIX_18 : process.env.CURRENT_REQUEST_DOMAIN+'/'+ process.env.API_PREFIX_18;




export const post_excel_export = (params, which) => {
  console.log(
    "=====================================ALAIR============================================"
  );
  console.log(params,which);
  console.log(process.env);
  let prefix = "";
  if (!which) {
    prefix = prefix3;
  } else if (which) {
    prefix = prefix1;
  }

  let i18n = LocalStorage.getItem('language') || 'zs'
  if(i18n=='zh'){ i18n='zs' }
  params['language'] = i18n;

 

  let urlp = `${prefix}${params.url}?`;
  let keys = Object.keys(params);
  keys.map(x => {
    if (x != "url") {
      if (params[x]) {
        urlp += `${x}=${params[x]}&`;
      }
      if (params[x] === 0) {
        urlp += `${x}=${params[x]}&`;
      }
    }
  });
  urlp = urlp.substring(0, urlp.length - 1);
  console.log(keys);
  console.log( '下载 文件  最终 拼接 完成的 路径为 ：     ',urlp  );
  console.log(
    "=====================================ALAIR============================================"
  );
  // return false
  // window.open(urlp, "_blank");

  window.open(urlp,  "_blank");
  // window.open('http://172.18.178.165:10711/order/merchantFileExport?pageNum=1&pageSize=50&dateType=day&filter=1', "_self");
};

export const post_exportTicketList = (params, url = "/order/user/exportTicketList", which) => {
  let prefix = !which? prefix3: prefix1;
  return axios.post(`${prefix}${url}`, params, { responseType:"blob" })
}

export const post_dj_exportTicketList = (params, url = "/order/user/exportTicketList", which) => {
  let prefix =which?prefix1:prefix4;
  return axios.post(`${prefix}${url}`, params, { responseType:"blob" })}

export const post_exportTicketList_json = (params, url = "/order/user/exportTicketList", which) => {
  let prefix = !which? prefix3: prefix1;
  return axios.post(`${prefix}${url}`, params)
}

export const post_dj_exportTicketList_json = (params, url = "/order/user/exportTicketList", which) => {
  let which_= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
  let prefix = which_?prefix3:prefix4;
  if(which){
     prefix = prefix1;
  }
  return axios.post(`${prefix}${url}`, params)
}


//列表查询
export const post_find_file_list = (params, url = "/order/file/findList") =>
  axios.post(`${prefix}${url}`, params, {type: 2});

//我的消息列表删除
export const post_find_file_delete = (params, url = "/order/file/deleteFile") =>
axios.post(`${prefix}${url}`, params, {type:2});


//列表查询
export const post_find_file_rate = (params, url = "/order/file/queryFileRate") =>
  axios.post(`${prefix}${url}`, params, {type: 2});

  

//查询正在生成中
export const post_find_new_file_id = (params, url = "/order/file/queryExecuteFile") =>
axios.post(`${prefix}${url}`, params, {type: 2});


//重新生成
export const post_find_execute = (params, url = "/order/file/execute") =>
axios.post(`${prefix}${url}`, params, {type: 2});

