/*
 * @Desc: 
 * @Author:Nice
 * @Date:
 * @FilePath: /src/api/external/module/data/index.js
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_17;

//下拉联赛
export const query_pull_down_tournament = (
  params,
  url = "/admin/match/pullDownTournament"
) => axios.post(`${prefix}${url}`, params);

//用户投注统计
export const query_userbetlist_byTime = (
  params,
  url = "/admin/userReport/queryUserBetListByTime"
) => axios.post(`${prefix}${url}`, params);

//注单投注查询统计接口
export const post_queryStatistics = (
  params,
  url = "/admin/userReport/getStatistics"
) => axios.post(`${prefix}${url}`, params);

//注单投注统计
export const query_userorderlist = (
  params,
  url = "/admin/userReport/queryTicketList"
) => axios.post(`${prefix}${url}`, params);

// 合买进度查询
export const querySyndicateOrderList = (
  params,
  url = "/admin/syndicate/querySyndicateOrderList"
) => axios.post(`${prefix}${url}`, params);

  //合买进度查询  查询统计接口 
  export const post_querySyndicateStatistics = (
    params,
    url = "/admin/syndicate/querySyndicateStatistics"
  ) => axios.post(`${prefix}${url}`, params);
  
  //投注用户管理-战绩
  export const post_querySyndicatedRecordList = (
    params,
    url = "/admin/userPurchase/querySyndicatedRecordList"
  ) => axios.post(`${prefix}${url}`, params);
  
  //投注用户管理-战绩 查询统计接口 
  export const post_getRecordStatistics = (
    params,
    url = "/admin/userPurchase/getRecordStatistics"
  ) => axios.post(`${prefix}${url}`, params);
// 百家赔页面列表/查询接口
export const post_oddsComparison_list = (
  params,
  url = "/admin/BettingOrder/oddsComparison/list"
) => axios.post(`${prefix}${url}`, params);

// 百家赔页面导出接口
export const post_oddsComparison_export = (
  params,
  url = "/admin/BettingOrder/oddsComparison/export"
) => axios.post(`${prefix}${url}`, params);





  //注单查询   --注单列表  7 天 和 35天 
  export const post_admin_userReport_queryTicketList = (
  params,
  url = "/admin/userReport/queryTicketList"
  ) => axios.post(`${prefix}${url}`, params);
  // 注单查询(ES)    7 天 和 35天 
  export const post_admin_userReport_queryTicketListES = (
  params,
  url = "/admin/userReport/queryTicketListES"
  ) => axios.post(`${prefix}${url}`, params );

    // 注单查询(ES实时流)    7 天 和 35天 
    export const post_admin_userReport_queryTicketListES_timely = (
      params,
      url = "/v1/player/order/stream/queryTicketList"
      ) => axios.post(`${prefix}${url}`, params );

   // 注单查询(ES)   查询统计接口
  export const post_getStatisticsES = (
    params,
    url = "/admin/userReport/getStatisticsES"
  ) => axios.post(`${prefix}${url}`, params);












//注单查询 -- 注单投注统计
export const post_admin_userReport_queryAppointmentLis = (
  params,
  url = "/admin/userReport/queryAppointmentList"
) => axios.post(`${prefix}${url}`, params);


//商户注单统计接口
export const query_merchantlist = (params, url = "/admin/merchantReport/merchantList") =>
  axios.post(`${prefix}${url}`, params);

//对外   (数据中心-商户报注单统计 主列表)同 (财务中心-对账单 主列表 ) 共用同一个接口
export const query_FinanceayTotalList = (params, url = "/admin/financeMonth/queryFinanceayTotalList") =>
  axios.post(`${prefix}${url}`, params);



  

//用户投注统计信息--投注用户管理详情
export const get_admin_userReport_getUserInfo = (params, url = "/admin/userReport/getUserInfo") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });

//用户信息  10711
export const get_order_user_detail = (params, url = "/order/user/detail") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });


//赛事投注统计 (主列表-对外)
export const post_admin_match_queryMatchStatisticList = (params, url = "/admin/match/queryMatchStatisticListNew") =>
  axios.post(`${prefix}${url}`, params);

//赛事投注统计-玩法投注统计
export const post_admin_match_queryPlayStatisticList = (params, url = "/admin/match/queryPlayStatisticList") =>
  axios.post(`${prefix}${url}`, params);

//即时赛事统计报表-玩法投注统计
export const post_admin_match_marketBetDetailList = (params, url = "/admin/reportMatch/marketBetDetailList") =>
  axios.post(`${prefix}${url}`, params);

//即时赛事统计报表-玩法投注统计- 多盘口二级展开详情
export const post_admin_match_matchMarketList = (params, url = "/admin/reportMatch/matchMarketList") =>
  axios.post(`${prefix}${url}`, params);

//赛事投注统计—单个赛事多币种
export const post_admin_match_queryMatchStatisticById = (params, url = "/admin/match/queryMatchStatisticById") =>
  axios.post(`${prefix}${url}`, params);

//投注用户管理 用户详情
export const get_admin_player_getPlayerOrderDetail = (params, url = "/admin/player/getPlayerOrderDetail") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });

//投注用户管理 用户详情 盈利率
export const get_admin_player_profit = (params, url = "/admin/player/profit") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });

//投注用户管理 最近12个月的统计
export const get_admin_player_orderMonth = (params, url = "/admin/player/orderMonth") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });

//投注用户管理 某月的统计
export const get_admin_player_orderMonthDays = (params, url = "/admin/player/orderMonthDays") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });

//玩法列表
export const post_admin_userReport_queryHotPlayName = (params, url = "/admin/userReport/queryHmOrderPlayName") =>
  axios.post(`${prefix}${url}`, params);


// 商户联想
export const post_admin_merchantReport_queryMerchantList = (params, url = "/admin/merchantReport/queryMerchantList") =>
  axios.post(`${prefix}${url}`, params);


// 商户-tree
export const post_admin_merchantReport_getMerchantListTree = (params, url = "/admin/merchant/getMerchantListTree") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });



//用户投注管理详情查询
export const post_admin_userReport_queryUserOrderList = (
  params,
  url = "/admin/userReport/queryUserOrderList"
) => axios.post(`${prefix}${url}`, params);


// 联赛等级查询
export const get_admin_user_queryTournamentList = (params, url = "/admin/user/queryTournamentList") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });

//数据中心查询赛事种类接口  10711
export const get_admin_player_getSportList = (params, url = "/admin/player/getSportList") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });


  // 注单取消

  export const post__admin_merchant_orderOperation = (
    params,
    url = "/admin/merchant/orderOperation"
  ) => axios.post(`${prefix}${url}`, params);

  
//冻结、解冻注单
  export const post__admin_merchant_orderFreeze = (
    params,
    url = "/admin/BackEndOrder/orderFreeze"
  ) => axios.post(`${prefix}${url}`, params);
  
  
//取消提前结算 子列表请求 ronney
export const post_queryPreSettleOrder = (params, url = "/admin/userReport/queryPreSettleOrder?") =>
axios.post(`${prefix}${url}`, params);

// 投注用户管理——根据用户ID查询用户名
export const post_find_userInfo = (params, url = "/admin/user/findUserInfo") =>
  axios.post(`${prefix}${url}`, params);
// 日常协查提交接口
export const post_checkUserLog = (params, url = "/admin/userReport/checkUserLog") =>
axios.post(`${prefix}${url}`, params);

// 日常协查列表接口
export const post_getCheckLogList = (params, url = "/admin/userReport/getCheckLogList") =>
axios.post(`${prefix}${url}`, params);
export const post_rate_queryRateList = (
  // 汇率列表
  params,
  url = "/admin/rate/queryRateList"
) => axios.post(`${prefix}${url}`, params);

export const post_rate_queryRateLogList = (
  // 汇率详细
  params,
  url = "/admin/rate/queryRateLogList"
) => axios.post(`${prefix}${url}`, params);

// 联赛等级查询
export const get_queryUserIdListByName = (params, url = "/admin/player/queryUserIdListByUserName") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });

  export const get_userByRealName = (params, url = "/admin/userReport/getUserByRealName") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });