{
    "fileheader.configObj": {
        "createFileTime": true,
        "language": {
          "languagetest": {
            "head": "/$$",
            "middle": " $ @",
            "end": " $/"
          }
        },
        "autoAdd": true,
        "autoAddLine": 6000,
        "autoAlready": true,
        "annotationStr": {
          "head": "/*",
          "middle": " * @",
          "end": " */",
          "use": false
        },
        "headInsertLine": {
          "php": 2
        },
        "beforeAnnotation": {
          "文件后缀": "该文件后缀的头部注释之前添加某些内容"
        },
        "afterAnnotation": {
          "文件后缀": "该文件后缀的头部注释之后添加某些内容"
        },
        "specialOptions": {
          "特殊字段": "自定义比如LastEditTime/LastEditors"
        },
        "switch": {
          "newlineAddAnnotation": true
        },
        "supportAutoLanguage": ["vue","js"],
        "prohibitAutoAdd": ["json", "html","css","styl","md","less","sass","scss","ts"],
        "folderBlacklist": [ "node_modules" ], 
        "prohibitItemAutoAdd": [
          "项目的全称, 整个项目禁止自动添加头部注释, 可以使用快捷键添加"
        ],
        "moveCursor": true,
        "dateFormat": "YYYY-MM-DD HH:mm:ss",
        "atSymbol": ["@", "@"],
        "atSymbolObj": {
          "文件后缀": ["头部注释@符号", "函数注释@符号"]
        },
        "colon": [": ", ": "],
        "colonObj": {
          "文件后缀": ["头部注释冒号", "函数注释冒号"]
        },
        "filePathColon": "/",
        "showErrorMessage": false,
        "wideSame": false,
        "wideNum": 15,
        "CheckFileChange": false,
        "createHeader": true,
        "useWorker": true,
        "typeParam": true,
        "designAddHead": false,
        "headDesignName": "random",
        "headDesign": false
      },
      "fileheader.cursorMode": {},
      "fileheader.customMade": {
        "FilePath": "no item name",
        "Description": ""
      },
      "i18n-ally.localesPaths": [
        "src/i18n"
      ],
      "explorer.copyRelativePathSeparator": "/",
  "pathAlias.aliasMap": {
    "src/*": [
      "src/*"
    ],
    "app/*": [
      "*"
    ],
    "project/*": [
      "project/*"
    ]
  },
}