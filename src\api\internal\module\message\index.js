/*
 * @Desc:
 * @Date: 2019-12-28 20:59:34
 * @Author:Nice
 * @FilePath: /src/api/internal/module/message/index.js
 */
import axios from "src/api/common/axioswarpper.js";
import { LocalStorage, SessionStorage } from "quasar";

let prefix = process.env.API_PREFIX_4; //yewu9
let prefix_2 = process.env.API_PREFIX_18;  //yewu21
let prefix_1 = process.env.API_PREFIX_3;  //yewu8
let play_type= "/dj"


//跑马灯消息
export const post_manage_news_getLightNews = (params={}, url = "/manage/news/getLightNews") =>
  axios.post(`${prefix}${url}`, params, {type: 1});

//公告栏列表
export const post_manage_notice_list = (params, url = "/manage/notice/list") =>
  axios.post(`${prefix}${url}`, params, {type: 1});

//删除公告栏
export const post_manage_notice_delete = (params, url = "/manage/notice/delete") =>
  axios.post(`${prefix}${url}`, params, {type: 1});

//公告栏详情
export const post_manage_notice_detail = (params, url = "/manage/notice/detail") =>
  axios.post(`${prefix}${url}`, params, { type: 1 });

// 公告编辑
export const post_manage_notice_editDetail = (params, url = "/manage/notice/editDetail") =>
  axios.post(`${prefix}${url}`, params, { type: 1 });


//添加公告栏
export const post_manage_notice_add = (params, url = "/manage/notice/add") =>
  axios.post(`${prefix}${url}`, params, {type: 10});

//编辑公告栏
export const post_manage_notice_editPost = (params, url = "/manage/notice/editPost") =>
  axios.post(`${prefix}${url}`, params, {type: 10});

//我的消息列表
export const post_manage_news_list = (params, url = "/manage/news/list") =>
  axios.post(`${prefix}${url}`, params, {type: 1});

//我的消息列表删除
export const post_manage_news_delete = (params, url = "/manage/news/delete") =>
  axios.post(`${prefix}${url}`, params, {type: 1});

//我的消息列表查看
export const post_manage_news_findById = (params, url = "/manage/news/findById") =>
  axios.post(`${prefix}${url}`, params, {type: 1});

//公告类型名称
export const post_manage_notice_noticeType = (params, url = "/manage/notice/noticeType") =>
  axios.post(`${prefix}${url}`, params, {type: 1});

//取消公告 
export const post_manage_notice_cancelNotice = (params, url = "/manage/notice/cacelNotice") =>
  axios.post(`${prefix}${url}`, params, {type: 1});

//恢复公告 
export const post_manage_notice_backNotice = (params, url = "/manage/notice/backNotice") =>
axios.post(`${prefix}${url}`, params, {type: 1});



//公告的商户树
export const post_manage_notice_getMerchantTree = (params, url = "/manage/notice/getMerchantTree") =>
  axios.post(`${prefix}${url}`, params, {type: 10});

  export const post_manage_dj_notice_getMerchantTree= (params) => {
    let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
    let prefix_ =which? prefix: prefix_2;
    let url=which?`/manage/notice/getMerchantTree`:`/manage/dj/activityEntrance/getMerchantSearchTree`
    return axios.post(`${prefix_}${url}`, params, {type: 10});
  }
//商户树-已选树
export const post_getActivityMerchantTree = (params, url = "/manage/activityEntrance/getActivityMerchantTree") =>
axios.post(`${prefix}${url}`, params, {type: 10});


//公告的 语言类型
  export const post_manage_notice_langType = (params, url = "/manage/notice/langType") =>
  axios.post(`${prefix}${url}`, params, {type: 1});

//公告的 公告类型--异常赛事用户--选中
export const get_select_abnormal_match_user = (params, url = "/order/match/getMatchInfoByMatchId") =>
  axios.get(`${prefix_1}${url}`, { params: { ...params }});

// 获取电竞商户
export const get_getAnnDJtMerchantList = (
  params,
  url = "/manage/dj/activityEntrance/getMerchantSearchTree"
) => axios.post(`${prefix_2}${url}`, params, { type: 10 });