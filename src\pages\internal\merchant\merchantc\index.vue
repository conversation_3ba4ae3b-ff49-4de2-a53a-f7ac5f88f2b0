<!--
 * @Date           : 2021-06-17 23:07:53
 * @FilePath: /src/pages/internal/merchant/merchantc/index.vue
 * @description    : 商户中心/商户管理
-->
<template>
  <div class="full-height full-width">
    <div class="pl10x pt10x pb10x" id="top1">
      <!--商户中心/商户管理-->
      <q-breadcrumbs separator="/" active-color="whiddte" class="panda-text-2">
        <q-breadcrumbs-el :label="$t('internal.label.label3')" />
        <q-breadcrumbs-el
          :label="$t('internal.label.label9')"
          class="panda-text-1"
        />
      </q-breadcrumbs>
    </div>
    <div
      class="line-height-30px items-center text-panda-text-dark bg-panda-bg-6 ml10x mr10x border-radius-4px shadow-3"
    >
      <div id="top2" class="row pl10x pr10x pt10x pb10x">
        <a-radio-group v-model="pagination.agentLevel">
          <!-- 直营商户 -->
          <a-radio-button value="0">{{
            $t("internal.template.label59")
          }}</a-radio-button>
          <!-- 渠道商户 -->
          <a-radio-button value="1">{{
            $t("internal.template.label60")
          }}</a-radio-button>
          <!-- 二级商户 -->
          <a-radio-button value="2">{{
            $t("internal.template.label61")
          }}</a-radio-button>
        </a-radio-group>
        <!-- 请输入商户名称 -->
        <div class="append-handle-btn-input ml10x w-180 position-relative">
          <a-input
            @keydown.enter="handle_search"
            v-model.trim="searchForm.merchantName"
            :placeholder="$t('internal.placeholder.label34')"
            autocomplete="off"
            allowClear
          >
            <my-icon
              slot="suffix"
              type="p-icon-chazhao"
              class="text-panda-text-4 fs12"
            />
          </a-input>
          <!-- <div class="position-absolute select-left-border-bet"></div> -->
        </div>
        <!-- 商户编号 -->
        <div class="append-handle-btn-input ml10x w-180 position-relative">
          <a-input
            @keydown.enter="handle_search"
            v-model.trim="searchForm.merchantCode"
            :placeholder="$t('internal.placeholder.label5')"
            autocomplete="off"
            :maxLength="20"
            allowClear
          >
            <my-icon
              slot="suffix"
              type="p-icon-chazhao"
              class="text-panda-text-4 fs12"
            />
          </a-input>
          <!-- <div class="position-absolute select-left-border-bet"></div> -->
        </div>
        <!-- v-if="record.agentLevel=='0'||record.agentLevel=='2'" -->
        <!-- tag	integer 必须 0 现金网 1 信用网 -->
        <div
          class="append-handle-btn-input position-relative ml10x w-120"
          v-if="pagination.agentLevel == '0' || pagination.agentLevel == '2'"
        >
          <a-select
            autocomplete
            class="w-120"
            :placeholder="$t('internal.placeholder.label70')"
            v-model.trim="searchForm.merchantTag"
          >
          <!-- 全部 -->
            <a-select-option :value="''"> {{ $t("internal.common.label49") }}</a-select-option >
            <a-select-option
              v-for="(item, index) in merchantTag_list"
              :key="index"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
          <!-- <div class="position-absolute select-left-border-finance"></div> -->
        </div>
        <!-- 二级商户才显示 -->
        <!-- 请输入渠道商户名称 -->
        <div
          class="append-handle-btn-input ml10x position-relative w-200"
          v-if="pagination.agentLevel == '2'"
        >
          <a-input
            @keydown.enter="handle_search"
            v-if="pagination.agentLevel == '2'"
            v-model.trim="searchForm.parentName"
            :placeholder="$t('internal.common.qsr')+ $t('internal.common.label44') + $t('internal.common.label68')"
            autocomplete="off"
            :maxLength="20"
            allowClear
          >
            <my-icon
              slot="suffix"
              type="p-icon-chazhao"
              class="text-panda-text-4 fs12"
            />
          </a-input>
          <!-- <div class="position-absolute select-left-border-bet"></div> -->
        </div>
        <!-- 钱包类型 -->
        <div class="append-handle-btn-input position-relative ml10x w-190">
          {{ $t("internal.table_title.label000993") }}
          <a-select
            autocomplete
            class="w-120"
            :placeholder="$t('internal.placeholder.label70')"
            v-model.trim="searchForm.transferMode"
          >
            <a-select-option :value="''">
              {{ $t("internal.common.label49") }}</a-select-option
            >
            <a-select-option
              v-for="(item, index) in transferMode_list"
              :key="index"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
          <!-- <div class="position-absolute select-left-border-finance"></div> -->
        </div>
        <!-- 内外部商户 -->
        <div class="append-handle-btn-input position-relative ml10x w-190">
          {{ $t("internal.table_title.label000997") }}
          <a-select
            autocomplete
            class="w-110"
            :placeholder="$t('internal.placeholder.label70')"
            v-model.trim="searchForm.isExternal"
          >
            <a-select-option :value="''">
              {{ $t("internal.common.label49") }}</a-select-option
            >
            <a-select-option
              v-for="(item, index) in isExternaltTag_list"
              :key="index"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
          <!-- <div class="position-absolute select-left-border-finance"></div> -->
        </div>
        <!-- APP商户 -->
        <div
          class="append-handle-btn-input position-relative text-no-wrap ml20x w-180"
          v-if="pagination.agentLevel == '0' || pagination.agentLevel == '2'"
        >
          {{ $t("internal.table_title.label000994") }}
          <a-select
            autocomplete
            class="w-120"
            :placeholder="$t('internal.placeholder.label70')"
            v-model.trim="searchForm.isApp"
          >
            <a-select-option :value="''">
              {{ $t("internal.common.label49") }}</a-select-option
            >
            <a-select-option
              v-for="(item, index) in isApp_list"
              :key="index"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
          <!-- <div class="position-absolute select-left-border-finance"></div> -->
        </div>
        <!-- PC布局样式 -->
        <div class="append-handle-btn-input position-relative text-no-wrap ml35x w-200" v-if="pagination.agentLevel != '1'">
          {{ $t("internal.common.c6") }}
          <a-select
            autocomplete
            class="w-120"
            :placeholder="$t('internal.placeholder.label70')"
            v-model.trim="searchForm.pcLayout"
          >
            <a-select-option :value="''">{{ $t("internal.common.label49") }}</a-select-option>
            <a-select-option
              v-for="(item, index) in layout_list"
              :key="index"
              :disabled="item.value == 1"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </div>
        <!-- H5布局样式 -->
        <div class="append-handle-btn-input position-relative text-no-wrap ml20x w-200 q-mt-sm" v-if="pagination.agentLevel != '1'">
          {{ $t("internal.common.c7") }}
          <a-select
            autocomplete
            class="w-120"
            :placeholder="$t('internal.placeholder.label70')"
            v-model.trim="searchForm.h5Layout"
          >
            <a-select-option :value="''">{{ $t("internal.common.label49") }}</a-select-option>
            <a-select-option
              v-for="(item, index) in layout_list"
              :key="index"
              :value="item.value"
              :disabled="item.value == 1"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </div>
        <!-- 搜索 -->
        <q-btn
          class="panda-btn-primary-dense bg-primary ml20x q-mt-sm"
          style="width: 70px; height: 30px"
          :label="$t('internal.label.label5')"
          @click="handle_search"
        />
        <q-space />
        <!-- 批量启&禁用 -->
        <q-btn v-if="$_has('Merchant:Manage:updateDisabled')"
          class="panda-btn-primary-dense bg-primary mr10x q-mt-sm"
          style="width: 110px; height: 30px"
          :label="$t('internal.merchant.bettinguser.config.28')"
           @click="handle_disabled_merchant"
        />
        <!-- 导出 -->
        <div>
          <q-btn
            class="panda-btn-primary-dense bg-primary mr10x q-mt-sm"
            style="width: 60px; height: 30px; font-size: 12px"
            :label="$t('internal.label.label42')"
            @click="handle_export_excel"
          />
        </div>
        <!-- 新建直营商户 -->
        <div>
          <q-btn
            class="panda-btn-primary-dense bg-primary mr10x q-mt-sm"
            style="width: 110px; height: 30px; font-size: 12px"
            :label="btnName"
            @click="handle_new_merchant"
          />
        </div>
      </div>
      <!-- 表格区域 -->
      <a-table
        :columns="columns"
        class="pl10x pr10x"
        :dataSource="tabledata"
        :scroll="{ x: 1960, y: scrollHeight }"
        :pagination="false"
        :loading="tabledata_loading"
        @change="sorterForTable"
        size="middle"
        :rowKey="(record) => record.id"
      >
        <!-- c端多语言配置 （已废弃）-->
        <span
          slot="languageList"
          slot-scope="text, record"
          @mouseover="lan_key = record.id"
          @mouseleave="lan_key = false"
        >
          <span class="cursor-pointer display-block">
            <span v-if="record.new_languageList">{{
              record.new_languageList &&
              record.new_languageList[0] +
                "...(" +
                record.new_languageList.length +
                "个)"
            }}</span>
            <span v-else>
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span
            >
            <q-icon
              v-if="record.id == lan_key"
              @click.native="handleSetLan(record)"
              class="panda-icon panda-icon-bian-ji panda-icon-hover fs18 cursor-pointer handle-table-icon"
            ></q-icon>
          </span>
        </span>
        <!-- 商户编号 -->
        <span slot="merchantCode" slot-scope="text">
          <span>{{ text }}</span>
        </span>
        <!-- 商户名称 -->
        <span slot="merchantName" slot-scope="text, record">
          <table-cell-ellipsis-ant
            :span_html="
              '<div class=\'ellipsis\'>' + record.merchantName + '</div>'
            "
            :str_all="'<div>' + record.merchantName + '</div>'"
            :defaultplace="'topLeft'"
            :col_width="180"
            :str="record.merchantName"
          ></table-cell-ellipsis-ant>
          <!-- <span class="text-over-120">{{ record.merchantName }}</span> -->
        </span>
        <span slot="secondMerchantNum" slot-scope="text, record">
          <span>Lv{{ record.secondMerchantNum }} ++</span>
        </span>
        <!-- 二级商户数量 -->
        <span slot="childAmount" slot-scope="text, record">
          <span>
            {{ record.childAmount }}
            <span v-if="record.childMaxAmount">/</span>
            {{ record.childMaxAmount }}
          </span>
        </span>
        <!-- 商户等级 -->
        <span slot="level" slot-scope="text, record">
          <span>Lv{{ record.level }}</span>
        </span>
        <!-- 商户币种 -->
        <template slot="currencyCode" slot-scope="text, record">
          <table-cell-ellipsis :copytips="''" :str_all="record.currencyCode" :col_width="500"></table-cell-ellipsis>
        </template>
        <!-- 钱包类型 -->
        <span slot="transferMode" slot-scope="text, record">
 
          <span>{{
            record.transferMode == 1
              ? transferMode_list[0].label
              : transferMode_list[1].label
          }}</span>
 
        </span>
        <!-- 商户标签 -->
        <span
          slot="merchantTag"
          slot-scope="text, record"
          v-if="record.agentLevel == '0' || record.agentLevel == '2'"
        >
          <span
            :style="{ color: record.merchantTag == 1 ? '#1890ff' : '#3c4551' }"
            >{{ record.merchantTag | filterMerchantTag }}
          </span>
        </span>
        <!-- 内外部商户  0 内部  1外部  -->
        <span slot="isExternal" slot-scope="text, record">
          <!-- 显示值 或者未设置 -->
          <div
            v-if="!record.isExternal_editing"
            @mouseenter="handle_isExternal_enter(record)"
            @mouseleave="handle_isExternal_leavel(record)"
          >
            <!-- 未设置 -->
            <span class="q-mr-sm">
              <!-- {{ record.isExternal=='1'?'内部':'外部'   }} -->
              {{
                record.isExternal == "1"
                  ? $t("internal.template.label181")
                  : $t("internal.template.label182")
              }}
            </span>
            <!-- 外部编辑图标 -->
            <q-icon
              v-if="
                record.show_isExternal_edit_icon &&
                $_has('merchant:merchant_centre:I&e_merchant:view')
              "
              class="panda-icon panda-icon-bian-ji panda-icon-hover fs18 cursor-pointer"
              @click="handle_isExternal_edit_icon_click(record)"
            >
            </q-icon>
          </div>
          <div v-if="record.isExternal_editing">
            <!-- 编辑状态 -->
            <!-- 内外部下拉框 -->
            <a-select
              v-model="record.isExternal_edit"
              default-value=""
              style="width: 80px"
              @change="handle_merchants"
            >
              <a-select-option
                v-for="(item, index) in isExternaltTag_list"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
            <a-icon
              type="check"
              @click="handle_isExternal_editi_complete(record, 'isExternal')"
              class="duihao q-mx-sm"
            />
            <a-icon
              type="close"
              @click="handle_isExternal_edit_cancel(record)"
              class="guanbi"
            />
          </div>
        </span>
        <!-- 测试商户 -->
        <span slot="isTest" slot-scope="text, record">
          <div
            v-if="!record.isTest_editing"
            @mouseenter="handle_isTest_enter(record)"
            @mouseleave="handle_isTest_leave(record)"
          >
            <!-- 未设置 -->
            <span class="q-mr-sm">
              <!-- {{ record.isTest=='1'?'是':'否'    }} -->
              {{
                record.isTest == "1"
                  ? $t("internal.template.label185")
                  : $t("internal.template.label186")
              }}
            </span>
            <q-icon
              v-if="
                record.show_isTest_edit_icon &&
                $_has('merchant:merchant_centre:test_merchant:view')
              "
              class="panda-icon panda-icon-bian-ji panda-icon-hover fs18 cursor-pointer"
              @click="handle_isTest_edit_icon_click(record)"
            >
            </q-icon>
          </div>
          <!-- 编辑状态 -->
          <!-- 测试商户 下拉框 -->
          <div v-if="record.isTest_editing">
            <a-select
              v-model="record.isTest_edit"
              default-value=""
              style="width: 80px"
              @change="handle_merchants"
            >
              <a-select-option
                v-for="(item, index) in isTesttusTag_list"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
            <a-icon
              type="check"
              @click="handle_isTest_edit_complete(record, 'isTest')"
              class="duihao q-mx-sm"
            />
            <a-icon
              type="close"
              @click="handle_isTest_edit_cancel(record)"
              class="guanbi"
            />
          </div>
        </span>
        <!-- APP商户 -->
        <span slot="isApp" slot-scope="text, record">
          <div
            v-if="!record.isApp_editing"
            @mouseenter="handle_isApp_enter(record)"
            @mouseleave="handle_isApp_leave(record)"
          >
            <!-- 未设置 -->
            <span class="q-mr-sm">
              <!-- {{ record.isApp=='1'?'是':'否'}} -->
              {{
                record.isApp == "1"
                  ? $t("internal.template.label185")
                  : $t("internal.template.label186")
              }}
            </span>
            <q-icon
              v-if="
                record.show_isApp_edit_icon &&
                $_has('merchant:merchant_centre:app_merchant:view')
              "
              class="panda-icon panda-icon-bian-ji panda-icon-hover fs18 cursor-pointer"
              @click="handle_isApp_edit_icon_click(record)"
            >
            </q-icon>
          </div>
          <!-- 编辑状态 -->
          <!-- APP商户 下拉框 -->
          <div v-if="record.isApp_editing">
            <a-select
              v-model="record.isApp_edit"
              default-value=""
              style="width: 80px"
            >
              <a-select-option
                v-for="(item, index) in isApp_list"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
            <a-icon
              type="check"
              @click="handle_isApp_edit_complete(record, 'isApp')"
              class="duihao q-mx-sm"
            />
            <a-icon
              type="close"
              @click="handle_isApp_edit_cancel(record)"
              class="guanbi"
            />
          </div>
        </span>
        <!-- 客户端 -->
        <span slot="status" slot-scope="text, record">
          <span class="display-block" @mouseover="web_key = record.id" @mouseleave="web_key = false">
            <a-switch @click="handle_click_vs(record, 'status')" :checked="record.status != 0 ? true : false"
              :checked-children="$t('internal.template.label177')" :un-checked-children="$t('internal.template.label171')" />
            <q-icon
              style="font-size: 16px; margin-top: 4px"
              @click.native="handle_webStatus(record, 'status')"
              v-if="record.agentLevel != '1' && record.status != 0 && record.id == web_key"
              class="panda-icon panda-icon-bian-ji panda-icon-hover cursor-pointer handle-table-icon ml10x"
            ></q-icon>
          </span>
        </span>
        <!-- 商户后台 -->
        <span slot="backendSwitch" slot-scope="text, record">
          <span>
            <a-switch @click="handle_click_vs(record, 'backendSwitch')" :checked="record.backendSwitch != 0 ? true : false"
              :checked-children="$t('internal.template.label177')" :un-checked-children="$t('internal.template.label171')" />
          </span>
        <!-- 邮箱 -->
        <span slot="email" slot-scope="text, record">
          <span class="text-over-180">{{ record.email }}</span>
        </span>
      </span>
        <!-- 预约投注 -->
         <!-- @mouseover="yuYueTouZhu_key = record.id"
          @mouseleave="yuYueTouZhu_key = false" -->
        <span slot="bookBet" slot-scope="text, record">
          <span class="display-block"
          @mouseover="yuYueTouZhu_key = record.id"
          @mouseleave="yuYueTouZhu_key = false" 
          >
            <a-switch
              @click="handle_click_vs(record, 'bookBet')"
              :checked="record.bookBet !== 0 ? true : false"
              :checked-children="$t('internal.template.label177')"
              :un-checked-children="$t('internal.template.label171')"
            />
              <q-icon
              style="font-size: 16px; margin-top: 4px"
              @click.native="handleSetVsgkDefault2(record)"
              v-if="record.bookBet !== 0&&record.id == yuYueTouZhu_key"
              class="
                panda-icon panda-icon-bian-ji panda-icon-hover
                cursor-pointer
                handle-table-icon
                ml10x
              "
            ></q-icon>
          </span>
        </span>
        
        <!-- 额度转入 -->
        <span slot="merchantTransferIn" slot-scope="text, record">
          <span>
            <a-switch @click="handle_click_vs(record, 'merchantTransferIn')" :checked="record.merchantTransferIn != 0 ? true : false"
              :checked-children="$t('internal.template.label177')" :un-checked-children="$t('internal.template.label171')" />
          </span>
        </span>
        <!-- 额度转出 -->
        <span slot="merchantTransferOut" slot-scope="text, record">
          <span>
            <a-switch @click="handle_click_vs(record, 'merchantTransferOut')" :checked="record.merchantTransferOut != 0 ? true : false"
              :checked-children="$t('internal.template.label177')" :un-checked-children="$t('internal.template.label171')" />
          </span>
        </span>
        
        <!-- 合买开关 -->
        <span slot="merchantPurchase" slot-scope="text, record">
          <span>
            <a-switch @click="handle_click_vs(record, 'merchantPurchase')" :checked="record.merchantPurchase != 0 ? true : false"
              :checked-children="$t('internal.template.label177')" :un-checked-children="$t('internal.template.label171')" />
          </span>
        </span>
         <!-- 提前结算 足球 -->
         <span slot="settleSwitchAdvance1" slot-scope="text, record">
          <span  class="display-block"  @mouseover="football_key = record.id" @mouseleave="football_key = false">
            <a-switch
              @click="handle_click_vs(record, 'settleSwitchAdvance')"
              :checked="record.settleSwitchAdvance !== 0 ? true : false"
              :checked-children="$t('internal.template.label177')"
              :un-checked-children="$t('internal.template.label171')"
            />
            <!-- 提前结算-足球 按钮 -->
            <q-icon
              style="font-size: 16px; margin-top: 4px"
              @click.native="handleSetVsgkDefault4(record)"
              v-if="record.settleSwitchAdvance !== 0&&record.id == football_key"
              class="
                panda-icon panda-icon-bian-ji panda-icon-hover
                <!-- cursor-pointer -->
                handle-table-icon
                ml10x
              "
            ></q-icon> 
          </span> 
        </span>
          <!-- 提前结算 篮球   basketball_key -->
         <span slot="settleSwitchAdvance2" slot-scope="text, record">
          <span  class="display-block"  @mouseover="basketball_key = record.id" @mouseleave="basketball_key = false">
            <a-switch
              @click="handle_click_vs(record, 'settleSwitchBasket')"
              :checked="record.settleSwitchBasket !== 0 ? true : false"
              :checked-children="$t('internal.template.label177')"
              :un-checked-children="$t('internal.template.label171')"
            />
            <!-- 提前结算-篮球 按钮 -->
            <q-icon
              style="font-size: 16px; margin-top: 4px"
              @click.native="handleSetVsgkDefault5(record)"
              v-if="record.settleSwitchBasket !== 0&&record.id == basketball_key"
              class="
                panda-icon panda-icon-bian-ji panda-icon-hover
                cursor-pointer
                handle-table-icon
                ml10x
              "
            ></q-icon>
          </span> 
        </span>
        <!-- 电子体育 -->
        <span slot="openElectronicTy" slot-scope="text, record">
          <span class="display-block" @mouseover="openElectronicTy_key = record.id" @mouseleave="openElectronicTy_key = false">
            <a-switch
              @click="handle_click_vs(record, 'openElectronicTy')"
              :checked="record.openElectronicTy !== 0 ? true : false"
              :checked-children="$t('internal.template.label177')"
              :un-checked-children="$t('internal.template.label171')"
            />
              <q-icon
              style="font-size: 16px; margin-top: 4px"
              @click.native="handleSetElectronic(record)"
              v-if="record.openElectronicTy !== 0&&record.id == openElectronicTy_key"
              class="
                panda-icon panda-icon-bian-ji panda-icon-hover
                cursor-pointer
                handle-table-icon
                ml10x
              "
            ></q-icon>
            </span>
        </span>
        <!-- 虚拟体育 -->
        <span slot="openVrSport" slot-scope="text, record">
          <span>
            <a-switch
              @click="handle_click_vs(record, 'openVrSport')"
              :checked="record.openVrSport !== 0 ? true : false"
              :checked-children="$t('internal.template.label177')"
              :un-checked-children="$t('internal.template.label171')"
            />
          </span>
        </span>
        <!-- 真人开关 -->
        <span slot="zr" slot-scope="text, record">
          <span class="display-block" @mouseover="zr_key = record.id" @mouseleave="zr_key = false">
            <a-switch
              @click="handle_click_vs(record, 'zr')"
              :checked="record.zr !== 0 ? true : false"
              :checked-children="$t('internal.template.label177')"
              :un-checked-children="$t('internal.template.label171')"
            />
            <!-- 真人 按钮 -->
            <q-icon
              style="font-size: 16px; margin-top: 4px"
              @click.native="handleSetVsgkDefault_zr(record)"
              v-if="record.zr !== 0&&record.id == zr_key"
              class="
                panda-icon panda-icon-bian-ji panda-icon-hover
                <!-- cursor-pointer -->
                handle-table-icon
                ml10x
              "
            ></q-icon> 
          </span>
        </span>
        <!-- 彩票开关 -->
        <span slot="cp" slot-scope="text, record">
          <span class="display-block" @mouseover="cp_key = record.id" @mouseleave="cp_key = false">
            <a-switch
              @click="handle_click_vs(record, 'cp')"
              :checked="record.cp !== 0 ? true : false"
              :checked-children="$t('internal.template.label177')"
              :un-checked-children="$t('internal.template.label171')"
            />
            <!-- 彩票 按钮 -->
            <q-icon
              style="font-size: 16px; margin-top: 4px"
              @click.native="handleSetVsgkDefault_cp(record)"
              v-if="record.cp !== 0&&record.id == cp_key"
              class="
                panda-icon panda-icon-bian-ji panda-icon-hover
                <!-- cursor-pointer -->
                handle-table-icon
                ml10x
              "
            ></q-icon> 
          </span>
        </span> 
        <!-- 电子竞技 -->
        <span slot="openEsport" slot-scope="text, record">
          <span class="display-block" @mouseover="dz_key = record.id" @mouseleave="dz_key = false">
            <a-switch
              @click="handle_click_vs(record, 'openEsport')"
              :checked="record.openEsport !== 0 ? true : false"
              :checked-children="$t('internal.template.label177')"
              :un-checked-children="$t('internal.template.label171')"
            />
            <!-- 电子竞技 按钮 -->
            <q-icon
              style="font-size: 16px; margin-top: 4px"
              @click.native="handleSetVsgkDefault_dz(record)"
              v-if="record.openEsport !== 0&&record.id == dz_key"
              class="
                panda-icon panda-icon-bian-ji panda-icon-hover
                <!-- cursor-pointer -->
                handle-table-icon
                ml10x
              "
            ></q-icon> 
          </span>
        </span>
        <!-- 动画开关 -->
        <span slot="ommv" slot-scope="text, record">
          <span>
            <a-switch
              @click="handle_click_vs(record, 'ommv')"
              :checked="record.ommv !== 0 ? true : false"
              :checked-children="$t('internal.template.label177')"
              :un-checked-children="$t('internal.template.label171')"
            />
          </span>
        </span>
        <!--对内 视频开关 (0关 1开)-->
        <span slot="openVideo" slot-scope="text, record">
          <span>
            <a-switch
              @click="handle_click_vs(record, 'openVideo')"
              :checked="record.openVideo !== 0 ? true : false"
              :checked-children="$t('internal.template.label177')"
              :un-checked-children="$t('internal.template.label171')"
            />
          </span>
        </span>
    
        <!-- 视频流量管控开关 -->
        <span slot="videoSwitch" slot-scope="text, record">
          <span
            class="display-block"
            @mouseover="vedio_key = record.id"
            @mouseleave="vedio_key = false"
          >
            <a-switch
              @click="handle_click_vs(record, 'videoSwitch')"
              :checked="record.videoSwitch !== 0 ? true : false"
              :checked-children="$t('internal.template.label177')"
              :un-checked-children="$t('internal.template.label171')"
            />
            <q-icon
              style="font-size: 16px; margin-top: 4px"
              @click.native="handleSetVsgkDefault(record)"
              v-if="record.videoSwitch !== 0 && record.id == vedio_key"
              class="panda-icon panda-icon-bian-ji panda-icon-hover cursor-pointer handle-table-icon ml10x"
            ></q-icon>
          </span>
        </span>
        <!-- 聊天室开关 -->
        <span slot="chatRoomSwitch" slot-scope="text, record">
          <span
            class="display-block"
            @mouseover="chat_key = record.id"
            @mouseleave="chat_key = false"
          >
            <a-switch
              @click="handle_click_vs(record, 'chatRoomSwitch')"
              :checked="record.chatRoomSwitch !== 0 ? true : false"
              :checked-children="$t('internal.template.label177')"
              :un-checked-children="$t('internal.template.label171')"
            />
            <q-icon
              style="font-size: 16px; margin-top: 4px"
              @click.native="handleSetVsgkDefault3(record)"
              v-if="record.chatRoomSwitch !== 0 && record.id == chat_key"
              class="panda-icon panda-icon-bian-ji panda-icon-hover cursor-pointer handle-table-icon ml10x"
            ></q-icon>
          </span>
        </span>
        <!-- 注单历史开关 -->
        <span slot="betHistory" slot-scope="text, record">
  
          <span
            class="display-block"
            @mouseover="bh_key = record.id"
            @mouseleave="bh_key = false"
          >
            <a-switch
              @click="handle_click_vs(record, 'bh')"
              :checked="record.bh !== 0 ? true : false"
              :checked-children="$t('internal.template.label177')"
              :un-checked-children="$t('internal.template.label171')"
            />
            <q-icon
              style="font-size: 16px; margin-top: 4px"
              @click.native="handleSetVsgk_bethistory(record)"
              v-if="record.bh !== 0 && record.id == bh_key"
              class="panda-icon panda-icon-bian-ji panda-icon-hover cursor-pointer handle-table-icon ml10x"
            ></q-icon>
          </span>
        </span>
        <!-- c端默认语言 -->
        <span
          slot="defaultLanguage"
          slot-scope="text, record"
          @mouseover="lan_key = record.id"
          @mouseleave="lan_key = false"
        >
          <span class="cursor-pointer display-block">
            <span v-if="record.defaultLanguageList">{{
              record.defaultLanguageList && record.defaultLanguageList[0]
            }}</span>
            <q-icon
              v-if="record.id == lan_key"
              style="font-size: 16px; margin-top: 2px"
              @click.native="handleSetLanDefault(record)"
              class="panda-icon panda-icon-bian-ji panda-icon-hover cursor-pointer handle-table-icon ml10x"
            ></q-icon>
          </span>
        </span>

         <!-- 精彩回放 -->
        <span slot="eventSwitch" slot-scope="text, record">
          <span class="display-block"
          @mouseover="highlight_replay_key = record.id"
          @mouseleave="highlight_replay_key = false" 
          >
            <a-switch
              @click="handle_click_vs(record, 'eventSwitch')"
              :checked="record.eventSwitch !== 0 ? true : false"
              :checked-children="$t('internal.template.label177')"
              :un-checked-children="$t('internal.template.label171')"
            />
            <!-- v-if="record.eventSwitch !== 0&&record.id == highlight_replay_key" -->
              <q-icon
              style="font-size: 16px; margin-top: 4px"
              @click.native="handleHighlightReplay(record)"
              v-if="record.eventSwitch !== 0 &&record.id == highlight_replay_key"
              class="
                panda-icon panda-icon-bian-ji panda-icon-hover
                cursor-pointer
                handle-table-icon
                ml10x
              "
            ></q-icon>
          </span>
        </span>
        <!-- 商务联系人 -->
        <span slot="contact" slot-scope="text, record">
          <div class="text-over-150">
            <span v-if="record.contact && record.contact.indexOf(',') > 0">{{
              record.contact.slice(0, record.contact.indexOf(","))
            }}</span>
            <span v-else>{{ record.contact }}</span>
          </div>
        </span>
        <!-- 联系电话 -->
        <span slot="phone" slot-scope="text, record">
          <div style="max-width: 160px">
            <span v-if="record.phone && record.phone.indexOf(',') > 0">{{
              record.phone.slice(0, record.phone.indexOf(","))
            }}</span>
            <span v-else>{{ record.phone }}</span>
          </div>
        </span>
        <!-- 超级管理员 -->
        <span slot="merchantAdmin" slot-scope="text, record">
          <span class="text-over-160">{{ record.merchantAdmin }}</span>
        </span>
        <!-- 操作 -->
        <span
          slot="action"
          slot-scope="text, record, index"
          class="flex  ml10x mr10x option-span"
        >
          <!--在线会员 icon -->
          <a-tooltip placement="top" v-show="pagination.agentLevel!=1">
            <template slot="title">
              <div class="fs12">
                {{$t('internal.online_user_management.online_user_management')}}
                <!-- 在线会员 -->
              </div>
            </template>
            <q-icon
              @click.native="handle_edit_online_member(record, index)"
              class="panda-icon panda-icon-shang-hu panda-icon-hover fs18 cursor-pointer"
            ></q-icon>
            <!-- panda-icon-yong-hu -->
          </a-tooltip>           
          <!--编辑商户 icon -->
          <a-tooltip placement="top">
            <template slot="title">
              <div class="fs12">{{ $t("internal.template.label73") }}</div>
            </template>
            <q-icon
              @click.native="handle_edit(record, index)"
              class="panda-icon panda-icon-bian-ji panda-icon-hover fs18 cursor-pointer"
            ></q-icon>
          </a-tooltip>
          <!--查看商户 icon -->
          <a-tooltip placement="top">
            <template slot="title">
              <div class="fs12">{{ $t("internal.template.label74") }}</div>
            </template>
            <q-icon
              @click.native="handle_look(record, index)"
              class="panda-icon panda-icon-cha-kan panda-icon-hover fs18 cursor-pointer"
            ></q-icon>
          </a-tooltip>
          <!--设置管理员 icon -->
          <a-tooltip placement="top">
            <template slot="title">
              <div class="fs12">{{ $t("internal.template.label41") }}</div>
            </template>
            <q-icon
              @click.native="handle_set_admin(record, index)"
              class="panda-icon panda-icon-shezhi-guanliyuan panda-icon-hover fs20 cursor-pointer"
            ></q-icon>
          </a-tooltip>
          <!--设置IP白名单 icon -->
          <a-tooltip placement="top" v-if="record.agentLevel != '2'">
            <template slot="title">
              <div class="fs12">{{ $t("internal.template.label149") }}</div>
            </template>
            <q-icon
              style="margin-top: 1px"
              @click.native="handleSetIp(record, index)"
              class="panda-icon panda-icon-panda-icon-ip panda-icon-hover fs18 cursor-pointer"
            ></q-icon>
          </a-tooltip>
          <!--"C端综合设置 icon-->
          <a-tooltip
            placement="top"
            v-if="['0', '2'].includes(pagination.agentLevel)"
          >
            <template slot="title">
              <div class="fs12">{{ $t("internal.template.label137") }}</div>
            </template>
            <img
              src="~src/assets/internal/plate.svg"
              width="18"
              height="18"
              style="margin-top: 1px"
              class="panda-icon panda-icon-shezhi-guanliyuan panda-icon-hover fs20 cursor-pointer"
              @click="handle_set_cplate(record, index)"
            />
          </a-tooltip>
          <!--"商户球种设置 icon -->
          <a-tooltip
            placement="top"
            v-if="['0', '2'].includes(pagination.agentLevel)"
          >
            <template slot="title">
              <div class="fs12">{{ $t("internal.merchantc_.label2") }}</div>
            </template>
            <img
              src="~src/assets/internal/duoqiuzhong.svg"
              width="18"
              height="18"
              style="margin-top: 1px"
              @click="handlePitchesSet(record, index)"
              class="panda-icon panda-icon-duoqiuzhong panda-icon-hover fs18 cursor-pointer"
            />
          </a-tooltip>
          <!-- 设置二级商户 -->
          <a-tooltip
            placement="top"
            v-if="
              record.agentLevel == '1' &&
              record.childAmount != record.childMaxAmount
            "
          >
            <template slot="title">
              <div class="fs12">{{ $t("internal.template.label75") }}</div>
            </template>
            <q-icon
              style="margin-top: 1px"
              @click.native="handle_set_second_merchant(record, index)"
              class="panda-icon panda-icon-panda-icon-shezhi-erji panda-icon-hover fs20 cursor-pointer"
            ></q-icon>
          </a-tooltip>
          <!-- 新增证书 -->
          <a-tooltip
            placement="top"
            v-if="['0','1','2'].includes(pagination.agentLevel)"
          >
            <template slot="title">
              <div class="fs12">{{ $t("internal.template.label195") }}</div>
            </template>
            <img
              src= "~src/assets/internal/img/xinzengzhengshu.svg"
              style="margin-top: 1px"
              @click="handle_add_certificate(record)"
              class="panda-icon panda-icon-panda-icon-xinzengzhengshu panda-icon-hover fs20 cursor-pointer"
            />
          </a-tooltip>
          <q-icon
            v-else
            class="panda-icon panda-icon-panda-icon-shezhi-erji panda-icon-hover fs20 no-show"
          >
            <q-tooltip
              anchor="top middle"
              content-class="bg-white text-black shadow-6"
              self="bottom middle"
              :offset="[10, 10]"
            >
              <span>{{ $t("internal.template.label75") }}</span>
            </q-tooltip>
          </q-icon>
          <!-- :name="'img:'+xinyong_svg_icon" -->
          <!-- <q-img
            @click.native="handle_show_credit_limit(record, index)"
            v-if="
              (record.agentLevel == '0' || record.agentLevel == '2') &&
              record.merchantTag == 1
            "
            :src="xinyong_svg_icon"
            class="panda-icon panda-icon-hover cursor-pointer fs20 q-mr-xs"
            style="margin-top: 1px; width: 18px; height: 18px"
          >
            <q-tooltip
              anchor="top middle"
              content-class="bg-white text-black shadow-6"
              self="bottom middle"
              :offset="[10, 10]"
            >
              <span>{{ $t("internal.template2.label6") }} </span>
            </q-tooltip>
          </q-img> -->
          <!-- q球种排序 -->
          <a-tooltip
            placement="top"
            v-if="['0', '2'].includes(pagination.agentLevel)"
          >
            <template slot="title">
              <div class="fs12">{{ $t("internal.sport_order_list.ball_sorting") }}</div>
            </template>
            <img
              src= "~src/assets/internal/img/sportOrder/recording.png"
              style="margin-top: 1px"
              @click="handle_sport_order(record)"
              class="panda-icon panda-icon-panda-icon-xinzengzhengshu panda-icon-hover fs20 cursor-pointer"
            />
          </a-tooltip>    
        </span>
      </a-table>
      <!-- 分页器 -->
      <a-pagination
        v-if="tabledata.length > 0"
        :total="pagination.total"
        :current="pagination.current"
        show-size-changer
        show-quick-jumper
        :page-size-options="pagination.pageSizeOptions"
        :page-size="pagination.pageSize"
        :show-total="
          (total) => $t('internal.showTotal_text', [pagination.total])
        "
        @change="onChange"
        @showSizeChange="onShowSizeChange"
      />
    </div>
    <!-- 查看商户弹窗 -->
    <q-dialog
      v-model="lookMerchantShow"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-look :detailObj="showDialogObj"></dialog-look>
    </q-dialog>
    <!-- 设置管理员弹窗 -->
    <q-dialog
      v-model="setMerchantShow"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-set-admin
        :detailObj="showDialogObj"
        show_permissions_allocate
        @showModal ="showModal"
        @handle_close_dialog_set="handle_close_dialog_set"
      ></dialog-set-admin>
    </q-dialog>
    <!-- 设置IP弹窗 -->
    <q-dialog
      v-model="setIpShow"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-ip
        :detailObj="showDialogObj"
        @showModal ="showModal"
        @handle_close_dialog_ip="handle_close_dialog_ip"
      ></dialog-ip>
    </q-dialog>
    <!--  球种设置弹窗 -->
    <q-dialog
      v-model="pitchesShow"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-pitches-set
        :detailObj="showDialogObj"
        @handle_close_dialog_pitches_set="handle_close_dialog_pitches_set"
      ></dialog-pitches-set>
    </q-dialog>
    <!--对内 视频开关弹窗 、对外 视频开关弹窗 、  视频流量管控开关弹窗 、 提前结算开关弹窗 、预约投注开关弹窗 、电子竞技开关弹窗 -->
    <q-dialog
      v-model="vsShow"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-vs
        :detailObj="currentRecord"
        :show_which_config="show_which_config"
        @handle_confirm="show_which_callback_fn"
      ></dialog-vs>
    </q-dialog>
    <!-- 新建直营商户弹窗 -->
    <q-dialog
      v-model="setShow"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-set-merchant
        :isUpdate="isUpdate"
        :detailObj="showDialogObj"
        @closeDialogSetMerchantShow="handle_close_dialog_set_merchant"
      ></dialog-set-merchant>
    </q-dialog>
    <!--  C端板式设定弹窗 -->
    <q-dialog
      v-model="show_plate"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialo-plate
        :detailObj="showDialogObj"
        @handle_close_cplate="handle_close_cplate"
        :lan_list="lan_list"
      ></dialo-plate>
    </q-dialog>
    <!-- 修改语言配置弹窗  （已废弃）-->
    <q-dialog
      v-model="show_lan"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-lan
        :detailObj="showDialogObj"
        @handleCloseLan="handleCloseLan"
        :lan_list="lan_list"
      ></dialog-lan>
    </q-dialog>
    <!-- 修改默认语言配置弹窗 -->
    <q-dialog
      v-model="show_lan_default"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-lan-default
        :detailObj="showDialogObj"
        @handleCloseLanDefault="handleCloseLanDefault"
      ></dialog-lan-default>
    </q-dialog>
    <!-- 修改 精彩回放 配置弹窗 -->
    <q-dialog
      v-model="show_highlight_replay"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-highlight-replay
        :detailObj="showDialogObj"
        @handleCloseLanDefault="handleCloseLanDefault"
      ></dialog-highlight-replay>
    </q-dialog>
    <!-- 修改视频流量管控配置弹窗 -->
    <q-dialog
      v-model="show_vsgk_default"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-Vsgk
        :detailObj="showDialogObj"
        @handleCloseLanDefault="handleCloseLanDefault"
      ></dialog-Vsgk>
    </q-dialog>

    <!-- 修改聊天室开关配置弹窗 -->
    <q-dialog
      v-model="show_chatroom_default"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-chat
        :detailObj="showDialogObj"
        @handleCloseLanDefault="handleCloseLanDefault"
      ></dialog-chat>
    </q-dialog>
       <!-- 修改预约投注 弹窗-->
    <q-dialog
      v-model="show_yuYueTouZhu_default"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-YuYueTouZhu
        :detailObj="showDialogObj"
        @handleCloseLanDefault="handleCloseLanDefault"
      ></dialog-YuYueTouZhu>
    </q-dialog>
     <!-- 提前结算-足球 弹窗  -->
    <q-dialog
      v-model="show_football_default"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialogFootballEarlySettle
        :pagination_current = "pagination.current"
        :detailObj="showDialogObj"
        @handleCloseLanDefault="handleCloseLanDefault"
      ></dialogFootballEarlySettle>
    </q-dialog>
     <!-- 提前结算-篮球 弹窗  -->
     <q-dialog
      v-model="show_basketball_default"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialogBasketballEarlySettle
        :pagination_current = "pagination.current"
        :detailObj="showDialogObj"
        @handleCloseLanDefault="handleCloseLanDefault"
      ></dialogBasketballEarlySettle>
    </q-dialog>

     <!-- 真人弹窗  -->
     <q-dialog
      v-model="show_zr_default"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialogZr
        :pagination_current = "pagination.current"
        :detailObj="showDialogObj"
        @handleCloseLanDefault="handleCloseLanDefault"
      ></dialogZr>
    </q-dialog>
    <!-- 彩票弹窗  -->
    <q-dialog
      v-model="show_cp_default"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialogCp
        :pagination_current = "pagination.current"
        :detailObj="showDialogObj"
        @handleCloseLanDefault="handleCloseLanDefault"
      ></dialogCp>
    </q-dialog>
    <!-- 电子竞技弹窗  -->
    <q-dialog
      v-model="show_dz_default"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialogDz
        :pagination_current = "pagination.current"
        :detailObj="showDialogObj"
        @handleCloseLanDefault="handleCloseLanDefault"
      ></dialogDz>
    </q-dialog>
    

    <!-- 信用网商 查看弹窗   （功能已去除）-->
    <q-dialog
      v-model="show_credit_limit"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-credit-limit
        :detailObj="showDialogObj"
        @handleCloseCreditLimit="handleCloseCreditLimit"
      ></dialog-credit-limit>
    </q-dialog>
    <!-- 报表下载弹窗 -->
    <q-dialog
      v-model="exportExcelShow"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-excel :export_param="export_param"></dialog-excel>
    </q-dialog>
    <!-- 批量启&禁用 弹窗   -->
    <q-dialog v-model="show_disabled_merchant" persistent>
      <dialog-disabled-user 
      @on_close="handleCloseDisabledMerchant"
       :dialogtitle="$t('internal.template.label190')" 
       titleId="merchant"
       ></dialog-disabled-user>
    </q-dialog>
    <!-- 二次验证弹窗 -->
    <q-dialog
      v-model="visible"
      :position="position"
      persistent
      transition-show="slide-up"
      transition-hide="slide-down"
    >
      <token-code-popup
        :detailObj="showDialogObj"
        show_permissions_allocate
        @close_window="close_window"
      ></token-code-popup>
    </q-dialog>
    <!--  新增证书弹窗 -->
    <q-dialog
      v-model="show_add_certificate"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-add-certificate
        :detailObj="showDialogObj"
        @handle_close_add_certificate="handle_close_add_certificate"
      ></dialog-add-certificate>
    </q-dialog>
    <!-- 客户端状态 弹窗 -->
    <q-dialog v-model="show_add_webStatus" persistent>
      <dialogWebStatus :detailObj="showDialogObj" @onClose="on_emit_detail" @init_session_params="init_session_params"/>
    </q-dialog>
    <!-- 电子体育弹窗 -->
    <q-dialog
      v-model="show_add_electronic"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-open-electronic
        :detailObj="showDialogObj"
        @onClose="handle_close_add_electronic"
      ></dialog-open-electronic>
    </q-dialog>
    <!-- 注单历史 弹窗  -->
    <q-dialog
      v-model="show_bethistory_default"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialogBetHistory
        :pagination_current = "pagination.current"
        :detailObj="showDialogObj"
        @handleCloseLanDefault="handleCloseLanDefault"
      ></dialogBetHistory>
    </q-dialog>
    <!-- 在线会员弹框-->
    <q-dialog
      v-model="show_online_member"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-edit-online-number
        :isUpdate="isUpdate"
        :detailObj="showDialogObj"
        @closeDialogSetMerchantShow="handle_close_dialog_set_merchant"
      ></dialog-edit-online-number>
    </q-dialog>
    <!-- 球种排序弹框-->
    <q-dialog
      v-model="show_sport_order"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-sport-order
        :isUpdate="isUpdate"
        :detailObj="showDialogObj"
        @closeDialogSetMerchantShow="handle_close_dialog_set_merchant"
      ></dialog-sport-order>
    </q-dialog>    
  </div>
</template>
<script>
import { i18n } from "src/boot/i18n";
import { mapGetters } from "vuex";
import { api_merchant } from "src/api/index.js";
import mixins from "src/mixins/internal/index.js";
import datacentertablemixin from "src/components/common/bet_slip/internal/mixin/datacentertablemixin.js";
import {
  directlyMerchantConfig,
  secondMerchantConfig,
  channelMerchantConfig,
} from "src/pages/internal/merchant/merchantc/config/config.js"; //商户中心.商户管理(直营.渠道.二级)报表配置
import dialogLook from "src/components/common/dialog_look/index.vue"; //查看商户
import dialogSetAdmin from "src/components/common/dialog_set/index.vue"; //设置管理员弹窗
import dialogIp from "src/pages/internal/merchant/merchantc/component/dialogIp.vue"; //设置IP白色名单-弹窗
import dialogPitchesSet from "src/pages/internal/merchant/merchantc/component/dialogPitchesSet.vue"; //商户球种设置弹窗
import dialogSetMerchant from "src/pages/internal/merchant/merchantc/component/dialogSetMerchant.vue"; //商户中心.商户管理.(新建和编辑直营商户)弹窗
import dialogVs from "src/pages/internal/merchant/merchantc/component/dialogVs.vue"; // 通用开关弹窗
import dialoPlate from "src/components/common/c_plate_edit/index.vue"; //C端综合设置
import dialogLan from "src/pages/internal/merchant/merchantc/component/dialogLan.vue";
import dialogVsgk from "src/pages/internal/merchant/merchantc/component/dialog_vsgk.vue";//视频流量管控弹窗
import dialogChat from "src/pages/internal/merchant/merchantc/component/dialog_chat.vue";//聊天室开关弹窗
import dialogYuYueTouZhu from "src/pages/internal/merchant/merchantc/component/dialog_YuYueTouZhu.vue";//预约投注弹窗
import dialogLanDefault from "src/components/common/dialog_lan/index.vue";//c端默认语言 弹窗
import dialogHighlightReplay from "src/components/common/highlight_replay/index.vue";//精彩回放 弹窗
import dialogWebStatus from "src/components/common/web_status/index.vue";// 客户端 弹窗
import dialogCreditLimit from "src/pages/internal/merchant/merchantc/component/dialogCreditLimit.vue"; // 信用网商（功能已去除）
import lan_name from "src/components/common/dialog_lan/mixin/lan_name.js";
import otherconstantmixin from "src/mixins/internal/constant/otherconstantmixin.js"
const xinyong_svg_icon = require("src/assets/internal/icon/xinyong.svg");
import dialogExcel from "src/components/common/dialog/dialogExcel.vue";  // 导出
import dialogDisabledUser from "src/components/common/dialog_set/dialogDisabledUser.vue";    // 批量启&禁用
import tokenCodePopup from "src/pages/internal/merchant/merchantc/token_code_popup.vue"  //二次验证弹窗
import dialogAddCertificate from "src/pages/internal/merchant/merchantc/component/dialog_add_certificate.vue";  //新增证书弹窗
import dialogOpenElectronic from "src/components/common/openElectronic/index.vue"; // 电子体育 弹窗
import dialogFootballEarlySettle from "src/pages/internal/merchant/merchantc/component/dialogFootballEarlySettle.vue"
import dialogBasketballEarlySettle from "src/pages/internal/merchant/merchantc/component/dialogBasketballEarlySettle.vue"
import dialogZr from "src/pages/internal/merchant/merchantc/component/dialogZr.vue" //真人弹窗
import dialogCp from "src/pages/internal/merchant/merchantc/component/dialogCp.vue" //彩票弹窗
import dialogDz from "src/pages/internal/merchant/merchantc/component/dialogDzjj.vue" //电子竞技弹窗
import dialogBetHistory from "src/pages/internal/merchant/merchantc/component/dialogBetHistory.vue"

import dialogEditOnlineNumber from "src/pages/internal/merchant/merchantc/component/dialogEditOnlineNumber.vue" //在线会员弹框
import dialogSportOrder from "src/pages/internal/merchant/merchantc/component/dialogSportOrder/dialogSportOrder.vue"


export default {
  mixins: [...mixins, lan_name, datacentertablemixin, otherconstantmixin],
  components: {
    dialogLook, //查看商户
    dialogSetAdmin, //设置管理员弹窗
    dialogIp, //设置IP白色名单-弹窗
    dialogPitchesSet, //商户球种设置弹窗
    dialogSetMerchant, //商户中心.商户管理.(新建和编辑直营商户)弹窗
    dialogExcel, //报表下载弹窗
    dialogSetMerchant, //商户中心.商户管理.(新建和编辑商)弹窗
    dialogVs, //对内 视频开关弹窗 //对外 视频开关弹窗
    dialoPlate, // C端综合设置
    dialogLan,
    dialogLanDefault,//c 端默认语言弹窗
    dialogHighlightReplay,//精彩回放弹窗 弹窗
    dialogCreditLimit, // 信用网商 （功能已去除）
    dialogVsgk, // 视频管控弹窗
    dialogYuYueTouZhu,//预约投注弹窗
    dialogChat, //聊天室开关弹窗
    dialogDisabledUser,  // 批量启&禁用
    tokenCodePopup,  //二次验证弹窗
    dialogAddCertificate,  //新增证书弹窗
    dialogWebStatus,  // 客户端弹窗
    dialogOpenElectronic,  // 电子体育弹窗
    dialogFootballEarlySettle, //足球提前结算弹窗
    dialogBasketballEarlySettle, //篮球提前结算弹窗
    dialogZr, // 真人弹窗
    dialogCp, //彩票弹窗
    dialogDz, //电子竞技弹窗    
    dialogBetHistory, //注单历史弹窗
    dialogDz, //电子竞技弹窗
    dialogEditOnlineNumber, //在线会员弹框
    dialogSportOrder,//球种排序
  },
  data() {
    return {
      // 通用 开关参数
      show_which_config: {},
      //导出报表参数
      export_param: {},
      // 导出弹窗阈值
      exportExcelShow: false,
      //移动到语言配置上
      lan_key: "",
      vedio_key: "",
      chat_key: "",
      web_key: "",
      yuYueTouZhu_key: "",

      football_key: "", //提前结算 足球
      basketball_key: "", //提前结算 篮球
      zr_key: "", //真人
      cp_key: "", //彩票
      dz_key: "", //电子竞技      
      bh_key: "", //注单历史
      highlight_replay_key:"",//精彩回放 配置
      openElectronicTy_key: "", // 电子体育
      //语言
      xinyong_svg_icon,
      tabledata: [], // 表格数据
      columns: directlyMerchantConfig, // 表格配置项
      tabledata_loading: false, // 表格loading
      // 搜索条件
      searchForm: {
        merchantName: "", // 商户名称
        parentName: "", // 渠道商户名称
        merchantCode: "", //商户编码
        merchantTag: "", //  信用网 现金网
        transferMode: "", //钱包类型
        isApp: "", //App商户
        h5Layout: "", // H5布局样式
        pcLayout: "", // PC布局样式
        isExternal: "", //内外部商户
      },
   
      isExternaltTag_list: i18n.t("internal.filters.isExternaltTag"), //内外部商户 isExternal     0外部   1内部
      isTesttusTag_list: i18n.t("internal.filters.isTesttusTag"), //测试商户 isTest    0否    1是
      merchantTag_list: i18n.t("internal.filters.merchantTag"), //信用网 现金网
      transferMode_list: i18n.t("internal.filters.transferMode"), //转账钱包 免转钱包
      isApp_list: i18n.t("internal.filters.isApp"), // 0否   1是
      btnName: "+ " + i18n.t("internal.label.label12"), // 新建直营按钮
      lookMerchantShow: false, // 查看商户弹窗阀值
      setMerchantShow: false, // 设置管理员弹窗阀值
      setIpShow: false, // 设置ip白名单阀值
      pitchesShow: false, // 商户球种设置阀值
      show_lan: false, //设置c端多语言   （已废弃）
      show_lan_default: false, //设置c端m默认语言弹窗阀值
      show_highlight_replay: false, //设置 精彩回放 弹窗阀值
      show_credit_limit: false, // 信用网商 查看弹窗
      setShow: false, // 新建商户弹窗阀值
      show_plate: false, //c端板式设定
      vsShow: false, //  通用弹窗开关
      updateMerchantShow: false, // 修改商户弹窗阀值
      showDialogObj: {}, // 传值给弹窗的对象
      currentRecord: "", // 当前行表格数据对象
      isUpdate: false,   // 新建商户 还是 编辑商户 
      show_vsgk_default: false,  // 视频流量管控配置弹窗 阈值
      vsgkShow: false,
      show_yuYueTouZhu_default: false,  // 预约投注弹窗 阈值
      show_chatroom_default: false,   // // 聊天室开关配置弹窗 阈值
      show_disabled_merchant: false,  // 批量启&禁用 弹出阈值
      visible:false,
      position:'top',
      sessionStorage_data:null,  //用来获取sessionStorage里边的数据
      show_add_certificate: false,  //新增证书弹窗 阈值
      show_add_webStatus: false,  // 客户端状态弹窗
      show_add_electronic: false,  // 电子体育弹窗 阈值

      show_football_default: false, //提前结算足球弹窗 阈值
      show_basketball_default: false, //提前结算篮球弹窗 阈值
      show_zr_default: false, // 真人弹窗 阈值
      show_cp_default: false, // 彩票弹窗 阈值
      show_dz_default: false, // 电子弹窗 阈值

      show_bethistory_default: false, //注单历史弹窗
      component_key: "", // 展示组件
      show_dialog_component: false,  // 组件弹窗阈值
      show_online_member: false, // 设置在线会员弹窗
      // 球种排序弹框
      show_sport_order:false, 
    };
  },
  created() {
    this.tabledata_loading = true;
    this.init_session_params();
  },
  mounted() {
    this.sessionStorage_data = this.$q.sessionStorage.getItem('userInfo')
  },
  destroyed() {
    this.$q.sessionStorage.remove("params");
  },
  computed: {
    ...mapGetters(["get_data", "get_user_info"]),
  },
  watch: {
    "pagination.agentLevel"(val) {
      this.handle_function(val);
    },
  },
  methods: {
    // 导出报表
    handle_export_excel() {
      if (this.pagination.total > 0) {
        let params = this.compute_init_tabledata_params();
        params = this.delete_empty_property_with_exclude(params);
        let which =
          this.$q.localStorage.getItem("task_type") == 1 ||
          !this.$q.localStorage.getItem("task_type");
        if (which) {
          Object.assign(
            params,
            // { "user-id": this.get_user_info.userId },
            // { "app-id": this.get_user_info.appId },
            { url: "/manage/merchant/merchantInfoExport", which }
          );
        }
        this.export_param = params;
        this.exportExcelShow = true;
      } else {
        this.handle_error();
      }
    },
    // 鼠标进入测试商户
    handle_isTest_enter(record) {
      record.show_isTest_edit_icon = true;
    },
    // 鼠标离开测试商户
    handle_isTest_leave(record) {
      record.show_isTest_edit_icon = false;
    },
    // 鼠标进入APP商户
    handle_isApp_enter(record) {
      record.show_isApp_edit_icon = true;
    },
    // 鼠标离开APP商户
    handle_isApp_leave(record) {
      record.show_isApp_edit_icon = false;
    },
    // 鼠标进入内外部商户
    handle_isExternal_enter(record) {
      record.show_isExternal_edit_icon = true;
    },
    // 鼠标离开内外部商户
    handle_isExternal_leavel(record) {
      record.show_isExternal_edit_icon = false;
    },
    //打开内外部编辑框
    handle_isExternal_edit_icon_click(record) {
      record.isExternal_editing = true;
      record.show_isExternal_edit_icon = false;
    },
    //打开测试商户编辑框
    handle_isTest_edit_icon_click(record) {
      record.isTest_editing = true;
      record.show_isTest_edit_icon = false;
    },
    //打开测试商户编辑框
    handle_isApp_edit_icon_click(record) {
      record.isApp_editing = true;
      record.show_isApp_edit_icon = false;
    },
    // 内外部商户修改完成
    handle_isExternal_editi_complete(record, key) {
      record.isExternal_editing = false;
      record.show_isExternal_edit_icon = false;
      this.handle_update_isTestOrExternal(record, key);
    },
    //内外部商户取消修改
    handle_isExternal_edit_cancel(record) {
      record.isExternal_editing = false;
      record.show_isExternal_edit_icon = false;
      record.isExternal_edit = record.isExternal || 0;
    },
    // 测试商户修改完成
    handle_isTest_edit_complete(record, key) {
      record.isTest_editing = false;
      record.show_isTest_edit_icon = false;
      this.handle_update_isTestOrExternal(record, key);
    },
    //测试商户取消修改
    handle_isTest_edit_cancel(record) {
      record.isTest_editing = false;
      record.show_isTest_edit_icon = false;
      record.isTest_edit = record.isTest || 0;
    },
    // App商户修改完成
    handle_isApp_edit_complete(record, key) {
      record.isApp_editing = false;
      record.show_isApp_edit_icon = false;
      this.handle_update_isApp(record, key);
    },
    //App商户取消修改
    handle_isApp_edit_cancel(record) {
      record.isApp_editing = false;
      record.show_isApp_edit_icon = false;
      record.isApp_edit = record.isApp || 0;
    },
    handle_merchants(value) {
      console.log(`selected ${value}`);
    },
    /**
     * @description  通用开关 确认变更弹窗数据处理
     * @param  {Object} record 点击某行数据对象
     * @return {undefined} undefined
     */
    handle_click_vs(record, show_which) {
      this.currentRecord = record;
      this.show_which = show_which;
      this.show_which_config = {};
      switch (show_which) {
        // 客户端
        case "status":
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "get_manage_merchant_updateMerchantStatus",
            show_keyword: i18n.t("internal.template2.label35"),
          };
          break;
        // 商户后台
        case "backendSwitch":
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "get_manage_merchant_updateMerchantBackendStatus",
            show_keyword: i18n.t("internal.template2.label36"),
          };
          break;
          
        //额度转入
        case "merchantTransferIn":
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "post_add_merchant_dz_update_switchConfig",
            show_keyword: i18n.t("internal.template2.label41"),
          };
          break;
        //额度转出
        case "merchantTransferOut":
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "post_add_merchant_dz_update_switchConfig",
            show_keyword: i18n.t("internal.template2.label42"),
          };
          break;
        // 对内 视频开关
        case "openVideo":
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "post_manage_merchant_update_openVsport",
            show_keyword: i18n.t("internal.template2.label31"),
          };
        console.log("----------this.show_which_config",this.show_which_config);
          break;
        // 合买开关
        case "merchantPurchase":
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "post_add_merchant_dz_update_switchConfig",
            show_keyword: i18n.t("internal.template2.label60"),
          };
          break;
        //    视频流量管控开关
        case "videoSwitch":
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "post_vsgk_updateConfig",
            show_keyword: i18n.t("internal.template2.label33"),
          };
          break;
        // //    提前结算  开关
        // case "settleSwitchAdvance":
        //   this.show_which_config = {
        //     show_which,
        //     show_status: record[show_which],
        //     api_fn: "post_manage_merchant_update_opensettleSwitchAdvance",
        //     show_keyword: i18n.t("internal.template2.label3"),
        //   };
        //   break;
        //    提前结算 足球  开关
        case "settleSwitchAdvance":
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "post_manage_merchant_update_opensettleSwitchAdvance",
            show_keyword: i18n.t("internal.template2.label38"),
          };
          break;
        //    提前结算篮球 开关
        case "settleSwitchBasket":
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "post_manage_merchant_update_opensettleSwitchAdvance",
            show_keyword: i18n.t("internal.template2.label39"),
          };
          break;
        //虚拟体育开关
        case "openVrSport":
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "post_manage_merchant_update_openVrSport",
            show_keyword: i18n.t("internal.template2.label2"),
          };
          break;
        //预约投注开关
        case "bookBet":
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "post_manage_merchant_update_bookBet",
            show_keyword: i18n.t("internal.template2.label32"),
          };
          break;
        //电子竞技开关弹窗
        case "openEsport":
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "post_manage_merchant_update_openEsport",
            show_keyword: i18n.t("internal.template2.label30"),
          };
          break;
          //聊天室开关
        case "chatRoomSwitch":
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "post_manage_merchant_update_chatSwitch",
            show_keyword: i18n.t("internal.template2.label34"),
          };
          break;
        //注单历史  开关
        case "bh":
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "post_add_merchant_update_betHistory_switchConfig",
            show_keyword: i18n.t("internal.template2.label40" ),
          };
          break;
           //精彩回放 开关   确认将精彩回放开关变更为
        case "eventSwitch":
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "post_merchant_update_updateMerchantEventSwitch",
            show_keyword: i18n.t("internal.system_level_switch.label33") + i18n.t("internal.system_level_switch.label17") + i18n.t("internal.system_level_switch.label34"),
          };  
          break;
        case "ommv": 
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "post_add_merchant_update_switchConfig",
            show_keyword: i18n.t("internal.system_level_switch.label24"),
          };
          break
        // 电子体育  真人开关  彩票开关
        case "openElectronicTy":
        case "zr":
        case "cp":
          this.show_which_config = {
            show_which,
            show_status: record[show_which],
            api_fn: "post_add_merchant_update_switchConfig",
            show_keyword: i18n.t("internal.system_level_switch." + show_which),
          };
          break;
        default:
          this.show_which_config = {};
          break;
      }
      this.vsShow = true;
    },
 
    /**
     * @description  通用 弹窗 回调处理
     * @param  {Boolean} bool 是否更新成功
     * @return {undefined} undefined
     */
    async show_which_callback_fn() {
      let key = this.show_which_config["show_which"];
      // console.log("---------------- key = this.show_which_configshow_which++",key);
      let api_fn = this.show_which_config["api_fn"];
      let value = this.currentRecord[key] == 0 ? 1 : 0;
      let params = {};
      // 预约投注 bookBet   视频流量管控 videoSwitch   
      if (key == 'bookBet' || key == 'videoSwitch'){
        params = {
        id: this.currentRecord.id,
        merchantCode: this.currentRecord.merchantCode,
        agentLevel: this.currentRecord.agentLevel,
      [key]: value,
     };
      }else if(key =='openVideo'){
         params = {
        internalOrExternal:0,
        id: this.currentRecord.id,
      [key]: value,
     };
      }else if(['merchantTransferIn',"merchantTransferOut","merchantPurchase"].includes(key)){
        params={
          merchantCode: this.currentRecord.merchantCode,
      merchantParams: {
        [key]: value,
          }
      }
      }else if(key =='chatRoomSwitch' || key =='eventSwitch' ){
         params = {
        merchantCode: this.currentRecord.merchantCode,
      [key]: value,
     };
     // 客户端 status   商户后台 backendSwitch
      }else if (key == 'status' || key == 'backendSwitch'){
        params = {
          merchantCode: this.currentRecord.merchantCode,
          status: value,
        };
      // 电子体育
      }else if(key == "openElectronicTy"){
        params = {
          id: this.currentRecord.id,
          [key]: value,

        };
        if(!!value){
          params.openElectronicFootball = 1;
          params.openElectronicBasketball = 1;
        }else {
          params.openElectronicFootball = 0;
          params.openElectronicBasketball = 0;
        }
      // 真人
      } else if(key == "zr") {
        params = {
          id: this.currentRecord.id,
          [key]: value,

        };
        if(!!value){
          params.zrPc = 1;
          params.zrH5 = 1;
          params.zrSdk = 1;
          params.zrApi = 1;

        }else {
          params.zrPc = 0;
          params.zrH5 = 0;
          params.zrSdk = 0;
          params.zrApi = 0;
        }
      // 彩票
      } else if(key == "cp") {
        params = {
          id: this.currentRecord.id,
          [key]: value,

        };
        if(!!value){
          params.cpPc = 1;
          params.cpH5 = 1;
          params.cpSdk = 1;
          params.cpApi = 1;

        }else {
          params.cpPc = 0;
          params.cpH5 = 0;
          params.cpSdk = 0;
          params.cpApi = 0;
        }
      // 电子竞技
      } else if(key == "openEsport") {
        params = {
          id: this.currentRecord.id,
          [key]: value,

        };
        if(!!value){
          params.openEsportPc = 1;
          params.openEsportH5 = 1;
          params.openEsportSdk = 1;
          params.openEsportApi = 1;

        }else {
          params.openEsportPc = 0;
          params.openEsportH5 = 0;
          params.openEsportSdk = 0;
          params.openEsportApi = 0;
        }
      } else if(key == "bh") {
        params = {
          id: this.currentRecord.id,
          [key]: value,

        };
        if(!!value){
          params.bhTy = 1;
          params.bhZr = 1;
          params.bhCp = 1;
        }else {
          params.bhTy = 0;
          params.bhZr = 0;
          params.bhCp = 0;
        }
      } else{
        params = {
          id: this.currentRecord.id,
          [key]: value,
        };
        if(key=="settleSwitchBasket"){
          params.sportId=2
        }else if(key == "settleSwitchAdvance"){
          params.sportId=1
        }
     }
      try {
        let {
          data: { code, msg },
        } = await api_merchant[api_fn](params);
        console.log(code, msg);
        if (code == "0000000") {
          this.$message.success(i18n.t("internal.message.label106"));
          this.currentRecord[key] = value;
          // 电子体育关闭商户球种内开关
          if(["openElectronicTy", "openVrSport", "openEsport"].includes(key)){
            this.set_updateConfigFilterV2(value, key)
          }else if(key == 'status' || key == 'backendSwitch'){
              this.invalid_update_status(value, key)
          }
          this.$forceUpdate();
        } else {
          this.$message.error(i18n.t("internal.message.label107") + `${msg}`);
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.vsShow = false;
      }
    },
    // 测试商户修改   内外不商户更改
    handle_update_isTestOrExternal(record, key) {
      console.log("handle_update_isTestOrExternal(record) -", record, key);
      let params = {
        id: record.id,
      };
      // 是否测试商户0否1是
      if (key == "isTest") {
        params["isTest"] = record.isTest_edit;
      }
      //是否外部商户0外部1内部
      if (key == "isExternal") {
        params["isExternal"] = record.isExternal_edit;
      }
      api_merchant
        .post_manage_merchant_update_isTestOrExternal(params)
        .then((res) => {
          let { code, msg } = res.data;
          if (code == "0000000") {
            //设置成功
            this.$message.success(i18n.t("internal.template.label187"));
          } else {
            this.$message.warn(msg);
          }
          this.initTableData();
        });
    },
    handle_update_isApp(record, key) {
      console.log("handle_update-", record, key);
      // //是否接入App商户 0否 1是
      let params = {
        id: record.id,
      };
      if (key == "isApp") {
        params["isApp"] = record.isApp_edit;
      }
      api_merchant.post_manage_merchant_update_isApp(params).then((res) => {
        let { code, msg } = res.data;
        if (code == "0000000") {
          //设置成功
          this.$message.success(i18n.t("internal.template.label187"));
        } else {
          this.$message.warn(msg);
        }
        this.initTableData();
      });
    },
    /**
     * @description 解决新建商户和编辑商户参数回旋的问题！！！
     * @return {undefined} undefined
     */
    init_session_params() {
      // 解决由首页跳进来表格不动态配置问题
      this.handle_function(this.pagination.agentLevel);
      // 解决有我的信息跳转详情
      if (this.get_data) {
        this.searchForm.merchantName = this.get_data.merchantName;
        this.pagination.agentLevel = String(this.get_data.agentLevel);
        this.handle_look(this.get_data);
      }
      // 解决首页路由跳转
      // let editOrUpdate = this.$q.sessionStorage.getItem("params") || {};
      // if (Object.keys(editOrUpdate).length != 0) {
      //   alert(1)
      //   let { pageNum, pageSize, merchantName, agentLevel, parentName } = editOrUpdate;
      //   this.pagination.current = pageNum * 1;
      //   this.pagination.pageSize = pageSize * 1;
      //   this.pagination.agentLevel = agentLevel;
      //   if (merchantName || parentName || this.pagination.current == 1) {
      //     this.searchForm.merchantName = merchantName;
      //     this.searchForm.parentName = parentName;
      //     this.initTableData();
      //   }
      // } else {
      this.initTableData();
      // }
    },
    /**
     * @description:获取c端多语言（无用）
     * @param {type}
     * @return {type}
     */
    get_lan_list() {
      api_merchant.get_cplate_lan_list().then((res) => {
        let code = this.$lodash.get(res, "data.code");
        if (code == "0000000") {
          this.lan_list = this.$lodash.get(res, "data.data") || [];
        }
      });
    },
    /**
     * @description 初始化表格数据
     * @return {undefined} undefined
     */
    initTableData() {
      //  this.tabledata_loading = false;
      //   this.tabledata =[{}]
      //   return false
      this.tabledata_loading = true;
      let params = this.compute_init_tabledata_params();
      params = this.delete_empty_property_with_exclude(params);
      api_merchant.get_manage_merchant_list(params).then((res) => {
        this.tabledata_loading = false;
        let code = this.$lodash.get(res, "data.code");
        let msg = this.$lodash.get(res, "data.msg");
        if (code == "0000000") {
          let currentPage = this.$lodash.get(res, "data.data.pageNum") * 1 || 1;
          let arr = this.$lodash.get(res, "data.data.list") || [];
          this.pagination.start = this.$lodash.get(res, "data.data.startRow");
          this.tabledata = this.rebuild_tabledata_to_needed(arr);
          this.pagination.total =
            this.$lodash.get(res, "data.data.total") * 1 || 0;
        } else {
          msg && this.$message.error(msg, 5);
        }
      });
    },
    rebuild_tabledata_to_needed(arr) {
      let this_ = this;
      arr.map((item, index) => {
        item._index =
          this.pagination.start == 0
            ? this.pagination.start + index + 1
            : this.pagination.start + index;
        // 是否测试商户0否1是  默认 否
        item.isTest_editing = false;
        item.show_isTest_edit_icon = false;
        item.isTest_edit = item.isTest || 0;
        //是否外部商户0外部1内部 默认外部
        item.isExternal_editing = false;
        item.show_isExternal_edit_icon = false;
        item.isExternal_edit = item.isExternal || 0;
        // 是否APP商户0否1是  默认 否
        item.isApp_editing = false;
        item.show_isApp_edit_icon = false;
        item.isApp_edit = item.isApp || 0;
        item.defaultLanguage &&
          (item.defaultLanguageList = item.defaultLanguage
            .split(",")
            .map((x) => {
              let obj = this_.lan_list.find((lan) => x == lan.languageName);
              if(obj){ //这里不做判断的话会报错：msg_zs是undefined
                return this.is_zs == "zs" ? obj.msg_zs : this.is_zs == "en" ? obj.msg_en : obj.msg_ko;
              }
            }));
      });
      return arr;
    },
    /**
     * @description 处理给后台的参数
     * @return {Object} 处理过后后台所需要的参数对象
     */
    compute_init_tabledata_params() {
      let { agentLevel, sort, orderBy, status } = this.pagination;
      let {
        merchantName,
        parentName,
        merchantCode,
        merchantTag,
        transferMode,
        isApp,
        h5Layout,
        pcLayout,
        isExternal,
      } = this.searchForm;
      let params = {
        agentLevel,
        merchantCode,
        merchantName, //商户名称，可模糊搜索
        parentName, //渠道商名称，可模糊搜索
        // status: 1,//商户状态 、1.启用、2.禁用 搜索条件，不传默认查全部
        pageNum: this.pagination.current, //分页，查询第几页数据。
        pageSize: this.pagination.pageSize, //分页，每页查询多少条，默认20条。可不传
        sort,
        orderBy,
        status,
        merchantTag,
        transferMode,
        isApp,
        h5Layout,
        pcLayout,
        isExternal,
      };
      if (![0, 2].includes(Number(agentLevel))) {
        delete params.merchantTag;
        delete params.isApp;
      }
      return params;
    },
    /**
     * @description 根据商户等级不同显示不同按钮  直营-新建直营
     * @param  {String} val 商户等级
     * @return {undefined} undefined
     */
    handle_function(val) {
      if (val == "2") {
        //新建二级商户
        this.columns = secondMerchantConfig;
        this.btnName = "+" + i18n.t("internal.label.label13");
      } else if (val == "1") {
        //新建渠道商户
        this.columns = channelMerchantConfig;
        this.btnName = "+ " + i18n.t("internal.label.label8");
        this.searchForm.parentName = "";
      } else {
        //新建直营商户
        this.columns = directlyMerchantConfig;
        this.btnName = "+ " + i18n.t("internal.label.label12");
        this.searchForm.parentName = "";
      }
    },
    /**
     * @description 点击搜索按钮
     * @return {undefined} undefined
     */
    handle_search() {
      this.initTableData();
    },
    /**
     * 球类设置弹窗 关闭
     */
    handle_close_dialog_pitches_set() {
      this.handle_search();
      this.pitchesShow = false;
      this.$forceUpdate();
    },
    /**
     * @description 新建商户-根据不同商户等级新建不同的商户
     * @return {undefined} undefined
     */
    handle_new_merchant() {
      this.isUpdate = false;
      let name = "";
      if (this.pagination.agentLevel == "1") {
        name = "merchantc_channel_edit";
      } else if (this.pagination.agentLevel == "2") {
        name = "merchantc_second_edit";
      } else {
        name = "merchantc_edit";
      }
      this.setShow = true;
      this.showDialogObj = { ...this.pagination };
    },
    /**
     * @description 编辑在线会员按钮-编辑不同在线会员
     * @return {undefined} undefined
     */    
     handle_edit_online_member(record, index){
      let name = "";
      if (this.pagination.agentLevel == "1") {
        name = "merchantc_channel_update";
      } else if (this.pagination.agentLevel == "2") {
        name = "merchantc_second_update";
      } else {
        name = "merchantc_update";
      }
      // console.log(record, index, "qqq");
      this.showDialogObj = { ...record, ...this.pagination };
      this.show_online_member = true;
      this.isUpdate = true;
    },    
    /**
     * @description 编辑商户按钮-根据不同商户等级编辑不同的商户
     * @return {undefined} undefined
     */
    handle_edit(record, index) {
      let name = "";
      if (this.pagination.agentLevel == "1") {
        name = "merchantc_channel_update";
      } else if (this.pagination.agentLevel == "2") {
        name = "merchantc_second_update";
      } else {
        name = "merchantc_update";
      }
      // console.log(record, index, "qqq");
      this.showDialogObj = { ...record, ...this.pagination };
      this.setShow = true;
      this.isUpdate = true;
    },
    /**
     * @description 查看商户
     * @return {undefined} undefined
     */
    handle_look(record) {
      this.showDialogObj = { ...record };
      this.lookMerchantShow = true;
    },
    /**
     * @description 设置管理员
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     * @return {undefined} undefined
     */
    handle_set_admin(record, index) {
      this.showDialogObj = { ...record };
      this.showDialogObj.merchantAdmin_old = record.merchantAdmin;
      this.showDialogObj.sessionStorage_data = this.sessionStorage_data;
      this.setMerchantShow = true;
      this.setIpShow = false;
    },
    /**
     * @description 设置ip白名单
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     * @return {undefined} undefined
     */
    handleSetIp(record, index) {
      this.showDialogObj = { ...record };
      this.setIpShow = true;
      this.setMerchantShow = false;
    },
    /**
     * @description 设置c端多语言  （已废弃）
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     */
    handleSetLan(record) {
      this.showDialogObj = { ...record };
      this.show_lan = true;
    },
    /**
     * @description 设置c端默认语言
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     */
    handleSetLanDefault(record) {
      this.showDialogObj = { ...record };
      this.show_lan_default = true;
    },

       /**
     * @description 精彩回放
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     */
    handleHighlightReplay(record) {
      this.showDialogObj = { ...record };
      this.show_highlight_replay = true;
    },

    /**
     * @description 设置c端多语言 （已废弃）
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     */
    handleCloseLan(record, index) {
      this.show_lan = false;
      this.initTableData();
    },
    /**
     * @description 关闭c端默认语言弹窗
     */
    handleCloseLanDefault() {
      this.show_lan_default = false;
      this.show_vsgk_default = false;
      this.show_chatroom_default = false;
      this.show_yuYueTouZhu_default = false;
      this.show_highlight_replay = false;

      this.show_football_default = false;
      this.show_basketball_default = false;
      this.show_zr_default = false;
      this.show_cp_default = false;
      this.show_dz_default = false;      
      this.show_bethistory_default = false;
      
      this.initTableData();
    },

    
    /**
     * @description 设置视频流量管控配置弹窗
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     */
    handleSetVsgkDefault(record) {
      this.showDialogObj = { ...record };
      this.show_vsgk_default = true;
    },

  /**
     * @description 预约投注开关配置弹窗
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     */
    handleSetVsgkDefault2(record) {
      this.showDialogObj = { ...record };
      this.show_yuYueTouZhu_default = true;
    }, 

  /**
     * @description 提前结算-足球 弹窗
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     */
     handleSetVsgkDefault4(record) {
      this.showDialogObj = { ...record };
      this.show_football_default = true;
    },
    /**
     * @description 提前结算-篮球 弹窗
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     */
     handleSetVsgkDefault5(record) {
      this.showDialogObj = { ...record };
      this.show_basketball_default = true;
    },
    /**
     * @description  真人 弹窗
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     */
     handleSetVsgkDefault_zr(record) {
      this.showDialogObj = { ...record };
      this.show_zr_default = true;
    },
    /**
     * @description  彩票 弹窗
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     */
     handleSetVsgkDefault_cp(record) {
      this.showDialogObj = { ...record };
      this.show_cp_default = true;
    },
    /**
     * @description  电子竞技 弹窗
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     */
     handleSetVsgkDefault_dz(record) {
      this.showDialogObj = { ...record };
      this.show_dz_default = true;
    },
  /**
     * @description 聊天室开关配置弹窗
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     */
     handleSetVsgkDefault3(record) {
      this.showDialogObj = { ...record };
      this.show_chatroom_default = true;
    },
    
      /**
     * @description 注单历史弹窗
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     */
     handleSetVsgk_bethistory(record) {
      this.showDialogObj = { ...record };
      this.show_bethistory_default = true;
    },
    // 关闭  信用网商 弹窗（功能已去除）
    handleCloseCreditLimit() {
      this.show_credit_limit = false;
      this.initTableData();
    },
    // 显示  信用网商 弹窗（功能已去除）
    handle_show_credit_limit(record, index) {
      console.log("   handle_show_credit_limit(record,index){", record);
      this.showDialogObj = { ...record };
      this.show_credit_limit = true;
    },
    /**
     * @description 新建商户弹窗关闭
     * @param  {Boolean} val 是否新建成功
     * @return {undefined} undefined
     */
    handle_close_dialog_set_merchant(val) {
      if (val) {
        this.setShow = false;
        this.initTableData();
      } else {
        this.setShow = false;
      }
    },
    /**
     * @description:c端板式设定
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     */
    handle_set_cplate(record, index) {
      this.showDialogObj = { ...record };
      let domain = {
        pc: record.pcDomain || "",
        h5: record.h5Domain || "",
      };
      this.$q.sessionStorage.set("domain", domain);
      this.show_plate = true;
    },
    /**
     * @description:商户球种设置
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     */
    handlePitchesSet(record, index) {
      this.showDialogObj = { ...record };
      this.pitchesShow = true;
      this.setMerchantShow = false;
    },
    /**
     * @description 关闭商户球种设置弹窗
     * @return {undefined} undefined
     */
    /**
     * @description:关闭c端板式设定
     */
    handle_close_cplate() {
      this.show_plate = false;
      this.initTableData();
    },
    /**
     * @description 设置二级商户
     * @param  {Object} record 点击某行数据对象
     * @param  {Number} index 点击某行的索引
     * @return {undefined} undefined
     */
    handle_set_second_merchant(record, index) {
      this.isUpdate = false;
      record.channelToSecond = true;
      this.showDialogObj = { ...record, ...this.pagination };
      console.table(record.childConnectMode);
      let {
        transferMode,
        url,
        callbackUrl,
        balanceUrl,
        merchantName,
        id,
        whiteIp,
        startTime,
        endTime,
        merchantCode,
      } = record;
      let channelToSecond = {
        id,
        merchantName,
        transferMode,
        balanceUrl,
        url,
        callbackUrl,
        whiteIp,
        startTime,
        endTime,
        pageMode: 1,
        merchantCode,
      };
      this.$q.sessionStorage.set("channelToSecond", channelToSecond);
      this.setShow = true;
    },
    /**
     * @func handle_add_certificate
     * @desc 打开新增证书弹窗
     * @param {}  
     * @return {} 
     */
    handle_add_certificate(record) {
      this.showDialogObj = {...record};
      this.show_add_certificate = true;
    },
    /**
     * @func handle_close_add_certificate
     * @desc 关闭新增证书弹窗
     * @param {}  
     * @return {} 
     */
    handle_close_add_certificate(isinit) {
      this.show_add_certificate = false;
      if(isinit) {
        this.initTableData();
      }
    },
    /**
     * @func handle_sport_order
     * @desc 打开球种排序弹框
     * @param {}  
     * @return {} 
     */    
    handle_sport_order(record){
      this.showDialogObj = {...record};
      this.show_sport_order = true;
    },
    /**
     * @description 关闭管理员弹窗
     * @return {undefined} undefined
     */
    handle_close_dialog_set() {
      this.setMerchantShow = false;
      this.initTableData();
    },
    /**
     * @description 关闭设置ip弹窗
     * @return {undefined} undefined
     */
    handle_close_dialog_ip() {
      this.setIpShow = false;
      this.initTableData();
    },   
    /**
     * @description 批量启&禁用 弹出
     * @return {undefined} undefined
     */
    handle_disabled_merchant(){
      this.show_disabled_merchant = true;
    },
    /**
     * @description 批量启&禁用 关闭
     * @return {undefined} undefined
     */
    handleCloseDisabledMerchant(){
      this.show_disabled_merchant = false;
      this.initTableData();
    },
    /*
    * 失效商户管理-相同按钮联动代码
    */
    invalid_update_status(value,key){
      let api_fn;
          let params={
            merchantId: this.currentRecord.id,
            merchantCode: this.currentRecord.merchantCode,
            merchantName: this.currentRecord.merchantName,
            agentLevel: this.currentRecord.agentLevel,
          }
          if(key=="status"){
            params={
           ...params,
            backendStatus:this.currentRecord.backendSwitch,
            clientStatus:value,
          }

          }else if(key=="backendSwitch"){
            params={
              ...params,
              clientStatus:this.currentRecord.status,
              backendStatus:value,
          }
          }
        api_merchant.invalid.update_invalid_management_status(params);
    },
    /**
     * 计算币种
     * @param {*} currencyList
     */
    // computedCode(currencyList){
    //   let currencyCodeList = [];
    //   currencyList?.split(",").map(item => {
    //     this.currencyRateRawList.map(code => {
    //       if(code.currencyCode == item){
    //         currencyCodeList.push(code.countryCn)
    //       }
    //     })
    //   })      
    //   return currencyCodeList.toString();
    // }
    
    /**
     * @description 点开二次验证弹窗
     * @return {undefined} undefined
     */
    showModal( position ) {
      console.log(position)
      this.position = position
      this.visible = true;
    },
    /**
     * @description 关闭二次验证弹窗
     * @return {undefined} undefined
     */
    close_window( obj ) {
      this.visible = obj.x;
      this.initTableData();
    },
     /**
      * 客户端状态弹窗
      * @param {*} row 点击项
      * @param {*} type 点击行
      */
    handle_webStatus(row, type) {
      this.show_add_webStatus = true;
      this.showDialogObj = row;
    },
    /**
     * 客户端状态提交
     * @param {*} istrue 
     */
    async on_emit_detail(istrue) {
      if(istrue){
        this.show_add_webStatus = false;
      }
    },
    /**
     * 电子体育
     * @param {*} row 
     */
    handleSetElectronic(row) {
      this.showDialogObj = row;
      this.show_add_electronic = true;
    },
    /**
     * 电子体育 关闭弹窗
     * @param {*} istrue 
     */
    handle_close_add_electronic(istrue) {
      if(istrue){
        this.show_add_electronic = false;
        this.initTableData();
      }
    },
    /**
     * 关闭商户足球开关 - 虚拟足球篮球
     * @param {*} type 
     */
    async set_updateConfigFilterV2(type, id) {
      let idList = []
      if(id == "openElectronicTy"){
        // -1 C01-足球	-2 O01-足球	 -3 B03-足球  -4 B03-篮球 -5 C01-篮球  -7 O01-篮球
        idList = [-1, -2, -3, -4, -5, -7 ,-8 ,-9];
      }else if(id == "openVrSport"){
        // VR虚拟体育 1001VR足球 1002VR赛狗 1004VR篮球 1007VR泥地赛车 1008VR卡丁车  1009VR泥地摩托车  1010VR摩托车  1011VR赛马  1012VR马车赛
        idList = [1001, 1002, 1004, 1007, 1008, 1009, 1010, 1011, 1012];
      }else if(id == "openEsport"){
        // 电子竞技 100英雄联盟 101Dota2  102 CS:GO  103王者荣耀  104绝地求生
        idList = [100, 101, 102, 103, 104];
      }
      let params = {
          idList, 
          merchantCode: this.currentRecord.merchantCode,
          type: 1,
          tag: !!type ? 0 : 1,
          agentLevel: this.currentRecord.agentLevel,
        };
      await api_merchant.post_manage_merchant_updateConfigFilterV2(params)
      // 关闭电子竞技子开关
      if(id == "openEsport"){
        // type 1 id openEsport
        let params = {
            merchantCode: this.currentRecord.merchantCode,
            merchantParams: {
              openEsportPc: type ? 1 : 0,
              openEsportH5: type ? 1 : 0,
              openEsportSdk: type ? 1 : 0,
              openEsportApi: type ? 1 : 0,
              openEsport: type ? 1 : 0,
            }
        }
        await api_merchant.post_add_merchant_dz_update_switchConfig(params)
        
      }
    },
  },
};
</script>
<style lang="scss" scoped>
// loading样式
::v-deep .ant-spin-nested-loading > div > .ant-spin {
  max-height: 760px;
  min-height: 760px;
}
// 无数据时样式
::v-deep .ant-empty-normal {
  margin: 310px 0;
}
.text-over {
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
  white-space: nowrap;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  width: 160px;
  display: inline-block;
  white-space: nowrap;
}
.text-over-120 {
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
  white-space: nowrap;
  display: block;
}
.text-over-180 {
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 160px;
  white-space: nowrap;
  display: block;
}
.text-over-150 {
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
  white-space: nowrap;
}
.text-over-160 {
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 105px;
  white-space: nowrap;
  display: block;
}
.no-left ::v-deep .q-field__append {
  border-left: none;
}
::v-deep .ant-select {
  font-size: 12px;
}
// button-radio的文字颜色
::v-deep .ant-radio-button-wrapper {
  color: $panda-bg-4;
}
::v-deep .ant-radio-button-wrapper-checked {
  color: $panda-bg-2;
}
::v-deep
  .ant-table-middle
  > .ant-table-content
  > .ant-table-scroll
  > .ant-table-header
  > table
  > .ant-table-thead
  > tr
  > th {
  background: #f4f5f8;
}
::v-deep
  .ant-table-middle
  > .ant-table-content
  > .ant-table-fixed-right
  > .ant-table-header
  > table
  > .ant-table-thead
  > tr
  > th {
  background: #f4f5f8;
}
.duihao {
  font-size: 20px;
  color: #f00;
}
.guanbi {
  font-size: 20px;
  color: #008000;
}
.panda-icon-panda-icon-xinzengzhengshu {
  width:20px;
}

.option-span{
  i,img{
    margin-right: 2px;
  }

}

</style>
