<!--

 *  @description: 商户中心/商户管理设置 球类排序
 -->
 <template>
    <div
      style="width: 860px ; height:auto;max-width:1080px;overflow:hidden;"
      class="text-panda-text-7"
    >
      <q-card class="bg-white text-black">
        <q-card-section class="no-padding">
          <div
            class="row line-height-40px fs14 bg-panda-dialog text-panda-text-7 pr10x"
          >
            <div class="pl20x fw_600">
                {{ detailObj.merchantName }}
                {{ $t('internal.sport_order_list.merchant_ball_sorting') }}
            </div>
            <q-space></q-space>
            <q-btn
              class="mr5x text-panda-dialog-close"
              icon="close"
              v-close-popup
            />
          </div>
        </q-card-section>
        <q-separator></q-separator>
        <div class="row fs14 bg-panda-dialog-sh text-panda-text-7 pr10x">
            <a-tab-pane
              force-render
            >
             <sport-order-list  :detailObj="detailObj"></sport-order-list>
            </a-tab-pane>  
        </div>
      </q-card>
    </div>
  </template>
  <script>
  import { i18n } from 'src/boot/i18n';
  import { api_merchant } from "src/api/index.js";
  import sportOrderList from "src/components/common/sport_order_list/index.vue";

  export default {
    components: {

        sportOrderList
    },
    data() {
      return {

      };
    },
    props: {
      detailObj: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    beforeCreate() {
    },
    created(){
    },
    mounted() {
    },
    methods: {
  
    }
  };
  </script>