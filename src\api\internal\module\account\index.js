/*
 * @Desc:
 * @Date: 2019-12-28 20:59:34
 * @Author:Nice
 * @FilePath: /src/api/internal/module/account/index.js
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_1;
let url_prefix = process.env.API_PREFIX_URL_PREFIX+"/sysUser";

const prefix3 = process.env.API_PREFIX_3; // 真人


//查询账号列表
export const getFindListByPage = (
  params,
  url = "/findListByPage"
) => axios.get(`${prefix}${url_prefix}${url}`, { params: { ...params } });

// 查询部门列表
export const getShowOrgList = (
  params,
  url = "/showOrgList1"
) => axios.get(`${prefix}${url_prefix}${url}`, { params: { ...params } });

// 查询后台列表
export const getShowAppList = (
  params,
  url = "/showAppList"
) => axios.get(`${prefix}${url_prefix}${url}`, { params: { ...params } } );

// 添加账号信息
export const postAddAccount = (
  params,
  url = "/save"
) => axios.post(`${prefix}${url_prefix}${url}`, params );

// 验证用户账号是否存在
export const getByUserCode = (
  params,
  url = "/getByUserCode"
) => axios.get(`${prefix}${url_prefix}${url}`, { params: { ...params } });

// 查询账号信息
export const getUserInfo = (
  params,
  url = "/findById"
) => axios.get(`${prefix}${url_prefix}${url}`, { params: { ...params } });

// 修改账号信息
export const postUpdateAccount = (
  params,
  url = "/update"
) => axios.post(`${prefix}${url_prefix}${url}`, params );

// 账号启用/停用
export const postUpdateByEnabled = (
  params,
  url = "/updateByEnabled"
) => axios.post(`${prefix}${url_prefix}${url}`, params );

//查询平台所有角色信息
export const getAllAppRole = (
  params,
  url = "/showAppRole"
) => axios.get(`${prefix}${url_prefix}${url}`, { params: { ...params } });

//查询用户当前平台角色信息
export const getCurrentUserAppRole = (
  params,
  url = "/showUserRole"
) => axios.get(`${prefix}${url_prefix}${url}`, { params: { ...params } });

//账号绑定角色
export const postUpdateUserRole = (
  params,
  url = "/updateUserRole"
) => axios.post(`${prefix}${url_prefix}${url}`, params );

// 真人-交易&账变记录查询
export const post_zr_getTransferList = (params, url = "/order/zr/getTransferList") =>
  axios.post(`${prefix3}${url}`, params);

// 彩票-交易&账变记录查询
export const post_cp_getTransferList = (params, url = "/order/cp/getTransferList") =>
  axios.post(`${prefix3}${url}`, params);

//根据用户Id查看限额信息
export const post_order_user_queryUserBetLimitDetail = (
  params,
  url = "/admin/userReport/queryUserBetLimitDetail"
  ) => axios.get(`${prefix}${url}`, { params: { ...params } });


  
//获取特殊管控-

import * as rcs_user_delay_setting from "src/api/internal/module/account/module/rcs_user_delay_setting.js"
export {rcs_user_delay_setting}