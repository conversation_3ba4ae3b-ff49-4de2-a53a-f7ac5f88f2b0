<!--
 * @FilePath: /src/pages/internal/merchant/system_level_switch/index.vue
 * @Description: 商户中心-系统级别开关
-->
<template>
  <div class="full-height full-width">
    <div class="pl10x pt10x pb10x" id="top1">
      <q-breadcrumbs separator="/" active-color="whiddte" class="panda-text-2">
        <q-breadcrumbs-el :label="$t('internal.label.label3')" />
        <q-breadcrumbs-el :label="$t('internal.system_level_switch.label1')" class="panda-text-1" />
      </q-breadcrumbs>
    </div>
    <div class="line-height-30px items-center text-panda-text-dark bg-panda-bg-6 ml10x mr10x border-radius-4px shadow-3">
      <div class="ml30x pt20x pb30x">
        <!-- 预约投注 -->
        <!-- <span>{{$t("internal.system_level_switch.label2")}} </span>
        <div>
          <a-radio-group name="radioGroup" v-model="reservedBettingSwitch" @change="handle_state('reservedBettingSwitch')">
            <a-radio :value="'1'">{{$t("internal.system_level_switch.label3")}} </a-radio>
            <a-radio :value="'0'"> {{$t("internal.system_level_switch.label4")}} </a-radio>
          </a-radio-group>
        </div> -->
        <!-- 视频流量管控 -->
        <span> {{$t("internal.system_level_switch.label5")}} </span>
        <div>
          <a-radio-group name="radioGroup" v-model="videoControlSwitch" @change="handle_state('videoControlSwitch')">
            <!-- 0 是 关 1是 开 -->
            <a-radio :value="'1'">{{$t("internal.system_level_switch.label3")}} </a-radio>
            <a-radio :value="'0'"> {{$t("internal.system_level_switch.label4")}} </a-radio>
          </a-radio-group>
        </div>
        <!-- 聊天室 -->
        <span> {{$t("internal.system_level_switch.label6")}}</span>
        <div class="position-relative">
          <a-radio-group name="radioGroup" v-model="chatRoomSwitch" @change="handle_state('chatRoomSwitch')">
            <a-radio :value="'1'" v-if="chatRoomSwitch==1">{{$t("internal.system_level_switch.label3")}} </a-radio>
            <a-radio :value="'0'" v-if="chatRoomSwitch==0"> {{$t("internal.system_level_switch.label4")}} </a-radio>
          </a-radio-group>
          <q-icon style="font-size: 16px; margin-top: 8px" @click.native="handleSetVsgkDefault()" class="panda-icon panda-icon-bian-ji panda-icon-hover cursor-pointer handle-table-icon"></q-icon>
        </div>
        <!-- 精彩回放 -->
        <span> {{ $t("internal.system_level_switch.label17") }}</span>
        <div>
          <a-radio-group name="radioGroup" v-model="merchantEventSwitch" @change="handle_state('merchantEventSwitch')">
            <!-- 0 是 关 1是 开 -->
            <a-radio :value="'1'">{{$t("internal.system_level_switch.label3")}} </a-radio>
            <a-radio :value="'0'"> {{$t("internal.system_level_switch.label4")}} </a-radio>
          </a-radio-group>
        </div>
        <!-- PC列表附加玩法 -->
        <span>{{ $t("internal.table_title.yh8") }}</span>
        <div>
          <a-radio-group name="radioGroup" v-model="pcps_switch" @change="handle_state('pcps')">
            <!-- 0 是 关 1是 开 -->
            <a-radio :value="'1'">{{$t("internal.system_level_switch.label3")}} </a-radio>
            <a-radio :value="'0'"> {{$t("internal.system_level_switch.label4")}} </a-radio>
          </a-radio-group>
        </div>
        <!-- 列表盘口数 -->
        <span>{{ $t("internal.table_title.yh_listHandicap") }}</span>
        <div>
          <a-radio-group name="radioGroup" v-model="pcms_switch" @change="handle_state('pcms')">
            <!-- 0 是 关 1是 开 -->
            <a-radio :value="'1'">{{$t("internal.system_level_switch.label3")}} </a-radio>
            <a-radio :value="'0'"> {{$t("internal.system_level_switch.label4")}} </a-radio>
          </a-radio-group>
        </div>
        <!-- 简译/繁译切换开关 -->
        <span>{{ $t("internal.table_title.yh11") }}</span>
        <div>
          <a-radio-group name="radioGroup" v-model="languageSystemSwitch" @change="handle_state('languageSystemSwitch')">
            <!-- 0 是 关 1是 开 -->
            <a-radio :value="'1'">{{$t("internal.system_level_switch.label3")}} </a-radio>
            <a-radio :value="'0'"> {{$t("internal.system_level_switch.label4")}} </a-radio>
          </a-radio-group>
        </div>

        <!-- h5新旧版本切换开关 -->
        <span>{{ $t("internal.table_title.yh12") }}</span>
        <div>
          <a-radio-group name="radioGroup" v-model="h5VerSystemSwitch" @change="handle_state('h5VerSystemSwitch')">
            <!-- 0 是 关 1是 开 -->
            <a-radio :value="'1'">{{$t("internal.system_level_switch.label3")}} </a-radio>
            <a-radio :value="'0'"> {{$t("internal.system_level_switch.label4")}} </a-radio>
          </a-radio-group>
        </div>

        <!-- pc新旧版本切换开关 -->
        <span>{{ $t("internal.table_title.yh13") }}</span>
        <div>
          <a-radio-group name="radioGroup" v-model="pcVerSystemSwitch" @change="handle_state('pcVerSystemSwitch')">
            <!-- 0 是 关 1是 开 -->
            <a-radio :value="'1'">{{$t("internal.system_level_switch.label3")}} </a-radio>
            <a-radio :value="'0'"> {{$t("internal.system_level_switch.label4")}} </a-radio>
          </a-radio-group>
        </div>

        <!-- 部分提前结算开关 -->
        <span>{{ $t("internal.table_title.yh65") }}</span>
        <div>
          <a-radio-group name="radioGroup" v-model="partSettleSwitch" @change="handle_state('partSettleSwitch')">
            <!-- 0 是 关 1是 开 -->
            <a-radio :value="'1'">{{$t("internal.system_level_switch.label3")}} </a-radio>
            <a-radio :value="'0'"> {{$t("internal.system_level_switch.label4")}} </a-radio>
          </a-radio-group>
        </div>

        <!-- 预约提前结算开关 -->
        <span>{{ $t("internal.table_title.yh66") }}</span>
        <div>
          <a-radio-group name="radioGroup" v-model="bookedSettleSwitch" @change="handle_state('bookedSettleSwitch')">
            <!-- 0 是 关 1是 开 -->
            <a-radio :value="'1'">{{$t("internal.system_level_switch.label3")}} </a-radio>
            <a-radio :value="'0'"> {{$t("internal.system_level_switch.label4")}} </a-radio>
          </a-radio-group>
        </div>

        <!-- 串关提前结算开关 -->
        <span>{{ $t("internal.system_level_switch.label32") }}</span>
        <div>
          <a-radio-group name="radioGroup" v-model="seriesSettleSwBasketball" @change="handle_state('seSw')">
            <!-- 0 是 关 1是 开 -->
            <a-radio :value="'1'">{{$t("internal.system_level_switch.label3")}} </a-radio>
            <a-radio :value="'0'"> {{$t("internal.system_level_switch.label4")}} </a-radio>
          </a-radio-group>
        </div>        
        <!-- 体育规则自定义 -->
        <span>{{  $t("internal.ruleCustomization.label1")}}</span>
        <div>
          <a-radio-group name="radioGroup" v-model="tyRuleCustomSwitch"  @change="handle_state('tyRuleCustomSwitch')">
              <!-- 0 是 关 1是 开 -->
            <a-radio :value="'1'">{{$t("internal.system_level_switch.label3")}} </a-radio>
            <a-radio :value="'0'"> {{$t("internal.system_level_switch.label4")}} </a-radio>
          </a-radio-group>
        </div>
        <!-- 春节装饰开关 -->
        <span>{{  $t("internal.system_level_switch.label36")}}</span>
        <div>
          <a-radio-group name="radioGroup" v-model="springAdornSwitch"  @change="handle_state('springAdornSwitch')">
              <!-- 0 是 关 1是 开 -->
            <a-radio :value="'1'">{{$t("internal.system_level_switch.label3")}} </a-radio>
            <a-radio :value="'0'"> {{$t("internal.system_level_switch.label4")}} </a-radio>
          </a-radio-group>
        </div>
        <!-- 合买开关 -->
        <span>{{  $t("internal.system_level_switch.label38")}}</span>
        <div>
          <a-radio-group name="radioGroup" v-model="purchaseSwitch"  @change="handle_state('purchaseSwitch')">
              <!-- 0 是 关 1是 开 -->
            <a-radio :value="'1'">{{$t("internal.system_level_switch.label3")}} </a-radio>
            <a-radio :value="'0'"> {{$t("internal.system_level_switch.label4")}} </a-radio>
          </a-radio-group>
        </div>
        <!-- vip系统开关 -->
        <span>{{  $t("internal.system_level_switch.label39")}}</span>
        <div>
          <a-radio-group name="radioGroup" v-model="vipFunctionSwitch"  @change="handle_state('vipFunctionSwitch')">
              <!-- 0 是 关 1是 开 -->
            <a-radio :value="'1'">{{$t("internal.system_level_switch.label3")}} </a-radio>
            <a-radio :value="'0'"> {{$t("internal.system_level_switch.label4")}} </a-radio>
          </a-radio-group>
        </div>        

      </div>
    </div>
    <a-modal :visible="visible" :title="$t('internal.system_level_switch.label1')" @ok="handleOk" @cancel="handleCancel">
      <p v-html="content"></p>
    </a-modal>
    <q-dialog v-model="show_vsgk_default" persistent transition-show="scale" transition-hide="scale">
      <dialog-chatroom @handleCloseLanDefault="handleCloseLanDefault">
      </dialog-chatroom>
    </q-dialog>
  </div>
</template>

<script>
import { api_merchant } from "src/api/index.js";
import dialogChatroom from "./dialog_chatroom.vue";//聊天设置弹窗
export default {
  components: { dialogChatroom },
  data() {
    return {
      // reservedBettingSwitch: null, // 预约投注
      videoControlSwitch: null, // 视频流量管控
      chatRoomSwitch: null, // 聊天室
      allDataList: [],
      currentClickItem: {},
      visible: false,
      content: '',
      show_vsgk_default: false,
      merchantEventSwitch: null, //精彩回放
      pcms_switch: null, // PC附加盘展示
      pcps_switch: null, // PC列表附加玩法
      languageSystemSwitch: null, // 简译/繁译切换开关
      h5VerSystemSwitch: true, //h5新旧版本切换开关
      pcVerSystemSwitch: true, //pc新旧版本切换开关
      partSettleSwitch: true, //部分提前结算开关
      bookedSettleSwitch: true, //预约提前结算开关
      seriesSettleSwBasketball: true, //串关提前结算开关
      tyRuleCustomSwitch: true, //规则定制开关
      springAdornSwitch:true,//春节装饰开关
      purchaseSwitch:true,//合买开关
      vipFunctionSwitch:true,//vip系统开关
    };
  },
  created() {
    this.init()
  },
  methods: {
    init_detail() {
      const params = {
        merchantCode: this.detailObj.merchantCode,
      };
      api_merchant.post_getMerchantPcAdditionPlaySwitch(params).then((res) => {
        const code = this.$lodash.get(res, "data.code");
        const msg = this.$lodash.get(res, "data.msg");
        const data = this.$lodash.get(res, "data.data");
        if (code == "0000000") {
          this.pcam_switch = data.pcam == 1 ? true : false;
          this.pcap_switch = data.pcap == 1 ? true : false;
          this.languageSwitch = data.languageSwitch == 1 ? true : false;
          this.h5VerSystemSwitch = data.h5VerSystemSwitch == 1 ? true : false;
          this.pcVerSystemSwitch = data.pcVerSystemSwitch == 1 ? true : false;
          this.partSettleSwitch = data.partSettleSwitch == 1 ? true : false;
          this.bookedSettleSwitch = data.bookedSettleSwitch == 1 ? true : false;
          this.tyRuleCustomSwitch =  data.tyRuleCustomSwitch == 1 ? true : false;
          this.springAdornSwitch =  data.springAdornSwitch == 1 ? true : false;
          this.purchaseSwitch =  data.purchaseSwitch == 1 ? true : false;
          this.vipFunctionSwitch =  data.vipFunctionSwitch == 1 ? true : false;

          
          
          this.seriesSettleSwBasketball = data.seSw == 1 ? true : false;
        } else {
          msg && this.$message.error(msg);
        }
      });
    },
    //获取商户当前视频控制的状态
    init() {
      let params = {}
      api_merchant.get_Merchant_querySystemSwitch(params).then((res) => {
        let code = this.$lodash.get(res, "data.code");
        let msg = this.$lodash.get(res, "data.msg");
        let data = this.$lodash.get(res, "data.data");
        this.allDataList = data

        if (code == '0000000') {
          // this.reservedBettingSwitch = this.$lodash.find(data, { configKey: 'reservedBettingSwitch' }).configValue;
          this.videoControlSwitch = this.$lodash.find(data, { configKey: 'videoControlSwitch' }).configValue;
          this.chatRoomSwitch = this.$lodash.find(data, { configKey: 'chatRoomSwitch' }).configValue;
          this.merchantEventSwitch = this.$lodash.find(data, { configKey: 'merchantEventSwitch' }).configValue;
          this.pcms_switch = this.$lodash.find(data, { configKey: 'pcms' })?.configValue;
          this.pcps_switch = this.$lodash.find(data, { configKey: 'pcps' })?.configValue;
          this.languageSystemSwitch = this.$lodash.find(data, { configKey: 'languageSystemSwitch' }).configValue;
          this.h5VerSystemSwitch = this.$lodash.find(data, { configKey: 'h5VerSystemSwitch' }).configValue;
          this.pcVerSystemSwitch = this.$lodash.find(data, { configKey: 'pcVerSystemSwitch' }).configValue;
          this.partSettleSwitch = this.$lodash.find(data, { configKey: 'partSettleSwitch' })?.configValue;
          this.bookedSettleSwitch = this.$lodash.find(data, { configKey: 'bookedSettleSwitch' }).configValue;
          this.seriesSettleSwBasketball = this.$lodash.find(data, { configKey: 'seSw' }).configValue;
          this.tyRuleCustomSwitch = this.$lodash.find(data,{configKey:'tyRuleCustomSwitch'})?.configValue;
          this.springAdornSwitch  = this.$lodash.find(data,{configKey:'springAdornSwitch'})?.configValue
          this.purchaseSwitch  = this.$lodash.find(data,{configKey:'purchaseSwitch'})?.configValue
          this.vipFunctionSwitch  = this.$lodash.find(data,{configKey:'vipFunctionSwitch'})?.configValue
          
        } else {
          console.error(msg)
        }
      }).catch(err => {
        console.error(err)
      })
    },
    // 商户操作开关状态
    handle_state(str) {
      this.visible = true
      // 当前点击项
      this.currentClickItem = this.$lodash.find(this.allDataList, { configKey: str })
      // label5: 视频流量管控 label17: 精彩回放 yh8: PC列表附加玩法 yh9: PC附加盘展示 label25: 简译/繁译切换 label26: h5新旧版本切换 label27: pc新旧版本切换 label28: 部分提前结算 label29: 预约提前结算 label32: 串关提前结算
      let content_map = {
        // 'reservedBettingSwitch': this.$t("internal.system_level_switch.label7"),
        'videoControlSwitch': this.$t("internal.system_level_switch.label5"),
        'merchantEventSwitch': this.$t("internal.system_level_switch.label17"),
        'pcps': this.$t("internal.table_title.yh8"),
        'pcms': this.$t("internal.table_title.yh_listHandicap"),
        'languageSystemSwitch': this.$t("internal.system_level_switch.label25"),
        'h5VerSystemSwitch': this.$t("internal.system_level_switch.label26"),
        'pcVerSystemSwitch': this.$t("internal.system_level_switch.label27"),
        'partSettleSwitch': this.$t("internal.system_level_switch.label28"),
        'bookedSettleSwitch': this.$t("internal.system_level_switch.label29"),
        'seSw': this.$t("internal.system_level_switch.label32"),
        'tyRuleCustomSwitch': this.$t("internal.system_level_switch.tiyuguize"),
        'springAdornSwitch': this.$t("internal.system_level_switch.label35"),
        'purchaseSwitch': this.$t("internal.system_level_switch.label37"),
        "vipFunctionSwitch": this.$t("internal.system_level_switch.label39")
        
        
      }
      // label33: 确认将 label6: 聊天室 label34: 变更为 label13: 开放 label14: 不开放
      this.content = `${this.$t("internal.system_level_switch.label33")}${(content_map[str] || this.$t("internal.system_level_switch.label6"))}${this.$t("internal.system_level_switch.label34")}<span class="panda-text-red">${this.currentClickItem.configValue == '1' ? this.$t("internal.system_level_switch.label13") : this.$t("internal.system_level_switch.label14")}？</span>`;
    },
    handleOk() {
      let params = {
        id: this.currentClickItem.id,
        configValue: this.currentClickItem.configValue == '0' ? '1' : '0',
        configKey: this.currentClickItem.configKey 
      }
      api_merchant.get_Merchant_updateSystemSwitch(params).then((res) => {
        let code = this.$lodash.get(res, "data.code");
        let msg = this.$lodash.get(res, "data.msg");
        if (code == '0000000') {
          this.$message.success(msg)
          this.init()
        } else {
          this.$message.error(msg)
          console.error(msg)
        }
        this.visible = false
      }).catch(err => {
        this.$message.error(err)
        this.visible = false
      })
    },
    handleCancel() {
      // this.reservedBettingSwitch = this.$lodash.find(this.allDataList, { configKey: 'reservedBettingSwitch' }).configValue
      this.videoControlSwitch = this.$lodash.find(this.allDataList, { configKey: 'videoControlSwitch' }).configValue
      this.chatRoomSwitch = this.$lodash.find(this.allDataList, { configKey: 'chatRoomSwitch' }).configValue
      this.merchantEventSwitch = this.$lodash.find(this.allDataList, { configKey: 'merchantEventSwitch' }).configValue
      this.pcms_switch = this.$lodash.find(this.allDataList, { configKey: 'pcms' }).configValue
      this.pcps_switch = this.$lodash.find(this.allDataList, { configKey: 'pcps' }).configValue
      this.languageSystemSwitch = this.$lodash.find(this.allDataList, { configKey: 'languageSystemSwitch' }).configValue
      this.h5VerSystemSwitch = this.$lodash.find(this.allDataList, { configKey: 'h5VerSystemSwitch' }).configValue
      this.pcVerSystemSwitch = this.$lodash.find(this.allDataList, { configKey: 'pcVerSystemSwitch' }).configValue

      this.partSettleSwitch = this.$lodash.find(this.allDataList, { configKey: 'partSettleSwitch' }).configValue
      this.bookedSettleSwitch = this.$lodash.find(this.allDataList, { configKey: 'bookedSettleSwitch' }).configValue
        this.tyRuleCustomSwitch = this.$lodash.find(this.allDataList,{configKey:'tyRuleCustomSwitch'})?.configValue
        this.springAdornSwitch = this.$lodash.find(this.allDataList,{configKey:'springAdornSwitch'})?.configValue
        this.purchaseSwitch = this.$lodash.find(this.allDataList,{configKey:'purchaseSwitch'})?.configValue
        this.vipFunctionSwitch = this.$lodash.find(this.allDataList,{configKey:'vipFunctionSwitch'})?.configValue

        
      this.seriesSettleSwBasketball = this.$lodash.find(this.allDataList, { configKey: 'seSw' }).configValue

      this.visible = false
    },
    handleSetVsgkDefault() {
      this.show_vsgk_default = true;
    },
    handleCloseLanDefault() {
      this.show_vsgk_default = false;
      this.init();
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
