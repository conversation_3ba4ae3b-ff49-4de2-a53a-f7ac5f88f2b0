/*
 * @Desc: 
 * @Date: 2020-02-25 16:10:38
 * @Author:Nice
 * @FilePath: /src/api/internal/module/user/index.js
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_3;
let prefix1 = process.env.API_PREFIX_3;
let prefix2 = process.env.API_PREFIX_4;
let prefix27 = process.env.API_PREFIX_27;  // 真人
let prefix28 = process.env.API_PREFIX_28;   // 彩票
//用户投注统计
export const query_userbetlist_byTime = (
  params,
  url = "/order/user/queryUserBetListByTime"
  ) => axios.post(`${prefix1}${url}`, params );

//用户注单列表
export const query_userorderlist = (
  params,
  url = "/order/user/queryUserOrderList"
  ) => axios.post(`${prefix}${url}`, params );


  //注单查询--注单列表
export const post_order_user_queryAppointmentList = (
  params,
  url = "/order/user/queryAppointmentList"
  ) => axios.post(`${prefix}${url}`, params );


export const post_queryOrderOverLimitInfos = (
  params,
  url = "/order/user/queryOrderOverLimitInfos"
  ) => axios.post(`${prefix}${url}`, params );

 
//用户信息  10711
export const get_order_user_detail = (params, url = "/order/user/detail") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });

//注单中心统计接口
export const post_queryStatistics = (
  params,
  url = "/order/user/getStatistics"
) => axios.post(`${prefix}${url}`, params);

//查询赛事种类接口  10711
export const get_order_game_getSportList = (params, url = "/order/game/getSportList") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });

//查询赛事种类接口  10711   [商户中心/商户管理]：商户球类开关设置页面 球类列表
export const get_order_game_getSportListByFilter = (params, url = "/order/game/getSportListByFilter") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });
export const get_order_specialLimit_getSportListByFilter_ = (params, url = "/order/specialLimit/getSportListByFilter") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });

  
//用户投注管理查询(port:10711)]
export const post_order_user_queryUserBetList = (
  params,
  url = "/order/user/queryUserBetList"
) => axios.post(`${prefix}${url}`, params);

//根据用户Id查看限额信息
  export const post_order_user_queryUserBetLimitDetail = (
    params,
    url = "/order/user/queryUserBetLimitDetail"
  ) => axios.get(`${prefix}${url}`, { params: { ...params } });
  
//根据用户Id查看用户延时赛种
export const get_order_user_getUserTradeRestrict = (
  params,
  url = "/admin/userReport/getUserTradeRestrict"
  ) => axios.get(`${prefix}${url}`, { params: { ...params } });

// 最近12个月的统计 10716
export const get_order_user_orderMonth = (params, url = "/order/user/orderMonth") =>
  axios.get(`${prefix1}${url}`, { params: { ...params } });

// 盈利率
export const get_order_user_profit = (params, url = "/order/user/profit") =>
  axios.get(`${prefix1}${url}`, { params: { ...params } });

// 某月的统计
export const get_order_user_orderMonth_days = (params, url = "/order/user/orderMonth/days") =>
  axios.get(`${prefix1}${url}`, { params: { ...params } });


// //   联赛等级查询  (联赛开关页面)联赛列表接口           原
export const get_order_user_queryTournamentList = (params, url = "/order/user/queryTournamentList") =>
  axios.get(`${prefix1}${url}`, { params: { ...params } });
 
// //   联赛开关 页面 联赛列表  接口【联赛开关】          现
export const get_order_user_queryFilterTournamentList = (params, url = "/order/user/queryFilterTournamentList") =>
  axios.get(`${prefix1}${url}`, { params: { ...params } });

// //   联赛开关 页面 联赛列表  接口【联赛总控开关】          
export const post_order_all_user_getMerchantSwitch = (params, url = "/manage/switchConfig/getMerchantSwitch") =>
  axios.post(`${prefix2}${url}`, params);

// 商户球种设置-联赛开关设置-联赛数据商下拉框
export const get_order_user_getDataProviderTournamentList = (params, url = "/order/user/getDataProviderTournamentList") =>
  axios.get(`${prefix1}${url}`, { params: { ...params } });



//玩法列表
export const post_order_user_queryHotPlayName = (params, url = "/order/user/queryHmOrderPlayName") =>
  axios.post(`${prefix}${url}`, params);

//商户名称
export const post_manage_merchant_queryMerchantList = (params, url = "/manage/merchant/queryMerchantList") =>
  axios.post(`${prefix2}${url}`, params);

//商户名称-树形结构
export const post_manage_merchant_queryMerchantListTree = (params, url = "/manage/merchant/queryMerchantListTree") =>
  axios.post(`${prefix2}${url}`, params);

  //商户名称-树形结构-无name
  // export const post_manage_merchant_getMerchantCodeListTree = (params, url = "/manage/merchant/getMerchantCodeListTree") =>
     export const post_manage_merchant_getMerchantCodeListTree = (params, url = "/manage/merchant/getMerchantNameListTree") =>
    axios.get(`${prefix2}${url}`, { params: { ...params } });

      // // export const post_manage_merchant_getMerchantCodeListTree = (params, url = "/manage/merchant/getMerchantCodeListTree") =>
      // export const post_manage_merchant_getMerchantCodeListTree = (params, url = "/manage/merchant/getMerchantNameListTree") =>
      // axios.get(`${prefix2}${url}`, { params: { ...params } });


//取消提前结算 子列表请求 ronney
export const post_queryPreSettleOrder = (params, url = "/order/user/queryPreSettleOrder?") =>
  axios.post(`${prefix}${url}`, params);
  

// 投注用户管理——批量导入开启发起用户 用户
export const post_import_userPurchaseRemark = (params, url = "/order/user/batchUpdateUserSwitch") =>
  axios.post(`${prefix}${url}`, params);


  //导入线路vip
export const post_order_user_importVipUser = (params, url = "/order/user/uploadVipUser") =>
axios.post(`${prefix}${url}`, params);

//修改线路vip
export const get_update_IsVipDomain = (params, url = "/order/user/updateIsVipDomain") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });

// 投注用户管理——根据用户ID查询用户名
export const post_find_userInfo = (params, url = "/order/user/findUserInfo") =>
  axios.post(`${prefix}${url}`, params);

// 投注用户管理——批量启&禁用 用户
export const post_import_ToDisabled = (params, url = "/order/user/importToDisabled") =>
  axios.post(`${prefix}${url}`, params);

// 投注用户管理——启&禁用 用户
export const post_update_Disabled = (params, url = "/order/user/updateDisabled") =>
  axios.post(`${prefix}${url}`, params);
// 投注用户管理——额度转入转出
export const post_set_user_personalise_new = (params, url = "/order/user/setUserPersonaliseNew") =>
  axios.post(`${prefix}${url}`, params);

// 商户管理——根据商户ID查询商户名称
export const post_find_MerchantInfo = (
  params,
  url = "/manage/merchant/findMerchantInfo"
) => axios.post(`${prefix2}${url}`, params);

// 商户管理——批量启&禁用 商户
export const post_import_MerchantStatus = (
  params,
  url = "/manage/merchant/importMerchantStatus"
) => axios.post(`${prefix2}${url}`, params);

// 获取白名单列表: 2245
// GET: /order/allowlist/user/list 
//  参数：用户ID/name  String userInfo （精确筛选 ）
// 	选则商户名称  List<String> merchantCodeList（code数组）
// 	来源 Integer allowListSource （0或不传全部 其他的看枚举）
export const get_order_allowlist_usert_list = (params, url = "/order/allowlist/user/list") =>
axios.get(`${prefix}${url}`, { params: { ...params } });


// 删除单条  踢出单个用户 2245
// POST: /order/allowlist/user/del 
// 	参数：userId：用户id
// 	返回：data：1  -- 踢出数量
export const post_order_allowlist_user_del = (params, url = "/order/allowlist/user/del") =>
axios.post(`${prefix}${url}`, params);


// 全部删除 2245
// POST: /order/allowlist/user/delAll 
// 	参数 ： 无
// 	返回：data：100  -- 踢出数量
export const post_order_allowlist_user_delAll  = (
  params,
  url = "/order/allowlist/user/delAll"
) => axios.post(`${prefix}${url}`, params,{type:1 });




// 导入白名单用户  2245
// POST: /order/allowlist/user/import 
// 	参数示例：
// 	{
//     "userIdList":["1","2","3"],
//     "allowListSource":4
// 	}
// 	返回：data：1  -- 导入数量
export const post_order_allowlist_user_import = (params, url = "/order/allowlist/user/import") =>
axios.post(`${prefix}${url}`, params);


// 导出白名单用户
// POST: /order/allowlist/user/export 
// 参数同获取白名单列表 如下：
//  参数：用户ID/name  String userInfo （精确筛选 ）
// 	选则商户名称  List<String> merchantCodeList（code数组）
// 	来源 Integer allowListSource （0或不传全部 其他的看枚举）
export const post_order_allowlist_user_export  = (params, url = "/order/allowlist/user/export") =>
axios.post(`${prefix}${url}`, params);


//派彩可用额度查询
export const get_listAlbPayoutLimit = (params, url = "/order/merchant/listAlbPayoutLimit") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });

// 电竞会员限额查询
export const post_djListOrderLimit = (params, url = "/order/merchant/djListOrderLimit") =>
  axios.post(`${prefix}${url}`, params);





    //注单查询 --注单列表   7 天 和 35天 
    export const post_order_user_queryTicketList = (
    params,
    url = "/order/user/queryTicketList"
    ) => axios.post(`${prefix}${url}`, params );


    //注单查询(ES)    7 天 和 35天 
    export const post_order_user_queryTicketListES = (
    params,
    url = "/order/user/queryTicketListES"
    ) => axios.post(`${prefix}${url}`, params );

  //注单查询(ES实时流)    7 天 和 35天 
  export const post_order_user_queryTicketListES_timely = (
    params,
    url = "/v1/order/stream/queryTicketList"
    ) => axios.post(`${prefix}${url}`, params );

  //注单查询(ES)  查询统计接口 
  export const post_getStatisticsES = (
    params,
    url = "/order/user/getStatisticsES"
  ) => axios.post(`${prefix}${url}`, params);


// 数据中心-注单查询-真人注单查询 
export const post_zr_getOrderList = (params, url = "/manage/order/zr/queryOrder") =>
  axios.post(`${prefix2}${url}`, params);

// 数据中心-注单查询-彩票注单查询 
export const post_cp_getOrderList = (params, url = "/manage/order/cp/queryOrder") =>
  axios.post(`${prefix2}${url}`, params);

// 数据中心-注单查询-真人注单-统计接口 
export const post_zr_getStatistics = (params, url = "/manage/order/zr/getStatistics") =>
  axios.post(`${prefix2}${url}`, params);

// 数据中心-注单查询-彩票注单-统计接口
export const post_cp_getStatistics = (params, url = "/manage/order/cp/getStatistics") =>
  axios.post(`${prefix2}${url}`, params);

// 数据中心-注单查询-标签
// export const post_userProfileTags_listByType = (
//   params,
//   url = "/manage/BettingOrder/userProfileTags/listByType"
// ) => axios.post(`${prefix2}${url}`, params);

// 合买进度查询
export const querySyndicateOrderList = (
  params,
  url = "/v1/syndicate/querySyndicateOrderList"
) => axios.post(`${prefix}${url}`, params);

  //合买进度查询  查询统计接口 
  export const post_querySyndicateStatistics = (
    params,
    url = "/v1/syndicate/querySyndicateStatistics"
  ) => axios.post(`${prefix}${url}`, params);
  
  //投注用户管理-战绩
  export const post_querySyndicatedRecordList = (
    params,
    url = "/order/userPurchase/querySyndicatedRecordList"
  ) => axios.post(`${prefix}${url}`, params);
  
  //投注用户管理-战绩 查询统计接口 
  export const post_getRecordStatistics = (
    params,
    url = "/order/userPurchase/getRecordStatistics"
  ) => axios.post(`${prefix}${url}`, params);
  
// 百家赔页面列表/查询接口
// 数据中心-注单查询-百家赔-二级页面
export const post_riskOrder_getRiskOrderList = (
  params,
  url = "/manage/BettingOrder/riskOrder/getRiskOrderList"
) => axios.post(`${prefix2}${url}`, params);

// 数据中心-百家赔注单-列表/查询接口
export const post_oddsComparison_list = (
  params,
  url = "/manage/BettingOrder/oddsComparison/list"
) => axios.post(`${prefix2}${url}`, params);

// 百家赔页面导出接口
export const post_oddsComparison_export = (
  params,
  url = "/manage/BettingOrder/oddsComparison/export"
) => axios.post(`${prefix2}${url}`, params);