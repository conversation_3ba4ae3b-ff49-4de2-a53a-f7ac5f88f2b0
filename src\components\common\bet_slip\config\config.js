/*
 * @FilePath: /src/components/common/bet_slip/config/config.js
 * @Description: 数据中心.注单查询(主列表配置)
 */
import { i18n } from 'src/boot/i18n';
let title_config = i18n.t('internal.data.betslip.config')
// 对外
let all_cloums = {
  '序号': {
    title: title_config[0],//序号
    width: 70,
    dataIndex: "_index",
    key: "_index",
    align: "center",
    scopedSlots: {
      customRender: "_index",
    },
  },
  '用户名': {
    title: title_config[1], //用户名
    width: 250,
    dataIndex: "userId",
    key: "userId",
    scopedSlots: {
      customRender: "userId",
    },
    align: "left",
  },
  "用户名-预约": {
    title: title_config[1], //用户名
    width: 250,
    dataIndex: "userId2",
    key: "userId2",
    scopedSlots: {
      customRender: "userId2",
    },
    align: "left",
  },
  '赛事信息': {
    title: title_config[2],//赛事信息
    dataIndex: "betNoInfo",
    key: "betNoInfo",
    width: 250,
    scopedSlots: {
      customRender: "betNoInfo",
    },
    align: "left",
  },
  "赛事信息-预约": {
    title: title_config[2],//赛事信息
    dataIndex: "betNoInfo2",
    key: "betNoInfo2",
    width: 250,
    scopedSlots: {
      customRender: "betNoInfo2",
    },
    align: "left",
  },
  '注单详情': {
    title: title_config[3], //注单详情
    dataIndex: "betNoDetail",
    key: "betNoDetail",
    width: 300,
    scopedSlots: {
      customRender: "betNoDetail",
    },
    align: "left",
  },
  "注单详情-预约": {
    title: i18n.t("internal.common.label_724_3"),//注单详情
    dataIndex: "betNoDetail2",
    key: "betNoDetail2",
    width: 300,
    scopedSlots: {
      customRender: "betNoDetail2",
    },
    align: "left",
  },
  '赔率': {
    title: title_config[4], //赔率
    dataIndex: "oddsValue",
    key: "oddsValue",
    width: 145,
    scopedSlots: {
      customRender: "oddsValue",
    },
    align: "center",
  },
  赔率合买: {
    dataIndex: "oddsValue_hm",
    key: "oddsValue_hm",
    width: 145,
    scopedSlots: {
      customRender: "oddsValue_hm",
    },
    align: "center",
    slots: {
      title: "oddsValue_hm_title",
    },
  },
  注单进度: {
    title: title_config.text3,// 注单进度
    dataIndex: "bet_progress",
    key: "bet_progress",
    width: 210,
    scopedSlots: {
      customRender: "bet_progress",
    },
    align: "center",
  },
  "赔率-预约": {
    title: title_config[4],//赔率
    dataIndex: "oddsValue2",
    key: "oddsValue2",
    width: 130,
    scopedSlots: {
      customRender: "oddsValue2",
    },
    align: "center",
  },
  "注额(外汇)": {
    title: title_config[5],//注额(外汇)
    dataIndex: "settleAmount",
    key: "settleAmount",
    width: 120,
    scopedSlots: {
      customRender: "settleAmount",
    },
    align: "left",
  },
  "注额(本地)": {
    title: title_config[6],//注额(本地)
    dataIndex: "localBetAmount",
    key: "localBetAmount",
    width: 120,
    scopedSlots: {
      customRender: "localBetAmount",
    },
    align: "left",
  },
  "佣金(外汇)": {
    title: title_config.text6,
    dataIndex: "originalBrokerage",
    key: "originalBrokerage",
    width: 120,
    scopedSlots: {
      customRender: "originalBrokerage",
    },
    align: "left",
  },
  "佣金(本地)": {
    title: title_config.text7,
    dataIndex: "brokerage",
    key: "brokerage",
    width: 120,
    scopedSlots: {
      customRender: "brokerage",
    },
    align: "left",
  },
  "注额(本地)-预约": {
    title: title_config[6],//注额(本地)
    dataIndex: "localBetAmount2",
    key: "localBetAmount2",
    width: 120,
    scopedSlots: {
      customRender: "localBetAmount2",
    },
    align: "left",
  },
  '预约状态': {
    title: i18n.t("internal.common.label_724_4"), // 预约状态
    dataIndex: "preOrderStatusName",
    key: "preOrderStatusName",
    width: 120,
    scopedSlots: {
      customRender: "preOrderStatusName",
    },
    align: "left",
  },
  "输赢(外汇)": {
    title: title_config[7],//输赢(外汇)
    dataIndex: "profitAmount",
    key: "profitAmount",
    width: 140,
    scopedSlots: {
      customRender: "profitAmount",
    },
    align: "left",
  },
  "输赢(本地)": {
    title: title_config[8],//输赢(本地)
    dataIndex: "localProfitAmount",
    key: "localProfitAmount",
    width: 120,
    scopedSlots: {
      customRender: "localProfitAmount",
    },
    align: "left",
  },
  "有效注额(外汇)": {
    title: title_config[22],//有效投注额(外汇)
    dataIndex: "orderValidBetMoney",
    key: "orderValidBetMoney",
    width: 200,
    scopedSlots: {
      customRender: "orderValidBetMoney",
    },
    align: "left",
  },
  "有效注额(本地)": {
    title: title_config[23],//有效注额(本地)
    dataIndex: "localOrderValidBetMoney",
    key: "localOrderValidBetMoney",
    width: 120,
    scopedSlots: {
      customRender: "localOrderValidBetMoney",
    },
    align: "left",
  },
  '合买喜好': {
    title: title_config.text1,// 合买喜好
    dataIndex: "buying_preferences",
    key: "buying_preferences",
    width: 210,
    scopedSlots: {
      customRender: "buying_preferences",
    },
    align: "center",
  },
  '状态': {
    //状态
    dataIndex: "outcome",
    key: "outcome",
    width: 180,
    filters: title_config[9],
    onFilter: (value, record) => record.outcome == value,
    filterMultiple: true,
    align: "center",
    slots: {
      title: "outcomeTitle",
    },
    scopedSlots: {
      customRender: "outcome",
    },
  },
  '设备信息': {
    title: title_config[10],//设备信息
    dataIndex: "deviceType",
    key: "deviceType",
    width: 150,
    scopedSlots: {
      customRender: "deviceType",
    },
    align: "center",
  },
  百家赔: {
    title: title_config[16],// 百家赔
    dataIndex: "riskDescM",
    key: "riskDescM",
    width: 210,
    scopedSlots: {
      customRender: "riskDescM",
    },
    align: "left",
  },
  '注单标签': {
    title: title_config[17],// 注单标签
    dataIndex: "riskType",
    key: "riskType",
    width: 150,
    scopedSlots: {
      customRender: "riskType",
    },
    align: "center",
  },
  '下注时区': {
    dataIndex: "bettingTime",
    key: "bettingTime",
    width: 150,
    scopedSlots: {
      customRender: "bettingTime",
    },
    align: "center",
    slots: {
      title: "bttting_time_icon",
    },
  },
  '全选': {
    //全选 取消注单这一列
    dataIndex: "all_select",
    key: "all_select",
    width: 130,
    scopedSlots: {
      customRender: "all_select",
    },
    slots: {
      title: "all_select_header",
    },
    align: "center",
  },
  全选_freeze: {
    //全选 取消注单这一列
    dataIndex: "all_select_freeze",
    key: "all_select_freeze",
    width: 130,
    scopedSlots: {
      customRender: "all_select_freeze",
    },
    slots: {
      title: "all_select_freeze_header",
    },
    align: "center",
  },
};
function key_to_config(arr) {
  let columns = [];
  arr.map((x) => {
    columns.push(all_cloums[x]);
  });
  return columns;
}
// 数据中心-注单查询(投注查询模式---主列表配置)   对外
const _tablecolumns_config_external = [
  "序号",
  "用户名",
  "赛事信息",
  "注单详情",
  "赔率",
  "注额(外汇)",
  "注额(本地)",
  "输赢(外汇)",
  "输赢(本地)",
  "有效注额(外汇)",
  "有效注额(本地)",
  "状态",
  "设备信息",
  "下注时区",
  "全选",
  "全选_freeze",
];

export const tablecolumns_config_external = key_to_config(
  _tablecolumns_config_external
);

//数据中心.注单查询(投注查询模式—主列表配置)   对内
const _tablecolumns_config_internal = [
  "序号",
  "用户名",
  "赛事信息",
  "注单详情",
  "赔率",
  "注额(外汇)",
  "注额(本地)",
  "输赢(外汇)",
  "输赢(本地)",
  "状态",
  "设备信息",
  "下注时区",
];
export const tablecolumns_config_internal = key_to_config(
  _tablecolumns_config_internal
);
//数据中心.注单查询(预约模式—主列表配置)  对内
const _queryAppointmentList_config = [
  "序号",
  "用户名-预约",
  "赛事信息-预约",
  "注单详情-预约",
  "赔率-预约",
  "注额(外汇)",
  "注额(本地)-预约",
  "预约状态",
];
export const queryAppointmentList_config = key_to_config(
  _queryAppointmentList_config
);

const _bet_slip_order_no_config = [
  "序号",
  "用户名",
  "赛事信息",
  "注单详情",
  "赔率",
  "注额(外汇)",
  "注额(本地)",
  "输赢(外汇)",
  "输赢(本地)",
  "状态",
  "设备信息",
  "百家赔",
  "注单标签",
];

// 数据中心.注单查询-百家赔二级页面
export const bet_slip_order_no_config = key_to_config(_bet_slip_order_no_config);

const _bet_slip_order_no_page_config = [
  "序号",
  "用户名",
  "赛事信息",
  "注单详情",
  "赔率",
  "注额(本地)",
  "输赢(本地)",
  "设备信息",
  "百家赔",
  "注单标签",
];
// 数据中心.注单查询-百家赔页面
export const bet_slip_order_no_page_config = key_to_config(_bet_slip_order_no_page_config);
//数据中心-合买进度查询
const _check_the_progress_of_joint_purchase_config=[
  "序号",
  "用户名",
  "赛事信息",
  "注单详情",
  "赔率",
  "赔率合买",
  "注单进度",
  "注额(外汇)",
  "注额(本地)",
  "输赢(外汇)",
  "输赢(本地)",
  "佣金(外汇)",
  "佣金(本地)",
  "合买喜好",
  "状态",
  "设备信息"
]
// 数据中心.合买进度查询
export const check_the_progress_of_joint_purchase_config=key_to_config(_check_the_progress_of_joint_purchase_config);
/**
 * 标题一级面包屑breadcrumbs
 * 数据中心-注单百家赔注单
 * 数据中心-合买进度注单查询
 * 数据中心-注单查询-百家赔二级页面
 * 投注用户详情 / 百家赔
 */
export const title_map_breadcrumbs_1 = {
  'bet_slip': i18n.t("internal.data.betslip.index.bar.1"),
  'bet_slip_order_no': i18n.t("internal.merchant.bettinguser.user_detail.bar.2"),
  'bet_slip_order_no_page': i18n.t('internal.data.betslip.index.bar.0'),
  'check_the_progress_of_joint_purchase':  i18n.t('internal.data.betslip.index.bar.0'),
  'bet_slip_es': i18n.t('internal.data.betslip.index.bar.0'),
  'bet_slip_es_timely': i18n.t('internal.data.betslip.index.bar.0'),
}

/**
 * 标题二级面包屑breadcrumbs
 * 数据中心-注单百家赔注单
 * 数据中心-合买进度注单查询
 * 数据中心-注单查询-百家赔二级页面
 * 投注用户详情 / 百家赔
 */
export const title_map_breadcrumbs_2 = {
  'bet_slip': i18n.t("internal.data.betslip.config.16"),
  'bet_slip_order_no': i18n.t("internal.data.betslip.config.16"),
  'bet_slip_order_no_page': i18n.t("internal.menujs.baccarat_betting_slip"),
  'check_the_progress_of_joint_purchase': i18n.t("internal.menujs.purchase"),
  'bet_slip_es': i18n.t('internal.data.betslip.index.bar.2'),
  'bet_slip_es_timely': i18n.t('internal.data.betslip.index.bar.3'),
}