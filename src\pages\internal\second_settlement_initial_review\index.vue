
<!--
 * @FilePath:
 * @Description: 财务中心-二次结算初次审核
 -->
 <template>
  <div class="full-width full-height">
    <div class="pl10x pt10x pb10x" id="top1">
      <q-breadcrumbs
        separator="/"
        active-color="whiddte"
        class="panda-text-2"
      >
        <q-breadcrumbs-el
        :label="$t('internal.finance.liquidation.index.bar')[0]"
        />
        <q-breadcrumbs-el
         v-if="page_type==1"
          :label="$t('internal.menujs.second_settlement_initial_review')"
          class="fw_600 panda-text-1"
        />
        <q-breadcrumbs-el
         v-else-if="page_type==2"
          :label="$t('internal.menujs.second_settlement_and_second_review')"
          class="fw_600 panda-text-1"
        />
      </q-breadcrumbs>
    </div>
    <div
      class="bg-panda-bg-6 shadow-3 border-radius-4px mt0x mr10x mb10x ml10x"
    >
      <!-- 搜索条件   数据中心/前端域名查询    -->
      <div
        id="top2"
        style="min-width: 1600px; overflow-x: hidden"
        class="
          row
          line-height-30px
          items-center
          text-panda-text-7
          bg-panda-bg-6
          pb10x
          pt10x
          border-radius-4px
        "
      >
        <!-- 请输入查询域名,支持模糊搜索-->
      <div class="no-left append-handle-btn-input ml20x position-relative" >
      
        <!--本月-->
        <a-button
          class=" q-mx-md"
          :type="month_type==1?'primary':'default'"
          @click="domain_query(1)"
        >
          {{ $t('internal.second_settlement_initial_review.text__1') }}
        </a-button>
        <!--上月-->
        <a-button
          class=""
          :type="month_type==2?'primary':'default'"
          @click="domain_query(2)"
        >
        {{ $t('internal.second_settlement_initial_review.text__2') }}
        </a-button>
      </div>
        <q-space />
        <span v-if="page_type==2" class="text-blue mr20x icon-text-container cursor-pointer" @click="set_the_audit_amount_handle">
         <a-icon type="setting" style="color: #1890ff; font-size: 24px;" />
          {{ $t('internal.second_settlement_initial_review.text__30') }}</span>
      </div>
      <!-- 表格区域 -->
      <a-table
        bordered
        ref="TableInfo"
        class="pl10x pr10x expanded"
        :columns="columns"
        :dataSource="tabledata"
        :scroll="{ x: 1600, y: scrollHeight - 45 }"
        :pagination="false"
        :loading="tabledata_loading"
        size="middle"
        rowKey="_index"
      >   
          
      <!--勾选-->
      <div slot="all_select" slot-scope="text, record, index">
        <div v-if="[0,1,2,5,7].includes(record.approvalStatus)">
            <a-checkbox v-model="record.selected"   @change="handle_cancel_order_input_changed(record)">
            </a-checkbox>
        </div>
        </div>
        <!-- 申请审批日期 -->
        <div slot="applicationTime" slot-scope="text, record" class="tdpadding">
          {{moment(text).format('YYYY/MM/DD')}}
          </div>
          <!-- 结算日期 -->
         <div slot="settleTime" slot-scope="text, record" class="tdpadding">
           {{moment(text).format('YYYY/MM/DD')}}
           </div>
        <!-- 赔偿金额 -->
        <div slot="changeTotalAmount" slot-scope="text, record" class="tdpadding">
          {{addCommas(text)}}
          </div>
        <!-- 状态 -->
        <div slot="approvalStatus" slot-scope="text, record" class="tdpadding">
          <span :class="set_color_by_state(record)">{{$t("internal.finance.state_list")[record.approvalStatus]}}</span>
         
          </div>
          <!-- 操作 -->
          <div slot="action" slot-scope="text, record" class="tdpadding"> 
            <span  class="pl10x text-blue cursor-pointer" @click="go_detail(record)"">
              {{ $t("internal.second_settlement_initial_review.text__15") }}
            </span>
           </div>
          <template slot="footer">
          <!-- 分页器 -->
          <a-pagination
            v-if="tabledata.length > 0"
            :total="pagination.total"
            :current="pagination.current"
            show-size-changer
            show-quick-jumper
            :page-size-options="pagination.pageSizeOptions"
            :page-size="pagination.pageSize"
            :show-total="total => $t('internal.showTotal_text', [pagination.total])"
            @change="onChange"
            @showSizeChange="onShowSizeChange"
          />
          <div class="fs16 position-absolute line-height-24px" style="bottom: 10px; left: 25px;" v-if="tabledata.length>0"> 
          <div>
           <span class="row ml10x" v-if="tabledata.length>0">
          
              <!-- 待处理总额： -->
              <span >
                <span class="title-grey">{{ $t("internal.second_settlement_initial_review.text__12") }}:</span>
                 <span class="text-red">
            {{addCommas(order_totals.totalPayoutAmt)}}
             </span>    
              </span>
              <div v-if="show_handle_rejection_btn">
               <!--提交审核-->
               <a-button
               type="primary"
               class="ml10x"
                 style="height: 30px"
                 @click="show_submit_confirmation_handle()"
               >
               {{page_type==1? $t("internal.second_settlement_initial_review.text__13"): $t("internal.second_settlement_initial_review.text__27") }}
               </a-button>
               <!--审核驳回-->
               <a-button
               type="primary"
               class="ml10x"
               :style="{ backgroundColor: '#f6a04d', borderColor: '#f6a04d' }"
               @click="handle_rejection()"
               >{{ $t("internal.second_settlement_initial_review.text__14") }}
               </a-button>
              </div>
          </span>
        </div>
      </div> 
        </template>
      </a-table>
    </div>
    
    <!-- 驳回弹窗 -->
      <q-dialog v-model="show_rejection_confirm" persistent teansition-show="scale" transition-hide="scale">
      <dialog_rejection
      :cancel_arr="cancel_arr"
      @close_dialog="handle_break"/>
     </q-dialog>
     <!-- 赔付或审批 -->
     <q-dialog v-model="show_submit_confirmation" persistent teansition-show="scale" transition-hide="scale">
       <submit_confirmation :detailObj="submit_confirmation_order_detail" @close_dialog="handle_break"></submit_confirmation>
     </q-dialog>
    </q-dialog>
    <!-- 设置审核金额 -->
    <q-dialog v-model="show_set_the_audit_amount" persistent teansition-show="scale" transition-hide="scale">
      <dialog_set_the_audit_amount  @close_dialog="handle_break"></dialog_set_the_audit_amount>
    </q-dialog>
  </div>
</template>
<script>
import moment from "moment";
import { i18n } from "src/boot/i18n";
import { getFirstDayOfMonth, getLastDayOfMonth ,getFirstDayOfLastMonth,getLastDayOfLastMonth} from "src/pages/internal/second_settlement_initial_review/this_month_or_last_month.js";
import { api_finance  } from "src/api/index.js";
import mixins from "src/mixins/internal/index.js";
import { tablecolumns_config } from "./config/config.js";
import dialog_rejection from "src/pages/internal/second_settlement_initial_review/component/dialog_rejection.vue"
import submit_confirmation from "src/pages/internal/second_settlement_initial_review/component/submit_confirmation.vue"
import dialog_set_the_audit_amount from "src/pages/internal/second_settlement_initial_review/component/dialog_set_the_audit_amount.vue"



export default {
  mixins: [...mixins],
  components: {dialog_rejection,submit_confirmation,dialog_set_the_audit_amount},
  data() {
    return {
      //设置审核金额
      show_set_the_audit_amount:false,
      //提交赔付和审核--详情
      submit_confirmation_order_detail:{},
      //提交赔付和审核
      show_submit_confirmation:false,
      month_type:1,
      columns:tablecolumns_config,
      order_totals:{},//总额数据 
      //搜索条件
      searchForm: {
    // 1：初次待处理，2：二次待处理，3：审核通过 4：已驳回'
        approvalStatus:""
      },
      formdata: {},  //获取表单数据
      tabledata: [], // 表格数据
      tabledata_loading: false, //表格还没有加载过来的时候转圈圈
      cancel_arr: [], //准备去取消的  原始数据集合
 
      state_list:i18n.t("internal.finance.state_list"),
      show_rejection_confirm:false,
    };
  },
  computed: {
    show_handle_rejection_btn(){
     return this.tabledata.find(item=>[0,1,2,5,7].includes(item.approvalStatus))
    },
    page_type(){
      if(this.$route.name == "second_settlement_initial_review"){
        //初次审核
        return 1
      }else if(this.$route.name == "second_settlement_and_second_review"){
        //二次审核
        return 2
      }
    }
  },
  watch:{
    page_type(){
      this.initTableData()
    }
  },
  created() {
    this.tabledata_loading = true;
    this.initTableData();
  },
  methods: {
    moment,
    getFirstDayOfMonth, getLastDayOfMonth ,getFirstDayOfLastMonth,getLastDayOfLastMonth,
    // 添加千位符
    addCommas(num) {
      return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    /*
    * 设置审核金额
    */
    set_the_audit_amount_handle(){
      this.show_set_the_audit_amount=true
    },
    go_detail(record){
      let settleTime=moment(record?.settleTime).format("YYYY-MM-DD") 
      if(this.page_type==1){
      //初次
      this.$router.push({name: 'second_settlement_initial_review_detail', query: { settleTime }})
   
      } else if(this.page_type==2){
        //二次审核
      this.$router.push({name: 'second_settlement_and_second_review_detail', query: { settleTime }})
   
      }
    },
    /*
    * 提交赔付、提交审核
    */
    show_submit_confirmation_handle(){
      this.submit_confirmation_order_detail={
        list:this.cancel_arr,
        type:this.page_type,
      }
      this.show_submit_confirmation=true
    },
    /*
    * 审核驳回
    */
    handle_rejection(){
      this.show_rejection_confirm=true
    },
    handle_break(value){
      this.show_rejection_confirm=false
      this.show_submit_confirmation=false
      this.show_set_the_audit_amount=false
      if(value){
      this.initTableData();
      }
    },
    // 取消
    handle_cancel_order_input_changed(record) {
      //取消
      // cancel_arr:[] ,//准备去取消的  原始数据集合
      let selected = record.selected;
      if (selected) {
        //选中
        this.cancel_arr.push(record);
      } else {
        // 取消选中
        this.cancel_arr=this.cancel_arr.filter(item=>item._index != record._index)
      }
      this.$forceUpdate();
    },
    set_color_by_state(record){
      if([4,5,6,7].includes(record.approvalStatus)){
        return 'text-red'
      }else if([3].includes(record.approvalStatus)){
        return 'text-green'
      }
    },
    //查询
    domain_query(type=1) {
      this.month_type=type
      this.initTableData()
    },

    // 请求列表数据
    initTableData() {
      this.tabledata_loading = true;
      this.cancel_arr=[]
      let params = this.compute_init_tabledata_params();
      params = this.delete_empty_property_with_exclude(params);
      api_finance.settlement_inquiry.getSummaryList(params).then(res => {
          let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          if ( code && code == "0000000" ) {
            let arr = this.$lodash.get(res, "data.data") || [];
            this.tabledata_loading = false;
            let info_data=[]
            if(arr?.pageInfo?.records){
              info_data=arr?.pageInfo?.records
              this.order_totals.totalPayoutAmt=arr?.totalPayoutAmt
            }
            this.tabledata = this.rebuild_tabledata_to_needed(info_data);
            this.pagination.total =
            this.tabledata.length || 0;
          } else {
            this.$message.error(msg, 5);
          }
      });
    },
    
    rebuild_tabledata_to_needed(arr) {
      this.cancel_arr=[]
      if(!arr || !arr.length) {
        return []
      }
      // 就散这一页数据 能取消的所有 注单的总数
      arr.map((item, index) => {
        if([0,1,2,5,7].includes(item.approvalStatus)){
        item.selected = true;
        this.cancel_arr.push(item);
        }
       item._index =
       (this.pagination.current - 1) * this.pagination.pageSize + index + 1;
      });
      return arr;

    },
    // 计算请求参数
    compute_init_tabledata_params() {
      let params= {
        timeType:4,
      }
      if(this.month_type==1){
      //本月
      params.startTime=new Date(this.getFirstDayOfMonth()).getTime()
      params.endTime=new Date(this.getLastDayOfMonth()).getTime() 
      }else{
      //上月
      params.startTime=new Date(this.getFirstDayOfLastMonth()).getTime()
      params.endTime=new Date(this.getLastDayOfLastMonth()).getTime() 
      }
      if(this.page_type==1){
        params.approvalStatusList=[1,4,5]
      }else{
        
        params.approvalStatusList=[2,6,7]
      }
      return params;
    },
    
  }
};
</script>
<style lang="scss" scoped>
.icon-text-container {
  display: flex;
  align-items: center; /* 垂直居中 */
  gap: 8px; /* 图标和文字的间距 */
}
 
</style>
