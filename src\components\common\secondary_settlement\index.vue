<!--
* @Author: nico
 * @FilePath: /src/components/common/secondary_settlement/index.vue
 * @Description: 财务中心 / 二次结算查询
-->
<template>
  <div class="full-width full-height">
    <!--财务中心-二次结算查询-->
    <div class="pl10x pt10x pb10x" id="top1">
      <q-breadcrumbs separator="/" active-color="whiddte" class="panda-text-2">
        <q-breadcrumbs-el
          :label="$t('internal.finance.liquidation.index.bar')[0]"
        />
        <q-breadcrumbs-el
          :label="$t('internal.finance.secondarysettlement.label1')"
          class="fw_600 panda-text-1"
        />
      </q-breadcrumbs>
    </div>
    <div
      style="margin: 0px 10px 10px 10px"
      class="bg-panda-bg-6 shadow-3 border-radius-4px"
    >
      <div>
        <div
          id="top2"
          style="min-width: 1600px; overflow-x: hidden"
          class="row line-height-30px items-center text-panda-text-7 bg-panda-bg-6 pb10x pt10x border-radius-4px"
        >
          <!-- 结算时间 下拉框 -->
          <div class="append-handle-btn-input position-relative ml10">
            <a-select 
            autocomplete
              :default-value="$t('internal.finance.secondarysettlement.label2')"
              style="width: 200px"
              @change="handle_time_Type"
            >
               <a-select-option
               :value="item.value"
                 v-for="(item, index) in timeType_list"
                :key="index"
                >{{ item.label }}</a-select-option
              > 
            </a-select>
            <div class="position-absolute select-left-border-bet"></div>
          </div>
          <!-- 日期选择 -->
          <div class="append-handle-btn-input ml10x w-270">
            <a-range-picker 
            v-if="searchForm.timeType!=1"
            :show-time="true"
            showToday 
            :allowClear="false"
             :value="[
                moment(searchForm.startTime, 'YYYY-MM-DD HH:mm:ss'),
                moment(searchForm.endTime, 'YYYY-MM-DD HH:mm:ss'),
              ]"
              @change="on_change"
             />
             <a-date-picker 
             v-else
             :show-time="true"
             showToday 
             :disabled-date="disabledDate"
             :allowClear="false"
             format="YYYY-MM-DD"
             v-model="searchForm.jssjtime"
             @change="dateonChange"
              />
          </div>
          <!--请输入赛事id-->
           <div class="append-handle-btn-input ml10x w-180 position-relative">
            <a-input
              v-model="searchForm.matchId"
              :placeholder="$t('internal.placeholder.label73')"
              @keydown.enter="handle_search"
              autocomplete="off"
              allowClear
            >
              <my-icon
                slot="suffix"
                type="p-icon-chazhao"
                class="text-panda-text-4 fs12"
              />
            </a-input>
            <div class="position-absolute select-left-border-bet"></div>
          </div>
             <!-- 请输入或选择联赛名称 -->
           <div  class="append-handle-btn-input ml10x row position-relative w-200">
            <a-input
              :placeholder="$t('internal.data.betslip.index.placeholder.fetching')"
              autocomplete="off"
               v-model="searchForm.matchName"
              allowClear
            >
              <my-icon
                slot="suffix"
                type="p-icon-chazhao"
                class="text-panda-text-4 fs12"
              />
            </a-input>
            <div class="position-absolute select-left-border-finance"></div>
            </div>
          <!--请输入用户名/用户ID-->
          <div
            class="append-handle-btn-input ml10x row w-200 position-relative"
          >
            <a-input
              :placeholder="$t('internal.finance.secondarysettlement.label4')"
              autocomplete="off"
               v-model="searchForm.userId"
              allowClear
            >
              <my-icon
                slot="suffix"
                type="p-icon-chazhao"
                class="text-panda-text-4 fs12"
              />
            </a-input>
            <div class="position-absolute select-left-border-finance"></div>
          </div>
          <div  class="append-handle-btn-input ml10x row position-relative w-200">
            <a-input
              :placeholder="$t('internal.finance.secondarysettlement.label5')"
              autocomplete="off"
               v-model="searchForm.merchantName"
              allowClear
            >
             </a-input>
          </div>
          <!--请输入注单号-->
          <div class="append-handle-btn-input ml10x w-170 position-relative">
            <a-input
              v-model="searchForm.orderNo"
              :placeholder="$t('internal.placeholder.label72')"
              @keydown.enter="handle_search"
              autocomplete="off"
              allowClear
            >
              <my-icon
                slot="suffix"
                type="p-icon-chazhao"
                class="text-panda-text-4 fs12"
              />
            </a-input>
            <div class="position-absolute select-left-border-bet"></div>
          </div>

        </div>
      </div>
      <div class="row pb10x" style="min-width: 1600px; overflow-x: hidden">
        
          <!-- 请选择二次结算原因-->
          <div class="append-handle-btn-input position-relative ml10">
            <a-select 
            autocomplete
              style="width: 200px"
              v-model="searchForm.settleType"
            >
            <a-select-option
            :value="''"
             >{{$t('internal.finance.secondary_settlement_list_title') }}</a-select-option
           > 
               <a-select-option
               :value="index"
                 v-for="(item, index) in secondary_settlement_list"
                :key="index"
                >{{ item }}</a-select-option
              > 
            </a-select>
          </div>
          <!-- 是否赔偿-->
          <div class="append-handle-btn-input position-relative ml10">
            <a-select 
            autocomplete
              style="width: 200px"
              v-model="searchForm.compensateType"
            >
            <a-select-option
            :value="''"
             >{{$t('internal.finance.compensation_list_title') }}</a-select-option
           > 
           <a-select-option
           :value="index"
             v-for="(item, index) in compensation_list"
            :key="index"
            >{{ item }}</a-select-option
          > 
            </a-select>
          </div>
        <!-- 负余额 -->
        <div class="append-handle-btn-input ml10x row position-relative">
          <div style="line-height: 30px; margin-right: 10px;">{{$t('internal.finance.secondarysettlement.label7')}}</div>
          <div class="w-110">
            <a-input-number
                  v-model.trim="searchForm.negativeAmountMin"
                  :placeholder="
                    $t('internal.data.betslip.index.placeholder.minBetAmount')
                  "
                  :max="10000000000"
                  autocomplete="off"
                ></a-input-number>
          </div>
         <span class="ml5x mr5x">~</span>
          <div class="w-110">
                 <a-input-number
                  v-model.trim="searchForm.negativeAmountMax"
                  :placeholder="
                    $t('internal.data.betslip.index.placeholder.maxBetAmount')
                  "
                  :max="10000000000"
                  autocomplete="off"
                ></a-input-number>
          </div>
        </div>
        <!-- 当前余额-->
        <div class="append-handle-btn-input ml10x row position-relative">
          <div style="line-height: 30px; margin-right: 10px;">{{$t('internal.finance.secondarysettlement.label8')}}</div>

         <div class="w-110">
            <a-input-number
                  v-model.trim="searchForm.amountMin"
                  :placeholder="
                    $t('internal.data.betslip.index.placeholder.minBetAmount')
                  "
                  :max="10000000000"
                  autocomplete="off"
                ></a-input-number>
          </div>
         <span class="ml5x mr5x">~</span>
          <div class="w-110">
                 <a-input-number
                  v-model.trim="searchForm.amountMax"
                  :placeholder="
                    $t('internal.data.betslip.index.placeholder.maxBetAmount')
                  "
                  :max="10000000000"
                  autocomplete="off"
                ></a-input-number>
          </div>
        </div>
        <!--搜索-->
        <div class="append-handle-btn-input ml10x height-30px line-height-30px">
          <a-button type="primary" style="height: 30px; line-height: 30px" @click="handle_search">{{
            $t("internal.search")
          }}</a-button>
        </div>

        <div class="ml10x">
          <!--导出-->
            <q-btn
              class="panda-btn-primary-dense bg-primary mr5x"
              style="width: 68px; height: 30px"
               @click="handle_export_excel"
            >
              {{ $t("internal.finance.secondarysettlement.label9") }}
            </q-btn>
        </div>
        <div class="append-handle-btn-input pl10x row line-height-30px ">
          <!-- 当前余额大于0 -->
          <a-checkbox v-model="is_amountMin" >  {{ $t("internal.finance.secondarysettlement.label14") }}
          </a-checkbox>
        </div>
      </div>
      
      <div class=" pb10x pr10x overflow-hidden" v-if="searchForm.timeType=='1'">
          
       
        <div class="float-right">
          <div class="row">
        <!-- 是否赔偿-->
        <div class="append-handle-btn-input position-relative ml10">
          <a-select 
          autocomplete
            style="width: 80px"
            v-model="is_compensation"
          >
          <a-select-option
          :value="1"
           > {{ $t("internal.common.label63") }}</a-select-option
         > 
         <a-select-option
         :value="2"
          > {{ $t("internal.common.label64") }}</a-select-option
        > 
          </a-select>
        </div>
      <div class="ml10x">
        <!--批量修改-->
          <a-button
          type="primary"
            style="height: 30px"
            @click="batch_edit()"
          >
          {{ $t("internal.finance.secondarysettlement.label15") }}
          </a-button></div>
      </div>
    </div>
      </div>
      <!-- 表格数据 -->
      <a-table
        :columns="columns"
        :data-source="tabledata"
        :scroll="{ x: 1500,y: computed_scroll_y-30}"
        size="middle"
        :loading="tabledata_loading"
        :pagination="false"
        :rowKey="(record) => record.id"
      >
      <!-- 全选 列 （结算时间）是否赔偿-->
      <div v-if="searchForm.timeType=='1'" slot="all_select_header">
        <!-- 全选 -->
        <a-checkbox v-model="cancel_all_selected" @change="handle_cancel_all_selected_change()">
          {{ $t("external.label.label45") }}
        </a-checkbox>
      </div>
      
      <!-- 全选 列 （结算时间）是否赔偿-->
      <div slot="all_select" slot-scope="text, record, index">
        <div v-if="record.rejectTimes<3">
          <!-- 审核驳回次数小于3才能修改赔偿 -->
            <a-checkbox  v-model="record.selected" @change="handle_cancel_order_input_changed(record)">
            </a-checkbox>
        </div>
        </div>
        <!--用户名-->
        <div slot="username" slot-scope="text, record" class="tdpadding">
          <div>
            <div
              class=" cursor-pointer"
              @click="handleCopy(record.username, $t('internal.finance.secondarysettlement.config.2'))"
            >
              {{ record.username }}
            </div>
          </div>
        </div>
       <!--用户ID-->
        <div slot="uid" slot-scope="text, record" class="tdpadding">
          <div>
            <div
              class="cursor-pointer"
              @click="handleCopy(record.uid, $t('internal.finance.secondarysettlement.config.1'))"
            >
              {{ record.uid }}
            </div>
          </div>
        </div>
        <!--注单号-->
        <div slot="orderNo" slot-scope="text, record" class="tdpadding">
          <div>
            <div
              class="cursor-pointer"
              @click="handleCopy(record.orderNo, $t('internal.finance.secondarysettlement.config.5'))"
            >
              {{ record.orderNo }}
            </div>
          </div>
        </div>
      <!--赛事ID-->
        <div slot="matchId" slot-scope="text, record" class="tdpadding">
          <div>
            <div
              class="cursor-pointer"
              @click="handleCopy(record.matchId, $t('internal.finance.secondarysettlement.config.8'))"
            >
              {{ record.matchId }}
            </div>
          </div>
        </div>
        <div slot="settleType" slot-scope="text, record" class="tdpadding">
          <!-- 二次结算原因 -->
          <div>
              {{ secondary_settlement_list[record.settleType] }}
          </div>
        </div>
        <div slot="compensateType" slot-scope="text, record" class="tdpadding">
          <!-- 是否赔偿 -->
        <div v-if="searchForm.timeType==1&&record.rejectTimes<3">
          <a-select 
          autocomplete
            style="width: 100px"
            v-model="record.compensateType+''"
            @change="update_compensateType(record,$event)"
          >
         <a-select-option
         :value="index"
           v-for="(item, index) in compensation_list"
          :key="index"
          >{{ item }}</a-select-option
        > 
          </a-select>
      </div>
        <div v-else>
          {{ compensation_list[record.compensateType] }}
      </div>
        </div>
        <div slot="approvalStatus" slot-scope="text, record" class="tdpadding">
        <span :class="set_color_by_state(record)">{{$t("internal.finance.state_list")[record.approvalStatus]}}</span>
        <span v-if="[4,5,6,7].includes(record.approvalStatus)" class="pl10x text-blue cursor-pointer" @click="show_reason_for_rejection(record)">
          {{ $t("internal.finance.secondarysettlement.label16") }}
        </span>
        </div>
      <template slot="footer">
        <a-pagination
        v-if="tabledata.length>0"
        :total="pagination.total"
        :current="pagination.current"
        show-size-changer 
        show-quick-jumper
        :page-size-options="pagination.pageSizeOptions"
        :page-size="pagination.pageSize"
        :show-total="total => $t('internal.showTotal_text',[pagination.total])"
        @change="onChange"
        @showSizeChange="onShowSizeChange"
      />
    <div
        class="fs16 position-absolute line-height-24px"
        style="bottom: 10px; left: 25px;"
        v-if="tabledata.length > 0 "
      >
      <div>
       <span class="row ml10x" v-if="tabledata.length>0">
        <!-- 负余额总额： -->
          <span class="pr10x">
            <span class="title-grey">{{ $t("internal.finance.secondarysettlement.label10") }}:</span>
             <span class="fw_600">
          {{addCommas(order_totals.minusAmountTotal)}}
        </span>
        
        <span >
          <!-- 赔偿总额： -->
         <span class="title-grey">{{ $t("internal.finance.secondarysettlement.label21") }}:</span>
     <span class="text-red">
      {{addCommas(order_totals.secSettleCompTotal)}}
     </span>    
       </span>
          <span >
             <!-- 不赔偿总额： -->
            <span class="title-grey">{{ $t("internal.finance.secondarysettlement.label11") }}:</span>
        <span class="text-green">
          {{addCommas(order_totals.noDamageAmountTotal)}}
        </span>    
          </span>
          </span>
          <!-- 待定总额： -->
          <span >
            <span class="title-grey">{{ $t("internal.finance.secondarysettlement.label27") }}:</span>
             <span class="text-red">
          {{addCommas(order_totals.prepareAmountTotal)}}
         </span>    
          </span>
           <!--提交审核-->
           
           <a-button
           v-if="searchForm.timeType=='1'"
           type="primary"
           class="ml10x"
             style="height: 30px"
             @click="show_submit_confirmation_handle(1)"
           >
           
           {{ $t("internal.finance.secondarysettlement.label22") }}
           </a-button>
      </span>
    </div>
  </div> 
       </template>
       </a-table>
    </div>
    <!-- 报表下载弹窗 -->
    <q-dialog v-model="exportExcelShow" persistent teansition-show="scale" transition-hide="scale">
       <dialog-excel :export_param="export_param">
      </dialog-excel>
    </q-dialog>
    <!-- 驳回原因 -->
    <q-dialog v-model="show_reason_for_rejection_detail" persistent teansition-show="scale" transition-hide="scale">
      <reason_for_rejection_dialog :detailObj="detailObj"></reason_for_rejection_dialog>
    </q-dialog>
    <!-- 赔付或审批 -->
    <q-dialog v-model="show_submit_confirmation" persistent teansition-show="scale" transition-hide="scale">
      <submit_confirmation :detailObj="submit_confirmation_order_detail" @hidden_submit_confirmation="hidden_submit_confirmation"></submit_confirmation>
    </q-dialog>
  </div>
</template>

<script>
import { i18n } from "src/boot/i18n";
import { mapGetters } from "vuex";
import { api_finance } from "src/api/index.js";
import mixins from "src/mixins/internal/index.js";
import financesorter from "src/mixins/internal/module/financesorter.js";
import commonmixin from "src/mixins/internal/common/commontoolmixin.js";
import constantmixin from "src/mixins/internal/common/constantmixin.js";
import financeMixin from "src/pages/internal/finance/liquidation/mixin/index.js";
import dataCenterMixin from "src/mixins/internal/module/datacentertablemixin.js";
import datacentertablemixin from "src/components/common/bet_slip/internal/mixin/datacentertablemixin.js";
import moment from "moment";
import { columns_by_timeType } from "src/components/common/secondary_settlement/config/config.js"; //财务中心-二次结算查询表格配置
import { handleCopy,revise_userId_and_fakeName} from "src/util/module/common.js";
import dialogExcel from "src/components/common/dialog/dialogExcel.vue"
import treeSelect from "src/components/common/tree/tree_select.vue";
import reason_for_rejection_dialog from "src/components/common/secondary_settlement/component/reason_for_rejection_dialog.vue"

import submit_confirmation from "src/components/common/secondary_settlement/component/submit_confirmation.vue"


let mixins_custom=  [...mixins, financesorter, commonmixin, constantmixin, financeMixin,dataCenterMixin,datacentertablemixin];
export default {
  mixins: [...mixins_custom],
  components: {
    treeSelect,
    dialogExcel,
    reason_for_rejection_dialog,
    submit_confirmation
  },
  data() {
    return {
      detailObj:{},
      //提交赔付和审核--详情
      submit_confirmation_order_detail:{},
      //提交赔付和审核
      show_submit_confirmation:false,
      //历史驳回原因
      show_reason_for_rejection_detail:false,
      tabledata: [], // 表格数据
      //请选择二次结算原因
      secondary_settlement_list:i18n.t("internal.finance.secondary_settlement_list"),
      //是否赔偿
      compensation_list:i18n.t("internal.finance.compensation_list"),
      state_list:i18n.t("internal.finance.state_list"),
      timeType_list: i18n.t("internal.filters.timeTypelist"), //结算时间 timeType 1:结算时间  2:最后一次账变时间  3:第一次账变时间
      export_param:{},//导出报表参数
      exportExcelShow:false,//导出弹窗显示控制
      order_totals:{},//总额数据 
      tabledata_loading: false, //表格loading
      searchForm: {
        timeType:1, //时间类型 1: 结算时间 2:最后一次账变时间 3:第一次账变时间 默认为结算时间
        jssjtime: ``, //开始日期
        startTime: `${moment(new Date().setDate(new Date().getDate() - 2)).format("YYYY-MM-DD")} 00:00:00`, //开始日期
        endTime: `${moment(new Date().setDate(new Date().getDate())).format("YYYY-MM-DD")} 23:59:59`, // 结束日期 
        matchId: "", // 赛事ID
        userId: "", // 用户ID
        orderNo: "", //注单号
        settleType: "", //二次结算原因/变动原因
        //是否赔偿
        compensateType:"",
        //当前额度大于0
        matchName:"",
        merchantName: "", // 商户名称
        negativeAmountMin: "", //负余额最小值
        negativeAmountMax: "", //负余额最大值
        amountMin: "", //当前余额最小值
        amountMax: "", //当前余额最大值
      },
      //大于0
      is_amountMin:false,
      //批量修改-是否赔偿-默认值否
      is_compensation:2,
      cancel_num: 0,
    //---------------------------全选---start-------------------
      cancel_all_selected: false, // 取消全选
      cancel_arr: [], //准备去取消的  原始数据集合
      //---------------------------全选----end-------------------
    };
  },
  created() {
    this.init_date_time()
  this.initTableData();
  },
  computed: {
    ...mapGetters(['get_user_info', "get_dom",]),
    columns(){
      return columns_by_timeType(this.searchForm.timeType)
    },
    computed_scroll_y({is_expand,scrollHeight,cancel_num}) {
      let num = 0;
      num = !is_expand ? scrollHeight - 45 : scrollHeight;
      num = cancel_num > 0 ? num - 40 : num;
      return num;
    },
  },
  watch:{
    is_amountMin(val){
      if(val){
        this.searchForm.amountMin=1
      }else{
        
        this.searchForm.amountMin=""
      }
    }
  },
  methods: {
    moment,
    handleCopy,
    columns_by_timeType,
     // 添加千位符
     addCommas(num) {
      return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    //---------------------------全选---start-------------------
    
    // 全选 全部取消按钮
    handle_cancel_all_selected_change() {
      let all;
      all = this.cancel_all_selected;
      this.cancel_arr = [];
      if (all) {
        //全选
        this.tabledata.map((item) => {
          if(item.rejectTimes<3){
            item.selected = true;
          this.cancel_arr.push(item);
          }
        });
      } else {
        // 取消全选
        this.tabledata.map((item) => {
          item.selected = false;
          this.cancel_arr = [];
        });
      }
        this.compute_cancel_money_and_num();
   
    },
    //清空 已选中的 准备取消的 
    handle_clear_cancel_arr() {
      if(this.cancel_arr.length!=0){
        this.cancel_all_selected=false
        //清空取消注单
        
        this.handle_cancel_all_selected_change()
      }
    },
    // 取消
    handle_cancel_order_input_changed(record) {
      //取消
      // cancel_arr:[] ,//准备去取消的  原始数据集合
      let selected = record.selected;
      if (selected) {
        //选中
        this.cancel_arr.push(record);
      } else {
        // 取消选中
        this.cancel_arr=this.cancel_arr.filter(item=>item.id != record.id)
      }
      this.compute_cancel_money_and_num();
      this.$forceUpdate();
    },
    //计算 取消的 总数 和 总 金额 以及  取消注单全选  状态
    compute_cancel_money_and_num() {
      if(this.all_can_cancel_num !=0){
        this.cancel_all_selected = this.cancel_arr.length == this.all_can_cancel_num;
      }
    },
    
    //---------------------------全选----end-------------------
    update_compensateType(record,val){
      let params={
        settleOrderIds:[record.id],
        settleTimes:moment(this.searchForm.jssjtime).format("YYYY-MM-DD") ,
        compensateType:val
      }
      this.update_state(params)
    },
    /**
     * @description  修改是否赔偿
     */
     async update_state(params) {
       await api_finance.settlement_inquiry.update_batchSetCompensateStatus(params).then((res) => {

        let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          this.vsShow=false
          if (code == "0000000") {
            this.initTableData()
        }else {
            msg && this.$message.error(msg, 5);
          }
        })
    },
    /*
    * 批量修改赔偿
    */
    batch_edit(){
      let settleOrderIds=[]
      this.cancel_arr.forEach(item=>{
        settleOrderIds.push(item.id)
      })
      let params={
        settleOrderIds:settleOrderIds,
        settleTimes:moment(this.searchForm.jssjtime).format("YYYY-MM-DD") ,
        compensateType:this.is_compensation
      }
      this.update_state(params)
    },
    show_reason_for_rejection(record){
      this.detailObj=record
      this.show_reason_for_rejection_detail=true
    },
    /*
    * 提交赔付、提交审核
    */
    show_submit_confirmation_handle(type){
      let params = this.compute_init_tabledata_params();
      params = this.delete_empty_property_with_exclude(params);
      this.submit_confirmation_order_detail={
        list_by_query:params,
        secSettleCompTotal:Math.abs(this.order_totals.secSettleCompTotal)
      }
      this.show_submit_confirmation=true
    },
    hidden_submit_confirmation(){
      this.show_submit_confirmation=false
      this.initTableData()
    },
    
    set_color_by_state(record){
      if([4,5,6,7].includes(record.approvalStatus)){
        return 'text-red'
      }else if([3].includes(record.approvalStatus)){
        return 'text-green'
      }
    },
    init_date_time(){
      let gt12 = moment().format("HH") > 11;
      let new_day=gt12?1:0
      this.searchForm.jssjtime= `${moment(new Date().setDate(new Date().getDate() - 2+new_day)).format("YYYY-MM-DD")}`
    },
    disabledDate(current) {
      // 12 点等于12 ，也可以查
      let gt12 = moment().format("HH") > 11;
      let new_day=gt12?1:0
      return (
        current.isBefore(moment(Date.now()).add(-62+new_day, "days")) ||
        current.isAfter(moment(Date.now()).add(-2+new_day, "days"))
      );
    },
    // 搜索
    handle_search() {
      this.initTableData();
    },
   // 时间类型选择
    handle_time_Type(val) {
      this.searchForm.timeType = val;
      this.initTableData()
    },
    // 初始化表格数据
    initTableData() {
      this.tabledata_loading = true;
      this.cancel_arr=[]
      let params = this.compute_init_tabledata_params();
      params = this.delete_empty_property_with_exclude(params);
      api_finance
        .post_secondary_settlemen_pageList(params)
        .then(res => {
          this.tabledata_loading = false;
          let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          if (code == "0000000") {
            let currentPage =
              this.$lodash.get(res, "data.pageInfo.current") * 1 || 1;
            let arr = this.$lodash.get(res, "data.data.pageInfo.records") || [];
            this.order_totals =  this.$lodash.get(res, "data.data")
            this.pagination.start = this.$lodash.get(res, "data.data.pageInfo.current");
            this.tabledata = this.rebuild_tabledata_to_needed(arr);
            this.pagination.total = this.$lodash.get(res, "data.data.pageInfo.total") * 1 || 0;   
          } else {
            this.$message.error(msg, 5);
          }
        });
    },
    
    rebuild_tabledata_to_needed(arr) {
      if(!arr || !arr.length) {
        return []
      }
      // 就散这一页数据 能取消的所有 注单的总数
      let all_can = 0;
      arr.map((item, index) => {
        item.selected = false;
        all_can += 1;
        item._index =
          (this.pagination.current - 1) * this.pagination.pageSize + index + 1;
      });
        this.all_can_cancel_num = all_can;
        this.cancel_arr = [];
        this.compute_cancel_money_and_num();
      return arr;

    },
        compute_init_tabledata_params() {
      let { pageSize, sort, orderBy } = this.pagination;
      let {
        timeType, //时间类型 1: 结算时间 2:最后一次账变时间 3:第一次账变时间 默认为结算时间
        jssjtime,
        startTime, //开始日期
        endTime, // 结束日期
        matchId ,// 赛事ID
        userId ,// 用户ID
        orderNo, //注单号
        //是否赔偿
        compensateType,
        settleType, //二次结算原因/变动原因
        matchName,
        merchantName,
        negativeAmountMin, //负余额最小值
        negativeAmountMax, //负余额最大值
        amountMin, //当前余额最小值
        amountMax, //当前余额最大值
      } = this.searchForm;
      
      let params = {
        timeType,
        // merchantName, //商户名称，可模糊搜索
        startTime:new Date(startTime).getTime(), //开始日期
        endTime:new Date(endTime).getTime(), // 结束日期
        matchId,// 赛事ID
        userId ,// 用户ID
        orderNo, //注单号
        //是否赔偿
        compensateType,
        //当前额度大于0
        settleType, //二次结算原因/变动原因
        matchName,
        merchantName,
        negativeAmountMin, //负余额最小值
        negativeAmountMax, //负余额最大值
        amountMin, //当前余额最小值
        amountMax,
        pageNum: this.pagination.current, //分页，查询第几页数据。
        pageSize, //分页，每页查询多少条，默认20条。可不传
      };
      if(timeType==1){
        //结算时间
         params.startTime=new Date(jssjtime+` 00:00:00`).getTime()
         params.endTime=new Date(jssjtime+` 23:59:59`).getTime()
      }
       revise_userId_and_fakeName(params,this.src_external?false:true);
      return params;
    },
    // 日期选择
     on_change(dates, dateStrings) {
      if (!(dateStrings[0] && dateStrings[1])) {
        return false;
      }

      Object.assign(this.searchForm, {
        startTime: dateStrings[0],
        endTime: dateStrings[1],
      });
    },
    dateonChange(){
      this.searchForm.jssjtime= moment(this.searchForm.jssjtime).format("YYYY-MM-DD") 
    },
    // 导出报表
    handle_export_excel(){
    if(this.pagination.total>0){
             let params = this.compute_init_tabledata_params();
             params = this.delete_empty_property_with_exclude(params)  
             Object.assign(params,{url:"/order/orderTimeSettle/export"})
             this.export_param = params;
             this.exportExcelShow = true;
    }else{
      this.handle_error()
    }
    },
  },
};
</script>

<style lang="scss" scoped></style>
