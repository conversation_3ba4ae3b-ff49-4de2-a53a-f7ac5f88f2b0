/*
 * @FilePath: /src/boot/external/filters.js
 * @Description: 
 */
import Vue from "vue";
import { i18n } from "src/boot/i18n";
const  add_empty_value=(obj)=>{
  for(let i in obj ){
    if(!obj[i].hasOwnProperty('value')){
      obj[i]['value']=''
    }
  }
  return obj
}
let filters = {
  filterNumberFormat(value) {
    if (!value) return 0;
    value = value.toString().replace(/\$|,/g, "");
    if (isNaN(value)) value = "0";
    let sign = value == (value = Math.abs(value));
    value = Math.floor(value * 100 + 0.50000000001);
    let cents = value % 100;
    value = Math.floor(value / 100).toString();
    if (cents < 10) cents = "0" + cents;
    for (var i = 0; i < Math.floor((value.length - (1 + i)) / 3); i++)
      value =
        value.substring(0, value.length - (4 * i + 3)) +
        "," +
        value.substring(value.length - (4 * i + 3));
    return (sign ? "" : "-") + value;
  },
filterAmount(value){
    if (!value) return '0' + '人民币'
    value = value + ''
    return value.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') + '人民币'
  },
  /** 
   * 将数值四舍五入(保留2位小数)后格式化成金额形式 
   * @param value 数值(Number或者String) 
   * @return 金额格式的字符串,如'1,234,567.45' 
   * @type String 
   */ 
   moneyFormat(value ){
    if (!value) return 0;
    value = value.toString().replace(/\$|,/g, '');
    if (isNaN(value))
      value = "0";
    let sign = (value == (value = Math.abs(value)));
    value = Math.floor(value * 100 + 0.50000000001);
    let cents = value % 100;
    value = Math.floor(value / 100).toString();
    if (cents < 10)
      cents = "0" + cents;
    for (var i = 0; i < Math.floor((value.length - (1 + i)) / 3); i++)
      value = value.substring(0, value.length - (4 * i + 3)) + ',' +
        value.substring(value.length - (4 * i + 3));
    return (((sign) ? '' : '-') + value + '.' + cents);
  },
  /**
   * 将数值四舍五入(保留2位小数)后格式化成金额形式
   * @param value 数值(Number或者String)
   * @return 金额格式的字符串,如'1,234,567.45'
   * @type String
   */
  filterMoneyFormat(value) {
    if (!value) return 0;
    value = value.toString().replace(/\$|,/g, "");
    if (isNaN(value)) value = "0";
    let sign = value == (value = Math.abs(value));
    value = Math.floor(value * 100 + 0.50000000001);
    let cents = value % 100;
    value = Math.floor(value / 100).toString();
    if (cents < 10) cents = "0" + cents;
    for (var i = 0; i < Math.floor((value.length - (1 + i)) / 3); i++)
      value =
        value.substring(0, value.length - (4 * i + 3)) +
        "," +
        value.substring(value.length - (4 * i + 3));
    return (sign ? "" : "-") + value + "." + cents;
  },
 numberFormat(value){
    if (!value) return 0;
    value = value.toString().replace(/\$|,/g, '');
    if (isNaN(value))
      value = "0";
    let sign = (value == (value = Math.abs(value)));
    value = Math.floor(value * 100 + 0.50000000001);
    let cents = value % 100;
    value = Math.floor(value / 100).toString();
    if (cents < 10)
      cents = "0" + cents;
    for (var i = 0; i < Math.floor((value.length - (1 + i)) / 3); i++)
      value = value.substring(0, value.length - (4 * i + 3)) + ',' +
        value.substring(value.length - (4 * i + 3));
    return (((sign) ? '' : '-') + value );
  },
  amount(value){
    if (value === null || value === undefined) return '-'
    value = value + ''
    let result = '';//存储格式化的结果
    let j = 0;//控制每3位加一个逗号
    let floatIndex = value.indexOf('.');
    if (floatIndex >= 1) {
      if (Number(value) >= 0) {
        for (let i = value.length - 1; i >= 0; i--) {//从末尾开始遍历到第一个
          j++;
          if (i >= floatIndex) { // 小数部分不用添加分隔符
            j = 0;
          }
          result = value[i] + result;
          if (j == 3) {
            //金额的最后一个数字正好满足3位时，其前面不用加逗号，例:500.00
            if (i != 0) {
              result = ',' + result;
            }
            j = 0;
          }
        }
        return result;
      } else {
        value = value.substr(1);
        for (let i = value.length - 1; i >= 0; i--) {//从末尾开始遍历到第一个
          j++;
          if (i >= floatIndex) { // 小数部分不用添加分隔符
            j = 0;
          }
          result = value[i] + result;
          if (j == 4) {
            //金额的最后一个数字正好满足3位时，其前面不用加逗号，例:500.00
            if (i != 0) {
              result = ',' + result;
            }
            j = 0;
          }
        }
        return '-' + result;
      }
    } else {
      return value.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
    }
  },
 filterAmount_(value){
  return filters.amount(value)
},
  filterCycle(val) {
    val = Number(val);
    //   let arr = ['每月', '每季度', '每半年', '每年'];
    let arr = i18n.t("external.filterPaymentCycle");
    return arr[val - 1];
  },
  filterComputingStandard(val) {
    val = Number(val);
    //   let arr = ["投注额", "盈利额"];
    let arr = i18n.t("external.computingStandard");
    return arr[val - 1];
  },
  filterTransferMode(val) {
    val = Number(val);
    //   let arr = ['免转钱包', '转账钱包']
    let arr = [i18n.t("external.common.label21"), i18n.t("external.common.label22")];
    return arr[val - 1];
  },
  filterDirectSale(val) {
    val = Number(val);
    //   let arr = ['是', '否'];
    let arr = [i18n.t("external.common.label23"), i18n.t("external.common.label24")];
    return arr[val - 1];
  },
  filterChildConnectMode(val) {
    val = Number(val);
    //   let arr = ['对接panda', '对接渠道'];
    let arr = [i18n.t("external.common.label19"), i18n.t("external.common.label20")];
    return arr[val - 1];
  },
  filterPaymentCycle(val) {
    val = Number(val);
    let arr = i18n.t("external.filterPaymentCycle");
    return arr[val - 1];
  },
  filterTransferType(val) {
    val = Number(val);
    let arr = i18n.t("external.merchant.bettinguser.userAccount.filterTransferType");
    return arr[val - 1];
  },
  filterLoginType(val) {
    val = Number(val);
    let arr = [i18n.t("external.common.label4"), i18n.t("external.common.label5")];
    return arr[val - 1];
  },
  filterCurrency(val) {
    val = Number(val);
    // let arr = ["人民币", "新加坡元"];
    let arr = [i18n.t("external.common.label27"), i18n.t("external.common.label70")];
    return arr[val - 1];
  },
  filterAgentLevel(val) {
    val = Number(val);
    // let arr = ["直营商户", "渠道商户", "二级商户"];
    let arr = i18n.t("external.panel_type");
    return arr[val];
  },
  filterUserLevel(val) {
    val = Number(val);
    let arr = i18n.t("external.data.betslip.store.filterUserLevel");
    return arr[val - 1];
  },
  filterStatues(val) {
    val = Number(val);
    // 2是走水，3-输，4-赢，5-半赢，6-半输，7赛事取消，8赛事延期
    if (!val) return "-";
    return i18n.t("external.data.betslip.store.filterStatues_str")[val];
  },
  filterCancelType(val) {
    val = Number(val);
    // 0注单取消，1比赛取消，2比赛延期， 3比赛中断，4比赛重赛，5比赛腰斩，6比赛放弃，7盘口错误，8赔率错误，9队伍错误，10联赛错误，11比分错误，12电视裁判， 13主客场错误，14赛制错误，15赛程错误，16时间错误，17赛事提前，18自定义原因，19数据源取消，20比赛延迟，32投手变更，33进球无效，40PA手动拒单，41PA自动拒单，42业务拒单，43MTS拒单
    // if (!val) return "-";
    return i18n.t("internal.filters.filterCancelType_str")[val];
  },
  filterMatchLevel(val) {
    val = Number(val);
    return  i18n.t("external.data.matchbonus.store.filterMatchLevel")[val];
  },
  filterMerchantTag(val){
    //  现金用户    信用用户
    let arr = i18n.t('external.filters.merchantTag');
    return    arr[val]?  arr[val]['label']:''
  },
  //注单类型：1赛前 2滚球盘 3冠军盘  5活动
  filterMatchTypeList(val){
   let obj= add_empty_value(i18n.t('external.data.betslip.store.matchTypeList'))
   let values= Object.values(obj)
   let cobj= values.find(x=>x.value==val)
   return   cobj?cobj.label:val
  }
};
for (let i in filters) {
  // console.log(i, filters[i]);
  Vue.filter(i, filters[i]);
}
