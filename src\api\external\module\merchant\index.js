/*
 * @Desc:
 * @Author:Nice
 * @Date:
 * @FilePath: /src/api/external/module/merchant/index.js
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_4;
let prefix1 = process.env.API_PREFIX_3;
let prefix2 = process.env.API_PREFIX_17;
// let url_prefix =  process.env.API_PREFIX_URL_PREFIX+"/login";

// export const post1 = (params, url) => axios.post(`${prefix}${url}`, params)
// export const get1 = (params, url) => axios.get(`${prefix}${url}`, { params: { ...params } })

//根据查询渠道商户列表
export const get_manage_merchantAgent_list = (
  params,
  url = "/admin/merchantAgent/merchantList"
) => axios.get(`${prefix2}${url}`, { params: { ...params } });


//根据批量添加渠道商户
export const post_manage_merchant_add_merchant = (
  params,
  url = "/admin/merchantAgent/batchAddMerchant"
  ) => axios.get(`${prefix2}${url}`, { params: { ...params } });
  

//假刪除-渠道商戶
export const post_manage_merchant_delete_subagent = (
  params,
  url = "/admin/merchant/deleteSubAgent"
) => axios.get(`${prefix2}${url}`, { params: { ...params } });



//商户列表
export const get_manage_merchant_list = (
  params,
  url = "/manage/merchant/list"
) => axios.get(`${prefix}${url}`, { params: { ...params } });




//商户 查看历史代码 列表
export const get_manage_config_queryCodeConfigLogList = (
  params,
  url = "/manage/config/queryCodeConfigLogList"
) => axios.get(`${prefix}${url}`, { params: { ...params } });




//用户投注管理查询(port:10711)]
export const post_order_user_queryUserBetList = (
  params,
  url = "/order/user/queryUserBetList"
) => axios.post(`${prefix1}${url}`, params);

//生成管理员密码
export const get_manage_merchant_getPassword = (
  params,
  url = "/manage/merchant/getPassword"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//查询key列表
export const get_manage_merchant_queryKeyList = (
  params,
  url = "/manage/merchant/queryKeyList"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//生成KEY
export const get_manage_merchant_generateKey = (
  params,
  url = "/manage/merchant/generateKey"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//设置管理员
export const post_manage_merchant_admin_create = (
  params,
  url = "/manage/merchant/admin/create"
) => axios.post(`${prefix}${url}`, params);

//商户详情
export const get_manage_merchant_byid = (params, url = "/manage/merchant") =>
  axios.get(`${prefix}${url}/${params.id}`);

//修改商户
export const post_manage_merchant_update = (
  params,
  url = "/manage/merchant/update"
) => axios.post(`${prefix}${url}`, params);

//增加商户
export const post_manage_merchant_create = (
  params,
  url = "/manage/merchant/create"
) => axios.post(`${prefix}${url}`, params);

//修改管理员密码
export const post_manage_merchant_password_updates = (
  params,
  url = "/manage/merchant/password/update"
) => axios.post(`${prefix}${url}`, params);

//修改商户状态
export const get_manage_merchant_updateMerchantStatus = (
  params,
  url = "/manage/merchant/updateMerchantStatus"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//修改KEY
export const get_manage_merchant_updateKey = (
  params,
  url = "/manage/merchant/updateKey"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

/**
     * merchantId = null && creditId = null    查询模板配置
     * merchantId != null && creditId = null   查询商户配置
     * merchantId != null && creditId != null  查询信用代理配置
     */
// http://api.sportxxxifbdxm2.com/yewu17/admin/merchant/queryCreditLimitConfig?merchantId=1&creditId=1

export const post_merchant_queryCreditLimitConfig = (
  params,
  url = "/admin/merchant/queryCreditLimitConfig"
) => axios.post(`${prefix2}${url}?merchantId=${params.merchantId}${params.creditId?'&creditId='+params.creditId:'' }`,  params,{type:10} );


// http://api.sportxxxifbdxm2.com/yewu17/admin/merchant/saveOrUpdateCreditLimitConfig?merchantId=1&creditId=1
export const post_merchant_saveOrUpdateCreditLimitConfig = (
  params,
  url = "/admin/merchant/saveOrUpdateCreditLimitConfig"
) => axios.post(`${prefix2}${url}`, params );
// http://api.sportxxxifbdxm2.com/yewu17/admin/merchant/queryMechantAgentList?merchantCode=oubao

export const post_merchant_queryMechantAgentList = (
  params,
  url = "/admin/merchant/queryMechantAgentList"
) => axios.post(`${prefix2}${url}?merchantCode=${params.merchantCode}`, params,{type:10} );


// http://api.sportxxxifbdxm2.com/yewu17/admin/merchant/getMerchantMatchCreditConfig

export const post_merchant_getMerchantMatchCreditConfig = (
  params,
  url = "/admin/merchant/getMerchantMatchCreditConfig"
) => axios.post(`${prefix2}${url}`, params );


// http://api.sportxxxifbdxm2.com/yewu17/admin/merchant/saveCreditLimitTemplate
// --对外

export const post_merchant_saveCreditLimitTemplate = (
  params,
  url = "/admin/merchant/saveCreditLimitTemplate"
) => axios.post(`${prefix2}${url}`, params );


// 商户中心-平台用户风控-查询用户风控分页列表
export const post_queryPageUserRisk_ControlList = (
  params,
  url = "/admin/userRiskControl/queryPageUserRiskControlList"
) => axios.post(`${prefix2}${url}`,params );

// 修改用户风控状态
export const post_updateUserRiskControlStatus = (
  params,
  url = "/admin/userRiskControl/updateUserRiskControlStatus"
) => axios.post(`${prefix2}${url}`, params );
// -商户管控记录表批量导入
export const post_importUserRiskControlList = (
  params,
  url = "/admin/userRiskControl/importUserRiskControlList"
) => axios.post(`${prefix2}${url}`, params,{type:3} );
// 商户管控记录表导出

// export const exportUserRiskControlList = (
//   params,
//   url = "/admin/userRiskControl/exportUserRiskControlList"
// ) => axios.post(`${prefix2}${url}`, params );
export const exportUserRiskControlList = (params) => {
  let prefix = prefix2;
  let url = `/admin/userRiskControl/exportUserRiskControlList`;
  return axios.post(`${prefix}${url}`, params, {
    responseType: "blob"
  });
}


export const pending_Count = (
  params,
  url = "/admin/userRiskControl/pendingCount"
) => axios.get(`${prefix2}${url}`,params );

// 账户中心(对外)-异常用户名单 查询
export const post_userabnormal_list = (
  params,
  url = "/admin/abnormal/queryAbnormalList"
) => axios.post(`${prefix2}${url}`, params);

// 数据中心(对外)-特殊限额名单用户 查询
export const post_queryAbnormalUserList = (
  params,
  url = "/admin/abnormal/queryAbnormalUserList"
) => axios.post(`${prefix2}${url}`, params);

// 数据中心(对外)-特殊限额名单下拉列表 查询
export const post_queryUserComboList = (
  params,
  url = "/admin/abnormal/queryUserComboList"
) => axios.post(`${prefix2}${url}`, params);

// 账户中心(对外)-异常用户数量（左上角icon） 查询
export const post_userabnormal_count = (
  params,
  url = "/admin/abnormal/queryAbnormalCount"
) => axios.post(`${prefix2}${url}`, params);

// 账户中心(对外)-点击（左上角icon） 查询
export const post_update_abnormal_click = (
  params,
  url = `/admin/abnormal/updateAbnormalClickTime?abnormalClickTime=${Date.now()}`
) => axios.post(`${prefix2}${url}`, params);

// 账户中心(对外)-轮询
export const post_polling_abnormal_amount = (
  params,
  url = `/admin/abnormal/queryAbnormalCount`
) => axios.post(`${prefix2}${url}`, params);


// 账户中心(对外)-百家赔分布 查询
export const post_baijia_display = (
  params,
  url = "/admin/BaiJia/display"
) => axios.post(`${prefix2}${url}`, params);


// 商户中心-在线用户列表
export const post_get_user_online_list = (
  params,
  url = "/admin/merchant/queryOnlineUserList"
) => axios.post(`${prefix2}${url}`, params);


// 商户中心-在线用户列表-折线图
export const post_get_user_online_day_max = (
  params,
  url = "/admin/merchant/getUserOnlineDayMax"
) => axios.post(`${prefix2}${url}`, params);

// 商户中心-在线用户列表-踢出在线会员(单个、批量)	
export const post_kick_out_online_user = (
  params,
  url = "/admin/merchant/kickOutOnlineUser"
) => axios.post(`${prefix2}${url}`, params);


// 商户中心-在线用户列表-踢出日志
export const post_log_findLog= (
  params,
  url = "/admin/log/findLog"
  ) => axios.post(`${prefix2}${url}`, params);

