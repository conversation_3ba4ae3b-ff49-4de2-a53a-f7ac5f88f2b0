/*
 * @FilePath: d:/projects/new-merchant-management/src/api/internal/module/operate/module/props_management.js
 * @Description: 道具管理接口
 * @Author: tony
 */


import axios from "src/api/common/axioswarpper.js";


let prefix_1 = process.env.API_PREFIX_4;


// 编辑接口
export const update_userannual_reportconfig = (params) => {
  let prefix = prefix_1;
  let url = `/manage/userAnnualReport/updateUserAnnualReportConfig`;
  return axios.post(`${prefix}${url}`, params);
}


// 年报内容查询
export const get_user_annual_reportlist = (params) => {
  let prefix = prefix_1;
  let url = `/manage/userAnnualReport/getUserAnnualReportList`;
  return axios.post(`${prefix}${url}`,params);
}
// 大数据年报内容查询
export const get_big_data_annual_report_info = (params) => {
  let prefix = prefix_1;
  let url = `/manage/userAnnualReport/getBigDataAnnualReportInfo`;
  return axios.post(`${prefix}${url}`,params);
}


//排序
export const userannual_report_sort = (params) => {
    let prefix = prefix_1;
    let url = `/manage/userAnnualReport/changeOrderNo`;
    return  axios.post(`${prefix}${url}`, params);
}