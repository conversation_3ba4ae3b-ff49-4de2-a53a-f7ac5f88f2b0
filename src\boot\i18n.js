/*
 * @FilePath: /src/boot/i18n.js
 * @Description: 
 */
import Vue from "vue";
import VueI18n from "vue-i18n";
import messages_external from "src/i18n/external";
import messages_internal from "src/i18n/internal";
import { LocalStorage } from "quasar";
let messages = {};
const FOR_WHICH = process.env.FOR_WHICH;
// {
//   'en': enUS,
//   'zs': zhCN
// }
// internal:
  messages = {
    en: {
      internal: messages_internal["en"],
      external: messages_external["en"],
    },
    zs: {
      internal: messages_internal["zs"],
      external: messages_external["zs"],
    },
    ko: {
      internal: messages_internal["ko"],
      external: messages_external["ko"],
    },
  };
// json-editor模板
import CodeEditor from "bin-code-editor";
Vue.use(CodeEditor);
Vue.use(VueI18n);
let geti18n = LocalStorage.getItem("language");
const i18n = new VueI18n({
  locale: geti18n || "zs",
  fallbackLocale: geti18n || "zs",
  messages
});
export default ({ app }) => {
  // Set i18n instance on app
  app.i18n = i18n;
};
export { i18n };
