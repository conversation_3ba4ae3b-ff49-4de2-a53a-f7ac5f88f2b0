<!--
 * @Author: Nice
 * @Date: 2020-07-30 15:20
 * @Description: 账变弹窗
 * @FilePath: /src/components/common/bet_slip/component/dialogRecord.vue
-->
<template>
  <div
    style="width: 780px; height:auto;max-width:800px; min-height: 60px; overflow:hidden;"
    class="text-panda-text-7"
  >
    <q-card class="bg-white text-black">
      <q-card-section class="no-padding">
        <div class="row line-height-40px fs14 bg-panda-dialog text-panda-text-7 pr10x">
          <div class="pl20x fw_600">{{detailObj.userName}}-{{$t('internal.data.betslip.dialogRecord.title')}}</div>
          <q-space></q-space>
          <q-btn class="mr5x text-panda-dialog-close" flat dense icon="close" v-close-popup />
        </div>
      </q-card-section>
      <a-table
        :columns="columns"
        :dataSource="tabledata"
        :pagination="pagination"
        :loading="tabledata_loading1"
        :scroll="{ x: 510,y: 290}"
        size="small"
        rowKey="id"
      >
        <span
          slot="createTime"
          slot-scope="text,record"
        >{{moment(record.createTime).format("YYYY-MM-DD HH:mm:ss")}}</span>
        <!-- 交易类型 -->
        <span
          slot="bizType"
          slot-scope="text,record"
        >{{recordTypeList[record.bizType*1-1].label}}</span>
         <!-- 交易额 -->
        <span slot="amount" slot-scope="text,record">
          <span>
            <span v-if="record.transferType == 1">+</span>
            <span v-if="record.transferType == 2">-</span>
            <!-- {{record.amount | filterMoneyFormat}} -->
              {{ compute_current_record_amount(record) | filterMoneyFormat}} 
          </span>
        </span>
        <!-- 交易结果 -->
        <span slot="status" slot-scope="text,record">
          <span :class="record.status == 1?'panda-dot-success': 'panda-dot-red'" class="panda-dot mr10x"></span>
        <span>{{record.status == 1? $t('internal.status')[0]: $t('internal.status')[1]}} </span>
          <a-tooltip
          placement="top"
          >
            <template slot="title">
              <div v-html="record.mag" :style="`max-width: 116px; word-break:break-all;`"></div>
            </template>
          <a-icon v-if="record.status == 0" type="exclamation-circle"  class="text-red fs14 cursor-pointer"/>
          </a-tooltip>
        </span>
        <!-- 交易相关注单号 -->
        <div slot="orderNo" slot-scope="text,record">
          <div>
                <!-- <div v-for="(item,index) in  compute_record_all_order(record)" :key="index" class="text-left q-pl-lg q-my-sm">  -->
                <span   @click="handleCopy(detailObj.orderNo,   $t('internal.orderNo_text'))"> {{detailObj.orderNo}}</span>       
                   <!-- </div> -->
          </div>
        </div>
      </a-table>
    </q-card>
  </div>
</template>
<script>
import { i18n } from 'src/boot/i18n';
import { api_merchant,api_account } from "src/api/index.js";
import { record_config as record_config_internal} from "src/components/common/bet_slip/internal/config/recordConfig.js";
import { record_config as record_config_external} from  "src/components/common/bet_slip/external/config/recordConfig.js";
import { handleCopy } from "src/util/module/common.js";
import moment from "moment";
const FOR_WHICH = process.env.FOR_WHICH;
let record_config=record_config_internal
if(FOR_WHICH=="external"){
    record_config=record_config_external
}
export default {
  props: {
    detailObj: {
      type: Object
    }
  },
  data() {
    return {
      
      
      tabledata_loading1: false, //  弹窗loading
      columns: record_config,  // 表格配置
      tabledata: [], // 表格数据
      pagination: {
        pageSize: 100,
        current: 1,
        showTotal: total => i18n.t('internal.showTotal_text',[total]),
        hideOnSinglePage: true,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ["10", "30", "30", "40"],
        onShowSizeChange: (current, pageSize) =>
          (this.pagination.pageSize = pageSize),
        onChange: current => (this.pagination.current = current)
      },
      recordTypeList: i18n.t('internal.data.betslip.dialogRecord.recordTypeList'),
    };
  },
  created() {
    this.initTableData();
  },
  methods: {
    moment,
    handleCopy,
    initTableData() {
      this.tabledata_loading1 = true;
      let params = this.compute_init_tabledata_params();
      console.warn(params,this.detailObj);
      let post_manage_account_findRecord=this.src_internal?api_merchant.post_manage_account_findRecord: api_account
        .post_admin_account_findRecord
      post_manage_account_findRecord(params)
        .then(res => {
          this.tabledata_loading1 = false;
          let code = this.$lodash.get(res, "data.code");
          if (code == "0000000") {
            let arr = this.$lodash.get(res, "data.data.records") || [];
            this.tabledata = this.rebuild_tabledata_to_needed(arr);
            this.pagination.total = this.$lodash.get(res, "data.data.total") * 1 || 0;
          } else {
            this.$message.error(res.data.msg, 2);
          }
        });
    },
    rebuild_tabledata_to_needed(arr) {
      arr.map((item, index) => {
        item._index =
          (this.pagination.current - 1) * this.pagination.pageSize + index + 1;
        item.id = index + 1;
      });
      return arr;
    },
    compute_current_record_amount(record){
      if(!record.orderStr){ return ''}
      let orderStr_arr= JSON.parse(record.orderStr)
      let obj= orderStr_arr.find(x=>x.orderNo==this.detailObj.orderNo)
       return Number(obj.amount)
    },
    // 计算 这个交易记录相关的 所有注单数据 
    compute_record_all_order(record){
        if(!record.orderStr){ return []}
          let orderStr_arr= JSON.parse(record.orderStr)
          return orderStr_arr
    },
    compute_init_tabledata_params() {
      let { current, pageSize } = this.pagination;
      let { createTime } = this.detailObj;

      if(this.detailObj.orderDetailList[0].preOrder && this.detailObj.orderDetailList[0].preOrder == 1){
        return {
          pageNum: current,
          pageSize,
          orderId: this.detailObj.orderNo,
          userId: this.detailObj.uid,
        };
      }else{
        return {
          pageNum: current,
          pageSize,
          orderId: this.detailObj.orderNo,
          userId: this.detailObj.uid,
          startTime: createTime&&moment(createTime).valueOf(),
        };
      }
       
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .ant-spin-nested-loading > div > .ant-spin {
	 max-height: 70px !important;
	 min-height: 70px !important;
}
 ::v-deep .ant-empty-normal {
	 margin: 50px 0 !important;
}
 ::v-deep .ant-table-footer {
	 padding: 0;
	 height: 30px;
	 line-height: 30px;
}
 ::v-deep .ant-table-thead > tr > th {
	 color: #3c4551;
	 font-weight: 600;
}
 .text-over-130 {
	 display: inline-block;
	 max-width: 120px;
	 vertical-align: middle;
	 text-overflow: ellipsis;
	 overflow: hidden;
	 white-space: nowrap;
}
 
</style>
