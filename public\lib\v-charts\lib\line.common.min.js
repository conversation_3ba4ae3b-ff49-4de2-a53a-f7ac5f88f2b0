"use strict";function _interopDefault(e){return e&&"object"==typeof e&&"default"in e?e.default:e}var utils=require('./utils'),utilsLite=require("utils-lite");require("echarts/lib/chart/line");var Core=_interopDefault(require('./core')),_extends=Object.assign||function(e){for(var i=1;i<arguments.length;i++){var t=arguments[i];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e};function getLineXAxis(e){var i=e.dimension,t=e.rows,a=e.xAxisName,n=e.axisVisible,r=e.xAxisType;return i.map(function(e,i){return{type:r,nameLocation:"middle",nameGap:22,name:a[i]||"",axisTick:{show:!0,lineStyle:{color:"#eee"}},data:t.map(function(i){return i[e]}),show:n}})}function getLineSeries(e){var i=e.rows,t=e.axisSite,a=e.metrics,n=e.area,r=e.stack,l=e.nullAddZero,s=e.labelMap,o=e.label,u=e.itemStyle,d=e.lineStyle,x=e.areaStyle,c=e.dimension,m=[],p={},f=r&&utils.getStackMap(r);return a.forEach(function(e){p[e]=[]}),i.forEach(function(e){a.forEach(function(i){var t=null;null!=e[i]?t=e[i]:l&&(t=0),p[i].push([e[c[0]],t])})}),a.forEach(function(e){var i={name:null!=s[e]?s[e]:e,type:"line",data:p[e]};n&&(i.areaStyle={normal:{}}),t.right&&(i.yAxisIndex=~t.right.indexOf(e)?1:0),r&&f[e]&&(i.stack=f[e]),o&&(i.label=o),u&&(i.itemStyle=u),d&&(i.lineStyle=d),x&&(i.areaStyle=x),m.push(i)}),m}function getLineYAxis(e){for(var i=e.yAxisName,t=e.yAxisType,a=e.axisVisible,n=e.scale,r=e.min,l=e.max,s=e.digit,o={type:"value",axisTick:{show:!1},show:a},u=[],d=function(e){t[e]?u[e]=_extends({},o,{axisLabel:{formatter:function(i){return utils.getFormated(i,t[e],s)}}}):u[e]=_extends({},o),u[e].name=i[e]||"",u[e].scale=n[e]||!1,u[e].min=r[e]||null,u[e].max=l[e]||null},x=0;x<2;x++)d(x);return u}function getLineTooltip(e){var i=e.axisSite,t=e.yAxisType,a=e.digit,n=e.labelMap,r=e.tooltipFormatter,l=i.right||[],s=n?l.map(function(e){return void 0===n[e]?e:n[e]}):l;return{trigger:"axis",formatter:function(e){if(r)return r.apply(null,arguments);var i=[],n=e[0],l=n.name,o=n.axisValueLabel,u=l||o;return i.push(u+"<br>"),e.forEach(function(e){var n,r=e.seriesName,l=e.data,o=e.marker,u=~s.indexOf(r)?t[1]:t[0],d=utilsLite.isArray(l)?l[1]:l;n=utils.getFormated(d,u,a),i.push(o),i.push(r+": "+n),i.push("<br>")}),i.join("")}}}function getLegend(e){var i=e.metrics,t=e.legendName,a=e.labelMap;return t||a?{data:a?i.map(function(e){return null==a[e]?e:a[e]}):i,formatter:function(e){return null!=t[e]?t[e]:e}}:{data:i}}var line$1=function(e,i,t,a){i=utilsLite.isArray(i)?i:[],e=utilsLite.isArray(e)?e:[];var n=t.axisSite,r=void 0===n?{}:n,l=t.yAxisType,s=void 0===l?["normal","normal"]:l,o=t.xAxisType,u=void 0===o?"category":o,d=t.yAxisName,x=void 0===d?[]:d,c=t.dimension,m=void 0===c?[e[0]]:c,p=t.xAxisName,f=void 0===p?[]:p,y=t.axisVisible,g=void 0===y||y,v=t.area,A=t.stack,h=t.scale,b=void 0===h?[!1,!1]:h,S=t.min,L=void 0===S?[null,null]:S,T=t.max,N=void 0===T?[null,null]:T,w=t.nullAddZero,V=void 0!==w&&w,k=t.digit,M=void 0===k?2:k,O=t.legendName,_=void 0===O?{}:O,E=t.labelMap,F=void 0===E?{}:E,j=t.label,q=t.itemStyle,Z=t.lineStyle,C=t.areaStyle,D=a.tooltipVisible,X=a.legendVisible,Y=a.tooltipFormatter,$=e.slice();r.left&&r.right?$=r.left.concat(r.right):r.left&&!r.right?$=r.left:t.metrics?$=t.metrics:$.splice(e.indexOf(m[0]),1);var G=X&&getLegend({metrics:$,legendName:_,labelMap:F}),H=D&&getLineTooltip({axisSite:r,yAxisType:s,digit:M,labelMap:F,xAxisType:u,tooltipFormatter:Y}),I=getLineXAxis({dimension:m,rows:i,xAxisName:f,axisVisible:g,xAxisType:u}),P=getLineYAxis({yAxisName:x,yAxisType:s,axisVisible:g,scale:b,min:L,max:N,digit:M});return{legend:G,xAxis:I,series:getLineSeries({rows:i,axisSite:r,metrics:$,area:v,stack:A,nullAddZero:V,labelMap:F,label:j,itemStyle:q,lineStyle:Z,areaStyle:C,xAxisType:u,dimension:m}),yAxis:P,tooltip:H}},index=_extends({},Core,{name:"VeLine",data:function(){return this.chartHandler=line$1,{}}});module.exports=index;
