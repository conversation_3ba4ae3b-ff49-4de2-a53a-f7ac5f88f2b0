/*
 * @Author: downey
 * @Date: 2020-12-27 21:51:14
 * @Description: 热词接口
 * @FilePath       : /merchant-manage/src/api/internal/module/operate/module/keyword.js
 */

import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_10;
let url_prefix = "/v1/keyword";

// 热词列表
export const get_selectKeywordList = (
) => axios.get(`${prefix}${url_prefix}/deployKeywordList`);

//修改关键词
export const post_updateKeyword = (
  params,
  url = "/updateKeyword"
) => axios.post(`${prefix}${url_prefix}${url}`, params);