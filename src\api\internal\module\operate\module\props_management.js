/*
 * @FilePath: d:/projects/new-merchant-management/src/api/internal/module/operate/module/props_management.js
 * @Description: 道具管理接口
 * @Author: tony
 */


import axios from "src/api/common/axioswarpper.js";


let prefix_1 = process.env.API_PREFIX_4;

// 新增道具接口
export const add_props = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/addProps`;
  return axios.post(`${prefix}${url}`, params, {
    type: 1
  });
}

// 编辑道具接口
export const edit_props = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/editProps`;
  return axios.post(`${prefix}${url}`, params, {
    type: 1
  });
}

// 道具开关
export const modify_props_state = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/modifyPropsState`;
  return axios.post(`${prefix}${url}`, params, {
    type: 1
  });
}

// 道具列表
export const get_props_list = () => {
  let prefix = prefix_1;
  let url = `/slotMachine/propsList`;
  return axios.get(`${prefix}${url}`);
}

// 下拉道具列表
export const get_select_props_list = () => {
  let prefix = prefix_1;
  let url = `/slotMachine/selectPropsList`;
  return axios.get(`${prefix}${url}`);
}
