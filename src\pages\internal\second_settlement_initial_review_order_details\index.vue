<!--
 * @Description: 财务中心-二次结算初次审核-注单详情
-->
<template>
  <div class="full-width full-height">
    <!--财务中心-二次结算查询-->
    <div class="pl10x pt10x pb10x" id="top1">
      <q-breadcrumbs
      separator="/"
      active-color="whiddte"
      class="panda-text-2"
    >
      <q-breadcrumbs-el
      :label="$t('internal.finance.liquidation.index.bar')[0]"
      />
    
      <q-breadcrumbs-el
      v-if="page_type==1"
       :label="$t('internal.menujs.second_settlement_initial_review')"
       class="fw_600 panda-text-1"
     />
     <q-breadcrumbs-el
      v-else-if="page_type==2"
       :label="$t('internal.menujs.second_settlement_and_second_review')"
       class="fw_600 panda-text-1"
     />
     <q-breadcrumbs-el
     v-if="page_type==3"
     :label="set_cards[month_type].title1"
     class="fw_600 panda-text-1"
   />
      <q-breadcrumbs-el
      v-else
        :label="$t('internal.menujs.merchant_details')"
        class="fw_600 panda-text-1"
      />
      <q-breadcrumbs-el
        :label="$t('internal.menujs.bet_betails')"
        class="fw_600 panda-text-1"
      />
    </q-breadcrumbs>
    </div>
    <div
      style="margin: 0px 10px 10px 10px"
      class="bg-panda-bg-6 shadow-3 border-radius-4px"
    >
      <div>
        <div
          id="top2"
          style="min-width: 1600px; overflow-x: hidden"
          class="row line-height-30px items-center text-panda-text-7 bg-panda-bg-6 pb10x pt10x border-radius-4px"
        >
          <!--请输入赛事id-->
           <div class="append-handle-btn-input ml10x w-180 position-relative">
            <a-input
              v-model="searchForm.matchId"
              :placeholder="$t('internal.placeholder.label73')"
              @keydown.enter="handle_search"
              autocomplete="off"
              allowClear
            >
              <my-icon
                slot="suffix"
                type="p-icon-chazhao"
                class="text-panda-text-4 fs12"
              />
            </a-input>
            <div class="position-absolute select-left-border-bet"></div>
          </div>
             <!-- 请输入或选择联赛名称 -->
           <div  class="append-handle-btn-input ml10x row position-relative w-200">
            <a-input
              :placeholder="$t('internal.data.betslip.index.placeholder.fetching')"
              autocomplete="off"
               v-model="searchForm.matchName"
              allowClear
            >
              <my-icon
                slot="suffix"
                type="p-icon-chazhao"
                class="text-panda-text-4 fs12"
              />
            </a-input>
            <div class="position-absolute select-left-border-finance"></div>
            </div>
          <!--请输入用户名/用户ID-->
          <div
            class="append-handle-btn-input ml10x row w-200 position-relative"
          >
            <a-input
              :placeholder="$t('internal.finance.secondarysettlement.label4')"
              autocomplete="off"
               v-model="searchForm.userId"
              allowClear
            >
              <my-icon
                slot="suffix"
                type="p-icon-chazhao"
                class="text-panda-text-4 fs12"
              />
            </a-input>
            <div class="position-absolute select-left-border-finance"></div>
          </div>
          <!--请输入注单号-->
          <div class="append-handle-btn-input ml10x w-170 position-relative">
            <a-input
              v-model="searchForm.orderNo"
              :placeholder="$t('internal.placeholder.label72')"
              @keydown.enter="handle_search"
              autocomplete="off"
              allowClear
            >
              <my-icon
                slot="suffix"
                type="p-icon-chazhao"
                class="text-panda-text-4 fs12"
              />
            </a-input>
            <div class="position-absolute select-left-border-bet"></div>
          </div>

        </div>
      </div>
      <div class="row pb10x" style="min-width: 1600px; overflow-x: hidden">
        
          <!-- 请选择二次结算原因-->
          <div class="append-handle-btn-input position-relative ml10">
            <a-select 
            autocomplete
              style="width: 200px"
              v-model="searchForm.settleType"
            >
            <a-select-option
            :value="''"
             >{{$t('internal.finance.secondary_settlement_list_title') }}</a-select-option
           > 
               <a-select-option
               :value="index"
                 v-for="(item, index) in secondary_settlement_list"
                :key="index"
                >{{ item }}</a-select-option
              > 
            </a-select>
          </div>
          <!-- 请选择审核状态-->
          <div class="append-handle-btn-input position-relative ml10"
          v-if="page_type!=3">
            <a-select 
            autocomplete
              style="width: 200px"
              v-model="searchForm.approvalStatus"
            >
            <a-select-option
            :value="''"
             >{{$t('internal.second_settlement_initial_review.text__18') }}</a-select-option
           > 
           <a-select-option
           :value="index"
             v-for="(item, index) in state_list"
            :key="index"
            >{{ item }}</a-select-option
          > 
            </a-select>
          </div>
          <!-- 日期选择 -->
          <!-- <div
        v-if="page_type==3&&month_type==0"
          class="append-handle-btn-input ml10x w-270">
            {{$t("internal.second_settlement_initial_review.text__34")}}
            <a-range-picker 
            :show-time="true"
            showToday 
            :allowClear="false"
             :value="[
                moment(searchForm.startTime, 'YYYY-MM-DD'),
                moment(searchForm.endTime, 'YYYY-MM-DD'),
              ]"
              @change="on_change"
             />
          </div> -->
        <!--搜索-->
        <div class="append-handle-btn-input ml10x height-30px line-height-30px">
          <a-button type="primary" style="height: 30px; line-height: 30px" @click="handle_search">{{
            $t("internal.search")
          }}</a-button>
        </div>

        <div class="ml10x">
          <!--导出-->
            <q-btn
              class="panda-btn-primary-dense bg-primary mr5x"
              style="width: 68px; height: 30px"
               @click="handle_export_excel"
            >
              {{ $t("internal.finance.secondarysettlement.label9") }}
            </q-btn>
        </div>
        <q-space />
        <!--返回-->
        <div class="append-handle-btn-input ml10x height-30px line-height-30px mr10x">
          <a-button type="primary" style="height: 30px; line-height: 30px" @click="handle_back">{{
            $t("internal.back")
          }}</a-button>
        </div>
      </div>
      
      <!-- 表格数据 -->
      <a-table
        :columns="columns"
        :data-source="tabledata"
        :scroll="{ x: 1500,y: computed_scroll_y}"
        size="middle"
        :loading="tabledata_loading"
        :pagination="false"
        :rowKey="(record) => record.id"
      >
      <!-- 全选 列 待处理-->
      <div  slot="all_select_header">
        <!-- 全选 -->
        <a-checkbox v-model="cancel_all_selected" @change="handle_cancel_all_selected_change()">
          {{ $t("external.label.label45") }}
        </a-checkbox>
      </div>
      
      <!-- 全选 列 待处理-->
      <div slot="all_select" slot-scope="text, record, index">
        <div v-if="[0,1,2].includes(record.approvalStatus)">
            <a-checkbox v-model="record.selected" @change="handle_cancel_order_input_changed(record)">
            </a-checkbox>
        </div>
        </div>
        <!--用户名-->
        <div slot="username" slot-scope="text, record" class="tdpadding">
          <div>
            <div
              class=" cursor-pointer"
              @click="handleCopy(record.username, $t('internal.finance.secondarysettlement.config.2'))"
            >
              {{ record.username }}
            </div>
          </div>
        </div>
       <!--用户ID-->
        <div slot="uid" slot-scope="text, record" class="tdpadding">
          <div>
            <div
              class="cursor-pointer"
              @click="handleCopy(record.uid, $t('internal.finance.secondarysettlement.config.1'))"
            >
              {{ record.uid }}
            </div>
          </div>
        </div>
        <!--注单号-->
        <div slot="orderNo" slot-scope="text, record" class="tdpadding">
          <div>
            <div
              class="cursor-pointer"
              @click="handleCopy(record.orderNo, $t('internal.finance.secondarysettlement.config.5'))"
            >
              {{ record.orderNo }}
            </div>
          </div>
        </div>
      <!--赛事ID-->
        <div slot="matchId" slot-scope="text, record" class="tdpadding">
          <div>
            <div
              class="cursor-pointer"
              @click="handleCopy(record.matchId, $t('internal.finance.secondarysettlement.config.8'))"
            >
              {{ record.matchId }}
            </div>
          </div>
        </div>
        <div slot="settleType" slot-scope="text, record" class="tdpadding">
          <!-- 二次结算原因 -->
          <div>
              {{ secondary_settlement_list[record.settleType] }}
          </div>
        </div>
       <!-- 状态 -->
        <div slot="approvalStatus" slot-scope="text, record" class="tdpadding">
        <span :class="set_color_by_state(record)">{{$t("internal.finance.state_list")[record.approvalStatus]}}</span>
        </div>
        
          <!-- 操作 -->
          <div slot="action" slot-scope="text, record" class="tdpadding"> 
            <div v-if="record.rejectTimes!=0&&page_type==3"> 
              <!-- 驳回次数 -->
              <span class="pl10x text-blue cursor-pointer" @click="show_reason_for_rejection(record)">
              {{ $t("internal.finance.secondarysettlement.label28") }}
            </span></div>
            <span v-else-if="[4,5,6,7].includes(record.approvalStatus)" class="pl10x text-blue cursor-pointer" @click="show_reason_for_rejection(record)">
              {{ $t("internal.finance.secondarysettlement.label16") }}
            </span>
            <span v-if="[0,1,2].includes(record.approvalStatus)" class="text-red cursor-pointer"   @click="handle_rejection(record)">  
              {{ $t("internal.second_settlement_initial_review.text__24") }}</span>
           </div>
      <template slot="footer">
        <a-pagination
        v-if="tabledata.length>0"
        :total="pagination.total"
        :current="pagination.current"
        show-size-changer 
        show-quick-jumper
        :page-size-options="pagination.pageSizeOptions"
        :page-size="pagination.pageSize"
        :show-total="total => $t('internal.showTotal_text',[pagination.total])"
        @change="onChange"
        @showSizeChange="onShowSizeChange"
      />
    <div
        class="fs16 position-absolute line-height-24px"
        style="bottom: 10px; left: 25px;"
        v-if="tabledata.length > 0 "
      >
      <div>
       <span class="row ml10x" v-if="tabledata.length>0">
        <!-- 待处理总额 -->
          <span 
          v-if="page_type!=3">
            <span class="title-grey">{{ $t("internal.finance.secondarysettlement.label12") }}:</span>
             <span class="text-red">
          {{addCommas(order_totals.minusAmountTotal)}}
         </span>    
          </span>
          <!-- 赔付总金额 -->
          <span class="pr10x" v-else>
            <span class="title-grey">{{ $t("internal.second_settlement_initial_review.text__12") }}:</span>
             <span class="fw_600 text-red">
              {{addCommas(order_totals.secSettleCompTotal)}}
        </span>
      </span>
      <!--批量驳回-->
      <a-button
      v-if="page_type!=3&&show_handle_rejection_btn"
      type="primary"
      class="ml10x"
      @click="handle_rejection"
      >{{ $t("internal.second_settlement_initial_review.text__26") }}
      </a-button>
      </span>
      
    </div>
  </div> 
       </template>
       </a-table>
    </div>
    <!-- 报表下载弹窗 -->
    <q-dialog v-model="exportExcelShow" persistent teansition-show="scale" transition-hide="scale">
       <dialog-excel :export_param="export_param">
      </dialog-excel>
    </q-dialog>
    <!-- 驳回原因 -->
    <q-dialog v-model="show_reason_for_rejection_detail" persistent teansition-show="scale" transition-hide="scale">
      <reason_for_rejection_dialog :detailObj="detailObj"></reason_for_rejection_dialog>
    </q-dialog>
    <!-- 驳回弹窗 -->
      <q-dialog v-model="show_rejection_confirm" persistent teansition-show="scale" transition-hide="scale">
        <dialog_rejection
        :detailObj="detailObj"
        :cancel_arr="cancel_arr"
        @close_dialog="handle_break"/>
       </q-dialog>
  </div>
</template>

<script>
import { i18n } from "src/boot/i18n";
import { mapGetters } from "vuex";
import { api_finance } from "src/api/index.js";
import mixins from "src/mixins/internal/index.js";
import financesorter from "src/mixins/internal/module/financesorter.js";
import commonmixin from "src/mixins/internal/common/commontoolmixin.js";
import constantmixin from "src/mixins/internal/common/constantmixin.js";
import financeMixin from "src/pages/internal/finance/liquidation/mixin/index.js";
import dataCenterMixin from "src/mixins/internal/module/datacentertablemixin.js";
import datacentertablemixin from "src/components/common/bet_slip/internal/mixin/datacentertablemixin.js";
import moment from "moment";
import { columns_by_timeType } from "src/pages/internal/second_settlement_initial_review_order_details/config/config.js"; 
import { handleCopy,revise_userId_and_fakeName} from "src/util/module/common.js";
import dialogExcel from "src/components/common/dialog/dialogExcel.vue"
import treeSelect from "src/components/common/tree/tree_select.vue";
import reason_for_rejection_dialog from "src/components/common/secondary_settlement/component/reason_for_rejection_dialog.vue"

import dialog_rejection from "src/pages/internal/second_settlement_initial_review/component/dialog_rejection.vue"



let mixins_custom=  [...mixins, financesorter, commonmixin, constantmixin, financeMixin,dataCenterMixin,datacentertablemixin];
export default {
  mixins: [...mixins_custom],
  components: {
    treeSelect,
    dialogExcel,
    reason_for_rejection_dialog,
    dialog_rejection
  },
  data() {
    return {
      detailObj:{},
      set_cards: [
        { title1:i18n.t("internal.menujs.payout_bets") },
        { title1:i18n.t("internal.second_settlement_initial_review.text__36") },
        { title1:i18n.t("internal.second_settlement_initial_review.text__37") },
        { title1:i18n.t("internal.second_settlement_initial_review.text__38") },
      ],
      // 驳回原因
      show_rejection_confirm:false,
      //历史驳回原因
      show_reason_for_rejection_detail:false,
      tabledata: [], // 表格数据
      //请选择二次结算原因
      secondary_settlement_list:i18n.t("internal.finance.secondary_settlement_list"),
      //是否赔偿
      compensation_list:i18n.t("internal.finance.compensation_list"),
      timeType_list: i18n.t("internal.filters.timeTypelist"), //结算时间 timeType 1:结算时间  2:最后一次账变时间  3:第一次账变时间
      export_param:{},//导出报表参数
      exportExcelShow:false,//导出弹窗显示控制
      order_totals:{},//总额数据 
      tabledata_loading: false, //表格loading
      searchForm: {
        startTime: `${moment(new Date().setDate(new Date().getDate() - 2)).format("YYYY-MM-DD")} 00:00:00`, //开始日期
        endTime: `${moment(new Date().setDate(new Date().getDate())).format("YYYY-MM-DD")} 23:59:59`, // 结束日期 
        matchId: "", // 赛事ID
        userId: "", // 用户ID
        orderNo: "", //注单号
        settleType: "", //二次结算原因/变动原因
        //审核
        approvalStatus:"",
        //当前额度大于0
        matchName:"",
        merchantName: "", // 商户名称
      },
      //大于0
      is_amountMin:false,
      //批量修改-是否赔偿-默认值否
      is_compensation:2,
      cancel_num: 0,
    //---------------------------全选---start-------------------
      cancel_all_selected: false, // 取消全选
      cancel_arr: [], //准备去取消的  原始数据集合
      //---------------------------全选----end-------------------
    };
  },
  created() {
    this.init_date_time()
  this.initTableData();
  },
  computed: {
    ...mapGetters(['get_user_info', "get_dom",]),
    
    show_handle_rejection_btn(){
     return this.tabledata.find(item=>[0,1,2,5,7].includes(item.approvalStatus))
    },
    month_type(){
      return this.$route.query.month_type||0
    },
    page_type(){
      if(this.$route.name == "second_settlement_initial_review_order_details"){
        //初次审核
        return 1
      }else if(this.$route.name == "second_settlement_and_second_review_order_details"){
        //二次审核
        return 2
      } else if(this.$route.name == "payout_bets_order_details"){
        //赔付注单
        return 3
      }
    },
    state_list(){
      let state_list=i18n.t("internal.finance.state_list")
      if(1){
        //初次审核
      return {
        "1":state_list[1],
        "4":state_list[4],
      }
      }else{
        //二次审核
      return {
        "2":state_list[2],
        "4":state_list[6],
      }
      }
  },
    columns(){
      return columns_by_timeType(this.page_type)
    },
    computed_scroll_y({is_expand,scrollHeight,cancel_num}) {
      let num = 0;
      num = !is_expand ? scrollHeight - 45 : scrollHeight;
      num = cancel_num > 0 ? num - 40 : num;
      return num;
    },
  },
  methods: {
    moment,
    handleCopy,
    columns_by_timeType,

     // 添加千位符
     addCommas(num) {
      return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    
    /*
    * 审核驳回
    */
    handle_rejection(record){
      this.detailObj=""
      if(record){
        this.detailObj=record
      }
      this.show_rejection_confirm=true
    },
    handle_break(value){
      this.show_rejection_confirm=false
      if(value){
      this.initTableData();
      }
    },
    handle_back() {
      this.$router.go(-1);
    },
    
    //---------------------------全选---start-------------------
    
    // 全选 全部取消按钮
    handle_cancel_all_selected_change() {
      let all;
      all = this.cancel_all_selected;
      this.cancel_arr = [];
      if (all) {
        //全选
        this.tabledata.map((item) => {
          if([0,1,2].includes(item.approvalStatus)){
            item.selected = true;
          }
          this.cancel_arr.push(item);
        });
      } else {
        // 取消全选
        this.tabledata.map((item) => {
          if([0,1,2].includes(item.approvalStatus)){
          item.selected = false;
        }
          this.cancel_arr = [];
        });
      }
        this.compute_cancel_money_and_num();
   
    },
    //清空 已选中的 准备取消的 
    handle_clear_cancel_arr() {
      if(this.cancel_arr.length!=0){
        this.cancel_all_selected=false
        //清空取消注单
        
        this.handle_cancel_all_selected_change()
      }
    },
    // 取消
    handle_cancel_order_input_changed(record) {
      //取消
      // cancel_arr:[] ,//准备去取消的  原始数据集合
      let selected = record.selected;
      if (selected) {
        //选中
        this.cancel_arr.push(record);
      } else {
        // 取消选中
        this.cancel_arr=this.cancel_arr.filter(item=>item.id != record.id)
      }
      this.compute_cancel_money_and_num();
      this.$forceUpdate();
    },
    //计算 取消的 总数 和 总 金额 以及  取消注单全选  状态
    compute_cancel_money_and_num() {
      if(this.all_can_cancel_num !=0){
        this.cancel_all_selected = this.cancel_arr.length == this.all_can_cancel_num;
      }
    },
    
    //---------------------------全选----end-------------------
  
    /*
    * 驳回
    */
    show_reason_for_rejection(record){
      this.detailObj=record
      this.show_reason_for_rejection_detail=true
    },
    set_color_by_state(record){
      if([4,5,6,7].includes(record.approvalStatus)){
        return 'text-red'
      }else if([3].includes(record.approvalStatus)){
        return 'text-green'
      }
    },
    init_date_time(){
      let gt12 = moment().format("HH") > 12;
      let new_day=gt12?1:0
      this.searchForm.jssjtime= `${moment(new Date().setDate(new Date().getDate() - 2+new_day)).format("YYYY-MM-DD")}`
    },
    disabledDate(current) {
      let gt12 = moment().format("HH") > 12;
      let new_day=gt12?1:0
      return (
        current.isBefore(moment(Date.now()).add(-62+new_day, "days")) ||
        current.isAfter(moment(Date.now()).add(-2+new_day, "days"))
      );
    },
    // 搜索
    handle_search() {
      this.initTableData();
    },
    // 初始化表格数据
    initTableData() {
      this.tabledata_loading = true;
      this.cancel_arr=[]
      let params = this.compute_init_tabledata_params();
      params = this.delete_empty_property_with_exclude(params);
      api_finance
        .post_secondary_settlemen_pageList(params)
        .then(res => {
          this.tabledata_loading = false;
          let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          if (code == "0000000") {
            let currentPage =
              this.$lodash.get(res, "data.pageInfo.current") * 1 || 1;
            let arr = this.$lodash.get(res, "data.data.pageInfo.records") || [];
            this.order_totals =  this.$lodash.get(res, "data.data")
            this.pagination.start = this.$lodash.get(res, "data.data.pageInfo.current");
            this.tabledata = this.rebuild_tabledata_to_needed(arr);
            this.pagination.total = this.$lodash.get(res, "data.data.pageInfo.total") * 1 || 0;   
          } else {
            this.$message.error(msg, 5);
          }
        });
    },
    rebuild_tabledata_to_needed(arr) {
      if(!arr || !arr.length) {
        return []
      }
      // 就散这一页数据 能取消的所有 注单的总数
      let all_can = 0;
      arr.map((item, index) => {



        item.selected = false;
        all_can += 1;
        item._index =
          (this.pagination.current - 1) * this.pagination.pageSize + index + 1;
      });
        this.all_can_cancel_num = all_can;
        this.cancel_arr = [];
        this.compute_cancel_money_and_num();
      return arr;

    },
        compute_init_tabledata_params() {
      let { pageSize, sort, orderBy } = this.pagination;
      let {
        // startTime, //开始日期
        // endTime, // 结束日期
        approvalStatus,
        matchId ,// 赛事ID
        userId ,// 用户ID
        orderNo, //注单号
        //当前额度大于0
        settleType, //二次结算原因/变动原因
        matchName,
        merchantName,
      } = this.searchForm;
      
      let params = {
        // timeType:4,
        // startTime:new Date(startTime).getTime(), //开始日期
        // endTime:new Date(endTime).getTime(), // 结束日期
        approvalStatus,
        matchId,// 赛事ID
        userId ,// 用户ID
        orderNo, //注单号
        //当前额度大于0
        settleType, //二次结算原因/变动原因
        matchName,
        merchantName,
        pageNum: this.pagination.current, //分页，查询第几页数据。
        pageSize, //分页，每页查询多少条，默认20条。可不传
      };
       revise_userId_and_fakeName(params,this.src_external?false:true);
       
      // if(this.$route?.query?.batchNo){
        params.batchNo=this.$route?.query?.batchNo
    // }
      return params;
    },
    // 导出报表
    handle_export_excel(){
    if(this.pagination.total>0){
             let params = this.compute_init_tabledata_params();
             params = this.delete_empty_property_with_exclude(params)  
             if(this.src_external){
             Object.assign(params,{url:"/admin/orderTimeSettle/export"})
             }else{

              Object.assign(params,{url:"/order/orderTimeSettle/export"})
             }
             this.export_param = params;
             this.exportExcelShow = true;
    }else{
      this.handle_error()
    }
    },
  },
};
</script>

<style lang="scss" scoped></style>
