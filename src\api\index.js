/*
 * @FilePath: /src/api/index.js
 * @Description: 
 */

//公共 


const FOR_INTERNAL = process.env.FOR_INTERNAL;
import axios from 'src/api/common/axioswarpper.js'
const FILE_PREFIX =axios.prototype.API_PREFIX_FILE_REAL_YEWU
const FILE_PREFIX_FINANCE =  axios.prototype.API_PREFIX_FILE_FINANCE


//对 内 
import * as internal_api_home from "src/api/internal/module/home/<USER>"
import * as internal_api_log from "src/api/internal/module/log/index.js"
import * as internal_api_login from "src/api/internal/module/login/index.js"
import * as internal_api_account from "src/api/internal/module/account/index.js"
import * as internal_api_merchant from "src/api/internal/module/merchant/index.js"
import * as internal_api_public from "src/api/internal/module/public/index.js"
import * as internal_api_base from "src/api/internal/module/base/index.js"
import * as internal_api_user from "src/api/internal/module/user/index.js"
import * as internal_api_finance from "src/api/internal/module/finance/index.js"
import * as internal_api_export from "src/api/internal/module/export/index.js"
import * as internal_api_message from "src/api/internal/module/message/index.js"
import internal_api_operate from "src/api/internal/module/operate/index.js"
import * as internal_api_system from "src/api/internal/module/system/index.js";
import * as internal_api_set from "src/api/internal/module/set/index.js"
import * as internal_api_auth from "src/api/internal/module/auth/index.js"
import * as internal_api_article from "src/api/internal/module/match_article/index.js"
import * as internal_api_live from "src/api/internal/module/match_live/index.js";
import * as internal_api_front_end_domain_query from "src/api/internal/module/front_end_domain_query/index.js";
import * as internal_api_match_statistic from "src/api/internal/module/match_statistic/index.js";
import * as internal_api_advertising_space from "src/api/internal/module/advertising_space/index.js";
import * as internal_api_risk_control_set from "src/api/internal/module/risk_control_set/index.js";       // 风控查询 - 危险联赛池管理&&危险球队池管理
import * as internal_ship_order_management from "src/api/internal/module/ship_order_management/index.js"  // 风控查询 - 出货单管理
import * as internal_champion_boot_disk from "src/api/internal/module/champion_boot_disk/index.js"  // 冠军引导盘
import * as internal_activity_management from "src/api/internal/module/activity_manage/index.js"  // 运营管理 - 活动管理 烟花


//对 外 
import * as external_api_home from "src/api/external/module/home/<USER>"
import * as external_api_log from "src/api/external/module/log/index.js"
import * as external_api_login from "src/api/external/module/login/index.js"
import * as external_api_account from "src/api/external/module/account/index.js"
import * as external_api_auth from "src/api/external/module/auth/index.js"
import * as external_api_merchant from "src/api/external/module/merchant/index.js"
import * as external_api_public from "src/api/external/module/public/index.js"
import * as external_api_base from "src/api/external/module/base/index.js"
import * as external_api_user from "src/api/external/module/user/index.js"
import * as external_api_data from "src/api/external/module/data/index.js"
import * as external_api_finance from "src/api/external/module/finance/index.js"
import * as external_api_export from "src/api/external/module/export/index.js"
import * as external_api_message from "src/api/external/module/message/index.js"
import * as external_api_match_statistic from "src/api/external/module/match_statistic/index.js";
import * as external_api_advertising_space from "src/api/external/module/advertising_space/index.js";
import * as external_api_risk_control_set from "src/api/external/module/risk_control_set/index.js";       // 风控查询 - 危险联赛池管理&&危险球队池管理
import * as external_ship_order_management from "src/api/external/module/ship_order_management/index.js"  // 风控查询 - 出货单管理
import * as external_champion_boot_disk from "src/api/external/module/champion_boot_disk/index.js"  // 冠军引导盘
import  external_api_operate_vip from "src/api/external/module/operate/index.js"


 
   //对内
   const api_home  = FOR_INTERNAL?internal_api_home :external_api_home
   const api_log  = FOR_INTERNAL?internal_api_log :external_api_log
   const api_login   = FOR_INTERNAL?internal_api_login  :external_api_login 
   const api_account  = FOR_INTERNAL?internal_api_account :external_api_account
   const api_auth  = FOR_INTERNAL?internal_api_auth :external_api_auth
   const api_merchant  = FOR_INTERNAL?internal_api_merchant :external_api_merchant
   const api_public  = FOR_INTERNAL?internal_api_public :external_api_public
   const api_base  = FOR_INTERNAL?internal_api_base :external_api_base
   const api_user  = FOR_INTERNAL?internal_api_user :external_api_user
   const api_finance  = FOR_INTERNAL?internal_api_finance :external_api_finance
   const api_export  = FOR_INTERNAL?internal_api_export :external_api_export
   const api_message  = FOR_INTERNAL?internal_api_message :external_api_message
   const api_operate  = FOR_INTERNAL?internal_api_operate :external_api_operate_vip
   const api_system  = FOR_INTERNAL?internal_api_system :''
   const api_set  = FOR_INTERNAL?internal_api_set:''
   const api_data  = FOR_INTERNAL? '':external_api_data
   const api_article  = FOR_INTERNAL?internal_api_article:''
   const api_live = FOR_INTERNAL ? internal_api_live : '';
   const api_front_end_domain_query = FOR_INTERNAL ? internal_api_front_end_domain_query : '';
   const api_match_statistic = FOR_INTERNAL
     ? internal_api_match_statistic
     : external_api_match_statistic;
   const api_advertising_space = FOR_INTERNAL
      ? internal_api_advertising_space
      : external_api_advertising_space;
      
   const api_risk_control_set = FOR_INTERNAL
      ? internal_api_risk_control_set
      : external_api_risk_control_set;

   const api_ship_order_management = FOR_INTERNAL
      ? internal_ship_order_management
      : external_ship_order_management;

   const api_champion_boot_disk = FOR_INTERNAL ?  internal_champion_boot_disk : external_champion_boot_disk
 

export  {
   //对内
   api_home ,  //首页模块
   api_login,  //登录模块
   api_log,   //日志模块
   api_account,  //账号管理模块
   api_auth, //授权中心
   api_merchant,// 商户模块
   api_public,//公共模块
   api_base,// 基础
   api_user,// 用户
   api_finance,// 财务中心
   api_export,// 报表导出
   api_message, //消息中心
   api_article, //赛事文章
   api_live,  // 赛事直播
   api_operate ,//运营管理
   api_advertising_space,//广告位管理
   api_system,
   api_set,// 设置中心下的 域名池管理 域名切换日志 和域名设置 
   api_front_end_domain_query,//数据中心/前端域名查询
   api_match_statistic, // 即时赛事统计报表
   api_risk_control_set, //风险池管理
   api_ship_order_management, //出货单管理
   internal_activity_management, //活动管理
   //对外
   api_data, // 数据中心
   FILE_PREFIX,
   FILE_PREFIX_FINANCE,
   api_champion_boot_disk //冠军引导盘
};

