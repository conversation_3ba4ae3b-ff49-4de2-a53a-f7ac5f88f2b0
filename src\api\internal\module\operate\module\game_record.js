/*
 * @FilePath: d:/projects/new-merchant-management/src/api/internal/module/operate/module/game_record.js
 * @Description:游戏记录接口
 */

import axios from "src/api/common/axioswarpper.js";


let prefix_1 = process.env.API_PREFIX_4;

// 彩金记录列表
export const get_jackpot_record = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/jackpotRecord`;
  return axios.post(`${prefix}${url}`, params);
}


// 彩金记录导出
export const export_jackpot_record = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/jackpotRecordExport`;
  return axios.post(`${prefix}${url}`, params, {
    responseType: "blob"
  });
}

// 合成记录列表
export const get_synthetic_record = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/syntheticRecord`;
  return axios.post(`${prefix}${url}`, params);
}


// 合成记录导出
export const export_synthetic_record = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/syntheticRecordExport`;
  return axios.post(`${prefix}${url}`, params, {
    responseType: "blob"
  });
}

// 重置记录列表
export const get_reset_record = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/resetRecord`;
  return axios.post(`${prefix}${url}`, params);
}


// 重置记录导出
export const export_reset_record = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/resetRecordExport`;
  return axios.post(`${prefix}${url}`, params, {
    responseType: "blob"
  });
}
