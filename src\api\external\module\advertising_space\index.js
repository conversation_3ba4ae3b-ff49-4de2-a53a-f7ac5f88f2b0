/*
 * @FilePath: /src/api/external/module/advertising_space/index.js
 * @Description: 广告位管理
 */

import axios from "src/api/common/axioswarpper.js";
let prefix_1 = process.env.API_PREFIX_17;
// 广告位列表
export const getadvertisinglist = (
  params,
  url = "/admin/aggregateFestival/listAnn"
) => axios.post(`${prefix_1}${url}`, params, { type: 1 });

// 操作列表
export const getstateAnnList = (params) => {
  let prefix = prefix_1;
  let url = `/admin/aggregateFestival/stateAnnList`;
  return axios.get(`${prefix}${url}`, { params: { ...params } });
};

//广告开关
export const setenableBatchAnn = (
  params,
  url = "/admin/aggregateFestival/enableBatchAnn"
) =>
  axios.post(`${prefix_1}${url}`, params, {
    type: 1,
  });

//保存
export const addAnn = (params) => {
  let prefix = prefix_1;
  let url = `/admin/aggregateFestival/addAnn`;
  return axios.post(`${prefix}${url}`, params);
};

//编辑
export const updateAnn = (params) => {
  let prefix = prefix_1;
  let url = `/admin/aggregateFestival/updateAnn`;
  return axios.post(`${prefix}${url}`, params);
};

//删除
export const delAnn = (params, url = "/admin/aggregateFestival/delAnn") =>
  axios.post(`${prefix_1}${url}`, params, {
    type: 1,
  });

//获取平台类型  /yewu9/manage/aggregateFestival/getPlatformAnn
export const getPlatformAnn = (
  url = "/admin/aggregateFestival/getPlatformAnn"
) => axios.get(`${prefix_1}${url}`);
