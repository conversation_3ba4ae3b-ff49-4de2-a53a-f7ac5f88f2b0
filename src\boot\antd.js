/*
 * @FilePath: /src/boot/antd.js
 * @Description: 
 */
import Vue from 'vue'
const FOR_WHICH = process.env.FOR_WHICH;
const icon_src_internal= "//at.alicdn.com/t/font_1635903_lojcfbx9t0k.js" 
const icon_src_external ='//at.alicdn.com/t/font_1635903_3tzkrotnx76.js'
let icon_src=  FOR_WHICH=='internal'?icon_src_internal:icon_src_external

import { default as Space } from 'ant-design-vue/lib/space';

import { 
  Tree,
  Button, 
  Table, 
  Menu, 
  Layout, 
  Col, 
  Row, 
  Dropdown, 
  Icon,
  LocaleProvider,
  ConfigProvider,
  Tooltip, 
  Progress, 
  Tabs, 
  Radio, 
  DatePicker, 
  TimePicker,
  Select, 
  Upload, 
  message, 
  Badge, 
  Popconfirm, 
  Input, 
  InputNumber, 
  Form, 
  Checkbox, 
  Modal, 
  Popover, 
  TreeSelect, 
  Pagination, 
  Spin,
  FormModel,
  Switch,
  Divider,
 } from 'ant-design-vue';

const MyIcon = Icon.createFromIconfontCN({
  scriptUrl: icon_src, // 在 iconfont.cn 上生成
});


Vue.component('my-icon', MyIcon)

const antdModule = { 
  Tree,
  Button, 
  Table, 
  Menu, 
  Layout, 
  Col, 
  Row, 
  Dropdown, 
  Icon,
  LocaleProvider,
  ConfigProvider,
  Tooltip, 
  Progress, 
  Tabs, 
  Radio, 
  DatePicker, 
  TimePicker,
  Select, 
  Upload, 
  message, 
  Badge, 
  Popconfirm, 
  Input, 
  InputNumber, 
  Form, 
  Checkbox, 
  Modal, 
  Popover, 
  TreeSelect, 
  Pagination, 
  Spin,
  FormModel,
  Switch,
  Divider,
  Space
};


// Object.keys(antdModule).map(key => Vue.component(key, antdModule[key]))


Vue.prototype.$message = message;
Vue.prototype.$modal = Modal;




// import 'src/css/antd/index.less'

Object.keys(antdModule).map(key => Vue.use(antdModule[key]))
