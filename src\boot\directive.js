/*
 * @FilePath: /src/boot/directive.js
 * @Description: 指令相关
 */
import Vue from 'vue';
import { SessionStorage } from 'quasar'
import { message } from "ant-design-vue";
import { i18n } from "src/boot/i18n";
// v-dialogDrag: 弹窗拖拽属性
Vue.directive('drag', {
    bind(el, binding, vnode, oldVnode) {
        console.warn(el);
        const dialogHeaderEl = el.querySelector('.q-card__section');
        // const dragDom = el.querySelector('.q-dialog__inner') || el;
        const dragDom = document.querySelector('.q-dialog-set') || el;
        console.warn(dialogHeaderEl.offsetLeft, dialogHeaderEl.offsetTop);
        dialogHeaderEl.style.cssText += ';cursor:move;'
        // dragDom.style.cssText += ';top:0px;'

        // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
        const sty = (() => {
            if (window.document.currentStyle) {
                return (dom, attr) => dom.currentStyle[attr];
            } else {
                return (dom, attr) => getComputedStyle(dom, false)[attr];
            }
        })()

        dialogHeaderEl.onmousedown = (e) => {
            // 鼠标按下，计算当前元素距离可视区的距离
            const disX = e.clientX - dialogHeaderEl.offsetLeft;
            const disY = e.clientY - dialogHeaderEl.offsetTop;

            const screenWidth = document.body.clientWidth; // body当前宽度
            const screenHeight = document.documentElement.clientHeight; // 可见区域高度(应为body高度，可某些环境下无法获取) 

            const dragDomWidth = dragDom.offsetWidth; // 对话框宽度
            const dragDomheight = dragDom.offsetHeight; // 对话框高度

            const minDragDomLeft = dragDom.offsetLeft;
            const maxDragDomLeft = screenWidth - dragDom.offsetLeft - dragDomWidth;

            const minDragDomTop = dragDom.offsetTop;
            const maxDragDomTop = screenHeight - dragDom.offsetTop - dragDomheight;


            // 获取到的值带px 正则匹配替换
            let styL = sty(dragDom, 'left');
            let styT = sty(dragDom, 'top');
            console.warn(styL,styT,'styT')
            // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
            if (styL.includes('%')) {
                styL = +document.body.clientWidth * (+styL.replace(/\%/g, '') / 100);
                styT = +document.body.clientHeight * (+styT.replace(/\%/g, '') / 100);
            } else {
                styL = +styL.replace(/\px/g, '');
                styT = +styT.replace(/\px/g, '');
            };

            document.onmousemove = function (e) {
                // 通过事件委托，计算移动的距离 
                let left = e.clientX - disX;
                let top = e.clientY - disY;

                // 边界处理
                if (-(left) > minDragDomLeft) {
                    left = -(minDragDomLeft);
                } else if (left > maxDragDomLeft) {
                    left = maxDragDomLeft;
                }

                if (-(top) > minDragDomTop) {
                    top = -(minDragDomTop);
                } else if (top > maxDragDomTop) {
                    top = maxDragDomTop;
                }

                // 移动当前元素  
                // console.log(left,styL,top, styT);
                dragDom.style.cssText += `;left:${left + styL}px;top:${top + styT}px; position: fixed`;
            };

            document.onmouseup = function (e) {
                document.onmousemove = null;
                document.onmouseup = null;
            };
        }
    }
})

Vue.directive('pin', {
    bind: function (el, binding, vnode) {
        // console.log(el,binding)
        el.style.position = 'fixed'
        el.style.top = 200 + 'px'
    }
})

/**
 * 示例：
 * v-copy="{msg: $t('internal.username_text'), value: text}"      复制用户名:ty4YI4OVf4G7 成功！
 * v-copy="{value: text}"                                         复制: ty4YI4OVf4G7 成功！
 */
Vue.directive('copy', {
    bind(el,bind) {
        el.classList.add("cursor-pointer");
        let {value,msg} =bind.value
        value = value ? value:bind.value
        // console.warn(value,bind);
        if(bind.modifiers.msg){
            el.$msg = msg
        } else {
            el.$msg = msg
              ? `${i18n.t("internal.template.label167")}${msg}: ${value} ${i18n.t("internal.message.label110")}`
              : `${i18n.t("internal.template.label167")}: ${value} ${i18n.t("internal.message.label110")}`;
        }
        el.$value = value ? value:bind.value
        el.handler = () => {
            if (!el.$value) {
                // 值为空的时候，给出提示。可根据项目UI仔细设计
                console.log('无复制内容')
                return
            }
            // 动态创建 textarea 标签
            const textarea = document.createElement('textarea')
            // 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域
            textarea.readOnly = 'readonly'
            textarea.style.position = 'absolute'
            textarea.style.left = '-9999px'
            // 将要 copy 的值赋给 textarea 标签的 value 属性
            textarea.value = el.$value
            // 将 textarea 插入到 body 中
            document.body.appendChild(textarea)
            // 选中值并复制
            textarea.select()
            const result = document.execCommand('Copy')
            if (result) {
                if(el.$msg){
                    message.success(`${el.$msg}`);
                }else{
                    message.success(`${el.$value}`);
                }
            }
            document.body.removeChild(textarea)
        }
        // 绑定点击事件，就是所谓的一键 copy 啦
        el.addEventListener('click', el.handler)
    },
    // 当传进来的值更新的时候触发
    componentUpdated(el, bind) {
        el.$value = bind.value.value?bind.value.value:bind.value
    },
    // 指令与元素解绑的时候，移除事件绑定
    unbind(el) {
        el.classList.remove("cursor-pointer");
        el.removeEventListener('click', el.handler)
    },
})

/**
 * 示例：
 * v-line-clamp="2"  超过2行省略... 
 */
Vue.directive("line-clamp", {
  bind(el, binding) {
    if (binding.value) {
      el.classList.add("ant-line-clamp");
      el.style["-webkit-line-clamp"] = binding.value;
    }
  },
  update(el, binding) {
    if(binding.value) {
      el.style["-webkit-line-clamp"] = binding.value;
    }
  },
  unbind(el, binding) {
    el.classList.remove("ant-line-clamp");
    el.style["-webkit-line-clamp"] = null;
  },
});

// 对内-权限判断
Vue.prototype.$_has = function (value) {
    let isExist = false;
    //  获取拥有的权限
    let userInfo = SessionStorage.getItem("userInfo")
    let permissions = userInfo.permissions || []; // 资源权限
    
    // console.log( resource,perObj )
    if(!permissions) return isExist
    isExist = permissions.includes(value)// 某个权限的对象
    return isExist;
  };

  // 按钮权限判断
  Vue.prototype.$_btn_has = function (value) {
    //  获取拥有的权限
    let userInfo = SessionStorage.getItem("userInfo")
    const roles_list = userInfo.roles || [];
  
    return roles_list.includes(value);
  
};

  /**
 * @description: 后台操作提示语
 * @param {Number} code 返回状态码
 * @param {String} msg 返回提示的字符串
 * @return {undefined}
 */
  Vue.prototype.$show_msg = (code, msg) => {
    if (code == 200) {
      Vue.prototype.$message.success(i18n.t("internal.data.risk_control_query.placeholder.success_title")); //操作成功
    } else {
      console.error(msg);
      Vue.prototype.$message.error(msg); //操作异常  结果由后台抛出
    }
  };