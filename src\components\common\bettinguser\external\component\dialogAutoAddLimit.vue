<!--
 * @Desc: 对内/外-账户中心-投注用户管理-包含 特殊限额及 自动升额度 弹窗 
 * @FilePath: /src/components/common/bettinguser/external/component/dialogAutoAddLimit.vue
 -->


 <template>
  <div>

    <!-- 用户id  -->
    <span> {{ $t("internal.betting.label8")   }} : 	 {{ detailObj.userId }} </span>  
    <span style="margin-left:40px;"> {{ $t("internal.betting.label9")   }}: {{ detailObj.userName }}</span>
    <!-- 自动升额度type 6 -->
    <div v-if="detailObj.specialBettingLimitType == 6 " style="margin-top:7px" >
      <!-- 限额百分比按钮 -->
      
      <q-radio 
        style="margin-right: 140px;"  
        v-model="radio_type"
        :disable="disabled" 
        :label="special_percentage"   
        val="special_percentage"
        @input="handle_change_limit" 
      /> 

      <!-- 自动升额度按钮 -->
      <q-radio  
        v-model="radio_type"
        :disable="disabled" 
        :label="automatic_limit" 
        val="automatic_limit"
        @click="handle_change"
        
      />
    
    </div>
    <div v-if=" radio_type == 'special_percentage' " style="font-size: 14px;">
 

      <!-- 用户单日投注 -->
      <div class="row items-center justify-between"> {{ $t("internal.betting.label5") }}
        <a-select 
          v-model="modelObj.percent_danri_limit"
          :disabled="disabled"   size="small"
          style="width:105px;margin:7px 0px 0px 60px" class="disabled-white"
        >
          <a-select-option v-for="item in percentageLimit_danri_arr" :key="item" :value="item">
            {{ item }}%  
          </a-select-option>
        </a-select>
      </div>
      <!-- 用户单场投注 -->
      <div class="row items-center justify-between"> {{ $t("internal.betting.label6") }}  
        <a-select
          v-model="modelObj.percent_danchang_limit"

          :disabled="disabled" size="small"
          style="width:105px;margin:7px 0px 0px 60px" class="disabled-white"
        >
          <a-select-option v-for="item in percentageLimit_danchang_arr" :key="item" :value="item">{{ item }}%
          </a-select-option>
        </a-select>
      </div>
      <!-- 用户单注投注 -->
      <div class="row items-center justify-between"> {{ $t("internal.betting.label7") }}
        <a-select 
          v-model="modelObj.percent_danzhu_limit"

          :disabled="disabled" size="small"
          style="width:105px;margin:7px 0px 0px 60px" class="disabled-white"
        >
          <a-select-option v-for="item in percentageLimit_danzhu_arr" :key="item" :value="item">{{ item }}%
          </a-select-option>
        </a-select>
      </div>
    </div>



       <!-- 自动升额度% -->
    <div v-if=" radio_type == 'automatic_limit' &&  [6].includes(detailObj.specialBettingLimitType) "  style="font-size: 14px;">
        <div  class="mt10x"> 
          <!-- 自动升额度%  -->
          <span  style="margin-right: 70px;">{{ $t("internal.betting.label15") }}</span>
           <!-- <button style="background-color: #fff; ">1500%</button> -->
           <span class="limit_btn" style="padding: 0 12px 0 6px; border: 1px solid gray;"> {{ modelObj.autoUpgradeRatio }} </span>

        </div>
        <div class="mt10x"> 
          <!-- 自动升额度开关  -->
          <span  style="margin-right: 80px;">{{ $t("internal.betting.label16") }}</span>
    
          <span>
            <a-switch 
              v-model="autoUpgradeStatus"
              @click="handle_change_swith()"
              :checked="autoUpgradeStatus == 1 ? true : false"
              :checked-children="$t('internal.template.label177')"  
              :un-checked-children="$t('internal.template.label171')" 
              :disabled="disabled"
            />
          </span>
        </div>
      </div>



  </div>
</template>

<script>
import { i18n } from "src/boot/i18n";
import { api_account } from "src/api/index.js";

export default {
  props: {
    detailObj: {
      type: Object,
      default() {
        return {};
      }
    },
    percent_limit_obj:{
      type:Object,
      default(){
        return {}
      }
    },
    disabled:{
      type:Boolean,
      default:false
    },
    
    //   "1": "无", //   "2": "特殊百分比限额",    才能编辑
    // can_edit: false

  },
  data() {
    return {
     
      percentageLimit_arr: [1, 2, 5, 10, 25, 50, 75, 100], //原百分比
      percentageLimit_danri_arr: [1, 2, 5, 10, 25, 50, 75, 100], //单日百分比
      percentageLimit_danchang_arr: [1, 2, 5, 10, 25, 50, 75, 100], //单场百分比
      percentageLimit_danzhu_arr: [1, 2, 5, 10, 25, 50, 75, 100], //单注百分比


      special_percentage: i18n.t('internal.betting.label10'),  // 特殊百分比radio
      automatic_limit: i18n.t('internal.betting.label11'),  // 自动升额度radio
      radio_type: "special_percentage",  // 按钮
      autoUpgradeStatus:true, //升额度开关
      // integer: true, 
      modelObj:{}
    }
  },
  watch: {
    percent_limit_obj(){
      this.modelObj = JSON.parse(JSON.stringify(this.percent_limit_obj))
      this.filter_percent(this.percent_limit_obj)
  
    }
  },
  created() {
    this.checked_integer()
  },
  computed: { 
    read_percentageLimit() {
      let res = this.detailObj.rcsUserSpecialBetLimitConfigList[0].percentageLimit
      res =
        (item_list.percentageLimit * 100).toFixed(2) + "%";
        return  res
    }
  },
  methods: {
    init_data() {

      api_account
        .post_order_user_queryUserBetLimitDetail({
          userId: this.detailObj.userId
        })
        .then(res => {
          let code = this.$lodash.get(res, "data.code");
          if (code == "0000000") {
            this.specialBettingLimitRemark = this.$lodash.get(
              res,
              "data.data.specialBettingLimitRemark"
            );
            let arr =
              this.$lodash.get(
                res,
                "data.data.rcsUserSpecialBetLimitConfigList"
              ) || [];
            if(arr.length <1){ //arr.length  <1的时候，则该用户无限额
              api_account
              .post_order_user_queryUserBetLimitDetail({
                userId: this.detailObj.userId,
                type: 1
              }).then(res => {
                let code = this.$lodash.get(res, "data.code");
                if (code == "0000000") {
                  this.specialBettingLimitRemark = this.$lodash.get(
                    res,
                    "data.data.specialBettingLimitRemark"
                  );
                  let arr =
                    this.$lodash.get(
                      res,
                      "data.data.rcsUserSpecialBetLimitConfigList"
                    ) || [];
                    let  item_list = {}
                item_list.dailyPercentageLimit = 1
                item_list.singleGamePercentageLimit =  1
                item_list.singleBetPercentageLimit = 1
                this.modelObj.percent_danri_limit =   (item_list.dailyPercentageLimit * 100).toFixed(2) + "%";    //单日
              this.modelObj.percent_danchang_limit =  (item_list.singleGamePercentageLimit * 100).toFixed(2) + "%";   //单场 
              this.modelObj.percent_danzhu_limit =   (item_list.singleBetPercentageLimit * 100).toFixed(2) + "%"; //单注 
                }
            })
          }
            else {
              api_account
              .post_order_user_queryUserBetLimitDetail({
                userId: this.detailObj.userId,
                type: 2
              }).then(res => {
                let code = this.$lodash.get(res, "data.code");
                if (code == "0000000") {
                  this.specialBettingLimitRemark = this.$lodash.get(
                    res,
                    "data.data.specialBettingLimitRemark"
                  );
                  let arr =
                    this.$lodash.get(
                      res,
                      "data.data.rcsUserSpecialBetLimitConfigList"
                    ) || [];
                  let item_list = arr.find(item => item.status);
                  if(arr.length < 1) {
                    let  item_list = {}
                    item_list.dailyPercentageLimit = 1
                    item_list.singleGamePercentageLimit =  1
                    item_list.singleBetPercentageLimit = 1
                    this.modelObj.percent_danri_limit =   (item_list.dailyPercentageLimit * 100).toFixed(2) + "%";    //单日
                    this.modelObj.percent_danchang_limit =  (item_list.singleGamePercentageLimit * 100).toFixed(2) + "%";   //单场 
                    this.modelObj.percent_danzhu_limit =   (item_list.singleBetPercentageLimit * 100).toFixed(2) + "%"; //单注 
                  } else{
                    this.modelObj.percent_danri_limit =   (item_list.dailyPercentageLimit * 100).toFixed(2) + "%";    //单日
                    this.modelObj.percent_danchang_limit =  (item_list.singleGamePercentageLimit * 100).toFixed(2) + "%";   //单场 
                    this.modelObj.percent_danzhu_limit =   (item_list.singleBetPercentageLimit * 100).toFixed(2) + "%"; //单注 
                  }
                }
              })
  
            }

          }
        });
        

    },
    filter_percent(objVal) {
      let percentag_danri = objVal.percentag_dr? objVal.percentag_dr : 1 //单日限制最大值
      let percentag_danchang = objVal.percentag_dc? objVal.percentag_dc : 1 //单场限制最大值
      let percentag_danzhu = objVal.percentag_dz? objVal.percentag_dz : 1 //单注限制最大值
  
      // 单日过滤值
      if (percentag_danri && percentag_danri !== 0) {
        let percent_danri_limit = (percentag_danri * 100).toFixed(2)
        this.percentageLimit_danri_arr = this.percentageLimit_danri_arr.filter(el => el <=
          percent_danri_limit
        )
      }
      // 单场过滤值
      if (percentag_danchang && percentag_danchang !== 0) {
        let percent_danchang_limit = (percentag_danchang * 100).toFixed(2)
        this.percentageLimit_danchang_arr = this.percentageLimit_danchang_arr.filter(el => el <=
          percent_danchang_limit
        )
      }
      // 单注过滤值 
      if (percentag_danzhu && percentag_danzhu !== 0) {
        let percent_danzhu_limit = (percentag_danzhu *100 ).toFixed(2)
        this.percentageLimit_danzhu_arr = this.percentageLimit_danzhu_arr.filter(el => el <=
          percent_danzhu_limit
        )
      }
    },
    checked_integer() {
      this.radio_type = (this.detailObj.specialBettingLimitType == 6 ) ? "automatic_limit" : "special_percentage"
    },
        // 百分比、自动升额度按钮 选中
    handle_change() {
      // this.radio_type = type
    },
    handle_change_limit() {
      this.init_data()
    },
    handle_click(val) {
      console.log(val);
    },

    // 启&禁用 开关
    async handle_change_swith(record){
    // disabled   1 禁用 0启用  2-5 启用 
    // 修改传参 0 启用 1 禁用
      // try {
      //   let params = {
      //     userIds: record.userId,
      //     disabled: record.disabled == 1 ? 0: 1,
      //   }
      //   const res = await api_user.post_update_Disabled(params)
      //   const code = this.$lodash.get(res, "data.code");
      //   const msg = this.$lodash.get(res, "data.msg");
      //   if(code == "0000000"){
      //     this.$message.success(msg);
      //     this.$set(record,"disabled", record.disabled == 1 ? 0: 1)
      //   }else {
      //     this.$message.error(msg, 5);
      //   }
      // } catch (error) {
      //   console.error(error)
      // }

    },
    
  }
}
</script>

<style lang="scss" scoped>
  // .limit_text{
  //   margin-left: 30px;
  // }
  .limit_btn {
    padding: 0 10px;
    border: 1px solid gray;

  }
</style>