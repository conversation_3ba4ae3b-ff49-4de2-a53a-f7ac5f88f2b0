


import { i18n } from "src/boot/i18n";
export const online_number_table_columns_config = 
  //表格的列配置
  [
    {
      title: "",
      dataIndex: "selection-column",
      key: "selection-column",
      align: "center",
      width: "5%",
    },
    {
      title: i18n.t("internal.online_user_management.table_title.serial_number"),
      dataIndex: "serial",
      key: "serial",
      scopedSlots: { customRender: "serial" },
      align: "center",
      width: "18%",
    },
    {
    //   title: "用户名",
      title:i18n.t("internal.online_user_management.table_title.user_name"),
      dataIndex: "userName",
      key: "userName",
      align: "center",
      width: "18%",
    },
    {
    //   title: "注册时间",
      title:i18n.t("internal.online_user_management.table_title.time_of_registration"),
      dataIndex: "createTime",
      key: "createTime",
      scopedSlots: { customRender: "createTime" },
      align: "center",
      width: "18%",

    },
    {
    //   title: "最后投注时间",
      title:i18n.t("internal.online_user_management.table_title.last_bet_time"),
      dataIndex: "lastBetTime",
      key: "lastBetTime",
      scopedSlots: { customRender: "lastBetTime" },
      align: "center",
      width: "18%",
    },
    {
    //   title: "最后登录时间",
      title:i18n.t("internal.online_user_management.table_title.last_login_time"),
      dataIndex: "loginTime",
      key: "loginTime",
      scopedSlots: { customRender: "loginTime" },
      align: "center",
      width: "18%",
    },
    {
    //   title: "操作",
      title:i18n.t("internal.online_user_management.table_title.operation"),
      dataIndex: "operation",
      key: "operation",
      scopedSlots: { customRender: "operation" },
      align: "center",
      width: "18%",
    }
  ]
