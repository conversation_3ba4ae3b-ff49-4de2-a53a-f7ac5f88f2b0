
/*
 * @Author: 
 * @Date: 2021-07-02 23:39:34
 * @Description:每日任务
 * @FilePath: /src/api/internal/module/operate/module/blind_box.js
 */
import axios from "src/api/common/axioswarpper.js";
import { LocalStorage, SessionStorage } from "quasar";
let prefix_1 = process.env.API_PREFIX_4;
let prefix_2 = process.env.API_PREFIX_18;

let play_type= "/dj"
//盲盒领取记录查询接口
export const queryLuckyBoxHistory= (params) => {
  let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
  let prefix =which? prefix_1: prefix_2;
  let which_type=which?  "": play_type;
  let url=`/manage${which_type}/box/queryLuckyBoxHistory`
  return  axios.post(`${prefix}${url}`, params);
}
 

//盲盒设置后台列表
  export const luckybox= (params) => {
    let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
    let prefix =which? prefix_1: prefix_2;
    let which_type=which?  "": play_type;
    let url=`/manage${which_type}/luckybox/list`
    return  axios.post(`${prefix}${url}`, params);
  }

  //盲盒设置的编辑
  export const updateBox= (params) => {
    let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
    let prefix =which? prefix_1: prefix_2;
    let which_type=which?  "": play_type;
    let url=`/manage${which_type}/luckybox/updateBox`
    return  axios.post(`${prefix}${url}`, params);
  }

    
//排序
export const luckyboxchangeOrder= (params) => {
  let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
  let prefix =which? prefix_1: prefix_2;
  let which_type=which?  "": play_type;
  let url=`/manage${which_type}/luckybox/changeOrder`
  return axios.get(`${prefix}${url}`, { params: { ...params } });
}

  //设置每日盲盒发布的频率
  export const updateDaily= (params) => {
    let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
    let prefix =which? prefix_1: prefix_2;
    let which_type=which?  "": play_type;
    let url=`/manage${which_type}/luckybox/updateDaily`
    return axios.post(`${prefix}${url}`, params);
  }
  
  //盲盒每日的发布频率
  export const findDaily= (params) => {
    let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
    let prefix =which? prefix_1: prefix_2;
    let which_type=which?  "": play_type;
    let url=`/manage${which_type}/luckybox/findDaily`
    return axios.post(`${prefix}${url}`, params);
  }