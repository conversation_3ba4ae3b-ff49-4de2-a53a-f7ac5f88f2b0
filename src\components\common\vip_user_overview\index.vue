<!-- VIP用户总览 -->
<template>
    <div class="full-width full-height vip_user_overview">
        <div class="pl10x pt10x pb10x fs14" id="top1">
            <q-breadcrumbs separator="/" active-color="whiddte">
                <q-breadcrumbs-el :label="$t('internal.menujs')[36]" class="panda-text-2" />
                <!-- :label="$t('internal.menujs')[34]" -->
                <q-breadcrumbs-el :label="$t('internal.menujs.vip_user_overview')"  class="panda-text-1 fs14" />
            </q-breadcrumbs>
        </div>
        <div class="full-height full-width">
            <div class="bg-panda-bg-6 shadow-3 border-radius-4px" style="margin: 0 10px 10px 10px">
                <div class="row full-width justify-between items-center pl10x pt10x pb10x">
                    <div class="left">
                        <div class="row pl10x pt10x pb10x">
                            <!-- 投注周期tab栏 -->
                            <div class="ml10x">
                                <a-radio-group v-model="dateType" class="">
                                    <a-radio-button value="day">{{ $t('internal.day') }}</a-radio-button>
                                    <!-- <a-radio-button value="week">{{$t("internal.template.label3")}}</a-radio-button> -->
                                    <a-radio-button value="month">{{ $t('internal.month') }}</a-radio-button>
                                    <!-- <a-radio-button value="year">{{$t("internal.template.label4")}}</a-radio-button> -->
                                </a-radio-group>
                            </div>
                            <!-- 日期选择 -->
                            <div class="ml10x w-200">
                                <a-range-picker :allowClear="true"  v-if="dateType == 'day'"
                                    @change="on_month_change"  :disabledDate="disabledDate"   />
                                <a-range-picker :allowClear="true"   :disabledDate="disabledDate"  
                                    :placeholder="[$t('internal.data.user.index.placeholder.dateType')[0], $t('internal.data.user.index.placeholder.dateType')[1]]"
                                    v-else-if="dateType == 'month'" :value="monthvValue" :mode="monthMode"
                                    format="YYYY-MM" :open="isopen" @openChange="handleOpenChange"
                                    @panelChange="handlePanelChange" @change="on_month_change" />
                            </div>
                            <!-- 自然日切换 -->
                            <div class="append-handle-btn-input position-relative ml10x">
                                <a-select autocomplete class="w-140"
                                    :placeholder="$t('internal.data.user.index.placeholder.timeZone')"
                                    v-model="searchForm.timeZone" @change="handle_time_zone_change">
                                    <a-select-option :value="item.value" v-for="(item, index) in timeZoneList"
                                        :key="index">{{ item.label }}</a-select-option>
                                </a-select>
                                <div class="position-absolute select-left-border-finance"></div>
                            </div>
                        </div>
                        <div class="row pl10x pt10x pb10x ">
                            <div class="ml10x">
                                <span class="pr10x pt10x pb10x "> {{
                                    $t('internal.vip_merchant_settings.table_title.merchant_name') }}</span>
                                <a-select class="col-6" show-search option-filter-prop="children" :allowClear="true" 
                                    :filter-option="filterOption" :placeholder="$t('internal.common.qsr')"
                                    style="width: 160px;" label-in-value @change="handleChangeSelect">
                                    <a-select-option v-for="(item, index) in merchantList" :key="item.merchantCode">{{
                                        item.merchantName }}<q-tooltip anchor="bottom middle" self="top middle">
                                            {{ item.merchantName }} 
                                            <!-- {{ item.merchantCode }} -->
                                        </q-tooltip>
                                    </a-select-option>
                                </a-select>
                            </div>
                            <!-- 对内才显示 -->
                            <div class="ml10x" v-if="this.src_internal">
                                <!-- 系统默认切换 -->
                                <div class="append-handle-btn-input position-relative ml10x">
                                    <a-select autocomplete class="w-140"
                                        :placeholder="$t('internal.data.user.index.placeholder.timeZone')"
                                        v-model="searchForm.systemType" >
                                        <a-select-option :value="item.value" v-for="(item, index) in systemTypeList"
                                            :key="index">{{ item.label }}</a-select-option>
                                    </a-select>
                                    <div class="position-absolute select-left-border-finance"></div>
                                </div>
                            </div>                            
                            <div class="ml10x">
                                <a-button type="primary" @click="handle_search" class="mr10x">{{ $t('internal.search')
                                }}</a-button>
                            </div>
                            <div class="ml10x">
                                  <!-- :disabled="tabledata.length == 0" -->
                                <a-button type="primary"  @click="handle_export_excel_for_list_vip_info()"
                                    class="mr10x">{{
                                        $t('internal.export_text') }}</a-button>
                            </div>
                        </div>
                    </div>
                    <div class="right">
                        <div class="row pr20x pt10x pb10x justify-end">
                            <div class="ml10x" v-if="$_has('dlpm_init') || $_btn_has('dlpm_init')" >
                                <a-button type="primary" @click="handle_initialize"
                                    :disabled="vipSysConfig.status == 1 ? false : true">{{
                                        $t('internal.vip_user_overview.initialize') }}</a-button>
                            </div>
                            <div class="ml10x align-items items-center row">
                                <!-- 距离下次更新 -->
                                <span>{{ $t('internal.vip_user_overview.until_the_next_update') }}:</span>
                                <span>{{ formattedHours }}:{{ formattedMinutes }}:{{ formattedSeconds }}</span>
                            </div>

                        </div>
                        <div class="row pr20x pt10x pb10x" v-if="src_internal" >
                            <div class="row align-items items-center">
                                <!-- <a-switch @change="handle_switch" :checked-children="$t('internal.common.label1')"
                                    :un-checked-children="$t('internal.common.label2')" /> -->
                                <a-switch @click="disabled_swith_user(vipSysConfig)"
                                    :class="{ 'custom-disabled': vipSysConfig.isExistDaily == 0 || vipSysConfig.isExistRank == 0 }"
                                    :checked="vipSysConfig.status == 1 ? true : false"
                                    :checked-children="$t('internal.message.label8')"
                                    :un-checked-children="$t('internal.message.label9')" />
                            </div>
                            <div class="ml10x">
                                <a-button @click="handle_edit_day" type="primary"  
                                    :class="vipSysConfig.isExistDaily == 1 ? 'success' : ''">
                                    <span
                                        v-if="vipSysConfig.isExistDaily == 1">{{ $t('internal.vip_user_overview.edit_establish_a_daily_get_your_growth_setting') }}</span>
                                    <span v-else>
                                        {{ $t('internal.vip_user_overview.establish_a_daily_get_your_growth_setting') }}</span>                                    
                                    <!-- <span
                                        v-if="vipSysConfig.isExistDaily == 0">{{ $t('internal.vip_user_overview.establish_a_daily_get_your_growth_setting') }}</span>
                                    <span v-show="vipSysConfig.isExistDaily == 1">
                                        {{ $t('internal.vip_user_overview.edit_establish_a_daily_get_your_growth_setting') }}</span> -->
                                </a-button>
                            </div>
                            <div class="ml10x">
                                <a-button type="primary" :class="vipSysConfig.isExistRank == 1 ? 'success' : ''"
                                    @click="handle_edit_level" >
                                    <span
                                        v-if="vipSysConfig.isExistRank == 1">{{ $t('internal.vip_user_overview.edit_establish_rating_settings') }}</span>
                                    <span v-else>
                                        {{ $t('internal.vip_user_overview.establish_rating_settings') }}</span>
                                    <!-- <span
                                        v-show="vipSysConfig.isExistRank == 0">{{ $t('internal.vip_user_overview.establish_rating_settings') }}</span>
                                    <span v-show="vipSysConfig.isExistRank == 1">
                                        {{ $t('internal.vip_user_overview.edit_establish_rating_settings') }}</span> -->
                                </a-button>
                            </div>
                            <div class="ml10x">

                                <a-button @click="handle_create_vip_setup" type="primary"
                                    :class="vipSysConfig.id ? 'success' : ''">
                                    <span v-if="vipSysConfig.id">{{
                                        $t('internal.vip_user_overview.edit_a_merchant_wide_VIP_setup') }}</span>
                                    <span v-else>{{ $t('internal.vip_user_overview.create_a_merchant_wide_VIP_setup')
                                    }}</span>

                                </a-button>
                            </div>
                        </div>
                    </div>
                </div>
                <!--表格区域-->
                <div>
                    <a-table class="pl10x pr10x" :columns="columns" :dataSource="tabledata" :pagination="false"
                        :loading="tabledata_loading" size="middle" rowKey="id">
                        <!-- 用户 -->
                        <span slot="totalUsers" slot-scope="text, record">
                            {{ record.totalUsers | filterNumberFormat }}
                        </span>
                        <!-- 总投笔数 总投注笔数 -->
                        <span slot="totalBets" slot-scope="text, record">
                            {{ record.totalBets | filterNumberFormat }}
                        </span>
                        <!-- 平均投注笔数 -->
                        <span slot="avgBets" slot-scope="text, record">
                            {{ record.avgBets | filterNumberFormat }}
                        </span>
                        <!-- 总投注额 -->
                        <span slot="totalBetAmount" slot-scope="text, record">
                            {{ record.totalBetAmount | filterMoneyFormat }}
                        </span>
                        <!-- 平均投注额 -->
                        <span slot="avgBetAmount" slot-scope="text, record">
                            {{ record.avgBetAmount | filterMoneyFormat }}
                        </span>                                                                                                
                        <!-- 总输赢 -->
                        <span slot="totalWinLoss" slot-scope="text, record">
                            <div class="text-red" v-show="record.totalWinLoss > 0">
                                +{{ record.totalWinLoss | filterMoneyFormat }}
                            </div>
                            <div class="text-green" v-show="record.totalWinLoss < 0">
                                {{ record.totalWinLoss | filterMoneyFormat}}
                            </div>
                        </span>                 
                        <!-- 平均输赢 -->
                        <span slot="avgWinLossPerUser" slot-scope="text, record">
                            <div class="text-red" v-show="record.avgWinLossPerUser > 0">
                                +{{ record.avgWinLossPerUser | filterMoneyFormat }}
                            </div>
                            <div class="text-green" v-show="record.avgWinLossPerUser < 0">
                                {{ record.avgWinLossPerUser | filterMoneyFormat}}
                            </div>
                        </span>          
                    </a-table>
                </div>

            </div>
        </div>

        <!-- vip -->
        <q-dialog v-model="dialog_create_vip_setup_show" persistent transition-show="scale" transition-hide="scale">
            <dialog-edit-vip :rowData="vipSysConfig"
                @closeDialogSetShow="handleCloseDialogLevel('dialog_create_vip_setup_show')"></dialog-edit-vip>
        </q-dialog>
        <q-dialog v-model="dialog_edit_level_show" persistent transition-show="scale" transition-hide="scale">
            <!-- <dialog-edit-level></dialog-edit-level> -->
            <dialog-edit-level :rowData="vipSysConfig"
                @closeDialogSetShow="handleCloseDialogLevel('dialog_edit_level_show')"></dialog-edit-level>
        </q-dialog>
        <q-dialog v-model="dialog_edit_day_show" persistent transition-show="scale" transition-hide="scale">
            <!-- <dialog-edit-level></dialog-edit-level> -->
            <dialog-edit-day :rowData="vipSysConfig"
                @closeDialogSetShow="handleCloseDialogLevel('dialog_edit_day_show')"></dialog-edit-day>
        </q-dialog>
        <q-dialog v-model="dialog_confirm_show" persistent transition-show="scale" transition-hide="scale">
            <dialog-confirm :confirmProp="confirmProp" @handle_confirm_submit="handle_confirm_submit"></dialog-confirm>>
        </q-dialog>
        <!-- 报表下载弹窗 -->
        <q-dialog v-model="exportExcelShow" persistent transition-show="scale" transition-hide="scale">
            <dialog-excel :export_param="export_param"></dialog-excel>
        </q-dialog>
    </div>
</template>
<script>
import { i18n } from 'src/boot/i18n';
import { mapGetters } from "vuex";
import { tablecolumns_config } from "./config/config.js"; //
import moment from "moment";
import dialogEditVip from "./components/dialogEditVip.vue";
import dialogEditLevel from "./components/dialogEditLevel.vue";
import dialogEditDay from "./components/dialogEditDay.vue";
import { api_operate } from "src/api/index.js";
import dialogConfirm from "src/components/common/vip_merchant_settings/components/dialogConfirm.vue";
import commonmixin from "src/mixins/external/common/commontoolmixin.js";
import commonmixin_external from "src/mixins/external/common/commontoolmixin.js";
import dialogExcel from "src/components/common/dialog/dialogExcel.vue";


const FOR_WHICH = process.env.FOR_WHICH;
const src_internal = FOR_WHICH == "internal";
let mixins_custom = [
  commonmixin,
];
if (FOR_WHICH == "external") {
  mixins_custom = [
    commonmixin_external,
  ];
}
function getLastDayOfMonth(dateString) {
  // 解析年份和月份
  const parts = dateString.split('-');
  const year = parseInt(parts[0], 10);
  const month = parseInt(parts[1], 10);

  // 获取当月的最后一天
  const lastDay = new Date(year, month, 0);

  // 返回当月最后一天的日期
  return lastDay.getDate();
}
export default {
    mixins: [...mixins_custom],
    components: {
        dialogEditVip,
        dialogEditLevel,
        dialogEditDay,
        dialogConfirm,
        dialogExcel,
        
    },
    data() {
        return {
            //导出报表参数
            export_param: {},
            exportExcelShow: false,    
            tabledata: [], // 表格数据
            columns: tablecolumns_config, // 表格配置
            tabledata_loading: false, // 表格loading
            dateType: 'day',
            dateFormat: "YYYY-MM-DD",
            searchForm: {
                merchantCode: '', // 商户号
                startDate:'', // 日-开始时间
                endDate:'', // 日-结束时间
                startDate1: moment().format("YYYY-MM"),  // 月-开始时间
                endDate1: moment().format("YYYY-MM"), // 月-结束时间
                timeZone: "UTC8", //日期类型 EZ是帐务日，UTC8是自然日
                systemType:1,
            },
            isopen: false,  // 处理年份组件阀值
            monthvValue: [], // 处理年份组件值
            monthMode: ['month', 'month'],
            timeZoneList: i18n.t('internal.data.user.index.timeZoneList'),

            systemTypeList:[
                {          
                    label: i18n.t('internal.vip_user_overview.system'),
                    value: 1
                },
                {          
                    label: i18n.t('internal.vip_user_overview.merchant'),
                    value: 2
                },
            ],
            // 编辑全商户VIP设置
            dialog_create_vip_setup_show: false,
            // 
            dialog_edit_level_show: false,
            dialog_edit_day_show: false,
            vipSysConfig: {

            },
            // 商户列表选项
            merchantList: [],
            rowForKey: '', // 确认弹窗key
            // confirm弹窗 内容对象
            dialog_confirm_show: false,
            confirmObjForkey: {
                // 启用vip提示
                use_vip: {
                    title: i18n.t('internal.vip_merchant_settings.vip_tips.use_VIP'),
                    content: i18n.t('internal.vip_merchant_settings.vip_tips.tips_1'),
                    // type: 'danger',
                    type: 'primary',
                    contentClass: 'text-red',

                },
                // 状态启用提示
                status_enabled: {
                    title: i18n.t('internal.vip_merchant_settings.vip_tips.use_VIP'),
                    content: i18n.t('internal.vip_merchant_settings.vip_tips.tips_2'),
                    // type: 'danger',
                    type: 'primary',
                    contentClass: '',

                },
                // 状态禁用提示
                status_disabled: {
                    title: i18n.t('internal.vip_merchant_settings.vip_tips.disabled_use_VIP'),
                    content: i18n.t('internal.vip_merchant_settings.vip_tips.tips_3'),
                    type: 'danger',
                    contentClass: '',
                },
                // 初始化弹框提示
                init_tips: {
                    title: i18n.t('internal.vip_merchant_settings.vip_tips.initialize'),
                    content: i18n.t('internal.vip_merchant_settings.vip_tips.tips_4'),
                    type: 'danger',
                    contentClass: '',
                }


            },
            // 确认弹窗内容
            confirmProp: {},
            totalTime: 7200, // 初始时间为 2 小时（2 * 60 * 60 = 7200 秒）
            timer: null,
        }
    },
    computed: {
        formattedHours() {
            return String(Math.floor(this.totalTime / 3600)).padStart(2, '0');
        },
        formattedMinutes() {
            const minutes = Math.floor((this.totalTime % 3600) / 60);
            return String(minutes).padStart(2, '0');
        },
        formattedSeconds() {
            const seconds = this.totalTime % 60;
            return String(seconds).padStart(2, '0');
        },
    },
    beforeDestroy() {
        // 清除定时器
        clearInterval(this.timer);
    },
    created() {

    },
    mounted() {
        this.get_vip_config_list();
        this.startTimer();
        this.updateSecondsToTomorrow();
        this.handle_search();
        this.getMerchantList();
    },
    methods: {
        compute_params() {
            // 计算列表参数
            if (this.dateType == 'month') {
                // 获取当前日期
                const now = new Date();

                // 设置为下个月的第一天，然后减去一天，得到当月的最后一天
                const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                this.searchForm.startDate = this.monthvValue[0].format("YYYY-MM")+'-01';
                // this.searchForm.endDate = this.monthvValue[1].format("YYYY-MM")+'-31';
                const endDate = this.monthvValue[1].format("YYYY-MM")+'-'+getLastDayOfMonth(this.monthvValue[1].format("YYYY-MM"))
                this.searchForm.endDate = endDate;
            }
            let params = {
                merchantCode: this.searchForm.merchantCode,
                startDate: this.searchForm.startDate,
                endDate: this.searchForm.endDate,
                timeZone: this.searchForm.timeZone,
            }
            // this.src_internal
            if(this.src_internal){
                params['type'] = this.searchForm.systemType
            }
            return params;
        },
        /**
         * @description 获取VIP配置列表
         * @return {undefined
            }

        },
        /**
         * @description 搜索
         * @return {undefined}value
         */
        handle_search(show_msg) {
            // 清空总计
            
            let params = this.compute_params();

            api_operate.vip_config_listVipInfoStats(params).then(res => {
                let { code, msg } = res.data;
                // code == "0000000"
                if (code == "200") {
                    // 注意报表接口，返回数据格式不同 返回的code是200，他们不愿统一
                    
                    this.tabledata = res.data.data;
                    
                    if(!show_msg){
                        return;
                    }
                    this.$message.success(msg);
                } else {
                    this.$message.error(msg);
                }
            });
            this.get_vip_config_list();
        },
        /**
         * @description 账务日/自然日切换
         * @return {undefined} undefined
         */
        handle_time_zone_change(val) {
            // this.handle_search();
        },
        on_month_change(date, dateStrings) {
            // 手动输入同一个月份

            this.isManual = true;

            if (dateStrings) {
                Object.assign(this.searchForm, {
                    startDate: dateStrings[0],
                    endDate: dateStrings[1]
                });
            }
        },
        /**
         * @description handleOpenChange 切换月份时打开日历
         * @return {undefined} undefined
         */
        handleOpenChange(status) {
            this.isopen = status ? true : false;
        },
        // 更新月份值
        handlePanelChange(value, mode) {

            this.monthvValue = value;
  
            this.mode = [mode[0] == 'date' ? 'month' : mode[0], mode[1] == 'date' ? 'month' : mode[1]];
 
            if (mode.indexOf('date') > 0) {
                this.isopen = false;
                this.isManual = false;
            }
        },
        // 编辑全商户VIP设置
        handle_create_vip_setup() {
            this.dialog_create_vip_setup_show = true;
        },
        handle_edit_level() {
            this.dialog_edit_level_show = true;
        },
        handle_edit_day() {
            this.dialog_edit_day_show = true;
        },
        //关闭弹窗
        handleCloseDialogLevel(handleCloseDialogName) {
    
            this[handleCloseDialogName] = false;
            // this.dialog_edit_level_show = false;
            this.handle_search(false);
        },
        get_vip_config_list() {
            // 获取系统配置
            let params = {
                "vipName": "vip系统",
                "vipType": 1,
            }
            api_operate.vip_config_list(params).then(res => {
                let { code, msg } = res.data;
                if (code == "0000000") {
                   
                    if (res.data.data.length > 0) {
                        this.vipSysConfig = res.data.data[0];
                    }
                   
                } else {
                    this.$message.error(msg);
                }
            });
        },
        startTimer() {
            this.timer = setInterval(() => {
                if (this.totalTime > 0) {
                    this.totalTime--;
                } else {
                    clearInterval(this.timer);
                }
            }, 1000);
        },
        updateSecondsToTomorrow() {
            const now = new Date();
            const tomorrow = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0);
            const diff = tomorrow - now;
            this.totalTime = Math.floor(diff / 1000);
        },
        // 切换选中状态   
        disabled_swith_user(record) {
            // 条件判断
            if (record.isExistDaily == 0 || record.isExistRank == 0) {
                // use_vip status_enabled status_disabled
                this.rowForKey = 'use_vip'
                this.confirmProp = this.confirmObjForkey[this.rowForKey]
                this.dialog_confirm_show = true;
            } else {
                if (record.status == 0) {
                    this.rowForKey = 'status_enabled'
                    this.confirmProp = this.confirmObjForkey[this.rowForKey]
                } else {
                    this.rowForKey = 'status_disabled'
                    this.confirmProp = this.confirmObjForkey[this.rowForKey]
                }
                this.dialog_confirm_show = true;
            }
        },
        handle_initialize() {
            // 初始化 弹框
            // this.dialog_create_vip_setup_show = true;    
            this.rowForKey = 'init_tips'
            this.confirmProp = this.confirmObjForkey[this.rowForKey]
            this.dialog_confirm_show = true;
        },
        // 确认操作
        handle_confirm_submit() {
            // 根据选中行切换状态
            // this.dialog_confirm_show = false;
            let params = {
                "id": this.vipSysConfig.id,
                "status": this.vipSysConfig.status == 1 ? 0 : 1,
            }
            
            if (this.rowForKey == 'init_tips') {
          
                // vip_config_initialVipInfo
                this.initialVipInfo();

            } else {
                if (this.vipSysConfig.isExistDaily == 1 && this.vipSysConfig.isExistRank == 1) {
                    // 只有两个值都有的时候才能禁用或者启用
                    api_operate.vip_config_updateVipSwitch(params).then(res => {
                        let { code, msg } = res.data;
                        if (code == "0000000") {
                            this.dialog_confirm_show = false;
                            this.handle_search(false)
                        } else {
                            this.$message.error(msg);
                        }
                    });
                } else {
                    this.dialog_confirm_show = false;
                }
            }


        },
        initialVipInfo() {
            const params = {
                merchantCode:this.vipSysConfig.applyMerchantCode,
                vipId:this.vipSysConfig.id
            }
            api_operate.vip_config_initialVipInfo(params).then(res => {
                let { code, msg } = res.data;
                if (code == "200") {
                    this.dialog_confirm_show = false;
                    this.handle_search(false)
                } else {
                    this.$message.error(msg);
                }
            });
        },
        getMerchantList() {
            // 获取商户信息
            api_operate.vip_config_getMerchantList().then(res => {
                let { code, msg } = res.data;
                if (code == "0000000") {
                    this.merchantList = res.data.data
                } else {
                    this.$message.error(msg);
                }
            })
        },
        filterOption(input, option) {
            return (
                option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
            );
        },
        handleChangeSelect(value) {
            if (value) {
                this.searchForm.merchantCode = value.key
            }else{
                this.searchForm.merchantCode = ''
            }

        },
        disabledDate(date) {
            const currentDate = moment();
            const oneYearAgo = moment().subtract(1, 'year');
            return date.isBefore(oneYearAgo) || date.isAfter(currentDate);
        },
        // 导出
        handle_export_excel_for_list_vip_info() {
            if (this.tabledata.length > 0) {
                let params = this.compute_params();
                // let { userId, appId } = this.user_vip_datetail;
                let res = "";
                let url = "/vip/config/exportListVipInfo";
                // let url = e.key == 1 ? "/order/user/exportTicketList" : "/order/user/exportTicketAccountHistoryList";
                //账变导出
                params = {
                    ...params,
                    url,
                    // "user-id": userId,
                    // "app-id": appId,
                };
                params = this.delete_empty_property_with_exclude(params);
                Object.assign(params, { url: url });
                this.export_param = params;
                this.exportExcelShow = true;
            } else {
                // this.handle_error();
            }
        }

    }
}
</script>
<style lang="scss">
.vip_user_overview {
    .success {
        background-color: rgba(23, 149, 2, 1);
    }

    /* 自定义禁用样式 */
    .custom-disabled {
        background-color: #bebebe !important;
        /* 自定义背景颜色 */
    }

}
</style>