import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_10;
let url_prefix = "/v1/keyword";


//搜索设置关键词列表获取
export const get_selectKeywordList = (
) => axios.get(`${prefix}${url_prefix}/deployKeywordList`);

//修改关键词
export const post_updateKeyword = (
  params,
  url = "/updateKeyword"
) => axios.post(`${prefix}${url_prefix}${url}`, params);
// export const businessUserPaidgetUserPaid = (params  //GET/POST 获取单个用户最大赔付信息
//   ) => axios.post(`${prefix}/businessUserPaid/getUserPaid`,params,{type:1 }); 

