/*
 * @FilePath: d:/projects/new-merchant-management/src/api/internal/module/operate/module/ticket_management.js
 * @Description: 奖券管理接口
 * @Author: tony
 */


import axios from "src/api/common/axioswarpper.js";


let prefix_1 = process.env.API_PREFIX_4;

// 新增奖券接口
export const add_ticket = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/addTicket`;
  return axios.post(`${prefix}${url}`, params);
}

// 编辑奖券接口
export const edit_ticket = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/editTicket`;
  return axios.post(`${prefix}${url}`, params);
}

// 奖券开关
export const modify_ticket_state = (params) => {
  let prefix = prefix_1;
  let url = `/slotMachine/modifyTicketState`;
  return axios.post(`${prefix}${url}`, params, {
    type: 1
  });
}

// 奖券列表
export const get_ticket_list = () => {
  let prefix = prefix_1;
  let url = `/slotMachine/ticketList`;
  return axios.get(`${prefix}${url}`);
}


// 奖券下拉列表
export const get_select_ticket_list = () => {
  let prefix = prefix_1;
  let url = `/slotMachine/selectTicketList`;
  return axios.get(`${prefix}${url}`);
}
