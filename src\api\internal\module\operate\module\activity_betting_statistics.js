/*
 * @FilePath: /src/api/internal/module/operate/module/activity_betting_statistics.js
 * @Description: 活动投注统计
 */
import axios from "src/api/common/axioswarpper.js";
import { LocalStorage, SessionStorage } from "quasar";

let prefix = process.env.API_PREFIX_3;
let prefix_2 = process.env.API_PREFIX_18;

//获取活动报表列表
export const getActivityBetStatList= (params) => {
    let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
    let prefix_ =which? prefix: prefix_2;
    let which_type=which?  "/order":  "/activityCenter/dj";
  let url=`${which_type}/activity/getActivityBetStatList`
  return axios.post(`${prefix_}${url}`, params);
}




