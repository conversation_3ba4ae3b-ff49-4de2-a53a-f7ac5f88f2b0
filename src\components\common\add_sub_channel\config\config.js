/*
 * @Author:
 * @Date: 2020-01-27 15:42:23
 * @Description:  商户中心.代理商管理. 新增下级商户
 * @FilePath: /src/components/common/add_sub_channel/config/config.js
 */
import { i18n } from 'src/boot/i18n';
let table_title= i18n.t('internal.table_title')
//商户中心.代理商管理.(新增下级商户 配置文件)
export const set_sub_channel = [
    {//序号
      title:table_title.label1,
      width: 60,
      dataIndex: "_index",
      key: "_index",
      align: "left",
      // fixed: 'left'
    },
    {//商户编号
      title:table_title.label65,
      width: 120,
      dataIndex: "merchantCode",
      key: "merchantCode",
      align: "left",
      scopedSlots: { customRender: "merchantCode" },
      // fixed: 'left'
    },
    {//商户名称
      title:table_title.label3,
      dataIndex: "merchantName",
      key: "merchantName",
      width: 180,
      align: "left",
      scopedSlots: { customRender: "merchantName" }
      // fixed: 'left'
    },
    {//商户等级
      title:table_title.label66,
      dataIndex: "level",
      key: "level",
      width: 120,
      align: "left",
      scopedSlots: { customRender: "level" }
    },
    {//入住时间
      title:table_title.label63,
      dataIndex: "createTime",
      key: "createTime",
      width: 150,
      align: "left"
    },
    {//操作
      title:table_title.label17,
      dataIndex: "operation",
      key: "action",
      width: 80,
      scopedSlots: { customRender: "action" }
    }
  ];
 //商户中心.代理商管理. 新增下级商户 中(添加下级商户 配置文件)  
export const set_sub_channel_add = [
  {//商户编码
    title:table_title.label65,
    width: 90,
    dataIndex: "merchantCode",
    key: "merchantCode",
    align: "left",
    scopedSlots: { customRender: "merchantCode" },
    // fixed: 'left'
  },
  {//商户名称
    title:table_title.label3,
    dataIndex: "merchantName",
    key: "merchantName",
    width: 120,
    align: "left",
    scopedSlots: { customRender: "merchantName" }
    // fixed: 'left'
  },
  {//商户等级
    title:table_title.label66,
    dataIndex: "level",
    key: "level",
    width: 80,
    align: "left",
    scopedSlots: { customRender: "level" }
  },
  {//入住时间
    title:    table_title.label63,
    dataIndex: "createTime",
    key: "createTime",
    width: 140,
    align: "left"
  },
];

  