<!--
 * @FilePath: /src/components/common/bettinguser/index2.vue
 * @Description: 
-->
<template>
  <div>
      <div class="ggg5552"><div v-if="is_for_white_betting_users" class="ggg555"><real-comp></real-comp></div>  </div>
    <div class="ggg5551">   <div v-if="!is_for_white_betting_users"  class="ggg555666"><real-comp></real-comp></div> </div>
 
  </div>
</template>
<script>
import realComp from "src/components/common/bettinguser/index.vue";
import realComp2 from "src/components/common/bettinguser/index.vue";
export default {
  components: {
    realComp,
    realComp2,
  },
  data() {
    return {
      is_for_white_betting_users: false
    };
  },
  watch: {
    $route(newValue, oldValue) {
      // console.log("1----------- this.$route------", this.$route);
      let rname = this.$route.name;
      let fw = rname == "betting_user" ? "betting_user" : "white_betting_users";
      this.is_for_white_betting_users = fw == "white_betting_users";
      this.$forceUpdate();
    }
  }
};
</script>
<style lang="scss" scoped></style>
