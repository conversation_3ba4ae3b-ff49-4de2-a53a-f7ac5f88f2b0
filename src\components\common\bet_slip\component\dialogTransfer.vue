<!--
 * @Author: Nice
 * @Date: 2020-07-30 15:20
 * @Description: 数据中心.注单查询 查看注单账变(弹窗)-账变记录
 * @FilePath: /src/components/common/bet_slip/component/dialogTransfer.vue
-->

<template>
  <div
    style="width: 1300px; height:auto;max-width:1300px; min-height: 60px; overflow:hidden;"
    class="text-panda-text-7"
  >
    <q-card class="bg-white text-black">
    <!-- 账变记录 -->
      <q-card-section class="no-padding">
        <div class="row line-height-40px fs14 bg-panda-dialog text-panda-text-7 pr10x">
          <div class="pl20x fw_600">{{detailObj.userName}}-{{$t('internal.data.betslip.dialogTransfer.title')}}</div>
          <q-space></q-space>
          <q-btn class="mr5x text-panda-dialog-close" flat dense icon="close" v-close-popup />
        </div>
      </q-card-section>
       <q-separator></q-separator>
      <!--表格区域-->
      <a-table
        :columns="columns"
        :dataSource="tabledata"
        :pagination="pagination"
        :loading="tabledata_loading1"
        :scroll="{ x: 510,y: 290}"
        size="small"
        rowKey="id"
      >
      <!-- 账变时间 -->
        <span
          slot="createTime"
          slot-scope="text,record"
        >{{moment(record.createTime).format("YYYY-MM-DD HH:mm:ss")}}
        </span>
       <!-- 账变金额 -->
        <span slot="changeAmount" slot-scope="text,record">
        <span v-if="src_internal">
          <span v-if="record.bizType == 4">
            <!--bizType 为4，大于0显示+其他不管 -->
              <span v-if="record.changeAmount>0">+</span>
              {{record.changeAmount | filterAmount}}
          </span>
          <span v-else>
              <span v-if="record.changeType == 0">+</span>
              <span v-if="record.changeType == 1">-</span>
              {{record.changeAmount | filterAmount}}
          </span>
        </span>
        <span v-if="src_external">
          <span v-if="record.bizType == 4">
              <!--bizType 为4，大于0显示+其他不管 -->
            <span v-if="record.changeAmount>0">+</span>
              {{record.changeAmount | amount}}
          </span>
          <span v-else>
            <span v-if="record.changeType == 0">+</span>
            <span v-if="record.changeType == 1">-</span>
            {{record.changeAmount | amount}}
          </span>
        </span>
        </span>
        <!-- 账变前余额 -->
        <span slot="beforeTransfer" slot-scope="text,record">{{record.beforeTransfer | filterMoneyFormat}}</span>
        <!-- 账变后余额 -->
        <span slot="afterTransfer" slot-scope="text,record">{{record.afterTransfer | filterMoneyFormat}}</span>
        <!-- 交易相关注单号 -->
        <span slot="orderNo" slot-scope="text,record">  <span  @click="handleCopy(record.transferId||record.orderNo,   $t('internal.orderNo_text'))"> {{record.transferId||record.orderNo  }}</span>    </span>
     
      </a-table>
    </q-card>
  </div>
</template>
<script>
import { i18n } from 'src/boot/i18n';
import { api_merchant ,api_account} from "src/api/index.js";
import { transfer_config as transfer_config_internal} from "src/components/common/bet_slip/internal/config/transferConfig.js";//数据中心.注单查询(查看注单账变)弹窗配置
import { transfer_config as transfer_config_external} from "src/components/common/bet_slip/external/config/transferConfig.js";
import moment from "moment";
import { handleCopy } from "src/util/module/common.js";
const FOR_WHICH = process.env.FOR_WHICH;
let transfer_config=transfer_config_internal
if(FOR_WHICH=="external"){
    transfer_config=transfer_config_external
}
export default {
  props: {
    //汇总的数据
    betTodayData: {
      type: Array,
      default() {
        return {};
      }
    },
    //页面类型
    page_type:{
      type:Object,
    },
    //传给弹窗的数据
    detailObj: {
      type: Object
    }
  },
  data() {
    return {
      
      
      tabledata_loading1: false, //  弹窗loading
      columns: transfer_config,  // 表格配置
      tabledata: [], // 表格数据
      pagination: {
        pageSize: 100,
        current: 1,
        showTotal: total => i18n.t('internal.showTotal_text',[total]),
        hideOnSinglePage: true,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ["10", "30", "30", "40"],
        onShowSizeChange: (current, pageSize) =>
          (this.pagination.pageSize = pageSize),
        onChange: current => (this.pagination.current = current)
      }
    };
  },
  mounted() {
    this.initTableData();
  },
  methods: {
    moment,
     handleCopy,
    initTableData() {
      this.tabledata_loading1 = true;
      let params = this.compute_init_tabledata_params();
      let post_order_user_queryTransferList;
      // 注单查询页面
      if(this.$route.name == 'bet_slip'||this.page_type?.is_check_the_progress_of_joint_purchase){
        if(this.src_internal) {
          post_order_user_queryTransferList = api_merchant.post_order_user_queryTransferList
        } else {
          post_order_user_queryTransferList = api_account.post_admin_userReport_queryUserTransferList
        }
      } else if (this.$route.name == 'bet_slip_es'){
        // 注单查询ES页面
        if(this.src_internal) {
          post_order_user_queryTransferList = api_merchant.post_order_user_queryTransferListES
        } else {
          post_order_user_queryTransferList = api_account.post_admin_userReport_queryUserTransferListES
        }
      } else {
        // 注单查询ES实时流页面
        if(this.src_internal) {
          post_order_user_queryTransferList = api_merchant.post_order_user_queryTransferListES_timely
        } else {
          post_order_user_queryTransferList = api_account.post_admin_userReport_queryUserTransferListES_timely
        }
      }
      post_order_user_queryTransferList(params)
          .then(res => {
            this.tabledata_loading1 = false;
            let code = this.$lodash.get(res, "data.code");
            let msg = this.$lodash.get(res, "data.msg");
            if (code == "0000000") {
              let arr = this.$lodash.get(res, "data.data.list") || [];
              this.tabledata = this.rebuild_tabledata_to_needed(arr);
              this.pagination.total = this.$lodash.get(res, "data.data.total") * 1 || 0;
            } else {
              msg && this.$message.error(msg, 2);
            }
          });
    },
    rebuild_tabledata_to_needed(arr) {
      arr.map((item, index) => {
        item._index =
          (this.pagination.current - 1) * this.pagination.pageSize + index + 1;
        item.id = index + 1;
      });
      return arr;
    },
    compute_init_tabledata_params() {
      let { current, pageSize } = this.pagination;
      
    let params={
        pageNum: current,
        pageSize,
        orderNo: this.detailObj.orderNo,
        userId: this.detailObj.uid
      }
    if(this.page_type?.is_check_the_progress_of_joint_purchase){
      //合买
      if(this.detailObj.subscribeNo){
       //子单传认购人id
        params.subscribeNo=this.detailObj.subscribeNo
      }else {
        //母单不传userid
        delete params.userId
      }
    }
      return params;
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .ant-spin-nested-loading > div > .ant-spin {
	 max-height: 70px !important;
	 min-height: 70px !important;
}
::v-deep .ant-empty-normal {
	 margin: 50px 0 !important;
}
::v-deep .ant-table-footer {
	 padding: 0;
	 height: 30px;
	 line-height: 30px;
}
::v-deep .ant-table-thead > tr > th {
	 color: #3c4551;
	 font-weight: 600;
}
 
</style>
