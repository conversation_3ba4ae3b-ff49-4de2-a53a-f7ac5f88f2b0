<!--
 * @FilePath: /src/components/common/matchbonus/index.vue
 * @Description:投注用户管理-战绩
-->
<template>
  <div class="full-width full-height position-relative">
   
    <div class="bg-panda-bg-6 shadow-3" style="margin: 0 10px 10px 10px">
      <div
        id="top2"
        class="row pl10x pb10x pt10x"
      >
        <!--下拉选择联赛-->
        <div class="append-handle-btn-input pl10x position-relative">
          <a-select
            autocomplete
            class="w-100"
            :placeholder="$t('internal.data.user.index.placeholder.sportId')"
            v-model="searchForm.sportId"
          >
            <a-select-option
              :value="item.value"
              v-for="(item, index) in allMatches"
              :key="index"
              >{{ item.label }}</a-select-option
            >
          </a-select>
          <div class="position-absolute select-left-border-finance"></div>
        </div>
        <!-- 时间选择器筛选 -->
        <div class="append-handle-btn-input pl10x row line-height-30px ">
          <div class="mr10x">
            <a-range-picker show-time style="width: 323px" showToday :allowClear="false" v-model="searchForm.range_picker_value" @change="on_range_picker_value_change" />
          </div>
          <!-- 自然日 -->
          <a-checkbox v-model="isDay" >
            {{ $t("internal.data.betslip.index.searchForm.isDay") }}
          </a-checkbox>
        </div>
        <!--输入联赛名称或下拉选择联赛-->
        <div  class="append-handle-btn-input ml10x row position-relative w-160">
          <a-select
            show-search
            :placeholder="
              $t('internal.data.matchbonus.index.placeholder.filterOption')
            "
            option-filter-prop="children"
            style="width: 100%"
            :filterOption="filterOption"
            allowClear
            @search="init_pull_down_tournament"
            @change="handle_match_tournamentId_change"
          >
            <a-select-option
              :value="item.id"
              :label="item.name"
              v-for="item in pull_down_tournament"
              :key="'tournament' + item.id"
              >{{ item.name }}</a-select-option
            >
          </a-select>
          <div class="position-absolute select-left-border-finance"></div>
        </div>
        <!-- 请输入赛事ID -->
        <div class="append-handle-btn-input ml10x w-170 position-relative mb10x">
          <a-input v-model="searchForm.matchId" :placeholder="$t('internal.placeholder.label73_1')" @keydown.enter="handle_search" autocomplete="off" allowClear>
            <my-icon slot="suffix" type="p-icon-chazhao" class="text-panda-text-4 fs12" />
          </a-input>
          <div class="position-absolute select-left-border-bet"></div>
        </div>

        <!--搜索-->
        <div class="append-handle-btn-input ml10x height-30px line-height-30px ">
          <a-button
            type="primary"
            style="height: 30px; line-height: 30px"
            @click="handle_search"
            >{{ $t("internal.search") }}</a-button
          >
        </div>
      </div>
      <!--表格区域-->
      <a-table
        class="pl10x pr10x"
        :columns="computed_columns"
        :dataSource="tabledata"
        :scroll="{ x: 1670, y: scrollHeight - 186 - 130 }"
        :pagination="false"
        :loading="tabledata_loading"
        @change="sorterForTable"
        size="middle"
        rowKey="_index"
      >
      <!-- 赛种 -->
       <template slot="sportName" slot-scope="text, record">
        <div>
          <span>{{ text }}</span>
        </div>
       </template>
        <!--赛事对阵-->
        <div
          :slot=" 'matchInfo'"
          slot-scope="text, record"
          class="display-inline-block"
          @mouseover="tooltip_icotitle_hidden('ico-title' + record._index, 1)"
          @mouseout="tooltip_icotitle_hidden('ico-title' + record._index, 2)"
        >
          <div
            class="textwidth-2 float-left tournamentId cursor-pointer"
            @click="
              handleCopy(
                record.tournamentId,
                $t('internal.data.matchbonus.index.tournamentId')
              )
            "  >
            <table-cell-ellipsis-ant
              :span_html="text"
              :str_all="text"
            ></table-cell-ellipsis-ant>
          </div>
          <span
            class="float-right ml1x hidden"
            :ref="'ico-title' + record._index"
          >
            <a-tooltip placement="bottom">
              <template slot="title">
                <span>{{ record.matchId }}</span>
            </template>
              <img
                src="~src/assets/internal/copy.svg"
                class="tag-read cursor-pointer"
                @click="
                  handleCopy(
                    record.matchId,
                    $t('internal.data.betslip.index.matchId')
                  )
                "
              />
            </a-tooltip>
          </span>
        </div>
        
        <!-- 赛事等级 -->
        <span slot="tournamentLevel" slot-scope="text, record" >
          <span>{{ record.tournamentLevel | filterMatchLevel }}</span>
        </span>
        <!-- 投注玩法+投注项 -->
       <template slot="playName" slot-scope="text, record">
        <div>
          <span class="">{{ record.playName }}+{{record.playOptionName}}</span>
        </div>
       </template>
       <!-- 赛事赛果 -->
       <template slot="outCome" slot-scope="text, record">
        
        <!-- betStatus注单状态来展示异常信息  "比赛中断"-->
        <div class="text-red fs12" v-if="
            [3, 4, 5].includes(record.betStatus)">
          <!--280 【商户后台】注单中心展示危险球，且去除mts拒单信息 -->
          <span v-if="
          record.riskEvent && record.riskEvent !== 'market_expired'
            " :class="
              record.oderStatus == 3
                ? 'text-red'
                : record.orderStatus == 4
                ? 'text-gold'
                : ''
            ">
            {{ record.riskEvent }}
          </span>
          <a-tooltip placement="right" v-else>
            <template slot="title">
              <div :style="`max-width: 200px; word-break:break-all; font-size: 12px;`">
                {{ (src_internal && record.remark) || (src_external && getSubstrString(record.remark)) }}
              </div>
            </template>
            <span class="cursor-pointer" :class=" record.oderStatus == 3 ? 'text-red' : record.orderStatus == 4 ? 'text-gold' : '' ">
              {{$t("internal.filters.filterCancelType_str")[record.cancelType]}}
              <a-icon class="ml5x" type="info-circle" />
            </span>
          </a-tooltip>
        </div>
        <div v-else >
          <span class=" currency-detail"  :class="filterstatues_class[record.outCome]">{{filterstatues[record.outCome]}}</span>
        </div>
       </template>
        <!-- 合买结果 -->
       <template slot="purchaseFrontendStatus" slot-scope="text, record">
        <div>
          <span class="currency-detail" :class="syndicate_results_class[record.purchaseFrontendStatus]">{{syndicate_results[record.purchaseFrontendStatus]}}</span>
        </div>
       </template>
      
            <!-- 合买喜好-->
            <div slot="buying_preferences" slot-scope="text, record" >
              <div v-if="record.is_syndicate_child">-</div>
              <div v-else>
               <div class="justify-between row">
                <div>{{ $t("internal.syndicate.text1") }}</div>
                <!-- 发起人认购比例 -->
                <div class="Detail-text-grey">{{record.proportion||'0%'}}</div>
               </div>
               <div class="justify-between row">
                <div>{{ $t("internal.syndicate.text2") }}</div>
                <!-- 撤单范围 -->
                <div class="Detail-text-grey">{{expand_conditions[record.purchaseShow]}}</div>
               </div>
               <div class="justify-between row">
                <div>{{ $t("internal.syndicate.text3") }}</div>
                <!-- 认购最低限额 -->
                <div class="Detail-text-grey">{{ $t("internal.syndicate.text11",[record.minSubscribeAmount]) }}</div>
               </div>
               <div class="justify-between row">
                <div>{{ $t("internal.syndicate.text4") }}</div>
                <!-- 中奖后佣金 -->
                <div class="Detail-text-grey">{{record.brokeragePercent||0}}%</div>
               </div>
               <div class="justify-between row">
                <div>{{ $t("internal.syndicate.text5") }}</div>
                <!-- 撤单范围 -->
                <div class="Detail-text-grey">{{record.oddsRange}} ({{record.minOddsRange}}-{{record.maxOddsRange}}) </div>
               </div>
              </div>
              </div>
        <!--赛事状态-->
        <div slot="purchaseStatus" slot-scope="text, record">
         {{ purchaseStatus[text]}}
        </div>
        
   <template slot="footer">
      <a-pagination
        v-if="tabledata.length > 0"
        :total="pagination.total"
        :current="pagination.current"
        show-size-changer
        show-quick-jumper
        :page-size-options="pagination.pageSizeOptions"
        :page-size="pagination.pageSize"
        :show-total="(total) => $t('internal.showTotal_text', [pagination.total]) "
        @change="onChange"
        @showSizeChange="onShowSizeChange"
      />
      <!-- 统计数据 -->
      <div
        class="fs16 position-absolute line-height-24px"
        style="bottom: 5px; left: 25px"
        v-if="tabledata.length > 0 && Object.keys(userStatistics).length > 0"
      >
      <div style="display: flex;">
        <div  style="display: flex;"> 
          <div>
            <div>
              <!--发起历史合买数-->
                  <span class="title-grey"> {{ $t("internal.data.achievements.text3") }} </span>
              </div>
            <div>
              <!-- 胜率 -->
              <span class="title-grey"> {{ $t("internal.data.achievements.text6") }} 
              </span>
            </div>
          </div>
          <div class="ml10x">
            <div>
              <span>
              <!--发起历史合买数-->
              
              <span
              class="fw_600 "
              v-if="userStatistics.countPurchase !== undefined"
            >
           {{ $t("internal.data.betslip.index.sumBetNo_text", { s: userStatistics.countPurchase, }) }}
            </span>
            <span v-else>--</span>
              </span>
              <!-- 商户后台展示合买进行中与合买成功与未成功的所有总数 -->
              <a-tooltip trigger="hover">
                <template slot="title">
                  <div :style="`max-width: 240px; word-break:break-all;`">
                    {{ $t("internal.data.achievements.text10") }}
                  </div>
                </template>
                <a-icon type="question-circle" class="text-red fs15 cursor-pointer float-right pr10x pl10x" style="margin-top: 5px;"/>
              </a-tooltip>
               </div>
            <div>
              <!-- 胜率 -->
              <span>
                <span
                class="fw_600 pr10x"
                v-if="userStatistics.winningRate !== undefined"
              >
                {{ userStatistics.winningRate && userStatistics.winningRate }}
              </span>
              <span v-else>--</span>
            </span>
            <!-- 仅计算合买成功后的数量 -->
            <a-tooltip trigger="hover">
              <template slot="title">
                <div :style="`max-width: 240px; word-break:break-all;`">
                  {{ $t("internal.data.achievements.text15") }}
                </div>
              </template>
              <a-icon type="question-circle" class="text-red fs15 cursor-pointer float-right pr10x pl10x" style="margin-top: 5px;"/>
            </a-tooltip>
            </div>
          </div>
        </div>
        <div  style="display: flex;"> 
          <div>
            <div>  <span class="title-grey">
              {{ $t("internal.data.achievements.text4") }} 
            </span> </div>
            <div>  <span class="title-grey">{{ $t("internal.data.achievements.text7") }} </span></div>
          </div>
          <div class="ml10x">
            <div> <!--合买累计总金额-->
              <span >
              
                <span class="fw_600" v-if="userStatistics.purchaseAmountTotal&&src_internal" >
                  {{ compute_currencyRate_obj_by_code(userStatistics.currency)["countryCn"] }}
                  {{ userStatistics.purchaseAmountTotal | filterMoneyFormat }}
                  </span >
                <span class="fw_600" v-else-if="userStatistics.purchaseAmountTotal&&src_external">
                  ¥{{ userStatistics.purchaseAmountTotal | moneyFormat }}
                  </span>
                <span class="fw_600" v-else>--</span>
                <!-- 商户后台展示合买成功的所有总数，不含中奖金额 -->
              <a-tooltip trigger="hover">
                <template slot="title">
                  <div :style="`max-width: 240px; word-break:break-all;`">
                    {{ $t("internal.data.achievements.text11") }}
                  </div>
                </template>
                <a-icon type="question-circle" class="text-red fs15 cursor-pointer float-right pr10x pl10x" style="margin-top: 5px;"/>
              </a-tooltip>
              </span> </div>
            <div> <!-- 合买中奖总金额 -->
              <span >
           
                <span class="fw_600" v-if="userStatistics.settleAmountTotal&&src_internal">
                  {{ compute_currencyRate_obj_by_code(userStatistics.currency)["countryCn"] }}
                  {{ userStatistics.settleAmountTotal | filterMoneyFormat }}
                  </span>
                <span class="fw_600" v-else-if="userStatistics.settleAmountTotal&&src_external">
                  ¥{{ userStatistics.settleAmountTotal | moneyFormat }}
                  </span>
            <span class="fw_600" v-else>--</span> 
            <!-- 仅计算合买成功后的数量 -->
            <a-tooltip trigger="hover">
              <template slot="title">
                <div :style="`max-width: 240px; word-break:break-all;`">
                  {{ $t("internal.data.achievements.text12") }}
                </div>
              </template>
              <a-icon type="question-circle" class="text-red fs15 cursor-pointer float-right pr10x pl10x" style="margin-top: 5px;"/>
            </a-tooltip>
              </span> </div>
          </div>
        </div>
        <div  style="display: flex;"> 
          <div>
            <div> <span class="title-grey" >
              {{ $t("internal.data.achievements.text5") }}  </span></div>
            <div> <span class="title-grey">{{ $t("internal.data.achievements.text8") }} </span> </div>
          </div>
          <div class="ml10x">
            <div> 
              <!--输／赢场次-->
              <span >
               
                <span
                  v-if="userStatistics.winSessions !== undefined&&userStatistics.lostSessions!=undefined"
                  class="fw_600"
                  >{{ userStatistics.lostSessions }}/{{userStatistics.winSessions }}
                  </span>
                <span v-else>--</span>
                <!-- 仅包含结算后的场次 -->
              <a-tooltip trigger="hover">
                <template slot="title">
                  <div :style="`max-width: 240px; word-break:break-all;`">
                    {{ $t("internal.data.achievements.text13") }}
                  </div>
                </template>
                <a-icon type="question-circle" class="text-red fs15 cursor-pointer float-right pr10x pl10x" style="margin-top: 5px;"/>
              </a-tooltip>
              </span> </div>
            <div> 
              <!-- 合买成功／未成功次 -->
              <span >
               
                <span
                  v-if="userStatistics.successTotal !== undefined&&userStatistics.failTotal!=undefined"
                  class="fw_600"
                  >{{ userStatistics.successTotal }}/{{userStatistics.failTotal }}
                  </span>
                <span v-else>--</span>
                <!-- 所有合买成功与未成功的计数 -->
            <a-tooltip trigger="hover">
              <template slot="title">
                <div :style="`max-width: 240px; word-break:break-all;`">
                  {{ $t("internal.data.achievements.text14") }}
                </div>
              </template>
              <a-icon type="question-circle" class="text-red fs15 cursor-pointer float-right pr10x pl10x" style="margin-top: 5px;"/>
            </a-tooltip>
              </span> </div>
          </div>
        </div>
      </div>
      </div>
   </template>
      </a-table>
    </div>

  </div>
</template>
<script>
  
import moment from "moment";
import { i18n } from "src/boot/i18n";
import { mapGetters, mapActions } from "vuex";
import { api_user,api_merchant,api_data, api_match_statistic } from "src/api/index.js";
import { get_match_type, post_trader_select_list } from "src/api/internal/module/match_statistic/index.js";
import commonmixin from "src/mixins/internal/common/commontoolmixin.js";
import constantmixin from "src/mixins/internal/common/constantmixin.js";
import columns_fn from "src/components/common/achievements_page/config/common.js";//数据中心-赛事投注统计-主列表配置(对内/外)
import log_login_mixin from "src/components/common/bet_slip/internal/mixin/index.js";
import { handleCopy, getStrLength, download } from "src/util/module/common.js";
import commonmixin_external from "src/mixins/external/common/commontoolmixin.js";
import dataCenterMixin from "src/mixins/internal/module/datacentertablemixin.js";
import constantmixin_external from "src/mixins/external/common/constantmixin.js";
import dataCenterMixin_external from "src/mixins/external/module/datacentertablemixin.js";
import ruleComponent from "src/components/widget/rule/index.vue"//需求1726【商户】报表增加有效投注金额字段说明 #2
import treeSelect from "src/components/common/tree/tree_select.vue";
import tableCellEllipsisAnt from "src/components/internal/table/tableCellEllipsisAnt.vue";
import achievements_page from "src/components/common/achievements_page//module/achievements_page.js";


export default {
  mixins: [achievements_page],
  components: {
    treeSelect,
    tableCellEllipsisAnt,
    ruleComponent,//报表增加有效投注金额字段说明
    VNodes: {
      functional: true,
      render: (h, ctx) => ctx.props.vnodes,
    },
  }
  };
</script>
<style lang="scss" scoped>
::v-deep .ant-spin-nested-loading > div > .ant-spin {
	 max-height: 760px;
	 min-height: 760px;
}
 ::v-deep .ant-empty-normal {
	 margin: 310px 0;
}
 .textwidth-2 {
	 width: 172px;
}
 ::v-deep .ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-thead > tr > th {
	 background: #f4f5f8;
}
 ::v-deep .ant-table-middle > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-thead > tr > th {
	 background: #f4f5f8;
}
 ::v-deep .ant-table-middle > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-thead > tr > th {
	 background: #f4f5f8;
}
 .text-over-130 {
	 overflow: hidden;
	 text-overflow: ellipsis;
	 max-width: 110px;
	 white-space: nowrap;
}
.currency-detail.black {
  color:white;
  background:#555555 ;
}
.currency-detail.blue {
  color:white;
  background:#1990FF ;
}
.currency-detail.red {
  color:white;
  background:red ;
}
</style>
