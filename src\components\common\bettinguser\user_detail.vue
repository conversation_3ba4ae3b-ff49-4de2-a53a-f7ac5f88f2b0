<!--
 * @FilePath: /src/components/common/bettinguser/user_detail.vue
 * @Description: (商户中心&账户中心)/投注用户管理/投注用户详情
-->
<template>
  <div class="full-width full-height">
    <div class="pl10x pt10x pb10x" id="top1">
      <q-breadcrumbs separator="/" active-color="whiddte" class="panda-text-2">
        <q-breadcrumbs-el
          v-if="src_internal"
          :label="$t('internal.merchant.bettinguser.user_detail.bar')[0]"
        />
        <q-breadcrumbs-el
          v-else
          :label="$t('external.merchant.bettinguser.user_detail.bar')[0]"
        />
        <q-breadcrumbs-el
          :label="$t('internal.merchant.bettinguser.user_detail.bar')[1]"
        />
        <q-breadcrumbs-el
          :label="$t('internal.merchant.bettinguser.user_detail.bar')[2]"
          class="panda-text-1"
        />
      </q-breadcrumbs>
    </div>
    <div
      class="
        row
        line-height-30px
        shadow-3
        items-center
        text-panda-text-dark
        bg-panda-bg-6
        pb10x
        border-radius-4
        ml10x
        mr10x
      "
    >
      <!-- 用户投注详情 -->
      <div class="col-12">
        <div class="row mb10x">
          <div
            class="
              fs16
              line-height-16px
              pl20x
              pt20x
              text-panda-text-7
              fw_600
              row
            "
          >
            <div>
              <!-- 用户基本信息 -->
              <span class="title-block bg-panda-bg-2 mr5x"></span
              >{{ $t("internal.merchant.bettinguser.user_detail.title") }}
              <!-- 此处只汇总用户近90天的已结算注单记录！与用户基本信息中累计注单数据不一致属于正常现象，详情请咨询平台客服工作人员！ -->
              <a-tooltip trigger="hover">
                <template slot="title">
                  <div :style="`max-width: 240px; word-break:break-all;`">
                    {{
                      $t(
                        "internal.merchant.bettinguser.user_detail.tooltip_question"
                      )
                    }}
                  </div>
                </template>
                <a-icon
                  type="question-circle"
                  class="fs15 ml5x cursor-pointer"
                />
              </a-tooltip>
            </div>
          </div>
          <q-space />
          <div
            class="status line-height-16px pl20x pr20x text-panda-text-7 flex"
          >
            <!-- <div class="pt20x mr20x">{{$t('internal.template.label130')}}: {{userDetail.status == 1? '在线': '--'}}</div> -->
            <!-- 返回 -->
            <a-button
              type="primary"
              style="height: 30px; line-height: 16px"
              class="mt12x"
              @click="handle_back"
              >{{ $t("internal.back") }}</a-button
            >
          </div>
        </div>
        <div class="border-top mb10x"></div>
        <div class="row pl20x pr20x">
          <!-- 第一排数据 -->
          <!-- 用户名 -->
          <div class="bos" style="width: 25%">
            <div class="title">
              {{ $t("internal.merchant.bettinguser.user_detail.userName") }}
            </div>
            <div class="content">{{ userDetail.userName }}</div>
          </div>
          <!-- 可用余额 -->
          <div class="bos" style="width: 25%">
            <div class="title">
              {{ $t("internal.merchant.bettinguser.user_detail.amount") }}
            </div>
            <div class="content" v-if="src_internal">
              {{ userDetail.amount | filterAmount }}
            </div>
            <div class="content" v-else>{{ userDetail.amount | amount }}</div>
          </div>
          <!-- 注册时间 -->
          <div class="bos" style="width: 25%">
            <div class="title">
              {{ $t("internal.merchant.bettinguser.user_detail.createTime") }}
            </div>
            <div class="content">
              {{ moment(userDetail.createTime).format("YYYY-MM-DD HH:mm:ss") }}
            </div>
          </div>
          <!-- 累计登陆次数 -->
          <div class="bos" style="width: 25%">
            <div class="title">
              {{ $t("internal.merchant.bettinguser.user_detail.loginTimes") }}
            </div>
            <div class="content" v-if="userDetail.loginTimes">
              {{ userDetail.loginTimes }}
            </div>
            <div class="content" v-else>--</div>
          </div>
          <!-- 第二排数据 -->
          <!-- 用户ID  -->
          <div class="bos" style="width: 25%">
            <div class="title">
              {{ $t("internal.merchant.bettinguser.user_detail.userId") }}
            </div>
            <div class="content">{{ userDetail.userId }}</div>
          </div>
          <!-- 累计投注额 -->
          <div class="bos" style="width: 25%">
            <div class="title">
              {{ $t("internal.merchant.bettinguser.user_detail.betAmount") }}
            </div>
            <div class="content" v-if="src_internal">
              {{ userDetail.betAmount | filterAmount }}
            </div>
            <div class="content" v-else>
              {{ userDetail.betAmount | amount }}
            </div>
          </div>
          <!-- 注单数量 -->
          <div class="bos" style="width: 25%">
            <div class="title">
              {{ $t("internal.merchant.bettinguser.user_detail.betNum") }}
            </div>
            <div class="content" v-if="src_internal">
              {{ userDetail.betNum | filterAmount }}
            </div>
            <div class="content" v-else>{{ userDetail.betNum | amount }}</div>
          </div>
          <!-- 最后登录时间 -->
          <div class="bos" style="width: 25%">
            <div class="title">
              {{ $t("internal.merchant.bettinguser.user_detail.lastLogin") }}
            </div>
            <div class="content" v-if="userDetail.lastLogin">
              {{
                moment(userDetail.lastLogin * 1).format("YYYY-MM-DD HH:mm:ss")
              }}
            </div>
            <div class="content" v-else>--</div>
          </div>
          <!-- 第三排数据 -->
          <!-- 用户币种 -->
          <div class="bos" style="width: 25%">
            <div class="title">
              {{ $t("internal.merchant.bettinguser.user_detail.currencyCode") }}
            </div>
            <div class="content">
              {{ filterCurrency[userDetail.currencyCode] }}
            </div>
          </div>
          <!-- 累计盈利 -->
          <div class="bos" style="width: 25%">
            <div class="title">
              {{ $t("internal.merchant.bettinguser.user_detail.profit") }}
            </div>
            <div class="content" v-if="src_internal">
              {{ userDetail.profit | filterAmount }}
            </div>
            <div class="content" v-else>{{ userDetail.profit | amount }}</div>
          </div>
          <!-- 所属商户 -->
          <div class="bos" style="width: 25%">
            <div class="title">
              {{ $t("internal.merchant.bettinguser.user_detail.merchantName") }}
            </div>
            <div class="content">{{ userDetail.merchantName }}</div>
          </div>
          <!-- 最后登录时间 -->
          <div class="bos" style="width: 25%">
            <div class="title">
              {{ $t("internal.merchant.bettinguser.user_detail.lastBetStr") }}
            </div>
            <div class="content" v-if="userDetail.lastBetStr">
              {{ userDetail.lastBetStr }}
            </div>
            <div class="content" v-else>--</div>
          </div>
          <!-- VIP升级时间 （显示格式 yyyy-mm-ss hh:ms:ss，非VIP用户空着即可）-->
          <div class="bos" style="width: 25%">
            <div class="title">
              {{ $t("internal.merchant.bettinguser.user_detail.a1") }}
            </div>
            <div class="content" v-if="userDetail.vipUpdateTime">
              {{
                moment(userDetail.vipUpdateTime).format("YYYY-MM-DD HH:mm:ss")
              }}
            </div>
            <div class="content" v-else>--</div>
          </div>
          <!--合买资格-->
          <div class="bos" style="width: 25%" v-if="src_internal|| (src_external && $_btn_has('userPurchase')) ">
            <div class="title">
              {{ $t("internal.merchant.bettinguser.user_detail.text1") }}
            </div>
            <div class="content">{{ purchase_name[userDetail.purchaseQualified] }}</div>
          </div>
          <!-- vip 等级场馆 -->
          <div class="bos" style="width: 25%">
            <div class="title">
              {{ $t("internal.merchant.bettinguser.user_detail.vip") }}
            </div>
            <div class="content" v-if="userDetail.userVipLevel">
             
              {{ userDetail.userVipLevel }} - <span v-show="userDetail.vipType ==1">{{$t('internal.vip_user_overview.system')}}</span><span v-show="userDetail.vipType ==2">{{$t('internal.vip_user_overview.customize')}}</span>
            </div>
            <div class="content" v-else>--</div>
          </div>    
           <!-- vip 等级场馆 -->
          <div class="bos" style="width: 25%" v-if="src_internal">
            <div class="title">
              {{ $t("internal.merchant.bettinguser.user_detail.venue_vip") }}
            </div>
            <div class="content" v-if="userDetail.userVenueVipLevel ">
              {{ userDetail.userVenueVipLevel  }}
            </div>
            <div class="content" v-else>--</div>
          </div>               
        </div>
      </div>
    </div>
    <div class="col-12 bg-panda-bg-6 radius-4 ml10x mr10x shadow-3">
      <!-- tab选项卡 -->
      <div class="row mt10x panda-text-2 position-relative">
        <span
          class="title-block bg-panda-bg-2 mr5x mt15x position-absolute"
          style="left: 20px"
        ></span>
        <a-tabs v-model="activeKey" class="col-12">
          <a-tab-pane
            key="2"
            :tab="$t('internal.merchant.bettinguser.user_detail.activeKey')[0]"
            force-render
          >
            <!-- //账变记录  -->
            <user-account
              v-if="activeKey == 2"
              :userDetail="userDetail"
            ></user-account>
          </a-tab-pane>
          <a-tab-pane
            key="3"
            :tab="$t('internal.merchant.bettinguser.user_detail.activeKey')[1]"
          >
            <!-- // 注单记录 -->
            <user-bet
              v-if="activeKey == 3"
              :show_currency_text="show_currency_text"
            ></user-bet>
          </a-tab-pane>
          
          <a-tab-pane
          v-if="(src_internal|| (src_external && $_btn_has('userPurchase')) )"
            key="4"
            :tab="$t('internal.merchant.bettinguser.user_detail.activeKey')[2]"
          >
          <!-- 合买记录 -->
          <bet_slip
          v-if="activeKey == 4"
          :bet_import_detail="{...userDetail,bet_import_type:'syndicate_records'}"
        ></bet_slip>
          </a-tab-pane>
          <!-- 参与人不显示合买战绩 -->
          <a-tab-pane
          v-if="userDetail.purchaseQualified!=1&&(src_internal|| (src_external && $_btn_has('userPurchase')) )"
            key="5"
            :tab="$t('internal.merchant.bettinguser.user_detail.activeKey')[3]"
          >
          <!-- 战绩 -->
          
          <achievements_page
          v-if="activeKey == 5"
          :userDetail="userDetail"
        ></achievements_page>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import { i18n } from "src/boot/i18n";
import { api_merchant, api_data } from "src/api/index.js";
import constantmixin from "src/mixins/internal/common/constantmixin.js";
import constantmixin_external from "src/mixins/external/common/constantmixin.js";
import userBet from "src/components/common/user_bet/index.vue"; // 注单记录
import bet_slip from "src/components/common/bet_slip/index.vue"; // 合买记录
import userAccount from "src/components/common/user_account/index.vue"; //账变记录
import achievements_page from "src/components/common/achievements_page/index.vue"; //合买战绩
import moment from "moment";
const FOR_WHICH = process.env.FOR_WHICH;
let mixins_custom = [constantmixin];
if (FOR_WHICH == "external") {
  mixins_custom = [constantmixin_external];
}
export default {
  components: {
    bet_slip,
    userBet,
    userAccount,
    achievements_page
  },
  mixins: [...mixins_custom],
  data() {
    return {
      purchase_name:{
        0: i18n.t("internal.merchant.bettinguser.user_detail.text2") ,
        1: i18n.t("internal.merchant.bettinguser.user_detail.text3") 
      },
      userDetail: "", //用户详情
      activeKey: "2", // 默认为账变记录
    };
  },
  computed: {
    show_currency_text() {
      let obj = this.compute_currencyRate_obj_by_code(
        this.userDetail.currencyCode
      );
      return obj["countryCn"];
    },
  },
  created() {
    this.init_session_record();
  },
  methods: {
    moment,
    handle_back() {
      this.$router.go(-1);
    },
    /**
     * @description 初始化用户基本信息
     * @return {undefined} undefined
     */
    async init_session_record() {
      let record = this.$q.sessionStorage.getItem("record");
      this.userDetail = record;
      let get_order_user_getUserDetail = this.src_internal
        ? api_merchant.get_order_user_getUserDetail
        : api_data.get_admin_userReport_getUserInfo;
      let res = await get_order_user_getUserDetail({
        uid: record.userId,
        currencyCode: this.src_internal
          ? record["record"]
          : record["currencyCode"],
      });
      // console.warn(res)
      if (res.data.code == "0000000") {
        this.userDetail = { ...record, ...res.data.data };
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .ant-progress-circle-path {
  stroke: #fbc02d;
}
::v-deep .q-tab-panel {
  padding: 0;
}
.bos {
  box-sizing: border-box;
  margin-top: -1px;
}
.title {
  width: 30%;
  display: inline-block;
  line-height: 32px;
  height: 32px;
  text-align: left;
  color: #666;
  font-size: 14px;
  box-sizing: border-box;
}
.content {
  width: 70%;
  display: inline-block;
  line-height: 32px;
  height: 32px;
  text-align: left;
  font-size: 14px;
  color: #333;
  vertical-align: top;
  box-sizing: border-box;
}
::v-deep .ant-tabs-nav .ant-tabs-tab {
  font-size: 16px;
  font-weight: 600;
  color: $panda-text-1;
}
::v-deep .ant-tabs-bar {
  margin: 0 0 0 20px;
}
::v-deep
  .ant-table-middle
  > .ant-table-content
  > .ant-table-scroll
  > .ant-table-header
  > table
  > .ant-table-thead
  > tr
  > th {
  background: #f4f5f8;
}
</style>
