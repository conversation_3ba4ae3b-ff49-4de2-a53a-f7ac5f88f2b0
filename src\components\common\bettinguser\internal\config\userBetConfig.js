/*
 * @Desc: 
 * @Date: 2020-06-24 16:02:47
 * @Author:Nice
 * @FilePath: /src/components/common/bettinguser/internal/config/userBetConfig.js
 */
import { i18n } from 'src/boot/i18n';
let table_title = i18n.t('internal.merchant.bettinguser.userBetConfig')
 export const userBetConfig = [
  {
    title: table_title[0],
    width: 70,
    dataIndex: "_index",
    key: "_index",
    align: "center"
  },
  {
    title: table_title[1],
    width: 210,
    dataIndex: "seriesValue",
    key: "0",
    align: "center"
  },
  {
    title: table_title[2],
    dataIndex: "orderDetailList",
    key: "1",
    width: 300,
    align: "center",
    scopedSlots:{customRender:"orderDetailList"},
  },
  {
    title: table_title[3],
    dataIndex: "productAmountTotal",
    key: "2",
    width: 170,
    sorter: true, 
    align: "center",
    scopedSlots:{customRender:"productAmountTotal"},
  },
  {
    title: table_title[4],
    dataIndex: "profitAmount",
    key: "3",
    width: 170,
    sorter: true, 
    scopedSlots:{customRender:"profitAmount"},
    align: "center"
  },
  {
    title: table_title[5],
    dataIndex: "orderNo",
    key: "4",
    width: 170,
    align: "center",
    scopedSlots:{customRender:"orderNo"},
  },
  {
    title: table_title[6],
    dataIndex: "createTimeStr",
    key: "5",
    width: 170,
    align: "center"
  },
  {
    title: table_title[7],
    dataIndex: "settleStatus",
    key: "6",
    width: 170,
    align: "center",
    scopedSlots:{customRender:"settleStatus"},
  }
];

