<!--
 * @FilePath: /src/components/common/bet_slip/component/bet_no_table.vue
 * @Description: 百家赔注单table页
-->
<template>
  <div class="bet_no_table">
    <a-table class="pl10x pr10x parent-table-open" :columns="columns" :dataSource="tabledata" :scroll="{ x: 1660, y: table_scrollHeight - 50 }" :pagination="false" :loading="tabledata_loading" :expanded-row-keys.sync="expandedRowKeys" size="middle" rowKey="orderNo">
      <!-- 用户名td -->
      <div slot="userId" slot-scope="text, record" class="tdpadding">
        <div>
          <!-- 用户名 -->
          <span class="cursor-pointer" @click="handleCopy(record.userName, $t('internal.username_text'))">{{ record.userName }}</span>
        </div>
        <div>
          <div class="Detail-text-grey cursor-pointer">
            <!-- 注单号 -->
            <span @click="handleCopy(record.orderNo, $t('internal.orderNo_text'))">
              {{ $t("internal.orderNo_text") }} {{ record.orderNo }}
            </span>
            <span v-if="record.preSettleList && record.preSettleList.length" :class="record.preSettleList && record.preSettleList.length ? cash_orange : ''" class="Detail-text-grey">CASH</span>
            <!-- +展开行按钮 提前结算注单有数据则显示-->
            <span v-if="record.preSettleList && record.preSettleList.length" class="float-right">
              <div @click="up_and_down_cash(record.orderNo)" :style=" src_internal ? 'line-height: 16px;' : 'line-height: 13px;' " :class=" expandedRowKeys.includes(record.orderNo) ? 'ant-table-row-expand-icon ant-table-row-expanded' : 'ant-table-row-expand-icon ant-table-row-collapsed' ">
              </div>
            </span>
          </div>
          <!-- 用户ID -->
          <div class="Detail-text-grey cursor-pointer" @click="handleCopy(record.userId, $t('internal.uid_text'))">
            {{ $t("internal.uid_text") }} {{ record.userId }}
          </div>
        </div>
      </div>
      <!-- 赛事信息 -->
      <div slot="betNoInfo" slot-scope="text, record">
          <div class="item-open position-relative">
            <table-cell-ellipsis-ant v-if="record.matchInfoZs == ' v '" :span_html="
                  '<div class=\'ellipsis\'>' +
                  $t('internal.data.betslip.index.virtual_sport') +
                  '</div><div class=\'ellipsis Detail-text-grey\'>' +
                  record.sportNameZs +
                  '&nbsp;/&nbsp;' +
                  record.tournamentNameZs +
                  '</div>'
                " :str_all="
                  '<div>' +
                  $t('internal.data.betslip.index.virtual_sport') +
                  '</div><div>' +
                  record.sportNameZs +
                  '&nbsp;/&nbsp;' +
                  record.tournamentNameZs +
                  '</div>'
                "></table-cell-ellipsis-ant>
            <table-cell-ellipsis-ant v-else :span_html="
                  '<div class=\'ellipsis\'>' +
                  record.matchInfoZs +
                  '</div><div class=\'ellipsis Detail-text-grey\'>' +
                  record.sportNameZs +
                  '&nbsp;/&nbsp;' +
                  record.tournamentNameZs +
                  '</div>'
                " :str_all="
                  '<div>' +
                  record.matchInfoZs +
                  '</div><div>' +
                  record.sportNameZs +
                  '&nbsp;/&nbsp;' +
                  record.tournamentNameZs +
                  '</div>'
                "></table-cell-ellipsis-ant>
            <!-- 展示轮次的第二种解决方案 -->
            <!-- 展示轮次的第一种解决方案 -->
            <span class="position-absolute fs12" style="right: 5px; top: 20px" v-if="record.managerCode == 3">{{ record.matchDay }}</span>
            <span class="row">
              <!-- 赛事ID -->
              <span class="Detail-text-grey mr5x">
                {{ $t("internal.data.betslip.index.matchId") }}
              </span>
              <a-tooltip v-if=" (record.matchId + '').length > 10 && record.managerCode !== 3 " placement="bottom">
                <template slot="title">
                  <div v-html="record.matchId + ''" :style="`max-width: 150px; word-break:break-all;`"></div>
                </template>
                <span class="text-over2 Detail-text-grey cursor-pointer" @click=" handleCopy( record.matchId, $t('internal.data.betslip.index.matchId') ) ">
                  {{ record.matchId }}
                </span>
              </a-tooltip>
              <!-- 工单：34142  对内复制长ID matchManageId -->
              <span v-else-if="src_internal" class="text-over2 Detail-text-grey cursor-pointer" @click="handleCopy(record.matchManageId || record.matchId, $t('internal.data.betslip.index.matchId'))">
                {{ record.matchId }}
              </span>
              <span v-else class="text-over2 Detail-text-grey cursor-pointer" @click=" handleCopy( record.matchId, $t('internal.data.betslip.index.matchId') ) ">
                {{ record.matchId }}
              </span>
            </span>
            <!-- 时间 -->
            <div class="Detail-text-grey col-2 position-absolute" style="right: 5px; bottom: 3px">
              {{ record.beginTime }}
            </div>
        </div>
      </div>
      <!-- 注单详情  -->
      <div slot="betNoDetail" slot-scope="text, record">
        <div class="item-content position-relative">
          <div class="item-open">
            <div class="row justify-between">
              <div class="ellipsis row">
                <!-- 投注名称 -->
                <table-cell-ellipsis-ant :span_html=" record.scoreBenchmark ? '<div class=\'ellipsis\' style=\'max-width:194px\'>' + record.playName + '</div>' : '<div class=\'ellipsis\'>' + record.playNameZs + '</div>' " :str_all="'<div>' + record.playNameZs + '</div>'" style="max-width: 160px">
                </table-cell-ellipsis-ant>
                <!-- 比分    示例："scoreBenchmark": "2:4"-->
                <span v-if="record.scoreBenchmark" class="ml5x">
                  [{{ record.scoreBenchmark.replace(":", "-") }}]
                </span>
              </div>
              <!-- 虚拟体育只有赛前盘 -->
              <div class="ellipsis" v-if="record.managerCode !== 3">
                {{ record.matchType | filterMatchTypeList }}
              </div>
              <!-- 赛前 -->
              <div class="ellipsis" v-else>{{ 1 | filterMatchTypeList }}</div>
            </div>
            <div class="row justify-between">
              <!-- 投注项名称 -->
              <div :class="[ 'col-12' ]">
                <table-cell-ellipsis-ant :span_html=" '<div class=\'text-FFBA72 ellipsis\'>' + record.playOptionZs + '</div>' " :str_all="'<div>' + record.playOptionZs + '</div>'"></table-cell-ellipsis-ant>
              </div>
            </div>
            <!-- 赛果 -->
            <div class="Detail-text-grey col-2 row" v-if="record.settleScore">
              <div>{{ $t("internal.data.betslip.index.settleScore") }}</div>
              <table-cell-ellipsis-ant :span_html=" '<div class=\'ellipsis ml5x\'  style=\'max-width:90px\'>' + record.settleScore + '</div>' " :str_all="'<div>' + record.settleScore + '</div>'">
              </table-cell-ellipsis-ant>
            </div>
          </div>
          <!-- 投注时间-->
          <div class="Detail-text-grey position-absolute" style="right: 10px; bottom: 0px">
            {{ $t("internal.data.betslip.index.betting") }}{{ record.createTime.slice(5, record.createTime.length - 4) + ` '${record.createTime.slice(-3)}''` }}
          </div>
        </div>
      </div>
      <!-- 赔率 -->
      <div slot="oddsValue" slot-scope="text, record">
        <template >
          <div class="item-open" :class="src_internal ? ' mid-center' : ''">
            <!-- 赔率 -->
            <div class="text-red" :class="record.betStatus == 0 ? 'pt10x' : ''">
              {{ record.oddsValue }}
            </div>
            <!-- 盘口类型 -->
            <div class="Detail-text-grey">[{{ record.marketType }}]</div>
            <!-- 结算时间 -->
            <div class="Detail-text-grey">
              {{
                record.settleTime && record.settleTime != "0"
                  ? `${record.settleTime.slice(5, record.settleTime.length - 4)} '${record.settleTime.slice(-3)}''`
                  : ""
              }}
            </div>
          </div>
        </template>
      </div>
      <!-- 注额(本地) -->
      <div slot="localBetAmount" slot-scope="text, record">
        <div class="item-open text-center">
          <!-- 金额 -->
          <div class="pt10x" v-if="src_internal">
            {{ record.betAmount == 0 || !record.betAmount ? "-" : record.betAmount | filterAmount }}
          </div>
          <div class="pt10x" v-else>
            {{ record.betAmount == 0 || !record.betAmount ? "-" : record.betAmount | amount }}
          </div>
          <!-- 币别 -->
          <div class="Detail-text-grey">{{ is_zs === 'zs' ? '人民币' : 'CNY' }}</div>
        </div>
      </div>
      <!-- 输赢(本地) -->
      <div slot="localProfitAmount" slot-scope="text, record">
        <div class="item-open text-center">
          <div v-if="src_internal" class="pt10x">
            {{ record.netAmount == 0 || !record.netAmount ? "-" : record.netAmount | filterAmount }}
          </div>
          <div v-else-if="src_external" class="pt10x">
            {{ record.netAmount == 0 || !record.netAmount ? "-" : record.netAmount | amount }}
          </div>
          <div class="Detail-text-grey">{{ is_zs === 'zs' ? '人民币' : 'CNY' }}</div>
        </div>
      </div>
      <!-- 设备信息 -->
      <div slot="deviceType" slot-scope="text, record">
        <div class="item-open">
          <div>[{{ text }}]</div>
          <div class="Detail-text-grey">{{ record.ip }}</div>
          <a-tooltip v-if="getStrLength(record.area) > 16" placement="bottom">
            <template slot="title">
              <div :style="`max-width: 200px; word-break:break-all;`">
                {{ record.area }}
              </div>
            </template>
            <span class="text-over2 Detail-text-grey">{{
                record.area
              }}</span>
          </a-tooltip>
          <span v-else class="text-over2 Detail-text-grey">{{
              record.area
            }}</span>
        </div>
      </div>
      <!-- 百家赔 -->
      <div slot="riskDescM" slot-scope="text, record">
        <div class="row ml10x">
          <!-- riskDesc手工上传赔率 -->
          <!-- <div v-for="(item, idx) in computed_bet_no(record.riskDesc)" :key="`descM-${idx}`" class="text-green-9">
            <span v-if="idx < 2">{{ item }}</span>
            <a-tooltip placement="bottom">
              <template slot="title">
                <div v-for="(desc, i) in computed_bet_no(record.riskDesc)" :key="`descM_${i}`">{{ desc }}</div>
              </template>
              <span v-if="idx + 1 == 2 && computed_bet_no(record.riskDesc).length > 2" class="ant-table-row-expand-icon q-ml-xs">+</span>
            </a-tooltip>
          </div> -->
          <!-- 新流程 -->
          <!-- 一两条数据 -->
          <div v-if="record.oddsList && record.oddsList.length<3">
            <div v-for="(item, index) in record.oddsList" :key="index" class="text-green-9">
              <div class="text-left text-panda-text-primary">
                <span class="fs12">{{computed_bet_no(item.riskDesc) }}</span>
                  <iIcon v-if="item.waterLevelNum && !item.waterLevelNum.includes('-')" type="ios-arrow-round-up" style="color: red;font-size: 20px;"/>
                  <iIcon v-if="item.waterLevelNum && item.waterLevelNum.includes('-')" type="ios-arrow-round-down" style="color: green;font-size: 20px;"/>
                  <span class="fs12" v-if="item.waterLevelNum" :style="{ color: item.waterLevelNum.includes('-') ? 'green' : 'red' }">
                    {{ item.waterLevelNum }}
                  </span>
                
                <span v-if="!item.waterLevelNum">--</span>
              </div>
            </div>
          </div>
          <!-- 多条数据 -->
          <div v-if="record.oddsList && record.oddsList.length>=3">
            <div class="q-gutter-md">
              <q-btn>
                <div>
                  <div class="text-panda-text-primary text-green-9 text-left">
                    <span class="fs12">{{  computed_bet_no(record.oddsList[0].riskDesc)  }}</span>
                      <iIcon v-if="record.oddsList[0].waterLevelNum && !record.oddsList[0].waterLevelNum.includes('-')" type="ios-arrow-round-up" style="color: red;font-size: 20px;" />
                      <iIcon v-if="record.oddsList[0].waterLevelNum && record.oddsList[0].waterLevelNum.includes('-')" type="ios-arrow-round-down" style="color: green;font-size: 20px;" />
                      <span class="fs12" v-if="record.oddsList[0].waterLevelNum" :style="{ color: record.oddsList[0].waterLevelNum.includes('-') ? 'green' : 'red' }">
                        {{ record.oddsList[0].waterLevelNum }}
                      </span>
                    <span v-if="!record.oddsList[0].waterLevelNum">--</span>
                  </div>
                  <div class="text-panda-text-primary text-green-9 text-left" v-if="record.oddsList.length>1">
                    <span class="fs12">{{ computed_bet_no(record.oddsList[1].riskDesc)  }}</span>
                    <iIcon v-if="record.oddsList[1].waterLevelNum && !record.oddsList[1].waterLevelNum.includes('-')" type="ios-arrow-round-up" style="color: red;font-size: 20px;" />
                    <iIcon v-if="record.oddsList[1].waterLevelNum && record.oddsList[1].waterLevelNum.includes('-')" type="ios-arrow-round-down" style="color: green;font-size: 20px;" />
                    <span class="fs12" v-if="record.oddsList[1].waterLevelNum" :style="{ color: record.oddsList[1].waterLevelNum.includes('-') ? 'green' : 'red' }">
                      {{ record.oddsList[1].waterLevelNum }}...
                    </span>
                    <span v-if="!record.oddsList[1].waterLevelNum">--</span>
                  </div>
                </div>
                <q-tooltip  content-class="bg-white" :offset="[10, 10]">
                  <div v-for="(item, index) in record.oddsList" :key="index" >
                    <div class="text-panda-text-primary text-green-9 text-left">
                      <span>{{ computed_bet_no(item.riskDesc)  }}</span>&nbsp;&nbsp;
                      <iIcon v-if="item.waterLevelNum && !item.waterLevelNum.includes('-')" type="ios-arrow-round-up" style="color: red;font-size: 20px;" />
                      <iIcon v-if="item.waterLevelNum && item.waterLevelNum.includes('-')" type="ios-arrow-round-down" style="color: green;font-size: 20px;" />
                      <span v-if="item.waterLevelNum" :style="{ color: item.waterLevelNum.includes('-') ? 'green' : 'red' }">
                        {{ item.waterLevelNum }}
                      </span>
                      <span v-else>--</span>
                    </div>
                  </div>
                </q-tooltip>
              </q-btn>
            </div>
          </div>
        </div>
      </div>
      <!-- 注单标签 -->
      <div slot="riskType" slot-scope="text, record">
        <div class="row justify-center text-secondary tdpadding">
          {{ record.riskType || '--' }}
        </div>
      </div>
      
      <!--提前结算子单 里面的内容完全和上面的table的代码一摸一样-->
      <div slot="expandedRowRender" slot-scope="text" style="margin: 0">
        <a-table class="expandedRowRender-detail" :columns="columns" :dataSource="tabledata_obj[text.orderNo]" :pagination="false" :loading="tabledata_loading" @change="sorterForTable" size="middle" rowKey="_index">
          <div slot="_index">-</div>
          <!--用户名-->
          <div slot="userId" slot-scope="text, record" class="tdpadding">
            <div>
              <div class="Detail-text-grey cursor-pointer" @click=" handleCopy(record.orderNo, $t('internal.orderNo_text')) ">
                {{ $t("internal.orderNo_text") }}{{ record.orderNo }}
                <span :class="cash_orange">CN</span>
              </div>
            </div>
          </div>
          <!--赛事信息-->
          <div slot="betNoInfo">
            <span class="item-open position-relative">-</span>
          </div>
          <!--注单详情-->
          <div slot="betNoDetail" slot-scope="text, record">
            <span class="item-open position-relative">
              {{ record.preSettle === '2' ? $t("internal.common.statisticalPointer_24") : $t("internal.common.statisticalPointer_23") }}
            </span>
          </div>
          <!-- 赔率 -->
          <div slot="oddsValue" slot-scope="text, record">
            <template>
              <div class="item-open mid-center">
                <div class="text-red">{{ record.oddsValue }}</div>
                <div class="Detail-text-grey">
                  <!-- 子单 统一使用欧赔 -->
                  [EU]
                  <!-- [{{ record.marketType }}] -->
                </div>
                <!-- 结算时间 -->
                <div class="Detail-text-grey">
                  {{
                    record.settleTime && record.settleTime != "0"
                      ? `${record.settleTime.slice(5, record.settleTime.length - 4)} '${record.settleTime.slice(-3)}''`
                      : ""
                  }}
                </div>
              </div>
            </template>
          </div>
          <!-- 注额(本地) -->
          <div slot="localBetAmount" slot-scope="text, record">
            <div class="item-open text-center">
              <div class="pt10x">
                {{ record.betAmount == "0" || record.betAmount == "0.00" || !record.betAmount ? "-" : record.betAmount | filterAmount_ }}
              </div>
              <div class="Detail-text-grey">
                {{ is_zs === 'zs' ? '人民币' : 'CNY' }}
              </div>
            </div>
          </div>
          <!-- 输赢(本地) -->
          <div slot="localProfitAmount" slot-scope="text, record">
            <div class="item-open text-center">
              <div class="pt10x">
                {{ record.netAmount == "0" || record.netAmount == "0.00" || !record.netAmount ? "-" : record.netAmount | filterAmount_ }}
              </div>
              <div class="Detail-text-grey">
                {{ is_zs === 'zs' ? '人民币' : 'CNY' }}
              </div>
            </div>
          </div>
          <!-- 设备信息 -->
          <div slot="deviceType" slot-scope="text, record">
            <div class="item-open">
              <div>[{{ text }}]</div>
              <div class="Detail-text-grey">{{ record.ip }}</div>
              <a-tooltip v-if="getStrLength(record.area) > 16" placement="bottom">
                <template slot="title">
                  <div :style="`max-width: 200px; word-break:break-all;`">
                    {{ record.area }}
                  </div>
                </template>
                <span class="text-over2 Detail-text-grey">{{ record.area }}</span>
              </a-tooltip>
              <span v-else class="text-over2 Detail-text-grey">{{ record.area }}</span>
            </div>
          </div>
          <!-- 百家赔 -->
          <div slot="riskDescM" slot-scope="text, record">
            <div class="row ml10x">
              <!-- riskDesc手工上传赔率 -->
              <!-- <div v-for="(item, idx) in computed_bet_no(record.riskDesc)" :key="`descM-${idx}`" class="text-green-9">
                <span v-if="idx < 2">{{ item }}</span>
                <a-tooltip placement="bottom">
                  <template slot="title">
                    <div v-for="(desc, i) in computed_bet_no(record.riskDesc)" :key="`descM_${i}`">{{ desc }}</div>
                  </template>
                  <span v-if="idx + 1 == 2 && computed_bet_no(record.riskDesc).length > 2" class="ant-table-row-expand-icon q-ml-xs">+</span>
                </a-tooltip>
              </div> -->
              <!-- 新流程  -->
              <!-- 一两条数据 -->
              <div v-if="record.oddsList && record.oddsList.length<3">
                <div v-for="(item, index) in record.oddsList" :key="index" class="text-green-9">
                  <div class="text-left text-panda-text-primary">
                    <span class="fs12">{{ computed_bet_no(item.riskDesc)  }}</span>&nbsp;
                      <iIcon v-if="item.waterLevelNum && !item.waterLevelNum.includes('-')" type="ios-arrow-round-up" style="color: red;font-size: 20px;"/>
                      <iIcon v-if="item.waterLevelNum && item.waterLevelNum.includes('-')" type="ios-arrow-round-down" style="color: green;font-size: 20px;"/>
                      <span class="fs12" v-if="item.waterLevelNum" :style="{ color: item.waterLevelNum.includes('-') ? 'green' : 'red' }">
                        {{ item.waterLevelNum }}
                      </span>
                    <span v-if="!item.waterLevelNum">--</span>
                  </div>
                </div>
              </div>
              <!-- 多条数据 -->
              <div v-if="record.oddsList && record.oddsList.length>=3">
                <div class="q-gutter-md">
                  <q-btn>
                    <div>
                      <div class="text-panda-text-primary text-green-9 text-left">
                        <span class="fs12">{{ computed_bet_no(record.oddsList[0].riskDesc)  }}</span>
                          <iIcon v-if="record.oddsList[0].waterLevelNum && !record.oddsList[0].waterLevelNum.includes('-')" type="ios-arrow-round-up" style="color: red;font-size: 20px;" />
                          <iIcon v-if="record.oddsList[0].waterLevelNum && record.oddsList[0].waterLevelNum.includes('-')" type="ios-arrow-round-down" style="color: green;font-size: 20px;" />
                          <span class="fs12" v-if="record.oddsList[0].waterLevelNum" :style="{ color: record.oddsList[0].waterLevelNum.includes('-') ? 'green' : 'red' }">
                            {{ record.oddsList[0].waterLevelNum }}
                          </span>
                        <span v-if="!record.oddsList[0].waterLevelNum">--</span>
                      </div>
                      <div class="text-panda-text-primary text-green-9 text-left" v-if="record.oddsList.length>1">
                        <span>{{ computed_bet_no(record.oddsList[1].riskDesc)  }}</span>
                        <iIcon v-if="record.oddsList[1].waterLevelNum && !record.oddsList[1].waterLevelNum.includes('-')" type="ios-arrow-round-up" style="color: red;font-size: 20px;" />
                        <iIcon v-if="record.oddsList[1].waterLevelNum && record.oddsList[1].waterLevelNum.includes('-')" type="ios-arrow-round-down" style="color: green;font-size: 20px;" />
                        <span v-if="record.oddsList[1].waterLevelNum" :style="{ color: record.oddsList[1].waterLevelNum.includes('-') ? 'green' : 'red' }">
                          {{ record.oddsList[1].waterLevelNum }}...
                        </span>
                        <span v-if="!record.oddsList[1].waterLevelNum">--</span>
                      </div>
                    </div>
                    <q-tooltip  content-class="bg-white" :offset="[10, 10]">
                      <div v-for="(item, index) in record.oddsList" :key="index" >
                        <div class="text-panda-text-primary text-green-9 text-left">
                          <span>{{ computed_bet_no(item.riskDesc)  }}</span>&nbsp;&nbsp;
                          <iIcon v-if="item.waterLevelNum && !item.waterLevelNum.includes('-')" type="ios-arrow-round-up" style="color: red;font-size: 20px;" />
                          <iIcon v-if="item.waterLevelNum && item.waterLevelNum.includes('-')" type="ios-arrow-round-down" style="color: green;font-size: 20px;" />
                          <span v-if="item.waterLevelNum" :style="{ color: item.waterLevelNum.includes('-') ? 'green' : 'red' }">
                            {{ item.waterLevelNum }}
                          </span>
                          <span v-else>--</span>
                        </div>
                      </div>
                    </q-tooltip>
                  </q-btn>
                </div>
              </div>
            </div>
          </div>
          <!-- 注单标签 -->
          <div slot="riskType" slot-scope="text, record">
            <div class="row justify-center text-secondary tdpadding">
              {{ record.riskType || '--' }}
            </div>
          </div>
        </a-table>
      </div>
    </a-table>
    <!-- 翻页器 -->
    <div class="col-5">
      <!-- v-if="!tabledata.length"  -->
      <a-pagination :total="pagination.total" :current="pagination.current" style="box-shadow: none" show-size-changer show-quick-jumper :page-size-options="pagination.pageSizeOptions" :page-size="pagination.pageSize" @change="onChange" @showSizeChange="onShowSizeChange" :show-total="
      (total) => $t('internal.showTotal_text', [pagination.total])" />
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { handleCopy, getStrLength } from "src/util/module/common.js";
import commonmixin from "src/mixins/internal/common/commontoolmixin.js";
import commonmixin_external from "src/mixins/external/common/commontoolmixin.js";
import dataCenterMixin from "src/components/common/bet_slip/internal/mixin/datacentertablemixin.js";
import dataCenterMixin_external from "src/components/common/bet_slip/external/mixin/datacentertablemixin.js";
import log_login_mixin from "src/components/common/bet_slip/internal/mixin/index.js";
import log_login_mixin_external from "src/components/common/bet_slip/external/mixin/index.js";
import constantmixin from "src/mixins/internal/common/constantmixin.js";
import constantmixin_external from "src/mixins/external/common/constantmixin.js";
import usernamemixin from "src/mixins/internal/common/usernamemixin.js";
import { Icon } from "view-design";
const FOR_WHICH = process.env.FOR_WHICH;
let mixins_custom = [
  commonmixin,
  dataCenterMixin,
  log_login_mixin,
  constantmixin,
  usernamemixin,
];
if (FOR_WHICH == "external") {
  mixins_custom = [
    commonmixin_external,
    dataCenterMixin_external,
    log_login_mixin_external,
    constantmixin_external,
  ];
}
export default {
  mixins: [...mixins_custom],
  components: {
    'iIcon': Icon,
  },
  props: {
    columns: {
      type: Array,
      required: true,
    },
    tabledata_loading: {
      type: Boolean,
      default: () => false,
    },
    is_zs: {
      type: String,
      default: () => true,
    },
    expandedRowKeys: {
      type: Array,
      default: () => [],
    }
  },
  data() {
    return {
      cash_orange: "text-FFBA72", // 橙色字体
      cash_gray: "text-gray", // 灰色字体
      tabledata: [],
      table_scrollHeight: 0,
      tabledata_obj: {},
    }
  },
  computed: {
    ...mapGetters(["get_window_size_change"]),
  },
  watch: {
    get_window_size_change() {
      this.compute_table_scrollHeight();
    },
  },
  mounted() {
    this.compute_table_scrollHeight();
  },
  methods: {
    handleCopy,
    getStrLength,
    onChange(current_page) {
      this.$emit('change_current_page', current_page);
    },
    onShowSizeChange(current_page, pageSize) {
      this.pagination.pageSize = pageSize;
      this.$emit('change_page_size', pageSize);
    },
    // 接受从父组件传过来的tabledata参数
    get_table_data(tabledata, pagination) {
      this.tabledata = tabledata;
      this.tabledata_obj = tabledata.reduce((acc, cur)=>{
        acc[cur.orderNo] = cur.preSettleList;
        return acc;
      },{})
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      this.pagination.total = pagination.total;
    },
    /**
     * 计算表格区域滚动高度
     */
    compute_table_scrollHeight() {
      this.get_max_height();
      this.table_scrollHeight = this.is_bet_slip_order_no ? window.innerHeight - 60 : this.scrollHeight;
      this.$forceUpdate();
    },
    // 处理百家赔str，返回格式为['N02:0.76/-0.88[MY]', 'N02:0.76/-0.88[MY]']
    computed_bet_no(bet_no_str) {
      if(!bet_no_str) {
        return '--';
      } else {
        return bet_no_str.replace(/T01-|L01-/g, "")?.replace("N01", "IM")?.replace("N02", "FB")?.replace("N03", "188bet");
      }
    },
    /**
     * @description:提前结算子母单
     * @param {type}
     * @return {type}
     */
    up_and_down_cash(orderNo) {
      let newkeys;
      if (this.expandedRowKeys.includes(orderNo)) {
        newkeys = this.expandedRowKeys.filter((x) => x != orderNo);
        this.expandedRowKeys = newkeys;
      } else {
        this.expandedRowKeys.push(orderNo);
      }
    },
  }
}
</script>
<style lang="scss" scoped>
::v-deep .ant-spin-nested-loading > div > .ant-spin {
  max-height: 660px;
  min-height: 660px;
}
::v-deep .ant-empty-normal {
  margin: 287px 0;
}
::v-deep .match-team-child .text-panda-text-light ::v-deep .show_toggle {
  position: absolute;
  right: 0;
}
::v-deep
  .ant-table-middle
  > .ant-table-content
  > .ant-table-scroll
  > .ant-table-header
  > table
  > .ant-table-thead
  > tr
  > th {
  background: #f4f5f8;
}
.center {
  width: 30px;
  border-left: 0;
  pointer-events: none;
  background-color: #fff;
}
.max {
  width: 100px;
  text-align: center;
  border-left: 0;
}
.min {
  width: 100px;
  text-align: center;
  border-right: 0;
  border-radius: 4px 0 0 4px;
}
::v-deep .leftdetail div {
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ellipsis {
  overflow: hidden;
  max-width: 235px;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.flipy {
  -moz-transform: scaleY(-1);
  -webkit-transform: scaleY(-1);
  -o-transform: scaleY(-1);
  transform: scaleY(-1);
  /* IE */
  filter: FlipV;
}
::v-deep .icon-tog {
  font-size: 16px;
}
.remarklist {
  width: 180px;
}
.item-content {
  // width: 250px;
}
.item-open {
  height: 60px;
  padding: 0 8px;
}
.tdpadding {
  padding: 0 8px;
}
::v-deep .ant-input-number {
  width: 110px;
}
.ellipsis {
  max-width: 230px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
::v-deep .ant-table-thead > tr > th .anticon-filter,
.ant-table-thead > tr > th .ant-table-filter-icon {
  right: 35px;
}
.hide {
  display: none;
}
.show {
  display: block;
}
::v-deep .ant-table-filter-dropdown {
  z-index: 100005;
}
::v-deep .ant-dropdown {
  z-index: 100006;
}
.text-over2 {
  display: inline-block;
  max-width: 110px;
  vertical-align: middle;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
::v-deep .ant-input-number {
  width: 90px;
}
::v-deep
  .ant-table-middle
  > .ant-table-content
  > .ant-table-scroll
  > .ant-table-header
  > table
  > .ant-table-thead
  > tr
  > th {
  background: #f4f5f8;
}
/*--------提前结算---------*/
::v-deep .ant-table-tbody .ant-table-row td.ant-table-row-expand-icon-cell {
  border: none !important;
  margin: 0;
  width: 0;
}
::v-deep .ant-table-tbody .ant-table-row td.ant-table-row-expand-icon-cell div {
  display: none;
}
::v-deep .ant-table-tbody .ant-table-expanded-row > td {
  padding: 0 !important;
  border: none !important;
}
::v-deep .ant-table-fixed .ant-table-thead th.ant-table-expand-icon-th {
  width: 0;
  padding: 0 !important;
  margin: 0;
}
.expandedRowRender-detail ::v-deep .ant-table-thead {
  display: none;
}
.currency-detail {
  cursor: pointer;
  color: #1990ff;
  padding: 0 4px;
  border: 1px solid #1990ff;
}
// 分页隐藏跳转
// .bet-pagination-page-size {
//   // 除了当前页都隐藏
//   ::v-deep .ant-pagination-item:not(.ant-pagination-item-active) {
//     display: none;
//   }
//   // 隐藏向前5页
//   ::v-deep .ant-pagination-jump-next {
//     display: none;
//   }
//   // 隐藏向后5页
//   ::v-deep .ant-pagination-jump-prev {
//     display: none;
//   }
//   // 隐藏跳转页
//   ::v-deep .ant-pagination-options .ant-pagination-options-quick-jumper {
//     display: none;
//   }
// }
::v-deep .ant-tabs-nav .ant-tabs-tab {
  font-size: 14px;
  font-weight: 600;
  color: #3c4551;
}
</style>
