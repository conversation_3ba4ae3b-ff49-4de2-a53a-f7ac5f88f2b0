<!--
 * @FilePath: /src/components/common/dialog_set/dialogDisabledUser.vue
 * @Description: 商户中心——投注用户管理——合买开启 弹窗
-->
<template>
  <div class="dialog-disable-user">
    <q-card>
      <q-card-section>
        <!-- 批量启/禁用用户  批量启/禁用商户 -->
        <div class="text-h6 text-weight-bolder text-center">
          {{ dialogtitle }}
        </div>
      </q-card-section>
      <q-card-section>
        <div class="">
          <div >
            <!-- 请选择合买发起功能开启或关闭 -->
            <div >{{$t('internal.merchant.bettinguser.config.text2')}}</div>
            <a-select style="width: 120px" v-model="userstatus">
              <a-select-option
                v-for="(item, index) in user_status_1"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
          <div class="mt20x">
            <!-- 请输入用户列表 -->
            <div 
              >{{$t('internal.merchant.bettinguser.config.text5')}}：</div
            >
            <!-- 请注意一个ID一行,末尾不用加逗号 -->
            <span class="text-red">
              {{ $t("internal.merchant.bettinguser.config.text9") }}
            </span>
          </div>
          <a-textarea
          v-model.trim="userInfo.disabled_user_id"
          class="textarea-height"
      />
          <div>
          <div class="mt20x">
            <!-- 备注 -->
            <span>{{ $t("internal.common.label71") }}：</span>
            <!-- 关闭时为必填 -->
            <span class="text-red" >
              {{ $t("internal.merchant.bettinguser.config.text6") }}
            </span>
          </div>
          <a-textarea
          v-model.trim="userInfo.disabled_user_remark"
          class="textarea-height"
      />
        </div></div>
      </q-card-section>
      <q-card-actions align="right">
      <div class="mb20x">
     <!-- 取消 -->
        <q-btn
          :label="$t('internal.abnormal_ip_domain_pool.title11')"
          color="primary"
          v-close-popup
          style="width: 80px; height: 30px; margin-right: 10px"
        />
        <!-- 确认 -->
        <q-btn
          :label="$t('internal.abnormal_ip_domain_pool.title10')"
          color="primary"
          class="q-mx-md"
          @click="handle_save_remark"
          style="width: 80px; height: 30px"
        />
      </div>
      </q-card-actions>
    </q-card>
    <!-- 二次确认弹窗 -->
    <q-dialog v-model="show_handle_two">
      <q-card style="min-width: 400px">
        <q-card-section>
          <!-- 提示 -->
          <div class="text-h5">{{ $t("internal.common.label5") }}</div>
        </q-card-section>
        <q-card-section>
          <!-- 此操作将禁用以下用户,是否继续？ : 此操作将启用以下用户,是否继续？ -->
          <span >{{
            userstatus==0
              ? $t("internal.merchant.bettinguser.config.text7")
              : $t("internal.merchant.bettinguser.config.34")
          }}</span>
          <div class="mt10x" style="overflow: scroll; max-height: 300px">
            <div v-for="(item, index) in id_name_list" :key="index">
              <!-- 用户ID：用户名 -->
              <span v-if="['user','purchase'].includes(titleId)"
                >{{ item.uidStr }}：{{ item.userName }}</span
              >
              <!-- 商户ID：商户名 -->
              <span v-else
                >{{ item.merchantCode }}：{{ item.merchantName }}</span
              >
            </div>
          </div>
        </q-card-section>
        <q-card-actions align="right">
          <!-- 确定 -->
          <q-btn
            :label="$t('internal.label.label39')"
            @click="handle_save_disabled_user"
            :loading="btnloading"
            color="primary"
            class="q-mx-md"
            style="width: 80px; height: 32px"
          />
          <!-- 取消 -->
          <q-btn
            :label="$t('internal.label.label38')"
            outline
            v-close-popup
            style="width: 80px; height: 32px"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>
<script>
import { i18n } from "src/boot/i18n";
import { api_user,api_data } from "src/api/index.js";
import commontoolmixin from "src/mixins/common/commontoolmixin.js";
import { getStrLength } from "src/util/module/common.js";
export default {
  name: "dialogDisabledUser",
  mixins: [commontoolmixin],
  props: {
    // 弹窗标题名称
    dialogtitle: {
      type: String,
      default: i18n.t("internal.common.suredelete"), // 你确认删除这项吗？
    },
    // 显示名称
    titleId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      userstatus: 1, // 操作类型
      user_status_1: i18n.t("internal.filters.user_status_1"), // 操作类型  1开启 0关闭
      userInfo: {
        disabled_user_id: "", // 用户ID
        disabled_user_remark: "", // 备注
      },
      // 二次确认弹窗
      show_handle_two: false,
      // ID及用户名
      id_name_list: [],
      // 按钮加载中
      btnloading: false,
    };
  },
  methods: {
    // 确定禁用用户
    async handle_save_disabled_user() {
      try {
        this.btnloading = true;
        let params = this.compute_params();
        params = this.delete_empty_property_with_exclude(params);
        
      if (this.titleId == "purchase") {
        params.userIds =  params.userIds.split(","); // 用户ID
      }
        // 投注用户管理：post_import_ToDisabled    商户管理：post_import_MerchantStatus
        let disable_user;
        if(this.titleId == "purchase"){
          disable_user=api_user.post_import_userPurchaseRemark
        }
        const res = await disable_user(params);
        const code = this.$lodash.get(res, "data.code");
        const msg = this.$lodash.get(res, "data.msg");
        if (code == "0000000") {
          this.btnloading = false;
          this.$emit("on_close");
          this.$message.success(msg);
        } else {
          this.$message.error(msg, 5);
        }
      } catch (error) {
        this.btnloading = false;
        console.error(error);
      }
    },
    // 组装请求参数
    compute_params() {
      let { disabled_user_id, disabled_user_remark } = this.userInfo;
      let userid = disabled_user_id
        .replace(/\s/g, "\n")
        .split("\n")
        .filter((x) => {
          const reg = /^[\d]+$/;
          // 投注用户管理 用户id过滤（防止接口报错）
          if (this.titleId == "purchase") {
            // 过滤输入字符长度小于20 且 数字
            if (getStrLength(x) < 20 && reg.test(x)) {
              return x;
            }
          } else {
            return x;
          }
        });
      let params = {
      };
      if (this.titleId == "purchase") {
        params.userIds = userid.join(","); // 用户ID
        params.userParams={
          userPurchase:this.userstatus+'',
          userPurchaseRemark:disabled_user_remark
        }
        
      }
      return params;
    },
    // 弹出二次确认弹窗
    async handle_save_remark() {
      const { merchantCodes, userIds } = this.compute_params();
      console.warn(userIds.split(','));
      let userids_list=userIds?userIds.split(','):[]
      if (!userIds && !merchantCodes) {
        // ID不能为空
        this.$message.error(i18n.t("internal.common.idnotnull"));
        return;
      }else if(userids_list.length>1000){
        //ID数量不能大于1000
        this.$message.error(i18n.t("internal.merchant.bettinguser.config.text10"));
        return;
      }
      // 操作类型为禁用 && 备注为空
      if (this.userstatus == 0 && !this.userInfo.disabled_user_remark) {
        // 请输入备注
        this.$message.error(i18n.t("internal.content_manage.t35"));
        return;
      }
      try {
        // 查询用户：post_find_userInfo     查询商户：post_find_MerchantInfo
        let api_fn;
        let res;
        if(this.titleId == "purchase"){
          
         api_fn=this.src_internal?api_user.post_find_userInfo :api_data.post_find_userInfo
          res = await api_fn({ userIds: userIds })
        }else{
          api_fn= api_user.post_find_MerchantInfo
          
          res = await api_fn({
                merchantCodes: merchantCodes,
              });
        }
        const { code, msg, data } = res.data;
        if (code == "0000000" && data.length > 0) {
          this.id_name_list = data;
          // 展示二次确认框
          this.show_handle_two = true;
        } else {
          // 暂无数据！
          this.$message.error(i18n.t("external.message2.label67"));
        }
      } catch (error) {
        this.$message.error(i18n.t("external.message2.label67"));
        console.error(error);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.dialog-disable-user {
  min-width: 600px;
  height: auto;
  width: 600px;
}
.textarea-height{
  height: 200px;
}
</style>