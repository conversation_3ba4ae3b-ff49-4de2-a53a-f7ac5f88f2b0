/*
 * @Desc: 
 * @Date: 2020-01-27 15:42:23
 * @Author:Nice
 * @FilePath: /src/components/common/bet_slip/external/config/recordConfig.js
 */

import { i18n } from 'src/boot/i18n';
const POSITION = 'right'
let table_title = i18n.t('internal.data.betslip.recordConfig')
export const record_config = [
  {
    title: table_title[0],
    width: 150,
    dataIndex: "createTime",
    key: "createTime",
    align: "center",
    scopedSlots: { customRender: 'createTime' },
  },
  {
    title: table_title[1],
    width: 160,
    dataIndex: "bizType",
    key: "bizType",
    align: 'center',
    scopedSlots: { customRender: 'bizType' },
  },
  {
    title: table_title[2],
    width: 100,
    dataIndex: "amount",
    key: "amount",
    align: POSITION,
    scopedSlots: { customRender: 'amount' },
  },
  {
    title: table_title[3],
    dataIndex: "status",
    key: "status",
    width: 130,
    align: 'center',
    scopedSlots:{customRender:"status"},
  },
    {
    title:  table_title[5],
    dataIndex: "transferId",
    key: "transferId",
    width: 180,
    align: 'center',
  },
      {//相关注单单号
        title:  table_title[4],
        dataIndex: "orderNo",
        key: "orderNo",
        width: 180,
        align: 'center',
        scopedSlots:{customRender:"orderNo"},
      }
];

