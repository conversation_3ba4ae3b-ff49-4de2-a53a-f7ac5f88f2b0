/*
 * @Date           : 2021-09-01 18:17:17
 * @FilePath: /src/api/internal/module/set/index.js
 * @description    : 
 */


import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_4+'/';
let prefix1 = process.env.API_PREFIX_3+'/';


  
// 【域名池管理】  
// Domain_pool_manage  域名池管理    这个是菜单的key
// http://api.sportxxxifbdxm2.com/yewu9/manage/merchantDomain/queryDomain  这个是列表查询接口

 
// 查询参数 domainType  domainName enable starDate  endDate pageNum pageSize 这些对应查询条件类型跟查询返回的类型是一样的
export const post_manage_merchantDomain_queryDomain = (
    params 
 
  ) =>{
    // tab: ty cp dj   计算相关 API 
    let tab= params.tab ||'ty'
    let url=''
    if(tab=='ty'){
      url="manage/merchantDomain/queryDomain"
    }else{
      url=`manage/merchantDomainGroup/${tab}/queryDomain` 
    } 

    delete params.tab
      return    axios.post(`${prefix}${url}`, params )

   }
   



// http://api.sportxxxifbdxm2.com/yewu9/manage/merchantDomain/deleteDomain    这个删除的 form-data形势传当前行id就行
 
export const post_manage_merchantDomain_deleteDomain  = (
    params 
 
  ) =>  {
    // tab: ty cp dj   计算相关 API 
    let tab= params.tab ||'ty'
    let url=''
    if(tab=='ty'){
      url=  "manage/merchantDomain/deleteDomain"
    }else{
      url=`manage/merchantDomainGroup/${tab}/deleteDomain` 
    } 
  
    delete params.tab
      return    axios.post(`${prefix}${url}?id=${params.id}`, params);
  
   }
   


 
  export const post_manage_merchantDomain_deleteDomainAll  = (
    params 
 
  ) =>  {
    // tab: ty cp dj   计算相关 API 
    let tab= params.tab ||'ty'
    let url=''
    if(tab=='ty'){
      url=  "manage/merchantDomain/deleteDomainAll"
    }else{
      url=`manage/merchantDomainGroup/${tab}/deleteDomainAll` 
    } 
  
    delete params.tab
      return     axios.post(`${prefix}${url}?id=${params.id}`, params);
  
   }
   
  
 



// 【域名池管理】 恢复使用
// http://api.sportxxxifbdxm2.com/yewu9/manage/merchantDomain/resetDomain  恢复使用的接口     form-data形势传当前行id就行（跟删除单个一样）
export const post_manage_merchantDomain_resetDomain  = (
  params 
 
) => {
  // tab: ty cp dj   计算相关 API 
  let tab= params.tab ||'ty'
  let url=''
  if(tab=='ty'){
    url= "manage/merchantDomain/resetDomain"
  }else{
    url=`manage/merchantDomainGroup/${tab}/resetDomain` 
  } 

  delete params.tab
    return   axios.post(`${prefix}${url}?id=${params.id}`, params);

 }
 








// http://api.sportxxxifbdxm2.com/yewu9/manage/merchantDomain/saveDomain这个是保存接口 
export const post_manage_merchantDomain_saveDomain = (
    params 
 
  ) =>{
    // tab: ty cp dj   计算相关 API 
    let tab= params.tab ||'ty'
    let url=''
    if(tab=='ty'){
      url="manage/merchantDomain/saveDomain"
    }else{
      url=`manage/merchantDomainGroup/${tab}/saveDomain` 
    } 

    delete params.tab
      return    axios.post(`${prefix}${url}`, params,{type:11})

   }
   






// 就这3个 没了
// 【5、商户组设置】
//===================================== 域名设置  ======================================




// /manage/log/findApiDomainLog        4、异常日志查询：(设置中心/域名切换日志)主列表

export const post_manage_log_findApiDomainLog = (
    params,
  ) =>{

    let tab= params.tab ||'ty'
    let url=''
    if(tab=='ty'){
      url='manage/log/findApiDomainLog' 
    }else{
      url=`manage/merchantDomainGroup/${tab}/findApiDomainLog` 
    } 

    delete params.tab

    return axios.post(`${prefix}${url}`, params );
  }

  
 
//=====================================  商户组   开始  ======================================
  
   // 获取商户组
   export const post_manage_merchant_domain_selectMerchantGroup =(params)=>{
    // tab: ty cp dj   计算相关 API 
    let tab= params.tab ||'ty'
    let url=''
    if(tab=='ty'){
      url='manage/merchant/domain/selectMerchantGroup' 
    }else{
      url=`manage/merchantDomainGroup/${tab}/getMerchantGroup` 
    } 

    delete params.tab
      return    axios.post(`${prefix}${url}`, params,{type:11})

   }
   




 
// 新建商户组 
export const post_manage_merchant_domain_createMerchantGroup =(params)=>{
  // tab: ty cp dj   计算相关 API 
  let tab= params.tab ||'ty'
  let url=''
  if(tab=='ty'){
    url='manage/merchant/domain/createMerchantGroup' 
  }else{
    url=`manage/merchantDomainGroup/${tab}/saveMerchantGroup` 
  } 
  delete params.tab
    return    axios.post(`${prefix}${url}`, params)

 }
 

// /manage/merchant/domain/updateMerchantGroup    这是更新 告警数量 
export const post_manage_merchant_domain_updateMerchantGroupNum = (
  params 
 
) =>   {
  // tab: ty cp dj   计算相关 API 
  let tab= params.tab ||'ty'
  let url=''
  if(tab=='ty'){
    url = "manage/merchant/domain/updateMerchantGroupNum"
  }else{
    url=`manage/merchantDomainGroup/${tab}/saveMerchantGroup` 
  } 
  delete params.tab



  return axios.post(`${prefix}${url}`, params,{type:111})
}







// /manage/merchant/domain/updateMerchantGroup    这是更新
export const post_manage_merchant_domain_updateMerchantGroup = (
  params 
 
) =>  {
  // tab: ty cp dj   计算相关 API 
  let tab= params.tab ||'ty'
  let url=''
  if(tab=='ty'){
    url = "manage/merchant/domain/updateMerchantGroup"
  }else{
    url=`manage/merchantDomainGroup/${tab}/saveMerchantGroup` 
  } 
  delete params.tab



  return axios.post(`${prefix}${url}`, params,{type:111})
}


 
// /manage/merchant/domain/deleteMerchantGroup    这是删除
export const post_manage_merchant_domain_deleteMerchantGroup = (
  params 

) =>{
  // tab: ty cp dj   计算相关 API 
  let tab= params.tab ||'ty'
  let url=''
  if(tab=='ty'){
    url = "manage/merchant/domain/deleteMerchantGroup"
  }else{
 
    url=`manage/merchantDomainGroup/${tab}/deleteMerchantGroup` 
  } 
  delete params.tab


  return axios.post(`${prefix}${url}`, params,{type:11});
} 




// 获取商户信息的接口  获取商户列表   电竞彩票专用 

 

//商户列表
export const get_manage_merchant_list = (
  params 
 
) =>  {

  // tab: ty cp dj   计算相关 API 
  let tab= params.tab ||'ty'
  let url=''
  if(tab=='ty'){
    url = "manage/merchant/list"
  }else{
 
    url=`manage/merchantDomainGroup/${tab}/getMerchantList` 
  } 
  delete params.tab


  return axios.get(`${prefix}${url}`, { params: { ...params } })
}


//【设置中心/域名设置/手动切换】
export const post_updateMerchantDomain = (
  params
 
) =>  {

  // tab: ty cp dj   计算相关 API 
  let tab= params.tab ||'ty'
  let url=''
  if(tab=='ty'){
    url = "manage/merchantDomain/updateMerchantDomain"
  }else{
 
    url=`manage/merchantDomainGroup/${tab}/updateMerchantDomain` 

 
  } 


//   {
//     "merchantGroupId":1,
//     "domainName":"www.aaaa.com",
//     "domainType":1
// }
// 域名类型 1 h5域名，2PC域名 ，3 App域名
  delete params.tab


  return axios.post(`${prefix}${url}`, params, {type:6});
}










//=====================================  商户组   结束  ======================================












  // 【设置中心/域名设置】动画域名接口展示  /manage/merchantDomain/queryAnimation

  export const post_manage_merchantDomain_queryAnimation = (
    params,
    url = "manage/merchantDomain/queryAnimation"
  ) => axios.post(`${prefix}${url}`, params);

   // 【设置中心/域名设置】数据商视频域名接口展示  /manage/merchantDomain/queryAnimation

   export const post_manage_queryDefault_videoDomain = (
    params,
    url = "manage/merchant/queryDefaultVideoDomain"
  ) => axios.post(`${prefix}${url}`, params);
  
  // 【设置中心/域名设置】动画域名更新  /manage/merchantDomain/updateAnimation
  export const post_manage_merchantDomain_updateAnimation = (
    params,
    url = "manage/merchantDomain/updateAnimation"
  ) => axios.post(`${prefix}${url}`, params,{type:1});

  

  //【设置中心/域名设置】 清除动画缓存  /manage/merchantDomain/deleteAniCache

  export const post_manage_merchantDomain_deleteAniCache = (
    params,
    url = "manage/merchantDomain/deleteAniCache"
  ) => axios.post(`${prefix}${url}`, params);
  //【设置中心/域名设置】 清除视频域名缓存  /manage/merchant/deleteVideoDomainCache

  export const post_manage_deleteVideoDomainCache = (
    params,
    url = "/manage/merchant/deleteVideoDomainCache"
  ) => axios.post(`${prefix}${url}`, params);



// 【设置中心/异常IP池管理】
// http://api.sportxxxifbdxm2.com/yewu9/manage/forbidip/queryIp 【设置中心/异常IP池管理】列表查询 分页查询
export const post_manage_forbidip_queryIp = (
  params,
  url = "manage/forbidip/queryIp"
) => axios.post(`${prefix}${url}`, params);


 // 【设置中心/异常IP池管理】
// http://api.sportxxxifbdxm2.com/yewu9/manage/forbidip/deleteby    这个删除的 form-data形势传当前行id行
export const post_manage_forbidip_deleteby= (
  params,
  url = "manage/forbidip/delete"
) => axios.post(`${prefix}${url}`, params,{type:1 });

 // 【设置中心/异常IP池管理】
// http://api.sportxxxifbdxm2.com/yewu9/manage/forbidip/save这个是保存接口 
export const post_manage_forbidip_save = (
  params,
  url = "manage/forbidip/save"
) => axios.post(`${prefix}${url}`, params);


 // 【设置中心/异常IP池管理】
// http://api.sportxxxifbdxm2.com/yewu9/manage/forbidip/deletebyname    全部删除
export const post_manage_forbidip_deletebyname  = (
  params,
  url = "manage/forbidip/deletebyname"
) => axios.post(`${prefix}${url}`, params,{type:1 });


//【设置中心/Api商户分组/列表】
export const post_api_merchant_domain_list = (
  params,
  url = "manage/merchantDomain/apiMerchantDomainList"
) => axios.post(`${prefix}${url}`, params);

//【设置中心/Api商户分组/添加Api商户分组】
export const post_add_api_merchantGroup = (
  params,
  url = "manage/merchantDomain/addApiMerchantGroup"
) => axios.post(`${prefix}${url}`, params);

//【设置中心/Api商户分组/更新Api商户分组】
export const post_update_api_merchantGroup = (
  params,
  url = "manage/merchantDomain/updateApiMerchantGroup"
) => axios.post(`${prefix}${url}`, params);

//【设置中心/Api商户分组/删除Api商户分组】
export const delete_api_merchantGroup = (
  params,
  url = "manage/merchantDomain/deleteApiMerchantGroup"
) => axios.post(`${prefix}${url}`, params);

//【设置中心/Api商户分组/查询api商户分组】
export const post_merchant_list= (
  params,
  url = "manage/merchant/list"
) => axios.post(`${prefix}${url}`, params);

// 获取业务组、公共组的域名设置
export const post_selectGroupDomain= (
  params,
  url = "manage/merchant/domain/selectGroupDomain"
) => axios.post(`${prefix}${url}`, params);

// 17ce检测开关
export const post_updateThirdEnable= (
  params,
  url = "manage/merchant/domain/updateThirdEnable"
) => axios.post(`${prefix}${url}`, params);

export const get_getThirdEnable = (
  params,
  url = "manage/merchant/domain/getThirdEnable"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//对账单数据同步接口
export const get_executeFinance = (
  params,
  url = "manage/dataSync/callExecuteFinance"
) => axios.post(`${prefix}${url}`,params);

//赛事投注统计同步接口
export const get_executeMatchBet = (
  params,
  url = "manage/dataSync/callExecuteMatchBet"
) => axios.post(`${prefix}${url}`,params);

//用户投注统计同步同步接口
export const get_execute = (
  params,
  url = "manage/dataSync/callExecuteUserBet"
) => axios.post(`${prefix}${url}`,params);

 // 【设置中心/域名设置】数据商视频域名更新  /manage/merchantDomain/updateAnimation
 export const post_manage_updateDefault_VideoDomain = (
  params,
  url = "manage/merchant/updateDefaultVideoDomain"
) => axios.post(`${prefix}${url}`, params);


 // 【设置中心/脱敏设置文件上传接口】
 export const post_manage_file_desensitization = (
  params,
  url = "manage/merchant/file/changMerchantName"
) => axios.post(`${prefix}${url}`, params);
