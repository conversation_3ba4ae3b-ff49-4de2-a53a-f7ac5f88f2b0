/*
 * @Desc: 
 * @Author:Nice
 * @Date:
 * @FilePath: /src/api/external/module/base/index.js
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_4;
let prefix1 = process.env.API_PREFIX_17;
//获取c端多语言
export const get_cplate_lan_list = (
  params,
  url = "/admin/merchant/getMerchantLanguageList"
) => axios.get(`${prefix1}${url}`, { params: { ...params } });

//商户费率设置-新增//
export const post_manage_merchantRate_add = (
  params,
  url = "/manage/merchantRate/add"
) => axios.post(`${prefix}${url}`, params);

//商户费率设置-修改//
export const post_manage_merchantRate_update = (
  params,
  url = "/manage/merchantRate/update"
) => axios.post(`${prefix}${url}`, params);

//商户费率设置--查询列表//
export const post_set_merchantrate_query = (
  params,
  url = "/manage/merchantRate/queryList"
) => axios.post(`${prefix}${url}`, params);

//商户等级查询--下拉列表无分页//
export const get_manage_merchantLevel_queryLevelList = (
  params,
  url = "/manage/merchantLevel/queryLevelList"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//商户等级查询--下拉列表无分页//
export const get_manage_merchantLevelcurrencyRate = (
  params,
  url = "/admin/merchant/currencyRate"
) => axios.post(`${prefix1}${url}`, params);

//查询费率列表无分页(port:10712)//
export const get_manage_merchantRate_queryRateList = (
  params,
  url = "/manage/merchantRate/queryRateList"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//商户等级设置--查询费率对象//
export const get_manage_merchantLevel_getMerchantLevel = (
  params,
  url = "/manage/merchantLevel/getMerchantLevel"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//商户等级设置--查询//
export const post_manage_merchantLevel_queryList = (
  params,
  url = "/manage/merchantLevel/queryList"
) => axios.post(`${prefix}${url}`, params);





//商户等级设置--修改//
export const post_manage_merchantLevel_update = (
  params,
  url = "/manage/merchantLevel/update"
) => axios.post(`${prefix}${url}`, params);

//商户等级设置--新增//
export const post_manage_merchantLevel_add = (
  params,
  url = "/manage/merchantLevel/add"
) => axios.post(`${prefix}${url}`, params);

// 商务列表  3-12
export const get_manage_merchant_admin_list = (
  params,
  url = "/manage/merchant/admin/list"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//商户等级设置--详情查询
export const post_manage_merchantLevel_byid = (params, url = "/manage/merchantLevel") =>
  axios.post(`${prefix}${url}/${params.id}`);
