<!--
 * @FilePath: /src/components/common/bet_slip/component/zr_bet_slip.vue
 * @Description: 注单查询-真人
-->
<template>
  <div class="full-width full-height position-relative">
    <div id=""  class="">
      <div id="top2" class="row mr10x line-height-31px items-center text-panda-text-dark bg-panda-bg-6 pb10x pt10x border-radius-4px">
        <!-- 会员ID -->
        <div class="append-handle-btn-input ml10x w-150 position-relative">
          <a-input v-model.trim="searchForm.uid" :placeholder="$t('internal.table_title.yh45')" autocomplete="off" allowClear />
        </div>
        <!-- 会员昵称 -->
        <!-- <div class="append-handle-btn-input ml10x w-150 position-relative">
         <div class="append-handle-btn-input ml10x w-150 position-relative">
          <a-input v-model.trim="searchForm.nickName" :placeholder="$t('internal.table_title.yh46')" autocomplete="off" allowClear />
        </div> -->
        <!-- 会员账号 -->
        <!-- <div class="append-handle-btn-input ml10x w-180 position-relative">
          <a-input v-model.trim="searchForm.userName" :placeholder="$t('internal.content_manage.t18')" autocomplete="off" allowClear />
        </div> -->
        <!-- 注单编号 -->
        <div class="append-handle-btn-input ml10x w-150 position-relative">
          <a-input v-model.trim="searchForm.id" :placeholder="$t('internal.table_title.label54')" autocomplete="off" allowClear />
        </div>
        <!-- 商户编号 对内展示，对外仅渠道(代理)商户展示 -->
        <div v-if="is_has_children" class="append-handle-btn-input ml10x w-150 position-relative">
          <a-input v-model.trim="searchForm.merchantCode" :placeholder="$t('internal.table_title.label72')" autocomplete="off" allowClear />
        </div>
        <!-- 局号 -->
        <div class="append-handle-btn-input ml10x w-150 position-relative">
          <a-input v-model.trim="searchForm.roundNo" :placeholder="$t('internal.table_title.yh40')" autocomplete="off" allowClear />
        </div>
        <!-- 注单状态 -->
        <div class="append-handle-btn-input ml10x w-120 position-relative">
          <a-select v-model="searchForm.betStatus" :placeholder="$t('internal.state')" autocomplete style="width: 120px">
            <a-select-option :value="item.value" v-for="(item, index) in zr_betStatus_list" :key="index">{{ item.label }}</a-select-option>
          </a-select>
        </div>
        <!-- 币种 -->
        <div class="append-handle-btn-input ml10x w-120 position-relative">
          <a-select v-model="searchForm.currency" :placeholder="$t('internal.table_title.label23')" autocomplete style="width: 120px">
            <a-select-option :value="item.value" v-for="(item, index) in zr_currency_list" :key="index">{{ item.label }}</a-select-option>
          </a-select>
        </div>
        <!-- 日期选择 -->
        <div class="ml10x append-handle-btn-input">
          <a-range-picker show-time style="width: 310px" showToday :allowClear="false" v-model="searchForm.range_picker_value" />
        </div>
        <!-- 7天实时库 -->
        <div
          class="append-handle-btn-input position-relative ml10x"
          v-if="!is_for_bet_slip_es && !is_for_bet_slip_es_timely"
        >
          <a-select
            autocomplete
            style="width: 110px"
            v-model="searchForm.databaseSwitch"
          >
            <a-select-option
              :value="item.value"
              v-for="(item, index) in database_type"
              :key="index"
              >{{ item.label }}</a-select-option
            >
          </a-select>
          <div class="position-absolute select-left-border-bet"></div>
        </div>
        <!-- 合并 -->
        <div class="append-handle-btn-input ml10x position-relative">
          <a-checkbox v-model="searchForm.merge">
            {{ $t("internal.table_title.yh62") }}
          </a-checkbox>
        </div>
        <!-- 查询 -->
        <div class="append-handle-btn-input height-31px line-height-31px">
          <a-button type="primary" @click="on_search">
            {{ $t("internal.label.label27") }}
          </a-button>
        </div>
        <!-- 导出报表 -->
        <div class="append-handle-btn-input pl10x height-31px line-height-31px">
          <a-button type="primary" @click="handle_export_excel">
            {{ $t("internal.export_text") }}
          </a-button>
        </div>
        <q-space />
      </div>
      <!-- 表格区域 -->
      <a-table 
        ref="TableTotalHeader" 
        class="full-width pl10x pr10x" 
        :scroll="{ x: 1600, y: table_scrollHeight - 50 }" 
        :pagination="false" 
        :loading="tabledata_loading" 
        :columns="zr_tablecolumns" 
        :dataSource="tabledata" 
        size="small" 
        :rowKey="record => record._index"
      >
        <!-- 会员ID -->
        <template slot="uid" slot-scope="text,record">
          <table-cell-ellipsis-ant :str="record.uid" :str_all="record.uid" :copytips="$t('internal.table_title.yh45')"></table-cell-ellipsis-ant>
        </template>
        <!-- 会员昵称 -->
        <!-- <template slot="nickName" slot-scope="text,record">
          <table-cell-ellipsis-ant :str="record.nickName" :str_all="record.nickName" :copytips="$t('internal.table_title.yh46')"></table-cell-ellipsis-ant>
        </template> -->
        <!-- 会员账号 -->
        <template slot="userName" slot-scope="text,record">
          <table-cell-ellipsis-ant :str="record.userName" :str_all="record.userName" :copytips="$t('internal.content_manage.t18')"></table-cell-ellipsis-ant>
        </template>
        <!-- 注单编号 -->
        <template slot="id" slot-scope="text,record">
          <table-cell-ellipsis-ant :str="record.id" :str_all="record.id" :copytips="'ID'"></table-cell-ellipsis-ant>
        </template>
        <!-- 商户编号 对内展示，对外仅渠道(代理)商户展示 -->
        <template v-if="is_has_children" slot="merchantCode" slot-scope="text,record">
          <table-cell-ellipsis-ant :str="record.merchantCode" :str_all="record.merchantCode" :copytips="$t('internal.table_title.label65')"></table-cell-ellipsis-ant>
        </template>
        <!-- 局号 -->
        <template slot="roundNo" slot-scope="text,record">
          <table-cell-ellipsis-ant :str="record.roundNo" :str_all="record.roundNo" :copytips="$t('internal.table_title.yh40')"></table-cell-ellipsis-ant>
        </template>
        <!-- 厅id / 厅名称 -->
        <template slot="platformName" slot-scope="text,record">
          <span>{{ record.platformName }}</span>
        </template>
        <!-- 终端 -->
        <template slot="deviceType" slot-scope="text,record">
          <span>{{ zr_device_type_map[record.deviceType] || "--" }}</span>
        </template>
        <!-- 投注额 -->
        <template slot="betAmount" slot-scope="text,record">
          <span>{{ record.betAmount | filterMoneyFormat }}</span>
        </template>
        <!-- 币种 -->
        <template slot="currency" slot-scope="text,record">
          <span>{{ zr_currency_map[record.currency] }}</span>
        </template>
        <!-- 投注时间 -->
        <template slot="createTime" slot-scope="text,record">
          <span>{{  moment(+record.createdAt).format("YYYY-MM-DD HH:mm:ss") || "--" }}</span>
        </template>
        <!-- TR结算时间 -->
        <template slot="netAt" slot-scope="text,record">
          <span v-if="record.netAt">{{  moment(+record.netAt).format("YYYY-MM-DD HH:mm:ss") }}</span>
        </template>
        <!-- TY更新时间 -->
        <template slot="updatedAt" slot-scope="text,record">
          <span v-if="record.updatedAt">{{  moment(+record.updatedAt).format("YYYY-MM-DD HH:mm:ss") }}</span>
        </template>
        <!-- 状态  注单状态 0=未结算 1=已结算 -->
        <template slot="betStatus" slot-scope="text,record">
          <span>{{ zr_betStatus_map[record.betStatus] || "--" }}</span>
        </template>
      </a-table>
      <!-- 底部翻页器 和总计区域 -->
      <div class="row q-px-lg" style="box-shadow: 0 -2px 4px rgb(102 102 102 / 10%); margin-top: 4px">
        <!-- 总计 -->
        <div class="col-7">
          <div class="fs16 line-height-24px q-pl-md" v-if="tabledata.length > 0">
            <div>
              <span class="pr10x">
                <!-- 总计 -->
                <span class="title-grey">
                  {{ $t("internal.data.betslip.index.sumBetNo") }}
                </span>
                <!-- xx单 -->
                <span class="fw_600">
                  {{ $t("internal.data.betslip.index.sumBetNo_text", { s: userStatistics.sumBetNo, }) }}
                </span>
              </span>
              <!--用户统计-->
              <span class="pr10x">
                <span class="title-grey">
                  {{ $t("internal.data.betslip.index.userAmount") }}
                </span>
                <span class="fw_600">
                  {{ $t("internal.data.betslip.index.userAmount_text", { s: userStatistics.userAmount, }) }}
                </span>
              </span>
              <!--总计投注额-->
              <span class="pr10x">
                <span class="title-grey">
                  {{ $t("internal.data.betslip.index.betAmount_text") }}
                </span>
                <span class="fw_600" v-if="userStatistics.betAmount">
                  {{ compute_currencyRate_obj_by_code(userStatistics.currency)[ "countryCn" ] }}
                  {{ userStatistics.betAmount | filterMoneyFormat }}
                </span>
                <span class="fw_600" v-else>--</span>
              </span>
              <!-- 用户净输赢 -->
              <span class="pr10x">
                <span class="title-grey">
                  {{ $t("internal.data.betslip.index.sumProfitAmount_text") }}
                </span>
                <span v-if="userStatistics.sumProfitAmount !== undefined" class="fw_600" :class=" userStatistics.sumProfitAmount > 0 ? 'text-red' : 'text-green' ">
                  {{ userStatistics.sumProfitAmount | filterMoneyFormat }}
                </span>
                <span v-else>--</span>
              </span>
              <!-- 平台盈利率 -->
              <span class="pr10x">
                <span class="title-grey">{{ $t("internal.data.betslip.index.userStatistics_text") }}
                </span>
                <span class="fw_600" v-if=" userStatistics.sumProfitAmount && userStatistics.betAmount " :class=" userStatistics.sumProfitAmount > 0 ? 'text-green' : 'text-red' ">
                  <span v-if="userStatistics.sumProfitAmount > 0">-</span>
                  {{ to_percent( Math.abs(userStatistics.sumProfitAmount) / userStatistics.betAmount ) }}
                </span>
                <span v-else>--</span>
              </span>
              <!-- 此处只汇总用户近90天的已结算注单记录！与用户基本信息中累计注单数据不一致属于正常现象，详情请咨询平台客服工作人员！ -->
              <a-tooltip trigger="hover">
                <template slot="title">
                  <div :style="`max-width: 240px; word-break:break-all;`">
                    {{ $t("internal.message.label115") }}
                  </div>
                </template>
                <a-icon type="question-circle" class="text-red fs15 cursor-pointer" />
              </a-tooltip>
            </div>
            <div>
              <span class="row">
                <!--总计有效投注额-->
                <span class="pr10x">
                  <span class="title-grey">
                    {{ $t("internal.data.betslip.index.label1") }}
                  </span>
                  <span class="fw_600" v-if="userStatistics.sumValidBetMoney">
                    {{ compute_currencyRate_obj_by_code(userStatistics.currency)[ "countryCn" ] }}
                    {{ userStatistics.sumValidBetMoney | filterMoneyFormat }}
                  </span>
                  <span class="fw_600" v-else>--</span>
                </span>
                <span class="pr10x">
                  <!-- 有效笔数 -->
                  <span class="title-grey">
                    {{ $t("internal.data.betslip.index.label2") }}
                  </span>
                  <!-- xx单 -->
                  <span class="fw_600" v-if="userStatistics.sumValidBetNo">
                    {{ $t("internal.data.betslip.index.sumBetNo_text", { s: userStatistics.sumValidBetNo, }) }}
                  </span>
                  <span class="fw_600" v-else>--</span>
                </span>
              </span>
            </div>
          </div>
          <!-- 统计数据正在努力加载中...... -->
          <div v-else class="fs16 position-absolute" style="bottom: 11px; left: 25px;">
            <span v-if="tabledata.length > 0">{{ $t("internal.data.betslip.index.loading_text") }}</span>
          </div>
        </div>
        <div class="col-5">
          <!-- 分页器 -->
          <a-pagination v-if="tabledata && tabledata.length > 0" :total="pagination.total" :current="pagination.current" show-size-changer show-quick-jumper :page-size-options="pagination.pageSizeOptions" :page-size="pagination.pageSize" :show-total="total => $t('internal.showTotal_text', [pagination.total])" @change="onChange" @showSizeChange="onShowSizeChange" style="box-shadow: none" />
        </div>
      </div>
    </div>
    <!-- 报表下载弹窗 -->
    <q-dialog v-model="exportExcelShow" persistent transition-show="scale" transition-hide="scale">
      <dialog-excel :export_param="export_param"></dialog-excel>
    </q-dialog>
  </div>
</template>

<script>
import { api_user } from "src/api/index.js";
import moment from "moment";
import mixins from "src/mixins/internal/index.js";
import { handleCopy } from "src/util/module/common.js";
import dataCenterMixin from "src/mixins/internal/module/datacentertablemixin.js";
import { mapGetters } from "vuex";

import {
  zr_tablecolumns_config,
  zr_betStatus_list,
  zr_betStatus_map,
  zr_device_type_map,
  zr_currency_list,
  zr_currency_map,
} from "src/components/common/bet_slip/config/zr_config.js";
import dialogExcel from "src/components/common/dialog/dialogExcel.vue";

const sart_time = `${moment(new Date().setDate(new Date().getDate())).format(
  "YYYY-MM-DD"
)} 00:00:00`;
const end_time = `${moment(new Date().setDate(new Date().getDate())).format(
  "YYYY-MM-DD"
)} 23:59:59`;

export default {
  name: "cpBetSlip",
  mixins: [...mixins, dataCenterMixin],
  components: {
    dialogExcel,
  },
  data() {
    return {
      zr_betStatus_list, // 注单状态
      zr_betStatus_map, // 注单状态映射
      zr_device_type_map, // 设备类型映射
      zr_currency_list, // 币种
      zr_currency_map, // 币种映射
      zr_tablecolumns: [], // 表头
      tabledata_loading: false, // 表格loading
      tabledata: [], // 表格数据
      database_type: this.$t("internal.filters.databaseTab"),  // 7天实时库  1：7天实时库  2：35天注单大表
      searchForm: {
        range_picker_value: [
          moment(sart_time, "YYYY-MM-DD HH:mm:ss"),
          moment(end_time, "YYYY-MM-DD HH:mm:ss"),
        ], // 日期选择器
        merge: false, // 0不合并  1合并
        databaseSwitch: 1 // 1 为查7天实时库 2 为查35天大表
      },
      // 导出报表参数
      export_param: {},
      exportExcelShow: false,
      // 汇总
      userStatistics: {},
      // 表格区域滚动高度
      table_scrollHeight: 0, 
    };
  },
  created() {
    // this.initTableData();
    // 表格配置
    this.zr_tablecolumns = zr_tablecolumns_config(this.src_internal, this.src_external, this.get_user_info);
  },
  computed: {
    ...mapGetters([ "get_window_size_change", "get_user_info"]),
    // 注单查询ES
    is_for_bet_slip_es(){
      return  this.$route.name == "bet_slip_es";
    },
    // 注单查询ES实时流
    is_for_bet_slip_es_timely(){
      return  this.$route.name == "bet_slip_es_timely";
    },
  },
  mounted() {
    this.compute_table_scrollHeight();
  },
  watch: {
    get_window_size_change() {
      this.compute_table_scrollHeight();
    },
  },
  methods: {
    moment,
    handleCopy,
    initTableData() {
      let params = this.compute_init_tabledata_params();
      this.tabledata_loading = true;
      api_user
        .post_zr_getOrderList(params)
        .then((res) => {
          let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          let arr = this.$lodash.get(res, "data.data.list", []);
          if (code == "0000000") {
            this.tabledata_loading = false;
            this.tabledata = this.rebuild_tabledata_to_needed(arr);
            this.pagination.total =
              this.$lodash.get(res, "data.data.total") * 1 || 0;
          } else {
            this.tabledata = [];
            this.$message.warn(msg);
          }
        })
        .finally(() => {
          this.tabledata_loading = false;
        });
      this.init_getStatistics();
    },
    // 计算请求参数
    compute_init_tabledata_params() {
      const [startTime, endTime] = this.searchForm.range_picker_value;
      const {
        merchantCode,
        uid,
        id,
        // nickName,
        betStatus,
        currency,
        userName,
        roundNo,
        merge,
        databaseSwitch,
      } = this.searchForm;
      let params = {
        page: this.pagination.current, // 分页，查询第几页数据。
        size: this.pagination.pageSize, // 分页，每页查询多少条，默认20条。可不传
        startTime: moment(startTime).valueOf(), // 开始注单时间 timeType=5 时要求必填
        endTime: moment(endTime).valueOf(), // 截止注单时间 timeType=5 时要求必填
        timeType: 5, // 1:一天内 2:三天内 3:七天内 4:三十天内 5:自定义时间
        uid, // 会员ID
        // nickName, // 会员昵称
        id, // 注单编号
        merchantCode, // 商户编号
        betStatus, // 注单状态
        currency, // 币种
        userName, // 会员账号
        roundNo, // 局号
        merge: merge ? 1 : 0, // 0不合并  1合并
        reportFlag: databaseSwitch === 1 ? false : true // false：7天实时库  true：35天注单大表
      };
      params = this.delete_empty_property_with_exclude(params);
      return params;
    },
    // 查询
    on_search() {
      this.initTableData();
    },
    // 导出报表
    handle_export_excel() {
      if (this.pagination.total > 0) {
        let params = this.compute_init_tabledata_params();
        if (this.src_internal) {
          Object.assign(
            params,
            // { "user-id": this.get_user_info.userId },
            // { "app-id": this.get_user_info.appId },
            // { which: true },
            { url: "/manage/order/orderZrExport" }
          );
        } else {
          Object.assign(
            params,
            { token: this.$q.sessionStorage.getItem("token") },
            { url: "/admin/order/orderZrExport" }
          );
        }
        this.export_param = params;
        this.exportExcelShow = true;
      } else {
        this.handle_error();
      }
    },
    // 汇总统计
    init_getStatistics() {
      let params = this.compute_init_tabledata_params();
      api_user.post_zr_getStatistics(params).then((res) => {
        let code = this.$lodash.get(res, "data.code");
        let data = this.$lodash.get(res, "data.data", {});
        if (code == "0000000") {
          this.userStatistics = data;
        }
      });
    },
    // 平台盈利率显示文字
    to_percent(point) {
      let str = Number(point * 100).toFixed(2);
      str += "%";
      return str;
    },
   /**
   * 计算表格区域滚动高度
   */
    compute_table_scrollHeight() {
      this.get_max_height();
      this.table_scrollHeight = this.scrollHeight;
      this.$forceUpdate();
    }
  },
};
</script>

