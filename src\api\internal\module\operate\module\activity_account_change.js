/*
 * @FilePath: d:/projects/new-merchant-management/src/api/internal/module/operate/module/activity_account_change.js
 * @Description: 活动账变记录
 */

import axios from "src/api/common/axioswarpper.js";


let prefix_1 = process.env.API_PREFIX_4;

// 活动奖券账变查询接口
export const get_token_change_history = (params) => {
  let prefix = prefix_1;
  let url = `/manage/tokenChangeHistory/getTokenChangeHistory`;
  return axios.post(`${prefix}${url}`, params);
}

// 活动奖券账变查询接口
export const get_token_type = () => {
  let prefix = prefix_1;
  let url = `/manage/tokenChangeHistory/getTokenType`;
  return axios.post(`${prefix}${url}`);
}

//  账变类型查询接口
export const get_change_type = () => {
  let prefix = prefix_1;
  let url = `/manage/tokenChangeHistory/getSlotChangeType`;
  return axios.post(`${prefix}${url}`);
}

// 账变记录导出
export const export_account_change_record = (params) => {
  let prefix = prefix_1;
  let url = `/manage/tokenChangeHistory/exportChangeHistory`;
  return axios.post(`${prefix}${url}`, params, {
    responseType: "blob"
  });
}
