/*
 * @Desc: 
 * @Date: 2020-01-27 15:42:23
 * @Author:Nice
 * @FilePath: /src/components/common/bet_slip/internal/config/recordConfig.js
 */
import { i18n } from 'src/boot/i18n';
const POSITION = 'right'
let title_config = i18n.t('internal.data.betslip.recordConfig')
export const record_config = [
  {
    title:  title_config[0],
    width: 150,
    dataIndex: "createTime",
    key: "createTime",
    align: "center",
    scopedSlots: { customRender: 'createTime' },
  },
  {
    title:  title_config[1],
    width: 140,
    dataIndex: "bizType",
    key: "bizType",
    align: 'center',
    scopedSlots: { customRender: 'bizType' },
  },
  {
    title:  title_config[2],
    width: 100,
    dataIndex: "amount",
    key: "amount",
    align: POSITION,
    scopedSlots: { customRender: 'amount' },
  },
  {
    title:  title_config[3],
    dataIndex: "status",
    key: "status",
    width: 120,
    align: 'center',
    scopedSlots:{customRender:"status"},
  },
  {
    title:  title_config[5],
    dataIndex: "transferId",
    key: "transferId",
    width: 180,
    align: 'center',
  },
  {
    title:  title_config[4],
    dataIndex: "orderNo",
    key: "orderNo",
    width: 180,
    align: 'center',
    scopedSlots:{customRender:"orderNo"},
  }
];

