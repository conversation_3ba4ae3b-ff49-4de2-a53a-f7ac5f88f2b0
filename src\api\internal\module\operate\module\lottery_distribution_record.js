/*
 * @FilePath: /src/api/internal/module/operate/module/lottery_distribution_record.js
 * @Description: 奖券派发记录
 */
import axios from "src/api/common/axioswarpper.js";
import { LocalStorage, SessionStorage } from "quasar";
let prefix_1 = process.env.API_PREFIX_4;
let prefix_2 = process.env.API_PREFIX_18;
let play_type= "/dj"
//搜索奖券派发记录
  export const bonusRecord_query= (params) => {
    let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
    let prefix =which? prefix_1: prefix_2;
    let which_type=which?  "": play_type;
    let url=`/manage${which_type}/bonusRecord/recordList`
    return  axios.post(`${prefix}${url}`, params);
  }


//奖券补发
  export const bonusRecord_update= (params) => {
    let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
    let prefix =which? prefix_1: prefix_2;
    let which_type=which?  "": play_type;
    let url=`/manage${which_type}/bonusRecord/reissueBonus`
    return axios.post(`${prefix}${url}`, params);
  }

//获取补发奖券数
export const getBonusByTaskId= (params) => {
  let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
  let prefix =which? prefix_1: prefix_2;
  let which_type=which?  "": play_type;
  let url=`/manage${which_type}/bonusRecord/getBonusByTaskId`
  return  axios.get(`${prefix}${url}`, { params: { ...params } });
}