<!--
 * @Desc: 对外-账户中心-投注用户管理-无特殊限额弹窗
 * @FilePath: /src/components/common/bettinguser/external/component/dialogNoSpecialBetLimit.vue
 -->
 <template>
  <div>

          <!-- 编辑情况下      //   "1": "无",      //   "2": "特殊百分比限额",  -->
       
          <div
            class="pl20x fw_600 col q-pr-md"
          >
          
            <!-- 对特殊VIP限额、特殊单注单场限额、单注限额百分比的用户限额需求 -->
            <a-tooltip trigger="hover">
              <template slot="title">
                {{ $t("internal.betting.label2") }} 
              </template>
              <a-icon type="question-circle" class=" fs15" />
            </a-tooltip>
            <!-- 用户单日、单场、玩法累计投注限额是正常用户的 -->
            {{ $t("internal.betting.label1") }}
            <a-select
              v-model="percentageLimit"
              style="width:105px;margin-top:7px"
              class="disabled-white"
              :disabled="[3, 4].includes(detailObj.specialBettingLimitType)"
            >
              <a-select-option
                v-for="item in percentageLimit_arr"
                :key="item"
                :value="item"
                >{{ item }}%
              </a-select-option>
            </a-select>
          </div>
  </div>
</template>

<script>
import { i18n } from "src/boot/i18n";

export default {
  props: {
    detailObj: {
      type: Object,
      default() {
        return {};
      }
    },
    // can_edit:{
    //   type:Boolean,
    //   default:false
    // },
    //   "1": "无", //   "2": "特殊百分比限额",    才能编辑
    can_edit: false
  },
  data() {
    return {
      percentageLimit: 100,
      percentageLimit_arr: [1, 2, 5, 10, 25, 50, 75, 100],
      // special_percentage:i18n.t('internal.betting.label10'),  // 特殊百分比radio
      // automatic_limit:i18n.t('internal.betting.label11'),  // 自动升额度radio
    }
  }
}
</script>

<style>

</style>