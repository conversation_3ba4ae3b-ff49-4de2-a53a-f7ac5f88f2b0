/*
 * @Author: 
 * @Date: 2021-07-02 23:39:34
 * @Description:每日任务
 * @FilePath: /src/api/internal/module/operate/module/task_settings.js
 */
import axios from "src/api/common/axioswarpper.js";
import { LocalStorage, SessionStorage } from "quasar";

let prefix_1 = process.env.API_PREFIX_4;
let prefix_2 = process.env.API_PREFIX_18;
let play_type= "/dj"


//玩法选择
export const getSportAndPlayTree1= (params) => {
  let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
  let prefix =which? prefix_1: prefix_2;
  let which_type=which?  "": play_type;
  let url=`/manage${which_type}/activity/getSportAndPlayTree1`
  return   axios.get(`${prefix}${url}`, { params: { ...params } });
}
//虚拟球种
export const getVirtualSportTree1= (params) => {
let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
let prefix =which? prefix_1: prefix_2;
let which_type=which?  "": play_type;
let url=`/manage${which_type}/activity/getVirtualSportTree1`
return   axios.get(`${prefix}${url}`, { params: { ...params } });
}
//搜索每日任务列表
export const post_v1_dailyTask_getSettings= (params) => {
  let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
  let prefix =which? prefix_1: prefix_2;
  let which_type=which?  "": play_type;
  let url=`/manage${which_type}/activity/list`
  return axios.post(`${prefix}${url}`, params, {type: 1});
}


//搜索每日任务添加
export const post_v1_dailyTask_add= (params) => {
  let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
  let prefix =which? prefix_1: prefix_2;
  let which_type=which?  "": play_type;
  let url=`/manage${which_type}/activity/add`
  return  axios.post(`${prefix}${url}`, params);
}

//搜索每日任务编辑
export const post_v1_dailyTask_update= (params) => {
  let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
  let prefix =which? prefix_1: prefix_2;
  let which_type=which?  "": play_type;
  let url=`/manage${which_type}/activity/save`
  return   axios.post(`${prefix}${url}`, params);
}

//玩法选择
  export const getSportAndPlayTree= (params) => {
    let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
    let prefix =which? prefix_1: prefix_2;
    let which_type=which?  "": play_type;
    let url=`/manage${which_type}/activity/getSportAndPlayTree`
    return   axios.get(`${prefix}${url}`, { params: { ...params } });
  }
//虚拟球种
export const getVirtualSportTree= (params) => {
  let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
  let prefix =which? prefix_1: prefix_2;
  let which_type=which?  "": play_type;
  let url=`/manage${which_type}/activity/getVirtualSportTree`
  return   axios.get(`${prefix}${url}`, { params: { ...params } });
}
//切换任务状态和状态
export const changeStatus= (params) => {
  let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
  let prefix =which? prefix_1: prefix_2;
  let which_type=which?  "": play_type;
  let url=`/manage${which_type}/activity/changeStatus`
  return   axios.post(`${prefix}${url}`, params, { type: 1 });
}

//排序
export const changeOrder= (params) => {
  let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
  let prefix =which? prefix_1: prefix_2;
  let which_type=which?  "": play_type;
  let url=`/manage${which_type}/activity/changeOrder`
  return  axios.post(`${prefix}${url}`, params, { type: 1 });
}

//领取任务
export const acBonusLog= (params) => {
  let which= LocalStorage.getItem("task_type")==1||!LocalStorage.getItem("task_type")
  let prefix =which? prefix_1: prefix_2;
  let which_type=which?  "": play_type;
  let url=`/manage${which_type}/acBonusLog/list`
  return axios.post(`${prefix}${url}`, params);
}