// 本月第一天 00:00:00
export  const getFirstDayOfMonth =   (has_hours_minutes_seconds=true)=>{    
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    if(has_hours_minutes_seconds){
        return `${year}-${month}-01 00:00:00`; // 2025-06-01 00:00:00
    }else{
        return `${year}-${month}-01`; // 2025-06-01
    }
    }
    // 本月最后一天 23:59:59
    export  const getLastDayOfMonth =   (has_hours_minutes_seconds=true)=>{    
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const lastDay = new Date(year, month, 0).getDate(); // 获取当月最后一天
    if(has_hours_minutes_seconds){

        return `${year}-${String(month).padStart(2, '0')}-${lastDay} 23:59:59`; // 2025-06-30 23:59:59
    }else{
        
    return `${year}-${String(month).padStart(2, '0')}-${lastDay}`; // 2025-06-30
    }
    }
    // 上个月第一天 00:00:00
    export  const getFirstDayOfLastMonth =   (has_hours_minutes_seconds=true)=>{    
    const date = new Date();
    date.setMonth(date.getMonth() - 1); // 减 1 个月
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    if(has_hours_minutes_seconds){

        return `${year}-${month}-01 00:00:00`; // 2025-05-01 00:00:00
    }else{
        
        return `${year}-${month}-01`; // 2025-05-01
    }
    }
    // 上个月最后一天 23:59:59
    export  const getLastDayOfLastMonth =   (has_hours_minutes_seconds=true)=>{    
    const date = new Date();
    date.setMonth(date.getMonth() - 1);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const lastDay = new Date(year, month, 0).getDate();
    if(has_hours_minutes_seconds){

        return `${year}-${String(month).padStart(2, '0')}-${lastDay} 23:59:59`; // 2025-05-31 23:59:59
    }else{
        
    return `${year}-${String(month).padStart(2, '0')}-${lastDay}`; // 2025-05-31 
    }
    }
    