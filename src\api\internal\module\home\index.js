/*
 * @Desc: 
 * @Date: 2020-02-01 21:19:42
 * @Author:Nice
 * @FilePath       : /merchant-manage/src/api/internal/module/home/<USER>
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_4;
let prefix1 = process.env.API_PREFIX_3;

//商户分布信息//
export const get_manage_merchant_queryAgentCount = (
  params,
  url = "/manage/merchant/queryAgentCount"
) => axios.get(`${prefix}${url}`, { params: { ...params } });


//本月商户盈利排行
export const get_manage_merchant_profit_top10 = (
  params,
  url = "/order/home/<USER>"
) => axios.post(`${prefix1}${url}`, params);
  
//今日投注信息// 716
export const post_report_home_queryBetToday = (
  params,
  url = "/order/home/<USER>"
) => axios.post(`${prefix1}${url}`, params);

//今日用户信息// 716
export const post_report_home_queryUserToday = (
  params,
  url = "/order/home/<USER>"
) => axios.post(`${prefix1}${url}`, params);

//今日投注金额 TOP10赛事// 716
export const post_report_home_matchTop10 = (
  params,
  url = "/order/home/<USER>"
) => axios.post(`${prefix1}${url}`, params);


//本月商户投注金额排行// 716
export const post_report_home_merchantOrderTop10 = (
  params,
  url = "/order/home/<USER>"
) => axios.post(`${prefix1}${url}`, params);

//14天投注金额趋势// 716
export const post_report_home_userOrderDay14 = (
  params,
  url = "/order/home/<USER>"
) => axios.post(`${prefix1}${url}`, params);

//本月商户投注金额排行// 716
export const post_report_home_userDay14 = (
  params,
  url = "/order/home/<USER>"
) => axios.post(`${prefix1}${url}`, params);




















