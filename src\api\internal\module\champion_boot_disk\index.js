/*  
 * @path: src/api/internal/module/champion_boot_disk/index.js
 * @Descripttion: 冠军引导盘
 * @Author: 
 * @Date: 2024-11-01 09:48:04
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_4; //yewu9

// 冠军引导盘查询大列表
export const post_champion_queryTournamentList = (
    params,
    url = "/manage/tournament/relation/queryTournamentList"
  ) => axios.post(`${prefix}${url}`, params);

// 冠军引导盘联赛绑定开关-修改
export const post_champion_updateSwitch = (
  params,
  url = "/manage/tournament/relation/update"
) => axios.post(`${prefix}${url}`, params);


// 冠军引导盘联赛绑定总开关-修改
export const post_champion_updateallSwitch = (
  params,
  url = "/manage/tournament/relation/updateAll"
) => axios.post(`${prefix}${url}`, params);