/*
 * @Desc:  赛事直播接口
 * @Date: 2022-8-31 11:30:34
 */
import axios from "src/api/common/axioswarpper.js";
let prefix  = process.env.CAIPIAO_API_DOMAIN
let prefix1  = process.env.SHUJUZHICHENG_API_DOMAIN

// let prefix = process.env.API_PREFIX_4;

// 主播管理列表
// export const anchor_manage_list = (
//     params,
//     url = "/livechat/zbHots/list"
// ) => axios.post(`${prefix}${url}`, params);

// 主播管理列表
export const anchor_manage_list = (
  params,
  url = "/livechat/zbHots/list"
) => axios.post(`${prefix1}${url}`, params);

// 主播管理
export const post_anchor_manage = (
  params,
  url = "/livechat/zbHots/save"
) => axios.post(`${prefix1}${url}`, params);
// 直播管理—直播列表
export const live_manage_list = (
  params,
  url = "/livechat/liveInfo/page"
) => axios.post(`${prefix1}${url}`, params);
// 直播管理-直播列表-体育类型
export const live_manage_sport_list = (
  params,
  url = "/livechat/liveInfo/querySport"
) => axios.post(`${prefix1}${url}`, params);

// 直播管理-赛前节目-联赛类型
export const live_match_sport_list = (
  params,
  url = "/livechat/liveInfo/listTournament"
) => axios.post(`${prefix1}${url}`, params);

// 直播管理-赛前节目-查询赛事信息
export const live_matchinfo_sport_list = (
  params,
  url = "/livechat/liveInfo/queryMathInfo"
) => axios.post(`${prefix1}${url}`, params);

// 直播管理—直播列表—直播场地
export const live_manage_place_list = (
  params,
  url = "/livechat/liveInfo/listSites"
) => axios.post(`${prefix1}${url}`, params);
// 直播管理—直播列表—主播
export const live_manage_anchor_list = (
  params,
  url = "/livechat/liveInfo/listZbHots"
) => axios.post(`${prefix1}${url}`, params);
// 直播管理—直播列表—直播状态
export const live_manage_live_status = (
  params,
  url = "/livechat/liveInfo/modifyLiveStatus"
) => axios.post(`${prefix1}${url}`, params);
// 直播管理—直播列表—节目状态
export const live_manage_modifyPre_status = (
  params,
  url = "/livechat/liveInfo/modifyPreStatus"
) => axios.post(`${prefix1}${url}`, params);
// 直播管理—直播列表—新增
export const post_live_manage = (
  params,
  url = "/livechat/liveInfo/save"
) => axios.post(`${prefix1}${url}`, params);

// 直播管理—直播列表—删除
export const delete_live_manage = (
  params, 
  url = "/livechat/liveInfo/delete"
) => axios.post(`${prefix1}${url}`, params);

// 直播管理—直播列表—赛前节目
export const live_manage_prematch = (
  params,
  url = "/livechat/liveInfo/queryPreMatchInfo"
) => axios.post(`${prefix1}${url}`, params);

// 直播管理—直播班表
export const post_live_schedule = (
  params,
  url = "/livechat/liveInfo/getLiveClassSchedule"
) => axios.post(`${prefix1}${url}`, params);

// 直播管理—赛前节目-列表
export const set_live_program = (
  params,
  url = "/livechat/PreMatchProgram/preMatchProgramList"
) => axios.post(`${prefix1}${url}`, params);

// 直播管理—赛前节目-新增
export const post_live_program = (
  params,
  url = "/livechat/PreMatchProgram/insertPreMatchProgram"
) => axios.post(`${prefix1}${url}`, params);

// 直播管理—赛前节目-修改
export const update_live_program = (
  params,
  url = "/livechat/PreMatchProgram/updatePreMatchProgram"
) => axios.post(`${prefix1}${url}`, params);

// 直播管理—赛前节目-修改状态
export const update_live_program_state = (
  params,
  url = "/livechat/PreMatchProgram/updateState"
) => axios.post(`${prefix1}${url}`, params);

// 直播管理—赛前节目-删除
export const delete_live_program_state = (
  params,
  url = "/livechat/PreMatchProgram/deletePreMatchProgram"
) => axios.post(`${prefix1}${url}`, params);

// 直播管理—推拉流配置—列表
export const post_live_stream_list = (
  params,
  url = "/livechat/zbStreamingConfiguration/list"
) => axios.post(`${prefix1}${url}`, params);

// 直播管理—推拉流配置-查询
export const set_live_stream = (
  params,
  url = "/livechat/zbStreamingConfiguration/listBySiteId"
) => axios.post(`${prefix1}${url}`, params);

// 直播管理—推拉流配置—新增修改
export const save_live_stream = (
  params,
  url = "/livechat/zbStreamingConfiguration/save"
) => axios.post(`${prefix1}${url}`, params);

// 直播管理—推拉流配置-删除
export const delete_live_stream = (
  params,
  url = "/livechat/zbStreamingConfiguration/delete"
) => axios.post(`${prefix1}${url}`, params);

// 内容管理-聊天室-导出消息
export const get_chat_room_export_message = (params, url = "/livechat-admin/chat/room/export/message") =>
axios.post(`${prefix}${url}`, params,{ type: 1, responseType: "blob"});
//内容管理-聊天室-列表查询接口
export const get_chat_room_list = (params, url = "/livechat-admin/chat/room/list") =>
axios.get(`${prefix}${url}`, { params: { ...params } });

//内容管理-聊天室-新增
export const post_add_chat_room = (params, url = "/livechat-admin/chat/room/add") =>
axios.post(`${prefix}${url}`, params,{type:6});

//内容管理-聊天室-状态修改
export const post_update_chat_room_list = (params, url = "/livechat-admin/chat/room/status/update") =>
axios.post(`${prefix}${url}`,{ ...params },{type:11});

// 内容管理 -敏感词管理列表查询接口
export const get_sensitivewords_list = (params, url = "/livechat-admin/sensitiveWord/list") =>
axios.get(`${prefix}${url}`, { params: { ...params } });

// 内容管理 -敏感词管理 新增
export const post_sensitivewords_save = (params, url = "/livechat-admin/sensitiveWord/add") =>
  axios.post(`${prefix}${url}`,params,{type:6});

// 内容管理 -敏感词管理 编辑
export const post_sensitivewords_editBatch = (params, url = "/livechat-admin/sensitiveWord/editBatch") =>
axios.post(`${prefix}${url}`,params )

// 内容管理 -内容审核-聊天室列表
export const get_content_chat_room_list = (
  params,
  url = "/livechat-admin/content/audit/chatRoomList"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

// 内容管理 -内容审核-消息词屏
export const post_livechat_message = (
  params,
  url = "/livechat-admin/content/audit/message"
) => axios.post(`${prefix}${url}`, params);

// 内容管理 -内容审核-清屏
export const post_livechat_clearScreen = (
  params,
  url = "/livechat-admin/content/audit/clearScreen"
) => axios.post(`${prefix}${url}`, params);

// 内容管理 -内容审核-撤回
export const post_livechat_recallmessage = (
  params,
  url = "/livechat-admin/content/audit/recallmessage"
) => axios.post(`${prefix}${url}`, params);

// 内容管理 -内容审核-禁言
export const post_livechat_mute = (
  params,
  url = "/livechat-admin/content/audit/mute"
) => axios.post(`${prefix}${url}`, params);

// 内容管理 -内容审核-聊天室解除禁言
export const post_livechat_relieveMute = (
  params,
  url = "/livechat-admin/content/audit/relieveMute"
) => axios.post(`${prefix}${url}`, params);

// 内容管理 -内容审核-禁止晒单
export const post_livechat_prohibitShareSorder = (
  params,
  url = "/livechat-admin/content/audit/prohibitShareSorder"
) => axios.post(`${prefix}${url}`, params);

// 内容管理 -内容审核-聊天室解除禁止晒单
export const post_livechat_relieveProhibitShareSorder = (
  params,
  url = "/livechat-admin/content/audit/relieveProhibitShareSorder"
) => axios.post(`${prefix}${url}`, params);

//内容管理-异常会员-禁言会员列表
export const get_abnormalMember_list = (params, url = "/livechat-admin/chat/abnormalMember/list") =>
axios.get(`${prefix}${url}`, { params: { ...params } });

//内容管理-异常会员-禁晒单会员-晒单状态/备注更新
export const post_abnormalMember_update = (params, url = "/livechat-admin/chat/abnormalMember/status/update") =>
axios.post(`${prefix}${url}`, { ...params },{type:6});

// //内容管理-异常会员-禁止晒单会员列表
// export const get_goposting_list = (params, url = "/livechat-admin/chat/goposting/list") =>
// axios.get(`${prefix}${url}`, { params: { ...params } });

// //内容管理-异常会员-禁言状态/备注更新
// export const post_goposting_status_update = (params, url = "/livechat-admin/chat/goposting/status/update") =>
// axios.post(`${prefix}${url}`, { ...params },{type:6});

//内容管理-聊天室-新增/状态修改时调用
export const post_operat_status_update = (params, url = "/livechat/LiveChatOperation/operationLiveChatStatus") =>
axios.post(`${prefix1}${url}`, params);

// 内容管理-聊天室公告—列表
export const post_bulletin_list = (params, url = "/livechat-admin/bulletin/list") => 
  axios.get(`${prefix}${url}`, { params: { ...params }});
// 内容管理-聊天室公告—新增
export const post_bulletin_add = (params, url = "/livechat-admin/bulletin/add") =>
  axios.post(`${prefix}${url}`, params, { type: 1 });

// 内容管理-聊天室公告—编辑
export const post_bulletin_edit = (params, url = "/livechat-admin/bulletin/edit") =>
  axios.post(`${prefix}${url}`, params, { type: 1 });

// 内容管理-聊天室公告—删除
export const post_bulletin_delete = (params, url = "/livechat-admin/bulletin/delete") =>
  axios.post(`${prefix}${url}`, params, { type: 1 });

// 内容管理-聊天室公告—拖拽排序
export const post_bulletin_sort = (params, url = "/livechat-admin/bulletin/sort") =>
  axios.post(`${prefix}${url}`, params, { type: 1 });

// 内容管理-超级会员-新增
export const post_superUser_add = (params, url = "/livechat-admin/superUser/add") =>
  axios.post(`${prefix}${url}`, params, { type: 1 });

// 内容管理-超级会员-列表
export const get_superUser_list = (params, url = "/livechat-admin/superUser/list") =>
  axios.get(`${prefix}${url}`, { params: { ...params } }, { type: 1 });

// 内容管理-超级会员-编辑
export const post_superUser_edit = (params, url = "/livechat-admin/superUser/edit") =>
  axios.post(`${prefix}${url}`, params, { type: 1 });

// 内容管理-超级会员-删除
export const post_superUser_delete = (params, url = "/livechat-admin/superUser/delete") =>
  axios.post(`${prefix}${url}`, params, { type: 1 });