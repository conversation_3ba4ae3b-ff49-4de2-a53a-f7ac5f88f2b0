/*
 * @FilePath: d:/projects/new-merchant-management/src/api/internal/module/operate/module/tag_management.js
 * @Description: 赛事文章-标签管理接口
 */

let prefix_1 = process.env.API_PREFIX_4;
import axios from "src/api/common/axioswarpper.js";

// 标签分页列表
export const get_article_tag_page_list = (params) => {
  let prefix = prefix_1;
  let url = `/manage/articleTag/list`;
  return axios.post(`${prefix}${url}`, params);
}

// 新增标签
export const add_article_tag = (params) => {
  let prefix = prefix_1;
  let url = `/manage/articleTag/add`;
  return axios.post(`${prefix}${url}`, params);
}

// 编辑标签
export const edit_article_tag = (params) => {
  let prefix = prefix_1;
  let url = `/manage/articleTag/edit`;
  return axios.post(`${prefix}${url}`, params);
}

// 删除标签
export const delete_article_tag = (params) => {
  let prefix = prefix_1;
  let url = `/manage/articleTag/delete`;
  return axios.post(`${prefix}${url}`, params, {
    type: 1
  });
}

// 联赛下拉选, 查询列表
export const get_league_list = (params) => {
  let prefix = prefix_1;
  let url = `/manage/articleTag/leagueList`;
  return axios.get(`${prefix}${url}`, {
    params: {
      ...params
    }
  });
}

// 标签下拉选项
export const get_article_tag_list = () => {
  let prefix = prefix_1;
  let url = `/manage/articleTag/tagList`;
  return axios.get(`${prefix}${url}`);
}
