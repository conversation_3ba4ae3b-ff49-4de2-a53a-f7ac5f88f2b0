"use strict";function _interopDefault(e){return e&&"object"==typeof e&&"default"in e?e.default:e}var constants=require('./constants'),utils=require('./utils'),utilsLite=require("utils-lite");require("echarts/lib/chart/bar");var Core=_interopDefault(require('./core')),defineProperty=function(e,i,t){return i in e?Object.defineProperty(e,i,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[i]=t,e},_extends=Object.assign||function(e){for(var i=1;i<arguments.length;i++){var t=arguments[i];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},VALUE_AXIS_OPACITY=.5;function getBarDimAxis(e){var i=e.innerRows,t=e.dimAxisName,a=e.dimension,n=e.axisVisible,r=e.dimAxisType,o=e.dims;return a.map(function(e){return{type:"category",name:t,nameLocation:"middle",nameGap:22,data:"value"===r?getValueAxisData(o):i.map(function(i){return i[e]}),axisLabel:{formatter:function(e){return String(e)}},show:n}})}function getValueAxisData(e){for(var i=Math.max.apply(null,e),t=[],a=Math.min.apply(null,e);a<=i;a++)t.push(a);return t}function getBarMeaAxis(e){for(var i=e.meaAxisName,t=e.meaAxisType,a=e.axisVisible,n=e.digit,r=e.scale,o=e.min,s=e.max,l={type:"value",axisTick:{show:!1},show:a},u=[],m=function(e){t[e]?u[e]=_extends({},l,{axisLabel:{formatter:function(i){return utils.getFormated(i,t[e],n)}}}):u[e]=_extends({},l),u[e].name=i[e]||"",u[e].scale=r[e]||!1,u[e].min=o[e]||null,u[e].max=s[e]||null},c=0;c<2;c++)m(c);return u}function getBarTooltip(e){var i=e.axisSite,t=e.isHistogram,a=e.meaAxisType,n=e.digit,r=e.labelMap,o=t?i.right||[]:i.top||[];return r&&(o=o.map(function(e){return void 0===r[e]?e:r[e]})),{trigger:"axis",formatter:function(e){var i=[];return i.push(e[0].name+"<br>"),e.forEach(function(e){var t=e.seriesName,r=~o.indexOf(t)?a[1]:a[0];i.push(constants.itemPoint(e.color)),i.push(t+": "),i.push(utils.getFormated(e.value,r,n)),i.push("<br>")}),i.join("")}}}function getValueData(e,i){for(var t=Math.max.apply(null,i),a=[],n=Math.min.apply(null,i);n<=t;n++){var r=i.indexOf(n);~r?a.push(e[r]):a.push(null)}return a}function getBarSeries(e){var i,t=e.innerRows,a=e.metrics,n=e.stack,r=e.axisSite,o=e.isHistogram,s=e.labelMap,l=e.itemStyle,u=e.label,m=e.showLine,c=void 0===m?[]:m,d=e.dimAxisType,p=e.barGap,x=e.opacity,f=e.dims,g={},b=o?r.right||[]:r.top||[],v=o?"yAxisIndex":"xAxisIndex",y=n&&utils.getStackMap(n);return a.forEach(function(e){g[e]=[]}),t.forEach(function(e){a.forEach(function(i){g[i].push(e[i])})}),!!(i=Object.keys(g).map(function(e,i){var t="value"===d?getValueData(g[e],f):g[e],a=defineProperty({name:null!=s[e]?s[e]:e,type:~c.indexOf(e)?"line":"bar",data:t},v,~b.indexOf(e)?"1":"0");n&&y[e]&&(a.stack=y[e]),u&&(a.label=u),l&&(a.itemStyle=l);var r=x||utilsLite.get(a,"itemStyle.normal.opacity");return"value"===d&&(a.barGap=p,a.barCategoryGap="1%",null==r&&(r=VALUE_AXIS_OPACITY)),null!=r&&utilsLite.set(a,"itemStyle.normal.opacity",r),a})).length&&i}function getLegend(e){var i=e.metrics,t=e.labelMap,a=e.legendName;return a||t?{data:t?i.map(function(e){return null==t[e]?e:t[e]}):i,formatter:function(e){return null!=a[e]?a[e]:e}}:{data:i}}function getDims(e,i){return e.map(function(e){return e[i[0]]})}var bar$1=function(e,i,t,a){var n=utilsLite.cloneDeep(i),r=t.axisSite,o=void 0===r?{}:r,s=t.dimension,l=void 0===s?[e[0]]:s,u=t.stack,m=void 0===u?{}:u,c=t.axisVisible,d=void 0===c||c,p=t.digit,x=void 0===p?2:p,f=t.dataOrder,g=void 0!==f&&f,b=t.scale,v=void 0===b?[!1,!1]:b,y=t.min,h=void 0===y?[null,null]:y,A=t.max,S=void 0===A?[null,null]:A,T=t.legendName,L=void 0===T?{}:T,M=t.labelMap,V=void 0===M?{}:M,w=t.label,O=t.itemStyle,D=t.showLine,N=t.barGap,_=void 0===N?"-100%":N,B=t.opacity,k=a.tooltipVisible,P=a.legendVisible,j=e.slice();o.top&&o.bottom?j=o.top.concat(o.bottom):o.bottom&&!o.right?j=o.bottom:t.metrics?j=t.metrics:j.splice(e.indexOf(l[0]),1);var E=t.xAxisType||["normal","normal"],G=t.yAxisType||"category",I=t.xAxisName||[],q=t.yAxisName||"";if(g){var C=g.label,H=g.order;C&&H?n.sort(function(e,i){return"desc"===H?e[C]-i[C]:i[C]-e[C]}):console.warn("Need to provide name and order parameters")}var R=getDims(n,l),F=P&&getLegend({metrics:j,labelMap:V,legendName:L}),U=getBarDimAxis({innerRows:n,dimAxisName:q,dimension:l,axisVisible:d,dimAxisType:G,dims:R}),X=getBarMeaAxis({meaAxisName:I,meaAxisType:E,axisVisible:d,digit:x,scale:v,min:h,max:S});return{legend:F,yAxis:U,series:getBarSeries({innerRows:n,metrics:j,stack:m,axisSite:o,isHistogram:!1,labelMap:V,itemStyle:O,label:w,showLine:D,dimAxisType:G,dimension:l,barGap:_,opacity:B,dims:R}),xAxis:X,tooltip:k&&getBarTooltip({axisSite:o,isHistogram:!1,meaAxisType:E,digit:x,labelMap:V})}},index=_extends({},Core,{name:"VeBar",data:function(){return this.chartHandler=bar$1,{}}});module.exports=index;
