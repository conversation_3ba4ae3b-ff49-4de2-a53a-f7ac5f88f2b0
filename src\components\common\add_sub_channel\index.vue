
<!--
 * @Author: 
 * @Date: 2020-09-02 20:41
 * @Description:  渠道商户管理.添加渠道商户(二级&直营)     查看代理商-下级渠道
 * @FilePath: /src/components/common/add_sub_channel/index.vue
-->
<template>
  <div
    style="width:800px ; height:auto;max-width:800px;overflow:hidden;"
    class="text-panda-text-7">
     <q-card class="bg-white text-black">
      <q-card-section class="no-padding" v-show="src_external">
        <div
          class="row line-height-40px fs14 bg-panda-dialog text-panda-text-7 pr10x"
        >
        <!--添加渠道商户-->
          <div class="pl20x fw_600">{{$t("external.account.channel_merchant.label1")}}</div>
          <q-space></q-space>
          <q-btn
            class="mr5x text-panda-dialog-close"
            icon="close"
            v-close-popup
          />
        </div>
      </q-card-section>
      <q-separator  v-show="src_external"></q-separator>
      <div class="append-handle-btn-input pl10x row mt20x">
      <!--请输入渠道商户名称-->
        <div class="w-200 height: 30px; position-relative" style="display: inline-table;">
          <a-input
            v-model="searchForm.merchantName"
             :placeholder='$t("internal.placeholder.label25")'
            autocomplete="off"
            @keydown.enter="handle_search"
            allowClear
          >
            <my-icon slot="suffix" type="p-icon-chazhao" class="text-panda-text-4 fs12" />
          </a-input>
          <div class="position-absolute select-left-border-finance"></div>
        </div>
       <!-- 商户类型   直营商户  直营商户 -->
         <div
          class="append-handle-btn-input pl20x position-relative"
          v-if="src_internal"
        >
          <a-select
            autocomplete
            class="w-130"
            :placeholder="$t('internal.table_title.yh2')" @change="handle_agent_level"
              @keydown.enter="handle_search"
             v-model.trim="searchForm.merchantType"
          >
            <a-select-option
              :value="item.value"
              v-for="(item, index) in merchantTypeList"
              :key="index"
              >{{ item.label }}</a-select-option
            >
          </a-select>
          <div class="position-absolute select-left-border-finance"></div>
        </div>
        <q-space />
          <div class="row flex justify-center mr5x">
            <div class="row mr5x">
            <!-- 保存 -->
              <q-btn
                class="panda-btn-primary-dense bg-primary mr10x"
                style="width:100px;height:32px; "
                @click.once="submit_page"
                :label="$t('internal.save')"
              />
              <!-- 取消 -->
              <q-btn
              v-show="src_internal"
                class="panda-btn-white border-1px mr10x"
                style="width:100px;height:32px; "
                @click="$emit('handle_close_add')"
                :label="$t('internal.cancel')"
              />
            </div>
          </div></div>
      <a-table
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="mt10x pl10x pr10x small-table"
        :columns="columns"
        :dataSource="tabledata"
        :scroll="{  y: 320 }"
        :pagination="false"
        :loading="tabledata_loading"
        @change="sorterForTable"
        size="small"
        rowKey="id"
      >
         <span slot="merchantCode" slot-scope="text">
          <span>{{ text }}</span>
        </span>
        <span slot="merchantName" slot-scope="text, record">
          <table-cell-ellipsis-ant
            :span_html="('<div class=\'ellipsis\'>'+record.merchantName+'</div>')"
            :str_all="('<div>'+record.merchantName +'</div>')"
            :defaultplace="'topLeft'"
            :col_width="180"
            :str='record.merchantName'
          ></table-cell-ellipsis-ant>
        </span>
        <!-- 商户等级 -->
        <span slot="level" slot-scope="text, record">
          <span>Lv{{ record.level }}</span>
        </span>
      </a-table>
      <a-pagination
        size="small"
        v-if="tabledata.length>0"
        :total="pagination.total"
        :current="pagination.current"
        show-size-changer 
        show-quick-jumper
        :page-size-options="pagination.pageSizeOptions"
        :page-size="pagination.pageSize"
        :show-total="total => $t('internal.showTotal_text',[pagination.total])"
        @change="onChange"
        @showSizeChange="onShowSizeChange"
          />
      </q-card>
  </div>
</template>
<script>
import { i18n } from 'src/boot/i18n';
import { mapGetters } from "vuex";
import { api_merchant} from "src/api/index.js";
import { set_sub_channel_add } from "src/components/common/add_sub_channel/config/config.js"; //商户中心.代理商管理.新增下级商户中(添加下级商户)表格配置文件
import { handleCopy } from "src/util/module/common.js";
import commonmixin from "src/mixins/internal/common/commontoolmixin.js";
import sortertablemixin from "src/mixins/internal/module/sortertablemixin.js";
import commonmixin_external from "src/mixins/external/common/commontoolmixin.js";
import sortertablemixin_external from "src/mixins/external/module/sortertablemixin.js";
import tableCellEllipsisAnt from "src/components/internal/table/tableCellEllipsisAnt.vue";
const FOR_WHICH = process.env.FOR_WHICH;
let mixins_custom= [commonmixin,sortertablemixin];
if(FOR_WHICH=="external"){
  mixins_custom=[commonmixin_external,sortertablemixin_external];
}
export default {
  mixins: [...mixins_custom],
  props: {
    detailObj: {
      type: Object,
      default() {
        return {};
      }
    },
  },
  components: {
    tableCellEllipsisAnt
  },
  data() {
    return {
      selectedRowKeys: [],
      tabledata: [],
      columns: set_sub_channel_add,//添加下级商户)表格配置文件
      tabledata_loading: false,
      merchantTypeList: i18n.t("internal.filters.merchantType"),//商户类型
      searchForm: {
        merchantName: "",//商户名称
        agentLevel: ""//商户类型
      }
    };
  },
  created() {
    this.tabledata_loading = true;
    this.initTableData();
  },
  computed: {
    ...mapGetters(["get_data",'get_user_info'])
  },
  watch: {
    searchForm: {
      handler: function(val) {
        this.initTableData();
      },
      deep: true
    },
  },
  methods: {
    handleCopy,
    onSelectChange(selectedRowKeys, selectedRows){
      this.selectedRowKeys = selectedRowKeys;
      
    },
    /**
    * @description:确定
    */
    submit_page(){
       let params ={}
      if(this.src_internal){
       params = { agentId: this.detailObj.id, merchantIds:this.selectedRowKeys.join(",") };
     
      }else if(this.src_external){
       params = {agentId: this.get_user_info.merchantId,  merchantIds:this.selectedRowKeys.join(",") };
       
      }
      let emit_=this.src_internal?"handle_close_add":"closeDialogSetMerchantShow"
      api_merchant.post_manage_merchant_add_merchant(params).then(res => {
        let { code, msg } = res.data;
        if (code == "0000000") {
          this.$message.success( i18n.t('internal.message.label10'));
              this.$emit(emit_,true);
        } else {
          this.$message.error(msg, 5);
        }
      });
    },
 /**
     * @description 商户类型切换
     * @return {undefined} undefined
     */
    handle_agent_level(val) {
      this.searchForm.agentLevel = val;
      this.initTableData();
    },
    initTableData() {
      this.tabledata_loading = true;
      let params = this.compute_init_tabledata_params();
      params = this.delete_empty_property_with_exclude(params);
       api_merchant
        .get_manage_merchantAgent_list(params)
        .then(res => {
        this.initdata_then(res)
        })
    },
    initdata_then(res){
      this.tabledata_loading = false;
          let code = this.$lodash.get(res, "data.code");
          if (code == "0000000") {
            let currentPage = this.$lodash.get(res, "data.data.page") * 1 || 1;
            let arr = this.$lodash.get(res, "data.data.list") || [];
            this.pagination.start = this.$lodash.get(res, "data.data.startRow");
            this.tabledata = this.rebuild_tabledata_to_needed(arr);
            this.pagination.total =
              this.$lodash.get(res, "data.data.total") * 1 || 0;
          } else {
            this.$message.error(res.data.msg);
          }
    },
    compute_init_tabledata_params() {
      let params = {
        merchantName: this.searchForm.merchantName, // 商户名称
        agentLevel: this.searchForm.agentLevel, // 商户类型
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize
      };
      return params;
    },
    rebuild_tabledata_to_needed(arr) {
      arr.map((item, index) => {
        item._index =
          (this.pagination.current - 1) * this.pagination.pageSize + index + 1;
      });
      return arr;
    },
    /**
     * @description 点击搜索按钮
     * @return {undefined} undefined
     */
    handle_search() {
      this.initTableData();
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-spin-nested-loading > div > .ant-spin {
	 max-height: 350px !important;
	 min-height: 350px !important;
}
 ::v-deep .ant-empty-normal {
	 margin: 100px 0 !important;
}
 ::v-deep .ant-table-thead > tr > th {
	 color: #3c4551;
	 font-weight: bold;
	 background: #fff;
}
 .ellipsis {
	 display: block;
	 max-width: 160px;
	 text-overflow: ellipsis;
	 overflow: hidden;
	 white-space: nowrap;
}
 .text-over-100 {
	 display: block;
	 max-width: 95px;
	 text-overflow: ellipsis;
	 overflow: hidden;
	 white-space: nowrap;
}
 ::v-deep .ant-table-tbody > tr > td {
	 color: #3c4551;
}
 ::v-deep .ant-table-pagination.ant-pagination {
	 margin: 16px 20px 16px 0;
}
 
</style>
