/*
 * @Desc: 
 * @Author:Nice
 * @Date:
 * @FilePath: /src/api/external/module/user/index.js
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_17;
let prefix1 = process.env.API_PREFIX_6;
let prefix27 = process.env.API_PREFIX_27;  // 真人
let prefix28 = process.env.API_PREFIX_28;   // 彩票
//用户投注统计
export const query_userbetlist_byTime = (
  params,
  url = "/report/user/queryUserBetListByTime"
  ) => axios.post(`${prefix}${url}`, params );

//用户投注统计详情列表
export const query_userorderlist = (
  params,
  url = "/admin/order/queryUserOrderList"
  ) => axios.post(`${prefix}${url}`, params );

//查询赛事种类接口  10711   [商户中心/商户管理]：商户球类开关设置页面 球类列表
export const get_order_specialLimit_getSportListByFilter_ = (params, url = "/admin/sport/getSportListByFilter") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });

//商户注单统计接口
export const query_merchantlist = (params, url = "/report/merchant/merchantList") =>
  axios.post(`${prefix}${url}`, params);
  
//用户信息  10711
export const get_order_user_detail = (params, url = "/order/user/detail") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });
//注单中心统计接口
export const post_queryStatistics = (
  params,
  url = "/report/order/getStatistics"
) => axios.post(`${prefix}${url}`, params);

// 最近12个月的统计 10716
export const get_order_user_orderMonth = (params, url = "/report/user/orderMonth") =>
  axios.get(`${prefix1}${url}`, { params: { ...params } });

// 盈利率
export const get_order_user_profit = (params, url = "/report/user/profit") =>
  axios.get(`${prefix1}${url}`, { params: { ...params } });

// 某月的统计
export const get_order_user_orderMonth_days = (params, url = "/order/user/orderMonth/days") =>
  axios.get(`${prefix1}${url}`, { params: { ...params } });

// 数据中心-注单查询-真人注单查询 
export const post_zr_getOrderList = (params, url = "/admin/order/zr/queryOrder") =>
  axios.post(`${prefix}${url}`, params);

// 数据中心-注单查询-彩票注单查询 
export const post_cp_getOrderList = (params, url = "/admin/order/cp/queryOrder") =>
  axios.post(`${prefix}${url}`, params);

// 数据中心-注单查询-真人注单-统计接口
export const post_zr_getStatistics = (params, url = "/admin/order/zr/getStatistics") =>
  axios.post(`${prefix}${url}`, params);

// 数据中心-注单查询-彩票注单-统计接口
export const post_cp_getStatistics = (params, url = "/admin/order/cp/getStatistics") =>
  axios.post(`${prefix}${url}`, params);

// 数据中心-注单查询-标签
// export const post_userProfileTags_listByType = (
//   params,
//   url = "/admin/BettingOrder/userProfileTags/listByType"
// ) => axios.post(`${prefix}${url}`, params);

// 数据中心-注单查询-百家赔  
export const post_riskOrder_getRiskOrderList = (
  params,
  url = "/admin/BettingOrder/riskOrder/getRiskOrderList"
) => axios.post(`${prefix}${url}`, params);



// 投注用户管理——额度转入转出
export const post_set_user_personalise_new = (params, url = "/admin/user/setUserPersonaliseNew") =>
  axios.post(`${prefix}${url}`, params);

// 投注用户管理——批量导入开启发起用户 用户
export const post_import_userPurchaseRemark = (params, url = "/admin/user/batchUpdateUserSwitch") =>
  axios.post(`${prefix}${url}`, params);