
export const  flattenNestedStructure=(nestedArray)=> {
    const result = [];
    // 遍历初始数组中的每个对象
    nestedArray.forEach((item, index) => {
      item['listLength'] = nestedArray.length
      item['levelIndex'] = index + 1
      item['showEdit'] = false
      result.push(item);
      if (Array.isArray(item.subSortList)) {
        item.subSortList.forEach((subitem, subIndex) => {
          subitem['listLength'] = item.subSortList.length
          subitem['levelIndex'] = subIndex + 1
          subitem['parentIndex'] = item['levelIndex']
          subitem['showEdit'] = false
          result.push(subitem);
          if (Array.isArray(subitem.subSortList)) {
            subitem.subSortList.forEach((lastitem, lastIndex) => {
              lastitem['listLength'] = subitem.subSortList.length
              lastitem['levelIndex'] = lastIndex + 1
              lastitem['parentIndex'] = item['levelIndex'] + '-' + subitem['levelIndex']
              lastitem['showEdit'] = false
              result.push(lastitem);
            })
          }
        });
      }
    });
  
    return result;
  }