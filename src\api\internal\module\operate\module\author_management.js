/*
 * @FilePath: d:/projects/new-merchant-management/src/api/internal/module/operate/module/author_management.js
 * @Description: 赛事文章-作者管理接口
 */

let prefix_1 = process.env.API_PREFIX_4;
import axios from "src/api/common/axioswarpper.js";

// 作者列表
export const get_article_author_list = (params) => {
  let prefix = prefix_1;
  let url = `/manage/articleAuthor/list`;
  return axios.post(`${prefix}${url}`, params);
}

// 切换作者状态
export const change_author_status = (params) => {
  let prefix = prefix_1;
  let url = `/manage/articleAuthor/changeStatus`;
  return axios.post(`${prefix}${url}`, params, {
    type: 1
  });
}

// 新增作者
export const add_article_author = (params) => {
  let prefix = prefix_1;
  let url = `/manage/articleAuthor/add`;
  return axios.post(`${prefix}${url}`, params);
}

// 编辑作者
export const edit_article_author = (params) => {
  let prefix = prefix_1;
  let url = `/manage/articleAuthor/edit`;
  return axios.post(`${prefix}${url}`, params);
}

// // 删除标签
// export const delete_article_author = (params) => {
//   let prefix = prefix_1;
//   let url = `/manage/articleauthor/delete`;
//   return axios.post(`${prefix}${url}`, params, {
//     type: 1
//   });
// }
