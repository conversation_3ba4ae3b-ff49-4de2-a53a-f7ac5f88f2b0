/*
 * @FilePath: d:/projects/new-merchant-management/src/boot/other.js
 * @Description:
 */
import Vue from "vue";

import htmlToPdf from 'src/util/module/htmlToPdf.js'
// json-editor模板
import CodeEditor from 'bin-code-editor';

Vue.use(CodeEditor);

//下载PDF插件
Vue.use(htmlToPdf)

import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'


import VueJsonEditor from 'vue-json-editor';
import VueQuillEditor from 'vue-quill-editor'
Vue.use(VueJsonEditor);
Vue.use(VueQuillEditor);


const FOR_WHICH = process.env.FOR_WHICH;
console.log("process.argv---------11111---", FOR_WHICH);

Vue.prototype.src_internal = FOR_WHICH == "internal"
Vue.prototype.src_external = FOR_WHICH == "external"
