<!--
 * @FilePath: /src/components/common/bet_slip/component/cp_bet_slip.vue
 * @Description: 注单查询-彩票
-->
<template>
  <div class="full-width full-height position-relative">
    <div id="" class="">
      <div id="top2" class="row mr10x line-height-31px items-center text-panda-text-dark bg-panda-bg-6 pb10x border-radius-4px">
        <!-- 会员ID -->
        <div class="append-handle-btn-input ml10x w-150 position-relative">
          <a-input v-model.trim="searchForm.uid" :placeholder="$t('internal.table_title.yh45')" autocomplete="off" allowClear />
        </div>
        <!-- 会员账号 -->
        <div class="append-handle-btn-input ml10x w-180 position-relative">
          <a-input v-model.trim="searchForm.memberAccount" :placeholder="$t('internal.content_manage.t18')" autocomplete="off" allowClear />
        </div>
        <!-- 商户编号 对内展示，对外仅渠道(代理)商户展示 -->
        <div v-if="is_has_children" class="append-handle-btn-input ml10x w-150 position-relative">
          <a-input v-model.trim="searchForm.merchantCode" :placeholder="$t('internal.table_title.label72')" autocomplete="off" allowClear />
        </div>
        <!-- 注单编号 -->
        <div class="append-handle-btn-input ml10x w-210 position-relative">
          <a-input v-model.trim="searchForm.orderId" :placeholder="$t('internal.table_title.label54')" autocomplete="off" allowClear />
        </div>
        <!-- 期号 -->
        <div class="append-handle-btn-input ml10x w-180 position-relative">
          <a-input v-model.trim="searchForm.ticketPlanNo" :placeholder="$t('internal.table_title.yh17')" autocomplete="off" allowClear />
        </div>
        <!-- 追号单编号 -->
        <div class="append-handle-btn-input ml10x w-180 position-relative">
          <a-input v-model.trim="searchForm.chaseId" :placeholder="$t('internal.table_title.yh111')" autocomplete="off" allowClear />
        </div>
        <!-- 注单状态 -->
        <div class="append-handle-btn-input ml10x w-120 position-relative">
          <a-select v-model="searchForm.betStatus" :placeholder="$t('internal.state')" autocomplete style="width: 120px">
            <a-select-option :value="item.value" v-for="(item, index) in betStatus_list" :key="index">{{ item.label }}</a-select-option>
          </a-select>
        </div>
        <!-- 币种 -->
        <div class="append-handle-btn-input ml10x w-120 position-relative">
          <a-select v-model="searchForm.currencyType" :placeholder="$t('internal.table_title.label23')" autocomplete style="width: 120px">
            <a-select-option :value="item.value" v-for="(item, index) in currencyType_list" :key="index">{{ item.label }}</a-select-option>
          </a-select>
        </div>
        <!-- 7天实时库 -->
        <div
          class="append-handle-btn-input position-relative ml10x"
          v-if="!is_for_bet_slip_es && !is_for_bet_slip_es_timely"
        >
          <a-select
            autocomplete
            style="width: 110px"
            v-model="searchForm.databaseSwitch"
          >
            <a-select-option
              :value="item.value"
              v-for="(item, index) in database_type"
              :key="index"
              >{{ item.label }}</a-select-option
            >
          </a-select>
          <div class="position-absolute select-left-border-bet"></div>
        </div>
        <!-- 日期选择 -->
        <div class="ml10x mt10x append-handle-btn-input">
          <a-range-picker show-time style="width: 310px" showToday :allowClear="false" v-model="searchForm.range_picker_value" />
        </div>
        <!-- 查询 -->
        <div class="append-handle-btn-input mt10x pl30x height-31px line-height-31px">
          <a-button type="primary" @click="on_search">
            {{ $t("internal.label.label27") }}
          </a-button>
        </div>
        <!-- 导出报表 -->
        <div class="append-handle-btn-input pl10x mt10x height-31px line-height-31px">
          <a-button type="primary" @click="handle_export_excel">
            {{ $t("internal.export_text") }}
          </a-button>
        </div>
        <q-space />
      </div>
      <!-- 表格区域 -->
      <a-table ref="TableTotalHeader" class="full-width pl10x pr10x" :scroll="{ x: 1600, y: scrollHeight - 50 }" :pagination="false" :loading="tabledata_loading" :columns="cp_tablecolumns" :dataSource="tabledata" size="small" :rowKey="record => record._index">
        <!-- 会员ID -->
        <template slot="uid" slot-scope="text,record">
          <table-cell-ellipsis-ant :str="record.uid" :str_all="record.uid" :copytips="$t('internal.table_title.yh45')"></table-cell-ellipsis-ant>
        </template>
       
        <!-- 注单编号 -->
        <template slot="orderId" slot-scope="text,record">
          <table-cell-ellipsis-ant :str="record.orderId" :str_all="record.orderId" :copytips="$t('internal.table_title.label54')"></table-cell-ellipsis-ant>
        </template>
        <!-- 商户编号 对内展示，对外仅渠道(代理)商户展示 -->
        <template v-if="is_has_children" slot="merchantCode" slot-scope="text,record">
          <table-cell-ellipsis-ant :str="record.merchantCode" :str_all="record.merchantCode" :copytips="$t('internal.table_title.label65')"></table-cell-ellipsis-ant>
        </template>
        <!-- 会员账号 -->
        <template slot="memberAccount" slot-scope="text,record">
          <table-cell-ellipsis-ant :str="record.memberAccount" :str_all="record.memberAccount" :copytips="$t('internal.content_manage.t18')"></table-cell-ellipsis-ant>
        </template>
        <!-- 更新时间 -->
        <template slot="updateAt" slot-scope="text,record">
          <span>{{  moment(+record.updateAt).format("YYYY-MM-DD HH:mm:ss") }}</span>
        </template>
        <!-- 投注时间 -->
        <template slot="betTime" slot-scope="text,record">
          <span>{{  moment(record.betTime).format("YYYY-MM-DD HH:mm:ss") }}</span>
        </template>
        <!-- 是否为追号单  true是 false否-->
        <template slot="chaseOrder" slot-scope="text,record">
          <span>{{ record.chaseOrder ?  $t('internal.common.label63') : $t('internal.common.label64') }}</span>
        </template>
        <!-- 追号单编号 -->
        <template slot="chaseId" slot-scope="text,record">
          <table-cell-ellipsis-ant :str="record.chaseId" :str_all="record.chaseId" :copytips="$t('internal.table_title.yh111')"></table-cell-ellipsis-ant>
        </template>
        <!-- 终端 -->
        <template slot="device" slot-scope="text,record">
          <span>{{ device_map[record.device] || "--" }}</span>
        </template>
        <!-- 开奖号码 -->
        <template slot="ticketResult" slot-scope="text,record">
          <table-cell-ellipsis-ant :str_all="record.ticketResult"></table-cell-ellipsis-ant>
        </template>
        <!-- 货币名称 -->
        <template slot="currencyType" slot-scope="text,record">
          <span>{{ currencyType_map[record.currencyType] || "--" }}</span>
        </template>
        <!-- 投注金额 -->
        <template slot="betMoney" slot-scope="text,record">
          <span>{{ record.betMoney | filterMoneyFormat }}</span>
        </template>
        <!-- 中奖金额 -->
        <template slot="winAmount" slot-scope="text,record">
          <span>{{ record.winAmount | filterMoneyFormat }}</span>
        </template>
        <!-- 状态 record.cancelStatus为true才匹配撤单类型  1-待开奖,2-未中奖,3-已中奖,4-挂起,5-已结算 -->
        <template slot="betStatus" slot-scope="text,record">
          <span>{{ record.cancelStatus ? cancelType_map[record.cancelType] : betStatus_map[record.betStatus] || "--" }}</span>
        </template>
      </a-table>
      <!-- 底部翻页器 和总计区域 -->
      <div class="row q-px-lg" style="box-shadow: 0 -2px 4px rgb(102 102 102 / 10%); margin-top: 4px">
        <!-- 总计 -->
        <div class="col-7">
          <div class="fs16 line-height-24px q-pl-md" v-if="tabledata.length > 0">
            <div>
              <span class="pr10x">
                <!-- 总计 -->
                <span class="title-grey">
                  {{ $t("internal.data.betslip.index.sumBetNo") }}
                </span>
                <!-- xx单 -->
                <span class="fw_600">
                  {{ $t("internal.data.betslip.index.sumBetNo_text", { s: userStatistics.sumBetNo, }) }}
                </span>
              </span>
              <!--用户统计-->
              <span class="pr10x">
                <span class="title-grey">
                  {{ $t("internal.data.betslip.index.userAmount") }}
                </span>
                <span class="fw_600">
                  {{ $t("internal.data.betslip.index.userAmount_text", { s: userStatistics.userAmount, }) }}
                </span>
              </span>
              <!--总计投注额-->
              <span class="pr10x">
                <span class="title-grey">
                  {{ $t("internal.data.betslip.index.betAmount_text") }}
                </span>
                <span class="fw_600" v-if="userStatistics.betAmount">
                  {{ compute_currencyRate_obj_by_code(userStatistics.currency)[ "countryCn" ] }}
                  {{ userStatistics.betAmount | filterMoneyFormat }}
                </span>
                <span class="fw_600" v-else>--</span>
              </span>
              <!-- 用户净输赢 -->
              <span class="pr10x">
                <span class="title-grey">
                  {{ $t("internal.data.betslip.index.sumProfitAmount_text") }}
                </span>
                <span v-if="userStatistics.sumProfitAmount !== undefined" class="fw_600" :class=" userStatistics.sumProfitAmount > 0 ? 'text-red' : 'text-green' ">
                  {{ userStatistics.sumProfitAmount | filterMoneyFormat }}
                </span>
                <span v-else>--</span>
              </span>
              <!-- 平台盈利率 -->
              <span class="pr10x">
                <span class="title-grey">{{ $t("internal.data.betslip.index.userStatistics_text") }}
                </span>
                <span class="fw_600" v-if=" userStatistics.sumProfitAmount && userStatistics.betAmount " :class=" userStatistics.sumProfitAmount > 0 ? 'text-green' : 'text-red' ">
                  <span v-if="userStatistics.sumProfitAmount > 0">-</span>
                  {{ to_percent( Math.abs(userStatistics.sumProfitAmount) / userStatistics.betAmount ) }}
                </span>
                <span v-else>--</span>
              </span>
              <!-- 此处只汇总用户近90天的已结算注单记录！与用户基本信息中累计注单数据不一致属于正常现象，详情请咨询平台客服工作人员！ -->
              <a-tooltip trigger="hover">
                <template slot="title">
                  <div :style="`max-width: 240px; word-break:break-all;`">
                    {{ $t("internal.message.label115") }}
                  </div>
                </template>
                <a-icon type="question-circle" class="text-red fs15 cursor-pointer" />
              </a-tooltip>
            </div>
            <div>
              <span class="row">
                <!--总计有效投注额-->
                <span class="pr10x">
                  <span class="title-grey">
                    {{ $t("internal.data.betslip.index.label1") }}
                  </span>
                  <span class="fw_600" v-if="userStatistics.sumValidBetMoney">
                    {{ compute_currencyRate_obj_by_code(userStatistics.currency)[ "countryCn" ] }}
                    {{ userStatistics.sumValidBetMoney | filterMoneyFormat }}
                  </span>
                  <span class="fw_600" v-else>--</span>
                </span>
                <span class="pr10x">
                  <!-- 有效笔数 -->
                  <span class="title-grey">
                    {{ $t("internal.data.betslip.index.label2") }}
                  </span>
                  <!-- xx单 -->
                  <span class="fw_600" v-if="userStatistics.sumValidBetNo">
                    {{ $t("internal.data.betslip.index.sumBetNo_text", { s: userStatistics.sumValidBetNo, }) }}
                  </span>
                  <span class="fw_600" v-else>--</span>
                </span>
              </span>
            </div>
          </div>
          <!-- 统计数据正在努力加载中...... -->
          <div v-else class="fs16 position-absolute" style="bottom: 11px; left: 25px;">
            <span v-if="tabledata.length > 0">{{ $t("internal.data.betslip.index.loading_text") }}</span>
          </div>
        </div>
        <div class="col-5">
          <!-- 分页器 -->
          <a-pagination v-if="tabledata && tabledata.length > 0" :total="pagination.total" :current="pagination.current" show-size-changer show-quick-jumper :page-size-options="pagination.pageSizeOptions" :page-size="pagination.pageSize" :show-total="total => $t('internal.showTotal_text', [pagination.total])" @change="onChange" @showSizeChange="onShowSizeChange" style="box-shadow: none" />
        </div>
      </div>
    </div>
    <!-- 报表下载弹窗 -->
    <q-dialog v-model="exportExcelShow" persistent transition-show="scale" transition-hide="scale">
      <dialog-excel :export_param="export_param"></dialog-excel>
    </q-dialog>
  </div>
</template>

<script>
import { api_user } from "src/api/index.js";
import moment from "moment";
import { mapGetters } from "vuex";
import mixins from "src/mixins/internal/index.js";
import { handleCopy } from "src/util/module/common.js";
import dataCenterMixin from "src/mixins/internal/module/datacentertablemixin.js";
import {
  cp_tablecolumns_config,
  currencyType_map,
  betStatus_map,
  betStatus_list,
  currencyType_list,
  cancelType_map,
} from "src/components/common/bet_slip/config/cp_config.js";
import dialogExcel from "src/components/common/dialog/dialogExcel.vue";
export default {
  name: "cpBetSlip",
  mixins: [...mixins, dataCenterMixin],
  components: {
    dialogExcel,
  },
  data() {
    const sart_time = `${moment(
      new Date().setDate(new Date().getDate())
    ).format("YYYY-MM-DD")} 00:00:00`;
    const end_time = `${moment(new Date().setDate(new Date().getDate())).format(
      "YYYY-MM-DD"
    )} 23:59:59`;
    return {
      currencyType_map, // 多币种映射
      betStatus_map, // 注单状态映射
      betStatus_list, // 注单状态
      currencyType_list, // 币种
      cancelType_map, // 撤单类型
      cp_tablecolumns: [], // 表格配置
      tabledata_loading: false, // 表格loading
      tabledata: [], // 表格数据
      database_type: this.$t("internal.filters.databaseTab"),  // 7天实时库  1：7天实时库  2：35天注单大表
      searchForm: {
        range_picker_value: [
          moment(sart_time, "YYYY-MM-DD HH:mm:ss"),
          moment(end_time, "YYYY-MM-DD HH:mm:ss"),
        ], // 日期选择器
        databaseSwitch: 1 // 1 为查7天实时库 2 为查35天大表
      },
      // 投注终端
      device_map: {
        1: "web",
        2: "IOS",
        3: "Android",
        4: "H5",
      },
      // 导出报表参数
      export_param: {},
      exportExcelShow: false,
      // 汇总
      userStatistics: {},
      userInfo: {} // 用户信息
    };
  },
  created() {
    // this.initTableData();
    // 表格配置
    this.cp_tablecolumns = cp_tablecolumns_config(this.src_internal, this.src_external, this.get_user_info);
  },
  computed: {
    ...mapGetters([ "get_window_size_change", "get_user_info"]),
    // 注单查询ES
    is_for_bet_slip_es(){
      return  this.$route.name == "bet_slip_es";
    },
    // 注单查询ES实时流
    is_for_bet_slip_es_timely(){
      return  this.$route.name == "bet_slip_es_timely";
    },
  },
  computed: {
    // 注单查询ES
    is_for_bet_slip_es(){
      return  this.$route.name == "bet_slip_es";
    },
    // 注单查询ES实时流
    is_for_bet_slip_es_timely(){
      return  this.$route.name == "bet_slip_es_timely";
    },
  },
  methods: {
    moment,
    handleCopy,
    initTableData() {
      let params = this.compute_init_tabledata_params();
      this.tabledata_loading = true;
      api_user
        .post_cp_getOrderList(params)
        .then((res) => {
          let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          let arr = this.$lodash.get(res, "data.data.list", []);
          if (code == "0000000") {
            this.tabledata_loading = false;
            this.tabledata = this.rebuild_tabledata_to_needed(arr);
            this.pagination.total =
              this.$lodash.get(res, "data.data.total") * 1 || 0;
          } else {
            this.tabledata = [];
            this.$message.warn(msg);
          }
        })
        .finally(() => {
          this.tabledata_loading = false;
        });
        this.init_getStatistics();
    },
    // 计算请求参数
    compute_init_tabledata_params() {
      const [startTime, endTime] = this.searchForm.range_picker_value;
      const {
        uid,
        orderId,
        merchantCode,
        memberAccount,
        ticketPlanNo,
        chaseId,
        betStatus,
        currencyType,
        databaseSwitch,
      } = this.searchForm;
      let params = {
        uid, // 会员ID
        page: this.pagination.current, // 分页，查询第几页数据。
        size: this.pagination.pageSize, // 分页，每页查询多少条，默认20条。可不传
        startTime: moment(startTime).valueOf(), // 开始注单时间 timeType=6 时要求必填
        endTime: moment(endTime).valueOf(), // 截止注单时间 timeType=6 时要求必填
        timeType: 6, // 1:今天 2:昨天 3:前天 4:近七天 5:近20天 6:自定义
        orderId, // 注单编号
        memberAccount, // 会员账号
        ticketPlanNo, // 期号
        chaseId,  // 追号单编号
        betStatus, // 状态
        currencyType, // 币种
        merchantCode, // 商户编号
        reportFlag: databaseSwitch === 1 ? false : true // false：7天实时库  true：35天注单大表
      };
      params = this.delete_empty_property_with_exclude(params);
      return params;
    },
    // 查询
    on_search() {
      this.initTableData();
    },
    // 导出报表
    handle_export_excel() {
      if (this.pagination.total > 0) {
        let params = this.compute_init_tabledata_params();
        if (this.src_internal) {
          Object.assign(
            params,
            // { "user-id": this.get_user_info.userId },
            // { "app-id": this.get_user_info.appId },
            // { which: false },
            { url: "/manage/order/orderCpExport" }
          );
        } else {
          Object.assign(
            params,
            { token: this.$q.sessionStorage.getItem("token") },
            { url: "/admin/order/orderCpExport" }
          );
        }
        this.export_param = params;
        this.exportExcelShow = true;
      } else {
        this.handle_error();
      }
    },
    // 汇总统计
    init_getStatistics() {
      let params = this.compute_init_tabledata_params();
      api_user.post_cp_getStatistics(params).then((res) => {
        let code = this.$lodash.get(res, "data.code");
        let data = this.$lodash.get(res, "data.data", {});
        if (code == "0000000") {
          this.userStatistics = data;
        }
      });
    },
    // 平台盈利率显示文字
    to_percent(point) {
      let str = Number(point * 100).toFixed(2);
      str += "%";
      return str;
    },
  },
};
</script>
