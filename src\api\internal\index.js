

import * as api_home from "src/api/internal/module/home/<USER>"
import * as api_log from "src/api/internal/module/log/index.js"
import * as api_login from "src/api/internal/module/login/index.js"
import * as api_account from "src/api/internal/module/account/index.js"
import * as api_merchant from "src/api/internal/module/merchant/index.js"
import * as api_public from "src/api/internal/module/public/index.js"
import * as api_base from "src/api/internal/module/base/index.js"
import * as api_user from "src/api/internal/module/user/index.js"
import * as api_finance from "src/api/internal/module/finance/index.js"
import * as api_export from "src/api/internal/module/export/index.js"
import * as api_message from "src/api/internal/module/message/index.js"
import api_operate from "src/api/internal/module/operate/index.js"
import * as api_system from "src/api/internal/module/system/index.js";
import * as api_set from "src/api/internal/module/set/index.js"
import * as api_auth from "src/api/internal/module/auth/index.js"
import * as api_risk_control_set from "src/api/internal/module/risk_control_set/index.js"

import axios from 'src/api/common/axioswarpper.js'
const FILE_PREFIX =axios.prototype.API_PREFIX_FILE_REAL_YEWU
const FILE_PREFIX_FINANCE =  axios.prototype.API_PREFIX_FILE_FINANCE

export  {

   api_home,  //首页模块
   api_login,  //登录模块
   api_log,   //日志模块
   api_account,  //账号管理模块
   api_auth, //授权中心
   api_merchant,// 商户模块
   api_public,//公共模块
   api_base,// 基础
   api_user,// 用户
   api_finance,// 财务中心
   api_export,// 报表导出
   api_message, //消息中心
   api_risk_control_set, // 风险控制设置
   
   api_operate,//运营管理
   api_system,
   api_set,// 设置中心下的 域名池管理 域名切换日志 和域名设置 
   FILE_PREFIX,
   FILE_PREFIX_FINANCE
}

