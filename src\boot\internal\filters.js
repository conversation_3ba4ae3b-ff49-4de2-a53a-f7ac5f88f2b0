/*
 * @FilePath: /src/boot/internal/filters.js
 * @Description: 
 */
import Vue from "vue";
import { i18n } from "src/boot/i18n";

const add_empty_value = obj => {
  for (let i in obj) {
    if (!obj[i].hasOwnProperty("value")) {
      obj[i]["value"] = "";
    }
  }
  return obj;
};
let filters = {
  filterNumberFormat(value) {
    if (!value) return 0;
    value = value.toString().replace(/\$|,/g, "");
    if (isNaN(value)) value = "0";
    let sign = value == (value = Math.abs(value));
    value = Math.floor(value * 100 + 0.50000000001);
    let cents = value % 100;
    value = Math.floor(value / 100).toString();
    if (cents < 10) cents = "0" + cents;
    for (var i = 0; i < Math.floor((value.length - (1 + i)) / 3); i++)
      value =
        value.substring(0, value.length - (4 * i + 3)) +
        "," +
        value.substring(value.length - (4 * i + 3));
    return (sign ? "" : "-") + value;
  },

  filterAmount(value) {
    if (value === null || value === undefined) return "-";
    value = value + "";
    let result = ""; //存储格式化的结果
    let j = 0; //控制每3位加一个逗号
    let floatIndex = value.indexOf(".");
    if (floatIndex >= 1) {
      if (Number(value) >= 0) {
        for (let i = value.length - 1; i >= 0; i--) {
          //从末尾开始遍历到第一个
          j++;
          if (i >= floatIndex) {
            // 小数部分不用添加分隔符
            j = 0;
          }
          result = value[i] + result;
          if (j == 3) {
            //金额的最后一个数字正好满足3位时，其前面不用加逗号，例:500.00
            if (i != 0) {
              result = "," + result;
            }
            j = 0;
          }
        }
        return result;
      } else {
        value = value.substr(1);
        for (let i = value.length - 1; i >= 0; i--) {
          //从末尾开始遍历到第一个
          j++;
          if (i >= floatIndex) {
            // 小数部分不用添加分隔符
            j = 0;
          }
          result = value[i] + result;
          if (j == 4) {
            //金额的最后一个数字正好满足3位时，其前面不用加逗号，例:500.00
            if (i != 0) {
              result = "," + result;
            }
            j = 0;
          }
        }
        return "-" + result;
      }
    } else {
      return value.toString().replace(/(\d)(?=(?:\d{3})+$)/g, "$1,");
    }
  },

  filterAmount_(value){
    return filters.filterAmount(value)
    
  },
  /**
   * 将数值四舍五入(保留2位小数)后格式化成金额形式
   * @param value 数值(Number或者String)
   * @return 金额格式的字符串,如'1,234,567.45'
   * @type String
   */

  filterMoneyFormat(value) {
    if (!value) return 0;
    value = value.toString().replace(/\$|,/g, "");
    if (isNaN(value)) value = "0";
    let sign = value == (value = Math.abs(value));
    value = Math.floor(value * 100 + 0.50000000001);
    let cents = value % 100;
    value = Math.floor(value / 100).toString();
    if (cents < 10) cents = "0" + cents;
    for (var i = 0; i < Math.floor((value.length - (1 + i)) / 3); i++)
      value =
        value.substring(0, value.length - (4 * i + 3)) +
        "," +
        value.substring(value.length - (4 * i + 3));
    return (sign ? "" : "-") + value + "." + cents;
  },

  filterAmount_2(value) {
    if (!value) return "0" + i18n.t("internal.common.label46");
    value = value + "";
    return (
      value.toString().replace(/(\d)(?=(?:\d{3})+$)/g, "$1,") +
      i18n.t("internal.common.label46")
    );
  },
  filterTransferType(val) {
    // "filterTransferType": { "0": "转入", "1": "转出", "2": "投注", "3": "结算", "4": "撤单" }
    let arr = i18n.t("internal.filters.filterTransferType");
    return arr[val - 1];
  },
  filterAgentLevel(val) {
    // let arr = ["直营商户", "渠道商户", "二级商户"];
    let arr = i18n.t("internal.panel_type");
    return arr[val];
  },

  filterPaymentCycle(val) {
    // "filterPaymentCycle": { "0": "每月", "1": "每季度", "2": "每半年", "3": "每年" },
    let arr = i18n.t("internal.filterPaymentCycle");
    return arr[val - 1];
  },

  filterComputingStandard(val) {
    // "computingStandard": { "0": "盈利额", "1": "投注额" },
    let arr = i18n.t("internal.filters.computingStandard");
    return arr[val - 1];
  },
  filterMatchLevel(val) {
    let str = "";
    let obj = i18n.t("internal.filters.matchLevelList")[val];
    return obj ? obj.label : val;
  },
  filterUserLevel(val) {
    let arr = i18n.t("internal.filters.filterUserLevel");
    return arr[val - 1];
  },
  filterStatues(val) {
    // 2是走水，3-输，4-赢，5-半赢，6-半输，7赛事取消，8赛事延期
    if (!val) return "-";
    let str = "";
    return (str = i18n.t("internal.filters.filterStatues_str")[val]);
  },
  filterCancelType(val) {
    // 0注单取消，1比赛取消，2比赛延期， 3比赛中断，4比赛重赛，5比赛腰斩，6比赛放弃，7盘口错误，8赔率错误，9队伍错误，10联赛错误，11比分错误，12电视裁判， 13主客场错误，14赛制错误，15赛程错误，16时间错误，17赛事提前，18自定义原因，19数据源取消，20比赛延迟，21操盘手取消，22主动弃赛，23并列获胜，24中途弃赛，25统计错误，40PA手动拒单，41PA自动拒单，42业务拒单，43MTS拒单
    // if (!val) return "-";
    let str = "";
    return (str = i18n.t("internal.filters.filterCancelType_str")[val]);
  },
  filterMerchantTag(val) {
    //  现金网    信用网
    let arr = i18n.t("internal.filters.merchantTag");
    return arr[val] ? arr[val]["label"] : "";
  },
  // 赛前  滚球  活动
  filterMatchTypeList(val) {
    let obj = add_empty_value(i18n.t("internal.data.betslip.store.matchTypeList"));
    let values = Object.values(obj);
    let cobj = values.find(x => x.value == val);
    return cobj ? cobj.label : val;
  },
  // 域名类型 
  filterDomainTypes(val){
      if(!val){ return ''}
    let arr = Object.values(i18n.t("internal.filters.domain_types"));
    let obj = arr.find(x => x.value == val);
    return obj.label;
  }



};

// filters:
for (let i in filters) {
  Vue.filter(i, filters[i]);
}
