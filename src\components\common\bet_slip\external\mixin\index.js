/*
 * @Desc: 
 * @Date: 2020-04-08 13:47:14
 * @Author:Nice
 * @FilePath       : /merchant-own-backstage/src/components/common/bet_slip/external/mixin/index.js
 */
/* eslint-disable */
import { i18n } from 'src/boot/i18n';

const  add_empty_value=(obj)=>{
  for(let i in obj ){
    if(!obj[i].hasOwnProperty('value')){
      obj[i]['value']=''
    }
  }
  return obj
}
const convert_value_to_string=(obj)=>{
  for(let i in obj ){
    obj[i]['value'] = ""+ obj[i]['value']

  }
  return obj
}



export default {
  data() {
    return {
      filterType: convert_value_to_string(i18n.t('external.data.betslip.store.filterType')) ,
      filterTypeReserve: convert_value_to_string(i18n.t('external.data.betslip.store.filterTypeReserve')) ,
      // settleTypeName: i18n.t('external.data.betslip.store.settleTypeName'),
      // matchTypeList: i18n.t('external.data.betslip.store.matchTypeList'),
      settleStatusName: i18n.t('external.data.betslip.store.settleStatusName'),
      deviceTypeName: i18n.t('external.data.betslip.store.deviceTypeName'),
      matchTypeName: i18n.t('external.data.betslip.store.matchTypeName'),
      settleStatusNameObj:  i18n.t('external.data.betslip.store.settleStatusNameObj'),
      filterstatues: i18n.t('external.data.betslip.store.filterstatues'),
      // filterType:   add_empty_value(i18n.t('external.data.betslip.store.filterType')),
      settleTypeName:  add_empty_value(i18n.t('external.data.betslip.store.settleTypeName')),
      matchTypeList:  add_empty_value(i18n.t('external.data.betslip.store.matchTypeList')),
      // settleStatusName:  add_empty_value(i18n.t('external.data.betslip.store.settleStatusName')),
      // deviceTypeName:  add_empty_value(i18n.t('external.data.betslip.store.deviceTypeName')),
      // matchTypeName:  add_empty_value(i18n.t('external.data.betslip.store.matchTypeName')),
      // settleStatusNameObj:   add_empty_value(i18n.t('external.data.betslip.store.settleStatusNameObj')),
      // filterstatues:  add_empty_value(i18n.t('external.data.betslip.store.filterstatues')),
      //展开条件
      expand_conditions:{
        1: i18n.t("internal.syndicate.text14") ,
        2: i18n.t("internal.syndicate.text15") ,
        3: i18n.t("internal.syndicate.text16") 
      }

      
    };
  },
 
};
