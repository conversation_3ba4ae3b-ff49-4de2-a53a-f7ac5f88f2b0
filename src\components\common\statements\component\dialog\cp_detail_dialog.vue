<!--
 * @FilePath: /src/components/common/statements/component/dialog/cp_detail_dialog.vue
 * @Description: 对账单-彩票-每日明细
-->
<template>
  <div style="width: 90vw; height: auto; max-width: 90vw; overflow: hidden;  min-width:1300px; " class="text-panda-text-7 bg-white">
    <q-card class="bg-white text-black">
      <q-card-section class="no-padding">
        <div class="row line-height-40px fs14 bg-panda-dialog text-panda-text-7 pr10x">
          <div class="col-11 q-pl-md text-weight-bolder">
            {{ detailObj.merchantName }} -- {{ $t("internal.table_title.label000996") }}
          </div>
          <q-space />
          <q-btn class="mr5x text-panda-dialog-close" icon="close" v-close-popup />
        </div>
      </q-card-section>
      <q-separator></q-separator>
      <!-- 表格区域 -->
      <a-table ref="TableTotalHeader" class="full-width pl10x pr10x" :scroll="{ x: 1600, y: scrollHeight }" :pagination="false" :loading="tabledata_loading" :columns="columns" :dataSource="tabledata" size="small" :rowKey="record => record._index">
        <!-- 币种 -->
        <template slot="currency" slot-scope="text,record">
          <span>{{ currencyType_cny_map[record.currency] }}</span>
        </template>
        <!-- 总投注额 -->
        <template slot="betAmount" slot-scope="text,record">
          <span>{{ record.betAmount | filterMoneyFormat }}</span>
        </template>
        <!-- 中奖总金额 -->
        <template slot="prizeAmount" slot-scope="text,record">
          <span>{{ record.prizeAmount | filterMoneyFormat }}</span>
        </template>
        <!-- 盈利额 -->
        <template slot="profit" slot-scope="text,record">
          <span>{{ record.profit | filterMoneyFormat }}</span>
        </template>
        <!-- 下载对账单 -->
        <template slot="operate" slot-scope="text,record">
          <span class="cursor-pointer">
            <a-tooltip placement="top">
              <template slot="title">
                <div class="fs12">{{ $t("internal.table_title.label95") }}</div>
                <div></div>
              </template>
              <q-icon @click.native="show_cp_detail_dialog(record)" class="panda-icon panda-icon-xia-zai panda-icon-hover fs18"></q-icon>
            </a-tooltip>
          </span>
        </template>
        <!-- 总计 -->
        <template slot="footer" slot-scope="">
          <a-table ref="TableTotalFooter" class="border-top expanded" :columns="columns" :dataSource="tabledata_total" :scroll="{ x: 1600 }" size="small" :bordered="false" :pagination="false" :showHeader="false" rowKey="rowkey">
            <template slot="financeDate" slot-scope="text,record">
              <div>
                <p>{{ detailObj.startDate }}</p>
                <p>{{ detailObj.endDate }}</p>
              </div>
            </template>
            <template slot="currency" slot-scope="text,record">
              <span>{{ $t("internal.sum") }}{{ record.currency }}</span>
            </template>
            <!-- 总投注额 -->
            <template slot="betAmount" slot-scope="text,record">
              <span>{{ record.betAmount | filterMoneyFormat }}</span>
            </template>
            <!-- 中奖总金额 -->
            <template slot="prizeAmount" slot-scope="text,record">
              <span>{{ record.prizeAmount | filterMoneyFormat }}</span>
            </template>
            <!-- 盈利额 -->
            <template slot="profit" slot-scope="text,record">
              <span>{{ record.profit | filterMoneyFormat }}</span>
            </template>
          </a-table>
        </template>
      </a-table>
      <!-- 分页器 -->
      <!-- <a-pagination v-if="tabledata && tabledata.length > 0" :total="pagination.total" :current="pagination.current" show-size-changer show-quick-jumper :page-size-options="pagination.pageSizeOptions" :page-size="pagination.pageSize" :show-total="total => $t('internal.showTotal_text', [pagination.total])" @change="onChange" @showSizeChange="onShowSizeChange" /> -->
    </q-card>
    <!-- 报表下载弹窗 -->
    <q-dialog v-model="exportExcelShow" persistent transition-show="scale" transition-hide="scale">
      <dialog-excel :export_param="export_param"></dialog-excel>
    </q-dialog>
  </div>
</template>


<script>
import moment from "moment";
import { api_finance } from "src/api/index.js";
import mixins from "src/mixins/internal/index.js";
import { handleCopy } from "src/util/module/common.js";
import dialogExcel from "src/components/common/dialog/dialogExcel.vue";
import dataCenterMixin from "src/mixins/internal/module/datacentertablemixin.js";
import { cp_columns_dialog } from "src/components/common/statements/internal/config/cp_table.js";
import { currencyType_cny_map } from "src/components/common/bet_slip/config/cp_config.js";
export default {
  name: "cpDetailDialog",
  mixins: [...mixins, dataCenterMixin],
  components: {
    dialogExcel,
  },
  props: {
    detailObj: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      columns: cp_columns_dialog, // 表格配置
      currencyType_cny_map, // 多币种映射
      tabledata_loading: false, // 表格loading
      tabledata: [], // 表格数据
      tabledata_total: [], // 汇总数据
      export_param: {}, // 导出报表参数
      exportExcelShow: false, // 导出报表弹窗阈值
    };
  },
  created() {
    this.initTableData();
  },
  mounted() {
    this.scroll_table();
  },
  methods: {
    moment,
    handleCopy,
    initTableData() {
      let params = this.compute_init_tabledata_params();
      this.tabledata_loading = true;
      api_finance
        .post_queryFinCpTotalDayList(params)
        .then((res) => {
          let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          let arr = this.$lodash.get(res, "data.data", []);
          if (code == "0000000") {
            this.tabledata_loading = false;
            this.tabledata = this.rebuild_tabledata_to_needed(arr);
            this.pagination.total =
              this.$lodash.get(res, "data.data.total") * 1 || 0;
          } else {
            this.tabledata = [];
            this.$message.warn(msg);
          }
        })
        .finally(() => {
          this.tabledata_loading = false;
        });
      // 获取汇总数据
      this.initTableData_Total();
    },
    // 汇总数据
    initTableData_Total() {
      const params = this.compute_init_tabledata_params();
      api_finance.post_queryFinCpDayTotal(params).then((res) => {
        let code = this.$lodash.get(res, "data.code");
        let msg = this.$lodash.get(res, "data.msg");
        let arr = this.$lodash.get(res, "data.data", {});
        if (code == "0000000") {
          this.tabledata_total = [arr];
        } else {
          this.$message.warn(msg);
        }
      });
    },
    // 计算请求参数
    compute_init_tabledata_params() {
      const { startDate, endDate, currency, merchantCode, agentLevel } = this.detailObj;
      let params = {
        startDate,
        endDate,
        currency,
        agentLevel,
        merchantCodeList: [merchantCode],
        pageNum: this.pagination.current, // 分页，查询第几页数据。
        pageSize: this.pagination.pageSize, // 分页，每页查询多少条，默认20条。可不传
      };
      params = this.delete_empty_property_with_exclude(params);
      return params;
    },
    // 查看每日明细
    show_cp_detail_dialog(rowData) {
      let params = {
        financeDayId: rowData.financeDayId,
        currency: rowData.currency,
        endTime: this.detailObj.endTime,
        startTime: this.detailObj.startTime,
      };
      if (this.src_internal) {
        params.url = "/order/financeCpMonth/financeDayCpDetailExport";
      } else {
        params.url = "/admin/financeCpMonth/financeDayCpDetailExport";
        params.token = this.$q.sessionStorage.getItem("token");
      }
      this.export_param = { ...params };
      this.exportExcelShow = true;
    },
    // 总计滚动
    scroll_table() {
      const TableHeaderDom =
        this.$refs.TableTotalHeader.$el.querySelectorAll(".ant-table-body");
      TableHeaderDom?.[0].addEventListener(
        "scroll",
        () => {
          this.$refs.TableTotalFooter.$el.querySelectorAll(
            ".ant-table-body"
          )[0].scrollLeft = TableHeaderDom[0].scrollLeft;
        },
        true
      );
    },
  },
};
</script>