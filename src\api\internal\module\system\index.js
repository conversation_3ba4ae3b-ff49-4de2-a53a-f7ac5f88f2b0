import axios from 'src/api/common/axioswarpper.js'

let prefix_1 = process.env.API_PREFIX_7;
let prefix = process.env.API_PREFIX_8;
let yewu_3 = process.env.API_PREFIX_9;

export const post1 = (params, url) => axios.post(`${prefix_1}${url}`, params)
export const get1 = (params, url) => axios.get(`${prefix_1}${url}`, { params: { ...params } })

//该控制器用于查询各种配置信息

export const post_systemtypedict_query = (params, url="/systemTypeDict/query") => axios.post(`${prefix_1}${url}`, params)
export const post_systemtypedict_getSystemTime = (params, url="/marketView/getSystemTime") => axios.get(`${prefix}${url}`, { params: { ...params } })
export const get_sportList = (params, url="/backend/game/getSportList") => axios.get(`${yewu_3}${url}`, { params: { ...params } })