/*
 * @Desc:
 * @Author:Nice
 * @Date:
 * @FilePath: /src/api/external/module/message/index.js
 */
import axios from "src/api/common/axioswarpper.js";

let prefix1 = process.env.API_PREFIX_17;


//跑马灯消息
export const post_admin_noticeNew_getLightNews = (params={}, url = "/admin/noticeNew/getLightNews") =>
  axios.post(`${prefix1}${url}`, params, {type: 1});

//公告栏列表
export const post_admin_noticeNew_notice = (params, url = "/admin/noticeNew/notice") =>
  axios.post(`${prefix1}${url}`, params, {type: 1});

//公告栏详情
export const post_admin_noticeNew_noticeDetail = (params, url = "/admin/noticeNew/noticeDetail") =>
  axios.post(`${prefix1}${url}`, params, {type: 1});

//我的消息列表
export const post_admin_noticeNew_news = (params, url = "/admin/noticeNew/news") =>
  axios.post(`${prefix1}${url}`, params, {type: 1});


//我的消息列表详情查看
export const post_admin_noticeNew_newsDetail = (params, url = "/admin/noticeNew/newsDetail") =>
  axios.post(`${prefix1}${url}`, params, {type: 1});


//公告类型
export const post_admin_noticeNew_noticeType = (params, url = "/admin/noticeNew/noticeType") =>
  axios.post(`${prefix1}${url}`, params);


  //公告的 语言类型
  export const post_admin_notice_langType = (params, url = "/admin/notice/langType") =>
  axios.post(`${prefix1}${url}`, params, {type: 1});