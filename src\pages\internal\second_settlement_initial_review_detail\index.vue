
<!--
 * @FilePath:
 * @Description: 财务中心-二次结算初次审核-商户详情
 -->
 <template>
  <div class="full-width full-height">
    <div class="pl10x pt10x pb10x" id="top1">
      <q-breadcrumbs
        separator="/"
        active-color="whiddte"
        class="panda-text-2"
      >
        <q-breadcrumbs-el
        :label="$t('internal.finance.liquidation.index.bar')[0]"
        />
        <q-breadcrumbs-el
         v-if="page_type==1"
          :label="$t('internal.menujs.second_settlement_initial_review')"
          class="fw_600 panda-text-1"
        />
        <q-breadcrumbs-el
         v-else-if="page_type==2"
          :label="$t('internal.menujs.second_settlement_and_second_review')"
          class="fw_600 panda-text-1"
        />
        <q-breadcrumbs-el
          :label="$t('internal.menujs.merchant_details')"
          class="fw_600 panda-text-1"
        />
      </q-breadcrumbs>
    </div>
    <div
      class="bg-panda-bg-6 shadow-3 border-radius-4px mt0x mr10x mb10x ml10x"
    >
      <!-- 搜索条件   数据中心/前端域名查询    -->
      <div
        id="top2"
        style="min-width: 1600px; overflow-x: hidden"
        class="
          row
          line-height-30px
          items-center
          text-panda-text-7
          bg-panda-bg-6
          pb10x
          pt10x
          border-radius-4px
        "
      >
        <!-- 请输入查询域名,支持模糊搜索-->
      <div class="no-left append-handle-btn-input ml20x position-relative" >
      
        <div  class="append-handle-btn-input ml10x row position-relative w-200">
          <a-input
            :placeholder="$t('internal.finance.secondarysettlement.label5')"
            autocomplete="off"
             v-model="searchForm.merchantName"
            allowClear
          >
           </a-input>
        </div>
      </div>
      <!-- 请输入查询域名,支持模糊搜索-->
    <div class="no-left append-handle-btn-input ml20x position-relative" >
    
      <div  class="append-handle-btn-input ml10x row position-relative w-200">
        <a-input
          :placeholder="$t('internal.finance.secondarysettlement.label26')"
          autocomplete="off"
           v-model="searchForm.merchantCode"
          allowClear
        >
         </a-input>
      </div>
    </div>
      <!-- 请选择审核状态-->
      <div class="append-handle-btn-input position-relative ml10">
        <a-select 
        autocomplete
          style="width: 200px"
          v-model="searchForm.approvalStatus"
        >
        <a-select-option
        :value="''"
         >{{$t('internal.second_settlement_initial_review.text__18') }}</a-select-option
       > 
       <a-select-option
       :value="index"
         v-for="(item, index) in state_list"
        :key="index"
        >{{ item }}</a-select-option
      > 
        </a-select>
      </div>
      
          <!-- 请选择二次结算原因-->
          <div class="append-handle-btn-input position-relative ml10">
            <a-select 
            autocomplete
              style="width: 200px"
              v-model="searchForm.remark"
            >
            <a-select-option
            :value="''"
             >{{$t('internal.finance.secondary_settlement_list_title') }}</a-select-option
           > 
               <a-select-option
               :value="item"
                 v-for="(item, index) in secondary_settlement_list"
                :key="index"
                >{{ item }}</a-select-option
              > 
            </a-select>
          </div>
          <!--搜索-->
          <div class="append-handle-btn-input ml10x height-30px line-height-30px">
            <a-button type="primary" style="height: 30px; line-height: 30px" @click="initTableData">{{
              $t("internal.search")
            }}</a-button>
          </div>
        <q-space />
        <!--返回-->
        <div class="append-handle-btn-input ml10x height-30px line-height-30px mr10x">
          <a-button type="primary" style="height: 30px; line-height: 30px" @click="handle_back">{{
            $t("internal.back")
          }}</a-button>
        </div>
      </div>
      <!-- 表格区域 -->
      <a-table
        bordered
        ref="TableInfo"
        class="pl10x pr10x expanded"
        :columns="columns"
        :dataSource="tabledata"
        :scroll="{ x: 1600, y: scrollHeight - 45 }"
        :pagination="false"
        :loading="tabledata_loading"
        size="middle"
        rowKey="_index"
      >   
          
      <!--勾选-->
      <div slot="all_select" slot-scope="text, record, index">
        <div v-if="[0,1,2,5,7].includes(record.approvalStatus)">
            <a-checkbox v-model="record.selected"  @change="handle_cancel_order_input_changed(record)">
            </a-checkbox>
        </div>
        </div>  <!-- 申请审批日期 -->
        <div slot="applicationTime" slot-scope="text, record" class="tdpadding">
          {{moment(text).format('YYYY/MM/DD')}}
          </div>
         <!-- 结算日期 -->
        <div slot="settleTime" slot-scope="text, record" class="tdpadding">
          {{moment(text).format('YYYY/MM/DD')}}
          </div>
        <!-- 赔偿金额 -->
        <div slot="changeTotalAmount" slot-scope="text, record" class="tdpadding">
          {{addCommas(text)}}
          </div>
        <div slot="remark" slot-scope="text, record" class="tdpadding">
          <!-- 二次结算原因 -->
          <div v-if="record.remark">
            {{record.remark}}
          </div>
        </div>
        <!-- 状态 -->
        <div slot="approvalStatus" slot-scope="text, record" class="tdpadding">
          <span :class="set_color_by_state(record)">{{$t("internal.finance.state_list")[record.approvalStatus]}}</span>
         
          </div>
          <!-- 操作 -->
          <div slot="action" slot-scope="text, record" class="tdpadding"> 
            <span  class="pl10x text-blue cursor-pointer" @click="go_detail(record)">
              {{ $t("internal.second_settlement_initial_review.text__15") }}
            </span>
           </div>
          <template slot="footer">
          <!-- 分页器 -->
          <a-pagination
            v-if="tabledata.length > 0"
            :total="pagination.total"
            :current="pagination.current"
            show-size-changer
            show-quick-jumper
            :page-size-options="pagination.pageSizeOptions"
            :page-size="pagination.pageSize"
            :show-total="total => $t('internal.showTotal_text', [pagination.total])"
            @change="onChange"
            @showSizeChange="onShowSizeChange"
          />
          <div class="fs16 position-absolute line-height-24px" style="bottom: 10px; left: 25px;" v-if="tabledata.length>0"> 
          <div>
           <span class="row ml10x" v-if="tabledata.length>0">
          
              <!-- 赔付总金额 -->
              <span >
                <span class="title-grey">{{ $t("internal.second_settlement_initial_review.text__12") }}:</span>
                 <span class="text-red">
            
                  {{addCommas(order_totals.totalPayoutAmt)}}
             </span>    
              </span>
              <div 
              v-if="show_handle_rejection_btn">
               <!--提交审核-->
               <a-button
               type="primary"
               class="ml10x"
                 style="height: 30px"
                 @click="show_submit_confirmation_handle()"
               >{{ $t("internal.second_settlement_initial_review.text__13") }}
               </a-button>
               <!--审核驳回-->
               <a-button
               type="primary"
               class="ml10x"
               :style="{ backgroundColor: '#f6a04d', borderColor: '#f6a04d' }"
               @click="handle_rejection"
               >{{ $t("internal.second_settlement_initial_review.text__14") }}
               </a-button>
              </div>
          </span>
        </div>
      </div> 
        </template>
      </a-table>
    </div>
    
    <!-- 驳回弹窗 -->
     <q-dialog v-model="show_rejection_confirm" persistent teansition-show="scale" transition-hide="scale">
      <dialog_rejection
      :cancel_arr="cancel_arr"
       @close_dialog="handle_break"/>
     </q-dialog>
     <!-- 赔付或审批 -->
     <q-dialog v-model="show_submit_confirmation" persistent teansition-show="scale" transition-hide="scale">
       <submit_confirmation :detailObj="submit_confirmation_order_detail" @close_dialog="handle_break"></submit_confirmation>
     </q-dialog>
  </div>
</template>
<script>
import moment from "moment";
import { i18n } from "src/boot/i18n";
import { api_finance } from "src/api/index.js";
import mixins from "src/mixins/internal/index.js";
import { tablecolumns_config } from "./config/config.js";
import dialog_rejection from "src/pages/internal/second_settlement_initial_review/component/dialog_rejection.vue"
import submit_confirmation from "src/pages/internal/second_settlement_initial_review/component/submit_confirmation.vue"


export default {
  mixins: [...mixins],
  components: {dialog_rejection,submit_confirmation},
  data() {
    return {
      //请选择二次结算原因
      secondary_settlement_list:i18n.t("internal.finance.secondary_settlement_list"),
      //提交赔付和审核--详情
      submit_confirmation_order_detail:{},
      //提交赔付和审核
      show_submit_confirmation:false,
      month_type:1,
      columns:tablecolumns_config,
      //搜索条件
      searchForm: {
        remark: "", //二次结算原因/变动原因
        //审核状态
        approvalStatus:"",
        merchantName: "", // 商户名称
        // 商户编码
        merchantCode:"",
      },
      order_totals:{},//总额数据 
      tabledata: [], // 表格数据
      tabledata_loading: false, //表格还没有加载过来的时候转圈圈
      cancel_arr: [], //准备去取消的  原始数据集合
 
      show_rejection_confirm:false,
    };
  },
  computed: {
    show_handle_rejection_btn(){
     return this.tabledata.find(item=>[0,1,2,5,7].includes(item.approvalStatus))
    },
    page_type(){
      if(this.$route.name == "second_settlement_initial_review_detail"){
        //初次审核
        return 1
      }else if(this.$route.name == "second_settlement_and_second_review_detail"){
        //二次审核
        return 2
      }
    },
  
    state_list(){
      let state_list=i18n.t("internal.finance.state_list")
      if(1){
        //初次审核
      return {
        "1":state_list[1],
        "4":state_list[4],
        "5":state_list[5]
      }
      }else{
        //二次审核
      return {
        "3":state_list[2],
        "4":state_list[6],
        "5":state_list[7]
      }
      }
    }
  },
  created() {
    this.tabledata_loading = true;
    this.initTableData();
  },
  methods: {
    moment,
    // 添加千位符
    addCommas(num) {
      return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    go_detail(record){
      
      if(this.page_type==1){
        
      this.$router.push({name: 'second_settlement_initial_review_order_details', query: { batchNo:record.batchNo }})
    
      }else if(this.page_type==2){ 
      this.$router.push({name: 'second_settlement_and_second_review_order_details', query: { batchNo:record.batchNo }})
    
      }},
    handle_back() {
      this.$router.go(-1);
    },
    /*
    * 提交赔付、提交审核
    */
    show_submit_confirmation_handle(){
          let item={
            ...this.tabledata[0],
            changeTotalAmount:this.order_totals?.totalPayoutAmt
          }
      this.submit_confirmation_order_detail={
        list:[item],
        type:this.page_type
      }
      this.show_submit_confirmation=true
    },
    /*
    * 审核驳回
    */
    handle_rejection(){
      this.show_rejection_confirm=true
    },
    handle_break(value){
      this.show_rejection_confirm=false
      this.show_submit_confirmation=false
      if(value){
      this.initTableData();
      }
    },
    // 取消
    handle_cancel_order_input_changed(record) {
      //取消
      // cancel_arr:[] ,//准备去取消的  原始数据集合
      let selected = record.selected;
      if (selected) {
        //选中
        this.cancel_arr.push(record);
      } else {
        // 取消选中
        this.cancel_arr=this.cancel_arr.filter(item=>item.id != record.id)
      }
      this.$forceUpdate();
    },
    set_color_by_state(record){
      if([4,5,6,7].includes(record.approvalStatus)){
        return 'text-red'
      }else if([3].includes(record.approvalStatus)){
        return 'text-green'
      }
    },
    //查询
    domain_query(type=1) {
      this.month_type=type
    },

    // 请求列表数据
    initTableData() {
      this.tabledata_loading = true;
      this.cancel_arr=[]
      let params = this.compute_init_tabledata_params();
      params = this.delete_empty_property_with_exclude(params);
      api_finance.settlement_inquiry.getSummaryMerchantList(params).then(res => {
          let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          this.tabledata_loading = false;
          if ( code && code == "0000000" ) {
            let arr = this.$lodash.get(res, "data.data") || [];
            let info_data=[]
            if(arr?.pageInfo?.records){
              info_data=arr?.pageInfo?.records
              this.order_totals.totalPayoutAmt=arr?.totalPayoutAmt
            }
            this.tabledata = this.rebuild_tabledata_to_needed(info_data);
            this.pagination.total =
            this.tabledata.length || 0;
          } else {
            this.$message.error(msg, 5);
          }
      });
    },
    
    rebuild_tabledata_to_needed(arr) {
      if(!arr || !arr.length) {
        return []
      }
      // 就散这一页数据 能取消的所有 注单的总数
      arr.map((item, index) => {

        // if([0,1,2,5,7].includes(item.approvalStatus)){
        // item.selected = true;
        // this.cancel_arr.push(item);
        // }
       item._index =
       (this.pagination.current - 1) * this.pagination.pageSize + index + 1;
      });
      return arr;

    },
    // 计算请求参数
    compute_init_tabledata_params() {
      let { pageSize, sort, orderBy } = this.pagination;
      let {
        //审核
        approvalStatus,
        remark, //二次结算原因/变动原因
        merchantName,
        merchantCode,
      } = this.searchForm;
      let params= {
        //审核
        // approvalStatus,
        remark, //二次结算原因/变动原因
        merchantName,
        merchantCode,
        "timeType": 1, //1.结算时间
        pageNum: this.pagination.current, //分页，查询第几页数据。
        pageSize, //分页，每页查询多少条，默认20条。可不传
      }

      if(this.page_type==1){
        //初次
        if(approvalStatus){
          params.approvalStatusList=[approvalStatus]
        }else{
          
          params.approvalStatusList=[1,4,5]
        }
      }else if(this.page_type==2){
        //二次
        //初次
        if(approvalStatus){
          params.approvalStatusList=[approvalStatus]
        }else{
          params.approvalStatusList=[2,6,7]
        }
      }
      if(this.$route?.query?.settleTime){
        let settleTime=this.$route?.query?.settleTime
        params.startTime=new Date(settleTime+` 00:00:00`).getTime()
         params.endTime=new Date(settleTime+` 23:59:59`).getTime()
      }
      return params;
    },
    
  }
};
</script>
<style lang="scss" scoped>
</style>
