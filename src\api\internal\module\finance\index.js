/*
 * @Desc: 
 * @Date: 2020-02-01 21:19:42
 * @Author:Nice
 * @FilePath: /src/api/internal/module/finance/index.js
 */
import axios from "src/api/common/axioswarpper.js";
let prefix1 = process.env.API_PREFIX_3;
let prefix2 = process.env.API_PREFIX_4;
//财务-清算管理列表// 716
export const post_report_financeMonth_queryFinanceMonthList = (
  params,
  url = "/order/financeMonth/queryFinanceMonthList"
) => axios.post(`${prefix1}${url}`, params);

//财务-清算管理-总计
export const post_report_financeMonth_queryFinanceMonthTotal = (
  params,
  url = "/order/financeMonth/queryFinanceMonthTotal"
) => axios.post(`${prefix1}${url}`, params);

//财务-清算管理-电子账单// 716
export const post_report_financeMonth_queryFinanceMonthDetail = (
  params,
  url = "/order/financeMonth/queryFinanceMonthDetail"
) => axios.post(`${prefix1}${url}`, params, {type: 5});

//财务-清算管理-费率修改记录// 716
export const post_report_financeMonth_getFinanceOperateRecordList = (
  params,
  url = "/order/financeMonth/getFinanceOperateRecordList"
) => axios.post(`${prefix1}${url}`, params,{type: 1});

//财务-清算管理-电子账单-update// 716
export const post_report_financeMonth_updateFinanceMonthDetail = (
  params,
  url = "/order/financeMonth/updateFinanceMonthDetail"
) => axios.post(`${prefix1}${url}`, params);

// //财务-日对账单// 716
// export const post_report_financeMonth_queryFinanceDay = (
//   params,
//   url = "/order/financeMonth/queryFinanceDay"
// ) => axios.post(`${prefix1}${url}`, params);



//财务-日对账单//   主列表 
export const post_report_financeMonth_queryFinanceDay = (
  params,
  url = "/order/financeMonth/queryFinanceayTotalList"
) => axios.post(`${prefix1}${url}`, params);




//财务-日对账单//   每日明细 弹窗用
export const post_report_financeMonth_queryFinanceDayV2 = (
  params,
  url = "/order/financeMonth/queryFinanceDayV2"
) => axios.post(`${prefix1}${url}`, params);






// 财务-日对账单-总计 // 713
export const post_report_financeMonth_queryFinanceDayTotal = (
  params,
  url = "/order/financeMonth/queryFinanceDayTotal"
) => axios.post(`${prefix1}${url}`, params);

//财务-清算管理二级-总计 716
export const post_report_financeMonth_queryFinanceMonthCount = (
  params,
  url = "/order/financeMonth/queryFinanceMonthCount"
) => axios.post(`${prefix1}${url}`, params);


//对内商户 财务中心/对账工具-开始对账
export const post_checkFinance = (
  params,
  url = "/manage/check/checkFinance"
) => axios.post(`${prefix2}${url}`, params);

//对内商户 财务中心/对账工具-修正对账报表
export const post_editFinance = (
  params,
  url = "/manage/check/editFinance"
) => axios.post(`${prefix2}${url}`, params);

//对内商户 财务中心—对账工具-用户对账—开始对账
export const post_checkUserFinance = (
  params,
  url = "/manage/check/checkUserFinance"
) => axios.post(`${prefix2}${url}`, params);

//对内商户 财务中心—对账工具—用户对账—更正报表
export const post_editUserFinance = (
  params, 
  url = "/manage/check/editUserFinance"
) =>  axios.post(`${prefix2}${url}`, params);
//对内商户 财务中心/二次结算列表
export const post_secondary_settlemen_pageList = (
  params,
  url = "/order/orderTimeSettle/pageList"
) => axios.post(`${prefix1}${url}`, params);

//对内商户 财务中心/关账
export const post_close_financeDay = (
  params,
  url = "/order/financeMonth/closeFinanceDay"
) => axios.post(`${prefix1}${url}`, params);

//对内商户 财务中心/重启对账单
export const post_rebootFinanceDay = (
  params,
  url = "/order/financeMonth/rebootFinanceDay"
) => axios.post(`${prefix1}${url}`, params);


// 对内商户 彩票-对账单
export const post_queryFinCpTotalList = (
  params,
  url = "/order/financeCpMonth/queryFinCpTotalList"
) => axios.post(`${prefix1}${url}`, params);

// 对内商户 彩票-对账单-总计
export const post_queryFinCpDayTotal = (
  params,
  url = "/order/financeCpMonth/queryFinCpDayTotal"
) => axios.post(`${prefix1}${url}`, params);

// 对内商户 彩票-对账单-导出
// export const post_financeCpFileExport = (
//   params,
//   url = "/order/financeCpMonth/financeCpFileExport"
// ) => axios.post(`${prefix1}${url}`, params);

// 对内商户 彩票-对账单-每日明细
export const post_queryFinCpTotalDayList = (
  params,
  url = "/order/financeCpMonth/queryFinCpTotalDayList"
) => axios.post(`${prefix1}${url}`, params);

// 对内商户 真人-对账单
export const post_queryFinZrTotalList = (
  params,
  url = "/order/financeZrMonth/queryFinZrTotalList"
) => axios.post(`${prefix1}${url}`, params);

// 对内商户 真人-对账单-总计
export const post_queryFinZrDayTotal = (
  params,
  url = "/order/financeZrMonth/queryFinZrDayTotal"
) => axios.post(`${prefix1}${url}`, params);

// 对内商户 真人-对账单-导出
// export const post_financeZrFileExport = (
//   params,
//   url = "/order/financeZrMonth/financeZrFileExport"
// ) => axios.post(`${prefix1}${url}`, params);

// 对内商户 真人-对账单-每日明细
export const post_queryFinZrTotalDayList = (
  params,
  url = "/order/financeZrMonth/queryFinZrTotalDayList"
) => axios.post(`${prefix1}${url}`, params);

// 对内商户 真人-数据中心-商户注单统计-列表
export const post_zr_merchant_queryFinZrTotalList = (
  params,
  url = "/order/financeZrMonth/queryFinZrTotalList"
) => axios.post(`${prefix1}${url}`, params);

// 对内商户 真人-数据中心-商户注单统计-汇总
export const post_zr_merchant_queryFinZrDayTotal = (
  params,
  url = "/order/financeZrMonth/queryFinZrDayTotal"
) => axios.post(`${prefix1}${url}`, params);

// 对内商户 彩票-数据中心-商户注单统计-列表
export const post_cp_merchant_queryFinCpTotalList = (
  params,
  url = "/order/financeCpMonth/queryFinCpTotalList"
) => axios.post(`${prefix1}${url}`, params);

// 对内商户 彩票-数据中心-商户注单统计-汇总
export const post_cp_merchant_queryFinCpDayTotal = (
  params,
  url = "/order/financeCpMonth/queryFinCpDayTotal"
) => axios.post(`${prefix1}${url}`, params);




  //结算查询
  import * as settlement_inquiry from "src/api/internal/module/finance/module/settlement_inquiry.js";

  export {settlement_inquiry}