


import { i18n } from "src/boot/i18n";
export const sport_order_columns_config = 
  //表格的列配置
  [
    {
    //   title: "序号",
      title: i18n.t("internal.online_user_management.table_title.serial_number"),
      dataIndex: "index",
      key: "index",
      scopedSlots: { customRender: "index" },
      align: "center",
      width: "5%",
    },
    {
    //   title: "球类",
      title: i18n.t('internal.sport_order_list.ball') ,
      dataIndex: "name",
      key: "name",
      slots:{ title: 'name' },
      scopedSlots: { customRender: "name" },
      align: "center",
      width: "10%",
    },
    {
        //   title: "操作",
          dataIndex: "option",
          key: "option",
          slots:{ title: 'optionName' },
          scopedSlots: { customRender: "option" },
          align: "center",
          width: "10%",
        },    
    ]


  export const num_value_rules_config =  [
      {
        required: true,
        message: i18n.t('internal.common.qsr')
      },
      { pattern: /^[0-9]*[1-9][0-9]*$/, message: i18n.t('internal.sport_order_list.more_than_0') }
    ]