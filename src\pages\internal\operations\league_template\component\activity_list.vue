<!--
 * @FilePath: src/pages/internal/operations/activity_manage/component/activity_list.vue
 * @Description: 运营管理/活动管理/活动列表
-->
<template>
  <div class="full-height full-width">
    <div
      class="bg-panda-bg-6 "
      style="margin: 10px 10px 10px 10px"
    >
      <div class="top" id="top2">
        <div
          class="
            row
            mr10x
            line-height-30px
            items-center
            text-panda-text-dark
            bg-panda-bg-6
            pb10x
            pt10x
            border-radius-4px
          "
        >
          <!--活动标题-->
          <div
            class="
              no-left
              append-handle-btn-input
              row
              ml10x 
              mr15x
              position-relative
            "
          >
            <span> {{ $t("internal.activity_manage.t2") }} </span>
            <a-input-search
              v-model.trim="searchForm.activityTitle"
              :placeholder="
                $t(
                  'internal.activity_manage.t10'
                )
              "
              class="ml5x w-200"
              @search="handle_search"
              autocomplete="off"
              allowClear
            >
            </a-input-search>
          </div>
          <!-- 活动类型 -->
          <div class="append-handle-btn-input  position-relative mr10x">
            {{ $t("internal.activity_manage.t3") }}
            <a-select
              autocomplete
              class="w-150"
              v-model="searchForm.activityType"
              allowClear
              show-search
                option-filter-prop="children"
                :filter-option="filterOption"
            >
              <a-select-option
                :value="item.value"
                v-for="(item, index) in activity_type_arr"
                :key="item.key + '_type'"
                >{{ item.label }}</a-select-option
              ></a-select
            >
          </div>
          <!-- 活动状态 -->
          <div class="append-handle-btn-input ml10x position-relative mr10x">
            {{ $t("internal.activity_manage.t4") }}
            <a-select
              autocomplete
              class="w-150"
              v-model="searchForm.activityStatus"
              allowClear
            >
              <a-select-option
                :value="item.value"
                v-for="(item, index) in activity_status_arr"
                :key="index + '_type'"
                >{{ item.label }}</a-select-option
              ></a-select
            >
          </div>
          <!-- 活动时间 -->
          <div class="append-handle-btn-input line-height-30px ml10x">
            <div>
              <div class="mr10x">
                {{ $t("internal.activity_manage.t5") }}
              <a-range-picker show-time style="width: 310px"  :allowClear="false" v-model="searchForm.range_picker_value" />

              </div>
            </div>
          </div>
          <!--搜索-->
          <q-btn
            class="panda-btn-primary-dense bg-primary ml10x mr10x"
            style="width: 100px; height: 30px; "
            :label="$t('internal.activity_manage.t6')"
            @click="handle_search"
          />
          <q-space />
          <!-- 重置 -->
          <q-btn
            class="panda-btn-primary-dense  mr20x"
            style="width: 100px; height: 30px;background-color: rgb(255, 142, 39) !important;"
            @click="handle_reset()"
          >
            <div class="ml10x">
              {{ $t("internal.activity_manage.t7") }}
            </div>
          </q-btn>
          <!--新增 -->
          <q-btn
            class="panda-btn-primary-dense bg-primary mr20x"
            style="width: 100px; height: 30px"
            :disabled="is_create_btn == 2"
             @click="toggle_dialog('add_config', true)"
          >
            <div class="ml10x"> {{ $t("internal.activity_manage.t8") }}</div>
          </q-btn>
        </div>
      </div>
      <a-table
        class="pl10x pr10x"
        :columns="columns"
        :dataSource="tabledata"
        :scroll="{ x: 1600, y: new_scrollHeight-40 }"
        :pagination="false"
        :loading="tabledata_loading"
        size="middle"
        @change="sorterForTable"
        :rowKey="(record) => record._index"
      >
        <!-- 活动标题  -->
        <span slot="activityTitle" slot-scope="text" v-copy="{msg: $t('internal.username_text'), value: text}">
          <a-tooltip placement="top" :title="text">
            <span style="max-width: 120px; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: normal;">
              {{ text.length > 50 ? text.substring(0, 50) + '...' : text }}
            </span>
          </a-tooltip>
        </span>
        <!-- 活动类型 activityType  -->
        <span slot="activityType" slot-scope="text" v-copy="{msg: $t('internal.username_text'), value: text}">
          {{ activityType_arr[text] }}
        </span>
        
        <!-- 活动时间 activityTime  -->
        <span slot="activityTime" slot-scope="text,record">
          {{ format_time(record.beginTime) }} ~ {{ format_time(record.endTime) }}
          
        </span>
        <!-- 展示设备 deviceType  -->
        <span slot="deviceType" slot-scope="text">
          {{ formatDeviceType(text) }}
        </span>
        <!-- 播放方式 playType  -->
        <span slot="playType" slot-scope="text,record">
          {{ playType_arr[text] }} 
        </span>
        <!-- 烟花款式 fireworkStyle  -->
        <span slot="fireworksType" slot-scope="text,record">
          {{ fireworksType_arr[text] }} 
        </span>
        
        <!-- logo图   -->
        <span slot="logo" slot-scope="text">
          <!-- {{ compute_image_src(text) }} -->
          <img
            v-if="text"
            :src="compute_image_src(text)"
            :style="'width: 72px;padding: 5px;object-fit: contain;height: 72px;'"
          />
        </span>
        <!-- 文案说明 copyDesc -->
        <span slot="copyDesc" slot-scope="text,record">
          <a-tooltip placement="top" :title="formatLanguageDescriptions(record)">
            <span style="max-width: 100px; display: inline-block; overflow: hidden; text-overflow: ellipsis;white-space: normal;">
              {{ formatLanguageDescriptions(record).length > 50 ? formatLanguageDescriptions(record).substring(0, 50) + '...' : formatLanguageDescriptions(record) }}
            </span>
          </a-tooltip>
        </span>
         <!-- 活动状态开关  -->
        <span slot="activityStatus" slot-scope="text, record">
          <span>
            <a-switch  @click="handle_click_change_switch(record, 'switch')" :checked="record.activityStatus != 0 ? true : false"
              :checked-children="$t('internal.template.label177')" :un-checked-children="$t('internal.template.label171')" />
          </span>
        </span>

        <!-- 最后操作时间 -->
        <span slot="updateTime" slot-scope="text">
          <span style="white-space: normal;">  {{ format_time(text) }}</span>
          <!-- <a-tooltip placement="top" :title="format_time(text)">
            <span style="max-width: 100px; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: normal;">
              {{ format_time(text) }}
            </span>
          </a-tooltip> -->
        </span>
        <!-- 最后操作人 -->
        <span slot="updateBy" slot-scope="text">
          {{ text }}
        </span>
        <!-- 操作  -->
        <span slot="operation" slot-scope="text, record">
          <span>
            <!-- 详情  -->
            <a-button type="primary" @click="toggle_dialog('add_config', true,record, true)">{{ $t("internal.activity_manage.config.t13") }}</a-button>
            <!-- 编辑 -->
            <a-button type="primary" class="ml5x mr5x" @click="toggle_dialog('add_config', true,record)">{{ $t("internal.activity_manage.config.t14") }}</a-button>
            <!-- 删除 -->
            <a-button type="danger"  @click="handle_click_change_switch(record, 'delete')">{{ $t("internal.activity_manage.config.t15") }}</a-button>

          </span>
        </span>
      </a-table>
      <!-- <a-pagination
        v-if="tabledata.length > 0"
        :total="pagination.total"
        :current="pagination.current"
        show-size-changer
        show-quick-jumper
        :page-size-options="pagination.pageSizeOptions"
        :page-size="pagination.pageSize"
        :show-total="
          (total) => $t('internal.showTotal_text', [pagination.total])
        "
        @change="onChange"
        @showSizeChange="onShowSizeChange"
      /> -->
    </div>

    <!-- 新增定时播放烟花活动弹窗 -->
    <q-dialog
      v-model="dialg_status['add_config']"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-create-activity
        :dialog_data="dialog_data"
        :select_ticket_list="select_ticket_list"
        :is_view="is_view"
        @on_close="(data) => on_dialog_close('add_config', data)"
      ></dialog-create-activity>
    </q-dialog>
    <!-- 活动状态开关 删除二次确认 弹窗  -->
    <a-modal :visible="visible"   @ok="handleOk" @cancel="handleCancel">
      <template #title>
    <span>
      <a-icon
        type="exclamation-circle"
        style="color: orange; cursor: pointer;width: 3em; font-size: 20px; "
  
      />
       <!-- '更改状态': '删除确认' -->
      <span> {{ is_edit_switch? $t("internal.activity_manage.config2.t17"): $t("internal.activity_manage.config2.t19") }}   </span>
    </span>
  </template>
      <p style="margin-left: 60px;" v-html="content"></p>
    </a-modal>
  </div>
</template>
<script>
import { api_operate, internal_activity_management } from "src/api/index.js";
import moment from "moment";

import mixins from "src/mixins/internal/index.js";
import { download } from "src/util/module/common.js";
import { tablecolumns_config } from "src/pages/internal/operations/activity_manage/config/activity_list.js";
import dialogCreateActivity from "src/pages/internal/operations/activity_manage/component/dialog_create_activity.vue";
import commonmixin from "src/mixins/internal/common/commontoolmixin.js";
import constantmixin from "src/mixins/internal/common/constantmixin.js";
import dataCenterMixin from "src/mixins/internal/module/datacentertablemixin.js";
import log_login_mixin from "src/components/common/bet_slip/internal/mixin/index.js";
import { compute_image_src, IMAGE_ORIGIN } from "src/pages/internal/web/config/other_domain.js";
import { date } from "quasar";
import { i18n } from "src/boot/i18n";

export default {
  mixins: [
    ...mixins,
    commonmixin,
    dataCenterMixin,
    log_login_mixin,
    constantmixin,
  ],
  components: {
    dialogCreateActivity,
  },
  data() {
    const sart_time = `${moment(
      new Date().setDate(new Date().getDate()-17)
    ).format("YYYY-MM-DD")} 00:00:00`;
    const end_time = `${moment(new Date().setDate(new Date().getDate()+12)).format(
      "YYYY-MM-DD"
    )} 23:59:59`;
    return {
      //表格数据
      tabledata: [],
      // 表格配置
      columns: tablecolumns_config,
      // 表格loading
      tabledata_loading: false,
      is_create_btn: null,
      // 传给编辑 详情弹窗的数据
      dialog_data: null,
      visible: false, // 开关对话框
      content: "", // 对话框内容
      currentClickItem: {}, //当前点击的item
      is_edit_switch: false, //是否是编辑开关
      // 弹窗状态
      dialg_status: {
        add_config: false, // 新增/编辑道具弹窗状态
      },
      searchForm: {
        activityTitle: "", // 活动标题
        activityType: "",
        activityStatus: "",
        range_picker_value: [
          // moment(sart_time, "YYYY-MM-DD HH:mm:ss"),
          // moment(end_time, "YYYY-MM-DD HH:mm:ss"),
        ], // 日期选择器
      },
      select_ticket_list: [], // 奖券类型列表
      change_type_list: [], // 账变类型列表
      date_val: [], // 日期选择器
      is_view: false, // 是否是查看
      dialogClickYanhuaShow: false, // 是否显示弹窗
      activity_type_arr: [
          {
            key: 1,
            value: "",
            // label: "请选择",
            label: i18n.t("internal.activity_manage.config2.t6"),
          },
          // {
          //   key: 2,
          //   value: "",
          //   // label: "全部",
          //   label: i18n.t("internal.activity_manage.config2.t2"),

          // },
          {
            key: 3,
            value: 1,
            // label: "定时播放烟花活动",
            label: i18n.t("internal.activity_manage.config2.t3"),

          },

        ],
        fireworksType_arr: {
          // 1: "礼花1",
          1: i18n.t("internal.activity_manage.config2.t4"),

          // 2: "礼花2",
          2: i18n.t("internal.activity_manage.config2.t5"),

        },
        activity_status_arr: [
          {
            key: 1,
            value: -1,
            // label: "请选择",
            label: i18n.t("internal.activity_manage.config2.t6"),
          },
          {
            key: 2,
            value: "",
            // label: "全部",
            label: i18n.t("internal.activity_manage.config2.t2"),

          },
          {
            key: 3,
            value: 1,
            // label: "开启",
            label: i18n.t("internal.activity_manage.config2.t7"),
          },
          {
            key: 4,
            value: 0,
            // label: "关闭",
            label: i18n.t("internal.activity_manage.config2.t8"),

          },
        ],
        // 展示设备映射
        deviceType_arr: {
          0: i18n.t("internal.activity_manage.config2.t9"),  //"PC亚洲版,移动亚洲版,PC欧洲版,H5欧洲版"
          1: i18n.t("internal.activity_manage.config2.t10"),  //PC亚洲版
          2: i18n.t("internal.activity_manage.config2.t11"), //"移动亚洲版"
          3: i18n.t("internal.activity_manage.config2.t12"), //"PC欧洲版",
          4: i18n.t("internal.activity_manage.config2.t13"), //"H5欧洲版",
        },
        // 播放方式映射 
        playType_arr: {
          0: i18n.t("internal.activity_manage.config2.t14"), //"仅一次",
          1: i18n.t("internal.activity_manage.config2.t15"), //"连续播放三次",
          2: i18n.t("internal.activity_manage.config2.t16"), //"循环播放",
        },
        // 活动类型映射
        activityType_arr: {
          1: "定时播放烟花活动",
        }
        // 模拟数据 
        // tabledata: [
        //   {
        //     _index: 1,
        //     id: 1,
        //     activityTitle: "定时播放烟花活动-text",
        //     activityType: "定时播放烟花活动",
        //     deviceType: 1,
        //     // activityTime: "2022-01-01 00:00:00 -2022-01-01 00:00:00",
        //     beginTime: 1640995200000,
        //     endTime: 1641081599000,
        //     playType: 2,
        //     fireworkStyle: "礼花类1",
        //     activityStatus: 1,
        //     logo: "group1/M00/00/14/rBX3NWc4T7qAFfaIAAAdwiby7pk816.png",
        //     copyDesc: "中简： 元旦快乐~",
        //     updateTime: 1731686400000,
        //     createBy: "admin",

        //   },
        //   {
        //     _index: 2,
        //     id: 2,
        //     activityTitle: "定时播放烟花活动-text2",
        //     activityType: "定时播放烟花活动2",
        //     deviceType: 2,
        //     // activityTime: "2022-02-02 00:00:00 -2022-03-02 00:00:00",
        //     beginTime: 1640995200000,
        //     endTime: 1641081599000,
        //     playType: 1,
        //     fireworkStyle: "礼花类2",
        //     activityStatus: 0,
        //     logo: "group1/M00/00/14/rBX3NWc4T7qAFfaIAAAdwiby7pk816.png",
        //     copyDesc: "中简： 新年快乐~",
        //     updateTime: 1731686400000,
        //     createBy: "admin2",

        //   },
        // ],
        
    };
  },

  computed: {
    new_scrollHeight({ scrollHeight }) {
      return scrollHeight - 50;
    },
  },
  created() {
    this.initTableData();
  },
  methods: {
    compute_image_src,
    handle_reset() {
      this.searchForm = {
        activityType: "",
        activityStatus: "",
        // beginTime: moment().format("YYYY-MM-DD"), // 日-开始时间,
        // endTime: moment().format("YYYY-MM-DD"), // 日-结束时间,
      };
      this.date_val = [];
    },
    /**
     * 详情 编辑 新增弹窗控制层
     * @param {String} dialog_key 弹窗key
     * @param {Boolean} status {true:打开 false:关闭}
     * @param {Object} dialog_data {record:当前行数据}
     * @param {Boolean} is_view {是否点击的是详情弹窗}
     */
    // 切换弹窗
    toggle_dialog(dialog_key, status, dialog_data, is_view) {
      // 如果是编辑则有校验逻辑
      if(dialog_key && status && dialog_data && !is_view ) {
        console.log('--编辑', dialog_data);
        if(dialog_data.activityStatus == 1) {
          this.$message.warn('请先关闭该活动，再进行编辑～');
          return
        }
      }
      this.dialg_status[dialog_key] = status;
      this.dialog_data = dialog_data;
      this.is_view = !!is_view;
    },
    delete_activity(record) {
      console.log('删除record',record);
    },
    // 组装请求参数
    compute_init_tabledata_params() {
      const [startTime, endTime] = this.searchForm.range_picker_value || [];

      let {
        activityTitle, 
        activityType,
        activityStatus,
      } = this.searchForm;
      // let { current, pageSize } = this.pagination;
      let params = {
        activityTitle, //标题
        activityType, //类型
        activityStatus,
        // beginTime: moment(startTime).valueOf(), // 开始时间 
        // endTime: moment(endTime).valueOf(), // 截止时间 
      };
      // 仅在 startTime 和 endTime 有值时添加它们
      if (startTime && endTime) {
          params.beginTime = moment(startTime).valueOf(); // 开始时间
          params.endTime = moment(endTime).valueOf(); // 截止时间
      }
      return params;
    },
    // 账变类型名字
    get_change_type_name(type) {
      const item = this.change_type_list.find(
        (item) => item.activityStatus == type
      );
      return item ? item.name : "";
    },

    // 奖券类型名字
    get_token_type_name(type) {
      const item = this.select_ticket_list.find(
        (item) => item.activityType == type
      );
      return item ? item.name : "--";
    },

    /**
     * @description 格式化时间
     * @return 格式化时间
     */
    format_time(time) {
      return date.formatDate(time * 1, "YYYY-MM-DD HH:mm:ss");
    },
    // 初始化表格数据
    async initTableData() {
      this.tabledata_loading = true;
      let params = this.compute_init_tabledata_params();
      params = this.delete_empty_property_with_exclude(params);
      console.log("params", params);

      const res = await internal_activity_management.post_querySelectActivity_list(params);
      this.tabledata_loading = false;
      let code = this.$lodash.get(res, "data.code");
      console.log('code---',code);
      if (code == "0000") {
        // console.log(111111);
        let arr = this.$lodash.get(res, "data.data") || [];
        this.is_create_btn = arr?.length
        // 最后操作时间倒序显示
        arr = arr.sort((item1, item2) => item2.updateTime - item1.updateTime);
        console.log('列表查询数据arr---',arr);
        // this.pagination.current = this.$lodash.get(res, "data.data");
        this.tabledata = this.rebuild_tabledata_to_needed(arr);
        // this.pagination.total =
        //   this.$lodash.get(res, "data.data.total") * 1 || 0;
      }
    },
    // 活动奖券类型查询接口
    async get_token_type() {
      const res = await api_operate.get_token_type();
      const code = this.$lodash.get(res, "data.code");
      if (code == "0000000") {
        let arr = this.$lodash.get(res, "data.data") || [];
        this.select_ticket_list = arr;
      }
    },
    // 账变类型查询接口
    async get_change_type() {
      const res = await api_operate.get_change_type();
      const code = this.$lodash.get(res, "data.code");
      if (code == "0000000") {
        let arr = this.$lodash.get(res, "data.data") || [];
        this.change_type_list = arr;
      }
    },
    // 关闭弹窗回调
    on_dialog_close(dialg_status_key, callback_data) {
      this.dialg_status[dialg_status_key] = false;
      // 如果请求成功，就初始化表格
      if (callback_data.operate_success) {
        this.pagination.current = 1;
        this.initTableData();
      }
    },
    // 搜索
    handle_search() {
      // this.pagination.current = 1;
      this.initTableData();
    },
    // 新增
    handle_add() {
      console.log('新增---');
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    /**
     * 活动状态 删除确认 文本配置
     * 
     * @param {Object} record 当前行数据
     * @param {String} status 
     */
    handle_click_change_switch(record,status) {
      // 开启弹窗
      this.visible = true;
      console.log("handle_click_vs",record);
      if(status == 'switch') { // 开关
        this.is_edit_switch = true;
        this.currentClickItem = this.tabledata.find( item => item.id == record.id);
      console.log(this.currentClickItem);

      // this.content = this.$t("internal.system_level_switch.label7")+`  <span class="panda-text-red">${this.currentClickItem.activityStatus=='1'?this.$t("internal.system_level_switch.label13"):this.$t("internal.system_level_switch.label14")}？</span>`
      // '是否更新该状态为'   '【关闭】': '【开启】'
      this.content = i18n.t("internal.activity_manage.config2.t18") +`  <span class="panda-text-red">${this.currentClickItem.activityStatus== 1? i18n.t("internal.activity_manage.config2.t8"): i18n.t("internal.activity_manage.config2.t7")} ？</span>`
      
      } else {
        this.is_edit_switch = false;
        this.currentClickItem = this.tabledata.find( item => item.id == record.id);
        // '是否确认删除该配置信息？'
        this.content = i18n.t("internal.activity_manage.config2.t20")
      }

    },
    // 活动状态开关确认弹窗
    handleOk() {
      // 调用接口
      if(this.is_edit_switch) {
        //开关操作
        console.log('开关操作');
        let parmas = {
          id: this.currentClickItem.id,
          activityStatus: this.currentClickItem.activityStatus == 1 ? 0 : 1
        }
        internal_activity_management.post_updateActivityStatus(parmas).then(res => {
          let code = this.$lodash.get(res, "data.code");
            console.log('code---',code);
          if (code == "0000") {
            this.$message.success(i18n.t("internal.activity_manage.config2.t21"));
            this.initTableData();

          }
        }).catch(error => {
            console.log('error---',error);
        })

      }else {
        //删除操作
        console.log('删除操作',this.currentClickItem);
        let parmas = {
          id: this.currentClickItem.id
        }
        internal_activity_management.post_delete_delActivity(parmas).then(res => {
          let code = this.$lodash.get(res, "data.code");
            console.log('code---',code);
          if (code == "0000") {
            this.$message.success("删除成功");
            this.initTableData();

          }
        }).catch(error => {
            console.log('error---',error);
        })

      }

      // 关闭弹窗
      this.visible = false
    },
    handleCancel(e) {
      this.visible = false;
    },
    // 设备类型映射
    formatDeviceType(deviceType) {
        // 将 deviceType 字符串转换为数组
        const typeArray = deviceType.split(',').map(Number);
        
        // 根据映射对象获取对应的设备类型名称
        return typeArray.map(type => this.deviceType_arr[type]).filter(Boolean).join(', ');
    },
    // 文案说明映射
    // formatCopyDesc(record) {
    //     // 根据语言类型构建描述字符串
    //     const languageDescriptions = {
    //         zs: `中简：${record.copyDesc}`,
    //         en: `英文：${record.copyDesc}`,
    //     };

    //     // 返回所有语言描述的连接字符串
    //     return Object.keys(languageDescriptions)
    //         .map(key => languageDescriptions[key])
    //         .join(' ');
    // }
    formatLanguageDescriptions(record) {
      // console.log('record-------语言',record);
        // 定义语言类型及其对应的标签
        const languageLabels = {
            zh: "中简",
            en: "英文",
            tw: "繁体",
            vi: "越南语",
            th: "泰语",
            ad: "印尼语",
            mya: "缅甸语",
            es: "西班牙语",
            ara: "阿拉伯语",
            ru: "俄语",
            hin: "印地语",
        };

        // 创建一个对象来存储各语言的描述
        const languageDescriptions = {};

        // 遍历语言请求列表
        record.languageReqList?.forEach(item => {
            const languageType = item.languageType;
            const copyDesc = item.copyDesc;
            // 检查语言是否在映射表中
            if (languageLabels[languageType]) {
                // 使用对象属性来避免重复
                if (!languageDescriptions[languageType]) {
                    languageDescriptions[languageType] = `${languageLabels[languageType]}：${copyDesc}`;
                }
            }
        });
        // 返回所有语言描述的连接字符串
        return Object.values(languageDescriptions).join(' ');
    }

  },
};
</script>


<style lang="scss" scoped>
::v-deep .ant-spin-nested-loading > div > .ant-spin {
  max-height: 760px;
  min-height: 760px;
}
::v-deep .ant-empty-normal {
  margin: 310px 0;
}
.no-left ::v-deep .q-field__append {
  border-left: none;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  width: 350px;
  display: inline-block;
  white-space: nowrap;
}
::v-deep
  .ant-table-middle
  > .ant-table-content
  > .ant-table-scroll
  > .ant-table-header
  > table
  > .ant-table-thead
  > tr
  > th {
  background: var(--q-color-panda-bg-3);
}
::v-deep
  .ant-table-middle
  > .ant-table-content
  > .ant-table-fixed-right
  > .ant-table-header
  > table
  > .ant-table-thead
  > tr
  > th {
  background: var(--q-color-panda-bg-3);
}
::v-deep
  .ant-table-middle
  > .ant-table-content
  > .ant-table-fixed-left
  > .ant-table-header
  > table
  > .ant-table-thead
  > tr
  > th {
  background: var(--q-color-panda-bg-3);
}
.q-dialog__inner--minimized > div {
  max-width: 800px;
}
</style>
