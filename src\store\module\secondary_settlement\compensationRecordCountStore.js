/*
 * @Desc: 财务中心-二次结算-赔付注单
 * @FilePath:
 */
export default {
  state: {
    compensation_record_count: null,  // 赔付注单
  },
  getters: {
    get_compensation_record_count(state) {
      return state.compensation_record_count;
    }
  },
  actions: {
    set_compensation_record_count({ commit }, compensation_record_count) {
      commit("set_compensation_record_count", compensation_record_count)
    },
    clear_compensation_record_count({ commit }) {
      commit("clear_compensation_record_count")
    },
  },
  mutations: {
    set_compensation_record_count(state, compensation_record_count) {
      state.compensation_record_count = compensation_record_count
    },
    clear_compensation_record_count(state) {
      state.compensation_record_count = null
    },
  }
}