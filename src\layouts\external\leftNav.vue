<!--
 * @Desc: 
 * @Author:Nice
 * @Date:
 * @FilePath: /src/layouts/external/leftNav.vue
 -->
<template>
  <a-layout-sider
    class="bg-panda-bg-6 border-right position-relative"
    id="components-layout-demo-custom-trigger"
    v-model="collapsed"
  >
    <!-- <div class="logo" /> -->
    <a-menu
      class="main-menu  overflow-y"
      :selectedKeys="selectedKeys"
      :openKeys="openKeys"
      @click="handle_click"
      mode="inline"
      theme="light"
      :inlineCollapsed="collapsed"
      @openChange="on_open_change"
    >
      <!-- <Menu :menu="menu"/> -->
      <!-- <template v-for="menu in menuList" >
        <Menu v-if="!menu.children" :menu="menu" :key="menu.path"/>
        <ReSubMenu v-else :key="menu.path" :data="menu"></ReSubMenu>
      </template>-->
      <!-- <navMenu :menuList="menuList"/> -->

      <!-- <template v-for="(item) in menuList">
        <a-menu-item v-if="item.children.length==0" :key="item.path">
          <my-icon class="ml10x" :type="item.icon" theme="filled"><span class="ml10x">{{ item.name }}</span></my-icon>
          
        </a-menu-item>
        <a-sub-menu :key="item.path" v-if="item.children && item.children.length > 0">
          <span slot="title">
            <my-icon class="ml10x" v-if="item.icon" :type="item.icon" theme="filled" ><span class="ml10x">{{item.name}}</span></my-icon>         
          </span>
          <a-menu-item v-for="item1 in item.children" :key="item1.path">
            <span class="fs16 ml15x">●</span>
            <span class="ml5x">{{ item1.name }}</span>
          </a-menu-item>
        </a-sub-menu>
      </template> -->

      <!-- <template v-for="item in menuList">


      </template> -->

      <template v-for="item in menuList.filter(x=>x.show&&x.path!='home1')">
        <a-menu-item v-if="compute_real_length(item.children)==0" :key="item.path">
          <my-icon type="p-icon-zhuye" theme="filled" />
          <span>{{ is_zs == "zs"?item.name: is_zs == "en"? item.nameEn : item.nameKo }}</span>
        </a-menu-item>

        <a-sub-menu :key="item.path" v-if="compute_real_length(item.children) > 0">
          <span slot="title">
            <my-icon :type="item.icon" theme="filled" style="font-size:16px;"/>
            <span>{{ is_zs == "zs"?item.name: is_zs == "en"? item.nameEn: item.nameKo }}</span>
            <span class="ml5x" v-if="item.path == 'account'">
                <a-badge :showZero='false' :count="pending_message"/>
            </span>
            <span class="ml5x" v-if="item.path == 'finance'">
              <!-- 财务中心 -赔付注单-->
                <a-badge :showZero='false' :count="compensationRecordCount"/>
            </span>
          </span>
          <a-menu-item v-for="item1 in item.children.filter(x=>x.show&&x.path!='liquidation')" :key="item1.path">
            
            <span class="fs16">●</span>
            <span class="ml5x">{{ is_zs == "zs"?item1.name: is_zs == "en"? item1.nameEn: item1.nameKo }}</span>
            <span class="ml10x fs16" v-if="item1.path=='record_query' && get_record.total > 0">
              <a-icon type="warning" class="text-red fs15" />
            </span>
            <span v-if="item1.path=='user_risk_control'">
                <a-badge :style="is_zs=='zs' ?'':'right:10px'" :showZero='false' :count="pending_message"/>
            </span>
            <span v-if="item1.path=='payout_bets'">
              <!-- 财务中心 -赔付注单-->
                <a-badge :style="is_zs=='zs' ?'':'right:10px'" :showZero='false' :count="compensationRecordCount"/>
            </span>
            
          </a-menu-item>
        </a-sub-menu>
      </template>
    </a-menu>
 

    <div class="collapsed-left-menu row justify-center items-center">
      <div :class="!collapsed ? 'panda-btn-toggle' : ''" class="text-center" @click="toggle_collapsed">
        <my-icon
          class="trigger"
          style="margin-top: -5px;"
          :type="collapsed ? 'p-icon-zhankai2' : 'p-icon-zhankai1'"
        />
      </div>
    </div>   
  </a-layout-sider>
</template>

<script>
import { i18n } from 'src/boot/i18n';
import { mapGetters, mapActions } from "vuex";
import { api_merchant,api_finance } from "src/api/index.js";
import menuList from "src/config/external/menu.js";
export default {
  data() {
    return {
      menuList, // 菜单列表
      collapsed: false, // 是否展开
      rootSubmenuKeys: [], // 有children的menu
      openKeys: [], // //当前展开的 SubMenu 菜单项 key 数组
      selectedKeys: [], // 当前选中的菜单项 key 数组
      collapsed_openKeys:'',//点击collapsed时openKeys的值
      pending_message:null,//待处理消息数量
      timer_message:null,//轮训的
      //待处理消息数量-二次结算
      compensationRecordCount:0,
      //二次结算-轮训的
      timer_settlement_message:null,
    };
  },
   inject:['is_zs'],
  created() {
    this.init_root_sub_menu(); //初始化菜单有展开项的子集菜单
    this.get_user_info && (this.menuList = this.get_user_info.menus)
    !this.get_record.list.length && this.set_record({pageNum: 1, pageSize: 50}) //未结算订单数量请求
    this.query_pending_message()//获取平台用户风控当前待处理消息的数量
    this.query_settlement_message()
  },
  computed: {
    ...mapGetters(["get_user_info",'get_record','get_compensation_record_count']),
    initMenuList() {
      return this.menuList;
    },

  },
  methods: {
    ...mapActions(['set_record','set_compensation_record_count']),
    /*
    * 获取二次结算的消息
    */
    query_settlement_message(){
      
      let menu_itme = this.menuList.find(el=>el.path=="finance")
      let payout_bets = menu_itme?menu_itme.children.find(el=>el.path=="payout_bets"):""
      if(payout_bets&&payout_bets.show){
        this.get_settlement_message()
      }
    },
    /*
    * 获取二次结算
    */
    get_settlement_message(){
      api_finance.settlement_inquiry.getRecordCounts().then(res=>{
            let code = this.$lodash.get(res, "data.code");
            let msg = this.$lodash.get(res, "data.msg");
            let data = this.$lodash.get(res, "data.data");
            if(code == "0000000"){
               let{
                compensationRecordCount}=data
              this.compensationRecordCount = compensationRecordCount;
              this.set_compensation_record_count(compensationRecordCount)
              this.timer_polling_settlement_message()
            }else{
              // this.$message.error(`${msg}`);
            }
          })
    },
    /*
    * 轮询
    */
    timer_polling_settlement_message(){
        if(this.timer_settlement_message){
            clearTimeout(this.timer_settlement_message)
            this.timer_settlement_message = null
        }
      this.timer_settlement_message = setTimeout(() => {
          clearTimeout(this.timer_settlement_message)
          this.get_settlement_message()
      }, 300000)
    },
        /**
     * @description 获取平台用户风控当前待处理消息的数量
     * @return {undefined} undefined
     */
    query_pending_message(){
      let menu_itme = this.menuList.find(el=>el.path=="account")
      let user_risk_show = menu_itme?menu_itme.children.find(el=>el.path=="user_risk_control"):""
      if(user_risk_show&&user_risk_show.show){
        this.get_pending_message()
      }
    },
    timer_pending_message(){
      this.timer_message = setTimeout(() => {
          clearTimeout(this.timer_message)
          this.get_pending_message()
      }, 300000)
    },
    get_pending_message(){
          api_merchant.pending_Count().then(res=>{
            let code = this.$lodash.get(res, "data.code");
            let msg = this.$lodash.get(res, "data.msg");
            let data = this.$lodash.get(res, "data.data");
            if(code == "0000000"){
              this.pending_message = data;
              this.timer_pending_message()
            }else{
              // this.$message.error(`${msg}`);
            }
          })
    },
    /**
     * @description 初始化菜单有展开项的子集菜单
     * @return {undefined} undefined
     */
    init_root_sub_menu() {
      let arr = this.menuList.filter(
        item => item.children && item.children.length > 0
      );
      arr.map(item => {
        this.rootSubmenuKeys.push( this.is_zs == "zs" ? item.name : this.is_zs == "en" ? item.nameEn : item.nameKo);
      });
      // console.log(this.rootSubmenuKeys, "init_root_sub_menu");
    },
    // 页面权限显示
    compute_real_length(arr=[]){
      let show= false
      let arr2= arr.filter(x=>x.show)
      return arr2.length 
    },
    /**
     * @description 点击菜单路由跳转
     * @return {undefined} undefined
     */
    handle_click(e) {
      console.log('click', e)
      this.$router.push({
        name: e.key
      });
      
    },
    /**
     * @description 切换左边导航的按钮
     * @return {undefined} undefined
     */
    toggle_collapsed() {
      if(this.collapsed){
        this.openKeys = this.collapsed_openKeys?JSON.parse(this.collapsed_openKeys):[]
      }else{
        this.collapsed_openKeys = JSON.stringify(this.openKeys)
        this.openKeys = []
      }
      // 为了解决缩小的时候，侧边菜单hover效果的停留显示  
      this.collapsed = !this.collapsed;
    },
    /**
     * @description 是否展开子菜单
     * @return {undefined} undefined
     */
    on_open_change(openKeys) {
      const latestOpenKey = openKeys.find(
        key => this.openKeys.indexOf(key)==-1
      );
      if (this.rootSubmenuKeys.indexOf(latestOpenKey)==-1) {
        this.openKeys = openKeys;
      } else {
        this.openKeys = latestOpenKey ? [latestOpenKey] : [];
      }
    }
  },
  watch: {
    $route: {
      handler(val) {
        // if (this.openKeys.length > 0) {
        //   this.openKeys.pop();
        // }
        // if (this.selectedKeys.length > 0) {
        //   this.selectedKeys.pop();
        // }
        this.openKeys = [val.matched[1].name];
        if (val.meta.father_menu) {
          this.selectedKeys = [...val.meta.father_menu];
        }else{
          this.selectedKeys = [val.name]
        }
        // console.log(this.selectedKeys, "当前选中的路由对应高亮菜单");
        // console.log(this.openKeys, "需要展开的sub");
        // console.log(val.meta, "meta对象");
      },
      immediate: true,
      deep: true
    },
    
    get_compensation_record_count(n){
            if(n=='refresh'){
              this.get_settlement_message()
            }
            if(n=='no_refresh'){
             this.total_settlement_message-= this.compensationRecordCount
             this.compensationRecordCount=0
             this.set_compensation_record_count(0)
            }
        }
  },
  destroyed() {
    if(this.timer_settlement_message){
      clearTimeout(this.timer_settlement_message)
      this.timer_settlement_message = null
    }
    clearTimeout(this.timer_message)
  }
};
</script>

<style lang="scss" scoped>
#components-layout-demo-custom-trigger .trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

#components-layout-demo-custom-trigger .trigger:hover {
  color: #1890ff;
}
.collapsed-left-menu{
  // padding-bottom 5px
  // padding-left 5px
  height: 66px;
  overflow: hidden;
  background-color: #fff
}
.main-menu{
  height: calc(100vh - 170px);
}
</style>