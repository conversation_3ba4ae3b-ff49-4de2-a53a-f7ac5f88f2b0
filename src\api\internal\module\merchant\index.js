/*
 * @Desc:
 * @Date: 2019-12-28 20:59:34
 * @Author:Nice
 * @FilePath: /src/api/internal/module/merchant/index.js
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_4;
let prefix2 = process.env.API_PREFIX_17
let prefix3 = process.env.API_PREFIX_3;

// let url_prefix =  process.env.API_PREFIX_URL_PREFIX+"/login";

// export const post1 = (params, url) => axios.post(`${prefix}${url}`, params)
// export const get1 = (params, url) => axios.get(`${prefix}${url}`, { params: { ...params } })

//下拉联赛
export const query_pull_down_tournament = (
  params,
  url = "/order/match/pullDownTournament"
  ) => axios.post(`${prefix3}${url}`, params );

//根据代理商ID查询渠道商户列表
export const get_manage_merchantAgent_list = (
  params,
  url = "/manage/merchantAgent/merchantList"
) => axios.get(`${prefix}${url}`, { params: { ...params } });


//根据代理商id批量添加渠道商户
export const post_manage_merchant_add_merchant = (
  params,
  url = "/manage/merchantAgent/batchAddMerchant"
  ) => axios.get(`${prefix}${url}`, { params: { ...params } });


//假刪除-渠道商戶
export const post_manage_merchant_delete_subagent = (
  params,
  url = "/manage/merchant/deleteSubAgent"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//商户报表列表
export const query_merchantlist = (params, url = "/order/merchant/merchantList") =>
  axios.post(`${prefix3}${url}`, params);



//对内    (数据中心-商户报注单统计 主列表)调用 (财务中心-对账单 主列表 ) 接口
export const query_FinanceayTotalList = (params, url ="/order/financeMonth/queryFinanceayTotalList") =>
  axios.post(`${prefix3}${url}`, params);

  // 赛事投注统计-单个赛事多币种
export const query_MatchStatisticById = (params, url = "/order/match/queryMatchStatisticById") =>
  axios.post(`${prefix3}${url}`, params);

// //赛事投注统计
// export const query_MatchStatisticList = (params, url = "/order/match/queryMatchStatisticList") =>
//   axios.post(`${prefix3}${url}`, params);

//赛事投注统计(主列表-对内)
export const query_MatchStatisticList = (params, url = "/order/match/queryMatchStatisticListNew") =>
  axios.post(`${prefix3}${url}`, params);


// 赛事投注统计-玩法投注统计
export const query_PlayStatisticList = (
  params,
  url = "/order/match/queryPlayStatisticList"
) => axios.post(`${prefix3}${url}`, params);

// 即时赛事统计报表-玩法投注统计
export const query_MarketBetDetailList = (
  params,
  url = "/order/reportMatch/marketBetDetailList"
) => axios.post(`${prefix3}${url}`, params);

// 即时赛事统计报表-玩法投注统计 - 多盘口二级展开详情
export const query_matchMarketList = (
  params,
  url = "/order/reportMatch/matchMarketList"
) => axios.post(`${prefix3}${url}`, params);

//商户列表   //公司代码设置列表列表
export const get_manage_merchant_list = (
  params,
  url = "/manage/merchant/list"
) => axios.get(`${prefix}${url}`, { params: { ...params } });


//商户 公司代码设置  查看历史代码 接口
export const get_manage_config_queryCodeConfigLogList = (
  params,
  url = "/manage/config/queryCodeConfigLogList"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

// //公司代码设置列表 更新接口
export const post_manage_merchant_updateKanaCode = (
  params,
   url = "/manage/merchant/updateKanaCode"
) => axios.post(`${prefix}${url}`, {   ...params   } ,{type:6});


//公司代码设置列表 更新接口
export const post_manage_merchant_updateSerialNumber = (
  params,
   url = "/manage/merchant/updateSerialNumber"
) => axios.post(`${prefix}${url}`, {   ...params   } ,{type:6});


//获取c端多语言
export const get_cplate_lan_list = (
  params,
  url = "/manage/merchant/getMerchantLanguageList"
) => axios.get(`${prefix}${url}`, { params: { ...params } });


//生成管理员密码
export const get_manage_merchant_getPassword = (
  params,
  url = "/manage/merchant/getPassword"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//查询key列表
export const get_manage_merchant_queryKeyList = (
  params,
  url = "/manage/merchant/queryKeyList"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//生成KEY
export const get_manage_merchant_generateKey = (
  params,
  url = "/manage/merchant/generateKey"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//设置管理员
export const post_manage_merchant_admin_create = (
  params,
  url = "/manage/merchant/admin/create"
) => axios.post(`${prefix}${url}`, params);

//商户详情
export const get_manage_merchant_byid = (params, url = "/manage/merchant") =>
  axios.get(`${prefix}${url}/${params.id}`);

// 商户IP检查重复
export const post_manage_merchant_byid = (
  params,
  url = "/manage/merchant/checkWhiteIp"
) => axios.post(`${prefix}${url}`, params);

//修改商户
export const post_manage_merchant_update = (
  params,
  url = "/manage/merchant/update"
) => axios.post(`${prefix}${url}`, params);

// //修改商户 主要用于 商户球中设置     ----- (商户中心.商户管理.商户球种设置) 保存
export const post_manage_merchant_updateConfigFilter = (
  params,
  url = "/manage/config/updateConfigFilter"
) => axios.post(`${prefix}${url}`, params);

// //修改商户 主要用于 商户球中设置     ----- (商户中心.商户管理.商户球种设置) 保存
export const post_manage_merchant_updateConfigFilterV2 = (
  params,
  url = "/manage/config/updateConfigFilterV2"
) => axios.post(`${prefix}${url}`, params);


//  ?merchantGroup=&merchantCode=     yewu22/init/domain/group/initDomainGroup?merchantGroup=COMMON
// 对内 商户中心 商户管理 新建直营商户  商户分库  初始域名分组  域名方案
export const init_domain_group = (
    params,
    url = "/manage/merchantDomain/initDomainGroup"
) =>  axios.get(`${prefix}${url}`, { params: { ...params } });



// 商户中心 商户 球种设置的 球种列表
export const get_admin_player_getSportList = (params, url = "/admin/player/getSportList") =>
  axios.get(`${prefix}${url}`, { params: { ...params } });


//增加商户
export const post_manage_merchant_create = (
  params,
  url = "/manage/merchant/create"
) => axios.post(`${prefix}${url}`, params);

//修改管理员密码  没有密码 调用这个
export const post_manage_merchant_password_updates = (
  params,
  url = "/manage/merchant/password/update"
) => axios.post(`${prefix}${url}`, params);


//修改管理员密码   有密码 调用这个
export const post_manage_merchant_updateAdminUserName = (
  params,
  url = "/manage/merchant/updateAdminUserName"
) => axios.post(`${prefix}${url}`, params, { type: 6 });










//修改客户端状态
export const get_manage_merchant_updateMerchantStatus = (
  params,
  url = "/manage/merchant/updateMerchantStatus"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

// 修改商户状态
export const get_manage_merchant_updateMerchantBackendStatus = (
  params,
  url = "/manage/merchant/updateMerchantBackendStatus"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//修改KEY
export const get_manage_merchant_updateKey = (
  params,
  url = "/manage/merchant/updateKey"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//商户中心-投注用户管理-投注用户详情
export const get_order_user_getUserDetail = (params, url = "/order/user/getUserDetail") =>
  axios.get(`${prefix3}${url}`, { params: { ...params } });

//商户中心-投注用户管理-用户名选择
export const get_order_user_queryFakeNameByCondition = (params, url = "/order/user/queryFakeNameByCondition") =>
  axios.get(`${prefix3}${url}`, { params: { ...params } });

//商户中心-投注用户管理-投注用户详情-账变记录
export const post_order_user_queryTransferList = (
  params,
  url = "/order/user/queryUserTransferList"
) => axios.post(`${prefix3}${url}`, params);

//商户中心-投注用户管理-投注用户详情-账变记录ES
export const post_order_user_queryTransferListES = (
  params,
  url = "/order/user/queryUserTransferListES"
) => axios.post(`${prefix3}${url}`, params);

//商户中心-投注用户管理-投注用户详情-账变记录ES实时流
export const post_order_user_queryTransferListES_timely = (
  params,
  url = "/order/user/queryUserTransferListStream"
) => axios.post(`${prefix3}${url}`, params);


//数据中心-交易记录查询-账变记录
export const post_manage_account_findAccountHistory = (
  params,
  url = "/order/account/findAccountHistory"
) => axios.post(`${prefix3}${url}`, params);

//数据中心-交易记录查询-上下分记录
export const post_manage_account_findAccountTransHistory = (
  params,
  url = "/order/account/findAccountTransHistory"
) => axios.post(`${prefix3}${url}`, params);


//数据中心-交易记录查询-账变记录 -加扣款 弹窗 (提交)
export const post_order_user_addChangeRecordHistory = (
  params,
  url = "/order/user/addChangeRecordHistory"
) => axios.post(`${prefix3}${url}`, params, {type:6});


//数据中心-交易记录查询-交易记录
export const post_manage_account_findRecord = (
  params,
  url = "/order/account/findRecord"
) => axios.post(`${prefix3}${url}`, params);

//数据中心-交易记录查询-待处理交易记录
export const post_manage_account_findReTryRecord = (
  params,
  url = "/order/account/findReTryRecord"
) => axios.post(`${prefix3}${url}`, params);



//数据中心-交易记录查询-重新发起 单个/多个
export const post_order_account_retryTransfer = (
  params,
  url = "/order/account/retryTransfer"
) => axios.post(`${prefix3}${url}`, params);


//商户中心-商户管理--ip设置
export const post_manage_merchant_update_ip = (
  params,
  url = "/manage/merchant/update/ip"
) => axios.post(`${prefix}${url}`, params, { type: 6 });

//商户中心-商户管理--虚拟开关状态更新
export const post_manage_merchant_update_openVrSport = (
  params,
  url = "/manage/merchant/update/openVrSport"
) => axios.post(`${prefix}${url}`, params);

//商户中心-商户管理--电子竞技开关状态更新
export const post_manage_merchant_update_openEsport = (
  params,
  url = "/manage/merchant/update/openESport"
) => axios.post(`${prefix}${url}`, params);

//商户中心-商户管理-视频开关状态更新
export const post_manage_merchant_update_openVsport = (
  params,
  url = "/manage/merchant/update/openVideo"
) => axios.post(`${prefix}${url}`, params);

//商户中心-商户管理-视频流量管控开关状态更新
export const post_vsgk_updateConfig = (
  params,
  url = "/manage/config/updateConfig"
) => axios.post(`${prefix}${url}`, params);


//商户中心-商户管理--预约投注状态更新
export const post_manage_merchant_update_bookBet = (
  params,
  url = "/manage/config/updateConfig"
) => axios.post(`${prefix}${url}`, params);

//商户中心-商户管理--预约投注状态更新
export const post_manage_merchant_update_chatSwitch = (
  params,
  url = "/merchantChatRoomSwitch/updateMerchantChatSwitch"
) => axios.post(`${prefix}${url}`, params);


//商户中心-商户管理--提前结算状态更新
export const post_manage_merchant_update_opensettleSwitchAdvance = (
  params,
  url = "/manage/merchant/update/settleSwitchAdvance"
) => axios.post(`${prefix}${url}`, params);

//商户中心-商户管理--注单历史
export const post_manage_merchant_update_betHistory = (
  params,
  url = "/manage/merchant/update/settleSwitchAdvance"
) => axios.post(`${prefix}${url}`, params);

//商户中心-商户管理--【内外部商户  &  测试商户】 状态更新
export const post_manage_merchant_update_isTestOrExternal = (
  params,
  url = "/manage/merchant/update/isTestOrExternal"
) => axios.post(`${prefix}${url}`, params);

//商户中心-代理商管理--【生成账单】 状态更新
export const post_manage_merchant_updateOpenBill = (
  params,
  url = "/manage/merchant/updateOpenBill"
) => axios.post(`${prefix}${url}`, params);



//商户中心-商户管理--【App商户】 状态更新
export const post_manage_merchant_update_isApp = (
  params,
  url = "/manage/merchant/update/isApp"
) => axios.post(`${prefix}${url}`, params);

//查询c端设定
export const get_cplate_config = (
  params,
  url = "/manage/config/getConfig"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//保存c端设定
export const post_cplate_updateConfig = (
  params,
  url = "/manage/config/updateConfig"
) => axios.post(`${prefix}${url}`, params);
//c端设定-更新logo地址
export const post_cplate_uploadImgUrl = (
  params,
  url = "/manage/config/uploadImgUrl"
) => axios.post(`${prefix}${url}`, params);




/**
     * merchantId = null && creditId = null    查询模板配置
     * merchantId != null && creditId = null   查询商户配置
     * merchantId != null && creditId != null  查询信用代理配置
     */



// http://api.sportxxxifbdxm2.com/yewu8/order/merchant/queryCreditLimitConfig?merchantId=1&creditId=1&app-id=10008&user-id=10019

export const post_merchant_queryCreditLimitConfig = (
  params,
  url = "/order/merchant/queryCreditLimitConfig"
) => axios.post(`${prefix3}${url}?merchantId=${params.merchantId}${params.creditId?'&creditId='+params.creditId:'' }`,  params,{type:10});



// http://api.sportxxxifbdxm2.com/yewu8/order/merchant/queryCreditLimitConfig?merchantId=1&creditId=1&app-id=10008&user-id=10019
export const post_merchant_saveOrUpdateCreditLimitConfig = (
  params,
  url = "/order/merchant/saveOrUpdateCreditLimitConfig"
) => axios.post(`${prefix3}${url}`,  params);

// http://api.sportxxxifbdxm2.com/yewu8/order/merchant/queryMechantAgentList?app-id=10008&user-id=10019&merchantCode=oubao

export const post_merchant_queryMechantAgentList = (
  params,
  url = "/order/merchant/queryMechantAgentList"
) => axios.post(`${prefix3}${url}?merchantCode=${params.merchantCode}`,  params,{type:10});


export const post_userSeriesAvailableLimit = (//c串关可用额度查询
  params,
  url = "/order/merchant/userSeriesAvailableLimit"
) => axios.post(`${prefix3}${url}`, params);

export const post_userSingleAvailableLimit = (//单关可用额度查询
  params,
  url = "/order/merchant/userSingleAvailableLimit"
) => axios.post(`${prefix3}${url}`, params);


export const get_getSportIdByMatchManageId = (// 根据赛事获取赛种
  params,
  url = "/order/merchant/getSportIdByMatchManageId"
) => axios.get(`${prefix3}${url}`, { params: { ...params } });

export const post_queryUserSeriesLimit = (//补齐风控维度 串关可用额度查询
  params,
  url = "/order/merchant/queryUserSeriesLimit"
) => axios.post(`${prefix3}${url}`, params);

export const post_userSingleLimitQuery  = (//补齐风控维度 单关可用额度查询
  params,
  url = "/order/merchant/userSingleLimitQuery"
) => axios.post(`${prefix3}${url}`, params);



// 商户中心-平台用户风控-查询用户风控分页列表
export const post_queryPageUserRisk_ControlList = (
  params,
  url = "/admin/userRiskControl/queryPageUserRiskControlList"
) => axios.post(`${prefix2}${url}`,params );

// 修改用户风控状态
export const post_updateUserRiskControlStatus = (
  params,
  url = "/admin/userRiskControl/updateUserRiskControlStatus"
) => axios.post(`${prefix2}${url}`, params );
// -商户管控记录表批量导入
export const post_importUserRiskControlList = (
  params,
  url = "/admin/userRiskControl/importUserRiskControlList"
) => axios.post(`${prefix2}${url}`, params,{type:3} );
// 商户管控记录表导出

// 数据中心(对内)-异常用户名单 查询
export const post_userabnormal_list = (
  params,
  url = "/order/abnormal/queryAbnormalList"
) => axios.post(`${prefix3}${url}`, params);

// 商户中心-视频控制管理-保存接口
export const save_Merchant_VideoManage = (
  params,
  url = "/merchantVideoManage/batchUpdateMerchantVideoManage"
) => axios.post(`${prefix}${url}`, params);

// 商户中心-视频控制管理-查询接口
export const get_Merchant_VideoManage = (
  params,
  url = "/merchantVideoManage/getMerchantVideoManageList"
) => axios.post(`${prefix}${url}`, params);

// 商户中心-视频控制管理-查询接口
export const get_Merchant_querySystemSwitch = (
  params,
  url = "/systemSwitch/querySystemSwitch"
) => axios.post(`${prefix}${url}`, params);
// 商户中心-视频控制管理-系统级别开关修改接口
export const get_Merchant_updateSystemSwitch = (
  params,
  url = "/systemSwitch/updateSystemSwitch"
  ) => axios.post(`${prefix}${url}`, params);

// 商户中心-视频控制管理-查询单个接口
export const get_One_Merchant_VideoManage = (
  params,
  url = "/merchantVideoManage/getMerchantVideoManage"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

// 商户中心-聊天室开关-查询聊天室开关配置接口
export const get_Merchant_chatRoomSwitch = (
  params,
  url = "/merchantChatRoomSwitch/getChatRoomSwitch"
  ) => axios.post(`${prefix}${url}`, params);

// 商户中心-聊天室开关-修改聊天室开关配置接口
export const get_Merchant_updateChatRoomSwitch = (
  params,
  url = "/merchantChatRoomSwitch/updateChatRoomSwitch"
  ) => axios.post(`${prefix}${url}`, params);


// 商户中心-查询预约盘口接口：
export const post_manage_config_queryMarketSwitch = (
  params,
  url = "/manage/config/queryMarketSwitch"
  ) => axios.post(`${prefix}${url}`, params);


// 商户中心-编辑预约盘口开关接口
export const post_manage_config_updateMarketSwitch = (
  params,
  url = "/manage/config/updateMarketSwitch"
  ) => axios.post(`${prefix}${url}`, params);



// 商户中心-视频控制管理-修改保存接口
export const update_Merchant_VideoManage = (
  params,
  url = "/merchantVideoManage/updateMerchantVideoManage"
) => axios.post(`${prefix}${url}`, params);

// 商户中心-c端综合设置 查询体育规则定义开关
export const post_switchConfig_getTiYuGuiZe = (
  params,
  url = "/manage/switchConfig/getMerchantSwitch"
) => axios.post(`${prefix}${url}`, params);

//  商户中心-c端综合设置 修改体育规则定义开关
export const post_TiYuGuiZe_update_switchConfig = (
  params,
  url = "/manage/switchConfig/setMerchantSwitch"
) => axios.post(`${prefix}${url}`, params);

//  商户中心-c端综合设置 获取体育规则下拉框数据
export const get_TiYuGuiZe_account_password = (
  params,
  url = "/ty/rule/getRule"
) => axios.get(`${prefix}${url}`, { params: { ...params } });



//  商户中心-c端综合设置 获取体育规则是否有账号密码
export const get_create_or_edit_getUser = (
  params,
  url = "/ty/rule/getUser"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//  商户中心-c端综合设置 体育规则定义账号密码保存接口  新建
export const post_TiYuGuiZe_create_account_password = (
  params,
  url = "/ty/rule/create/user"
) => axios.post(`${prefix}${url}`, params);

//  商户中心-c端综合设置 体育规则定义账号密码保存接口  修改
export const post_TiYuGuiZe_update_account_password = (
  params,
  url = "/ty/rule/update/user"
) => axios.post(`${prefix}${url}`, params);

// 数据中心(对内)-特殊限额名单用户列表 查询
export const post_queryAbnormalUserList = (
  params,
  url = "/order/abnormal/queryAbnormalUserList"
) => axios.post(`${prefix3}${url}`, params);

// 数据中心(对内)-特殊限额名单下拉列表 查询
export const post_queryUserComboList = (
  params,
  url = "/order/abnormal/queryUserComboList"
) => axios.post(`${prefix3}${url}`, params);



// 账户中心-百家赔分布 查询
export const post_manage_baijia_display = (
  params,
  url = "/manage/BaiJia/display"
) => axios.post(`${prefix}${url}`, params);

// 商户中心—商户组域名查询——TY域名池/DJ域名池/CP域名池
export const get_MerchantGroupDomain = (
  params,
  url = "/manage/merchant/getMerchantGroupDomain"
) => axios.post(`${prefix}${url}`, params);

// 商户中心—商户组域名查询——动画域名
export const get_queryAnimation = (
  params,
  url = "/manage/merchant/queryAnimation"
) => axios.post(`${prefix}${url}`, params);


// 商户中心-编辑  精彩回放 保存接口
export const post_manage_merchant_updateEvent = (
  params,
  url = "/manage/merchant/updateEvent"
  ) => axios.post(`${prefix}${url}`, params);

// 商户中心-精彩回放 开关接口
export const post_merchant_update_updateMerchantEventSwitch = (
  params,
  url = "/manage/merchant/update/updateMerchantEventSwitch"
  ) => axios.post(`${prefix}${url}`, params);
// 商户中心-精彩回放 点击编辑获取接口
export const post_merchant_getMerchantEventSwitch = (
  params,
  url = "/manage/merchant/getMerchantEventSwitch"
  ) => axios.post(`${prefix}${url}`, params);

// 商户中心-新增证书弹窗保存接口
export const post_add_merchant_certificate = (
  params,
  url = "/manage/merchant/addMerchantKey"
  ) => axios.post(`${prefix}${url}`, params);

// 商户中心-商户密钥启用/禁用接口
export const post_add_merchant_enableMerchantKey = (
  params,
  url = "/manage/merchant/enableMerchantKey"
) => axios.post(`${prefix}${url}`, params);

// 商户中心- 获取客户端-PC列表附加玩法/PC附加盘展示
export const post_getMerchantPcAdditionPlaySwitch = (
  params,
  url = "/manage/merchant/getMerchantPcAdditionPlaySwitch"
) => axios.post(`${prefix}${url}`, params);

// 商户中心- 修改客户端-PC列表附加玩法/PC附加盘展示 状态
export const post_pcAdditionMarketSwitch = (
  params,
  url = "/manage/merchant/update/pcAdditionMarketSwitch"
) => axios.post(`${prefix}${url}`, params);
// 商户中心- 电子体育启用/禁用接口
export const post_add_merchant_update_switchConfig = (
  params,
  url = "/switchConfig/update/switchConfig"
) => axios.post(`${prefix}${url}`, params);

// 商户中心-c端综合设置 查询客户端优惠盘口类型状态
export const post_switchConfig_getYouhuiPankou = (
  params,
  url = "/manage/switchConfig/getMerchantSwitch"
) => axios.post(`${prefix}${url}`, params);

// 商户中心- 客户端优惠盘口更新
export const post_add_merchant_pankou_update_switchConfig = (
  params,
  url = "/manage/switchConfig/setMerchantSwitch"
) => axios.post(`${prefix}${url}`, params);

// 商户中心- 电子竞技启用/禁用接口
export const post_add_merchant_dz_update_switchConfig = (
  params,
  url = "/manage/switchConfig/setMerchantSwitch"
) => axios.post(`${prefix}${url}`, params);

// 商户中心- 注单历史启用/禁用接口
export const post_add_merchant_update_betHistory_switchConfig = (
  params,
  url = "/switchConfig/update/switchConfig"
) => axios.post(`${prefix}${url}`, params);

// 商户中心- 获取电子足球/电子篮球状态
export const post_switchConfig_getElectronicTyDetail = (
  params,
  url = "/switchConfig/getElectronicTyDetail"
) => axios.post(`${prefix}${url}`, params);
// 商户中心- 获取客户端-H5底部banner
export const post_H5BoomBannerSwitch = (
  params,
  url = "/switchConfig/update/switchConfig"
) => axios.post(`${prefix}${url}`, params);
// 商户中心—ip白名单管理-列表
export const get_merchant_white_ip_list = (
  params,
  url = "/white/ip/manage/list"
) => axios.post(`${prefix}${url}`, params);

// 商户中心—ip白名单管理-详情
export const get_merchant_white_ip_detail = (
  params,
  url = "/white/ip/manage/detail"
) => axios.post(`${prefix}${url}`, params);

// 商户中心—ip白名单管理-列表-导出
export const get_merchant_white_ip_list_export = (
  params,
  url = "/white/ip/manage/exportList"
) => axios.post(`${prefix}${url}`, params);


// 商户中心—ip白名单管理-详情-导出
export const get_merchant_white_ip_detail_export = (
    params,
    url = "/white/ip/manage/exportDetail"
  ) => axios.post(`${prefix}${url}`, params);
  



// 商户中心- 获取真人状态
export const post_switchConfig_getZrDetail = (
  params,
  url = "/switchConfig/getZrDetail"
) => axios.post(`${prefix}${url}`, params);

// //修改商户 主要用于 商户真人     ----- (商户中心.商户管理.商户真人设置) 保存
export const post_manage_merchant_updateConfigZrorCp = (
  params,
  url = "/update/switchConfig/switchConfig"
) => axios.post(`${prefix}${url}`, params);

// 商户中心- 获取彩票状态
export const post_switchConfig_getCpDetail = (
  params,
  url = "/switchConfig/getCpDetail"
) => axios.post(`${prefix}${url}`, params);








  // 商户中心- 获取注单历史状态
  export const post_switchConfig_getBetHistory = (
      params,
      url = "/switchConfig/getBetHistoryDetail"
    ) => axios.post(`${prefix}${url}`, params);
    //--------
  
  // 商户中心-c端综合设置 查询客户端显示赔率类型状态
  export const post_switchConfig_getOddsDetail = (
      params,
      url = "/manage/switchConfig/getMerchantSwitch"
    ) => axios.post(`${prefix}${url}`, params);
  
  // 商户中心- 获取二级商户额度状态
  export const post_switchConfig_getErjiDetail = (
      params,
      url = "/manage/switchConfig/getMerchantSwitch"
    ) => axios.post(`${prefix}${url}`, params);
  
  // 商户中心- 获取电子竞技状态
  export const post_switchConfig_getDzDetail = (
    params,
    url = "/manage/switchConfig/getMerchantSwitch"
  ) => axios.post(`${prefix}${url}`, params);
  // 商户中心-c端综合设置 查询时区接口
  export const post_switchConfig_getShiquTime = (
    params,
    url = "/manage/switchConfig/getMerchantSwitch"
    ) => axios.post(`${prefix}${url}`, params);
  
  //----------
  
  // 商户中心- 二级商户额度修改
  export const post_add_merchant_erji_update_switchConfig = (
      params,
      url = "/manage/switchConfig/setMerchantSwitch"
    ) => axios.post(`${prefix}${url}`, params);
    // 商户中心-c端综合设置 修改客户端显示赔率类型状态
    export const post_add_merchant_odds_update_switchConfig = (
      params,
      url = "/manage/switchConfig/setMerchantSwitch"
    ) => axios.post(`${prefix}${url}`, params);
    // 商户中心-c端综合设置 修改时区接口
    export const post_add_merchant_shiquTime_update_switchConfig = (
    params,
    url = "/manage/switchConfig/setMerchantSwitch"
    ) => axios.post(`${prefix}${url}`, params);
  


  //失效商户管理
  import * as invalid from "src/api/internal/module/merchant/module/invalid.js";

  export {invalid}

// 商户中心-在线用户列表
export const post_get_user_online_list = (
  params,
  url = "/manage/merchant/queryOnlineUserList"
) => axios.post(`${prefix}${url}`, params);

      // 商户中心-在线用户列表-折线图
export const post_get_user_online_day_max = (
  params,
  url = "/manage/merchant/getUserOnlineDayMax"
  ) => axios.post(`${prefix}${url}`, params);

// 商户中心-在线用户列表-踢出在线会员(单个、批量)	
export const post_kick_out_online_user = (
  params,
  url = "/manage/merchant/kickOutOnlineUser"
  ) => axios.post(`${prefix}${url}`, params);


  // 商户中心-在线用户列表-踢出在线会员(单个、批量)	日志
export const post_log_findLog= (
  params,
  url = "/manage/log/findLog"
  ) => axios.post(`${prefix}${url}`, params);



    // 商户中心-球种排序
export const get_sport_list_order_by = (
  params,
  url = "/order/merchantSport/getSportListOrderBy"
  ) =>  axios.post(`${prefix3}${url}`,params );


  // 商户中心-修改排序接口
export const change_order_no = (
  params,
  url = "/order/merchantSport/changeOrderNo"
  ) =>  axios.post(`${prefix3}${url}`,params );

// 商户中心-修改排序接口改成默认排序接口:
export const change_default_list_order_by = (
  params,
  url = "/order/merchantSport/changeDefaultListOrderBy"
  ) =>  axios.post(`${prefix3}${url}`,params );