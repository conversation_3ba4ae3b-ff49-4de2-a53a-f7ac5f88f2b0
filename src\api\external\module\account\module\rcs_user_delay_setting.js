/*
 * @Desc:
 * @Date: 2019-12-28 20:59:34
 * @Author:Nice
 * @FilePath: /src/api/external/module/account/index.js
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_17;


//获取特殊管控--详情设定---赛种列表查询
export const get_findSportList = (
  params,
  url = "/admin/specialLimit/findSportList"
) => {
  return axios.get(`${prefix}${url}${params}`)
}


//获取特殊管控--详情设定---赛种列表查询
export const get_findTournamentLevelSettingBySportId = (
  params,
  url = "/admin/specialLimit/findTournamentLevelSettingBySportId"
) => {
  return axios.get(`${prefix}${url}${params}`)
}

//获取特殊管控--详情设定---赛种列表保存
export const post_handleLevelList = (
  params,
  url = "/admin/specialLimit/handleLevelList"
) => {
  return axios.post(`${prefix}${url}`, params)
}

//获取特殊管控--详情设定---赛事列表查询
export const post_tournamentPageList = (
  params,
  url = "/admin/specialLimit/tournamentPageList"
) => {
  return axios.post(`${prefix}${url}`, params)
}

//获取特殊管控--详情设定---赛事列表查询
export const post_batchHandleLevelList = (
  params,
  url = "/admin/specialLimit/batchHandleLevelList"
) => {
  return axios.post(`${prefix}${url}`, params)
}
//获取特殊管控--详情设定---赛事列表保存
export const post_handleTournamentList = (
  params,
  url = "/admin/specialLimit/handleTournamentList"
) => {
  return axios.post(`${prefix}${url}`, params)
}
