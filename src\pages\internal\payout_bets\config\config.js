/*
 * @FilePath: /src/pages/internal/data/front_end_domain_query/config/config.js
 * @Description: 数据中心/前端域名查询
 */

import { i18n } from "src/boot/i18n";
// 前端域名查询表格配置
export const tablecolumns_config = [
  {
    title: i18n.t("internal.second_settlement_initial_review.text__10"), // 序号
    key: "_index",
    width: 50,
    align: "center",
    dataIndex: "_index",
  },
  {
    title: i18n.t("internal.second_settlement_initial_review.text__19"), // 商户编码
    key: "merchantCode",
    align: "center",
    dataIndex: "merchantCode",
    scopedSlots: {
      customRender: "merchantCode",
    },
  },
  {
    title: i18n.t("internal.second_settlement_initial_review.text__20"), // 商户名
    key: "merchantName",
    align: "center",
    dataIndex: "merchantName",
    scopedSlots: {
      customRender: "merchantName",
    },
  },
  {
    title: i18n.t("internal.second_settlement_initial_review.text__21"), // 二次结算原因
    key: "remark",
    align: "center",
    dataIndex: "remark",
    scopedSlots: {
      customRender: "remark",
    },
  },
  {
    title: i18n.t("internal.second_settlement_initial_review.text__34"), // 审批日期
    key: "applicationTime",
    align: "center",
    dataIndex: "applicationTime",
    scopedSlots: {
      customRender: "applicationTime",
    },
  },
  {
    title: i18n.t("internal.second_settlement_initial_review.text__5"), // 赔偿金鸽
    key: "changeTotalAmount",
    align: "center",
    dataIndex: "changeTotalAmount",
    scopedSlots: {
      customRender: "changeTotalAmount",
    },
  },
  {
    title: i18n.t("internal.second_settlement_initial_review.text__11"), // 操作
    key: "action",
    align: "center",
    dataIndex: "action",
    scopedSlots: {
      customRender: "action",
    },
  },
];