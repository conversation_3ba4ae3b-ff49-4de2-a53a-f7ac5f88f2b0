<!--
 * @FilePath:
 * @Description: 二次结算查询-提交赔付和提交审批弹框
-->
<template>
  <div style="width: 400px; height: auto; overflow: hidden;  min-width:400px; " class="text-panda-text-7 bg-white">
    <div class="bg-white text-black">
      <q-card-section class="no-padding">
        <div class="row line-height-40px fs14 text-panda-text-7 ">
        
          <q-space />
          <q-btn class="mr5x text-panda-dialog-close" icon="close" v-close-popup />
        </div>
      </q-card-section>
      <!-- <q-separator></q-separator> -->
      <div>
        <div v-if="detailObj.type==1">
          <div v-if="list_one.length!=0">
            <div style="display: flex; justify-content: center;" class=" text-weight-bolder">
              <div style="display: flex; align-items: center;">
                <a-icon
                type="exclamation-circle"
                style="color: orange; cursor: pointer; width: 30px; font-size: 20px"
              />
              {{ $t("internal.second_settlement_initial_review.text__28")}}
              </div>
            </div>
            <div class="col-10 q-pl-md  mt10x">
              <div  class="row">
                <span class="col-6 text-center">  {{ $t("internal.second_settlement_initial_review.text__3")}}</span>
                <span class="col-6 text-center"> {{$t("internal.second_settlement_initial_review.text__5")}}</span>
              </div>
              <div v-for="(item) in list_one" class="row">
                <span class="col-6 text-center">   {{moment(item.applicationTime).format('YYYY/MM/DD')}}</span>
                <span class="col-6 text-center">  {{addCommas(item.changeTotalAmount)}}</span>
               
              </div>
            </div>
          </div>
          <div v-if="list_two.length!=0">
            <div class="col-10 q-pl-md text-weight-bolder  mt10x text-center">
              <a-icon
                type="exclamation-circle"
                style="color: orange; cursor: pointer; width: 30px; font-size: 20px"
              />
              
        {{ $t("internal.second_settlement_initial_review.text__29")}}
            </div>
            <div class="col-10 q-pl-md  mt10x">
              <div  class="row">
                <span class="col-6 text-center">  {{ $t("internal.second_settlement_initial_review.text__3")}}</span>
                <span class="col-6 text-center"> {{$t("internal.second_settlement_initial_review.text__5")}}</span>
              </div>
              <div v-for="(item) in list_two" class="row">
                <span class="col-6 text-center">   {{moment(item.applicationTime).format('YYYY/MM/DD')}}</span>
                <span class="col-6 text-center">  {{addCommas(item.changeTotalAmount)}}</span>
               
              </div>
            </div>
            </div>
        </div>
        <div
        v-else
        >
        <div class="col-10 q-pl-md text-weight-bolder  mt10x text-center">
          <a-icon
          type="exclamation-circle"
          style="color: orange; cursor: pointer; width: 30px; font-size: 20px"
        />
        
        {{ $t("internal.second_settlement_initial_review.text__27")}}
        </div>
        <div class="col-10 q-pl-md  mt10x">
          <div  class="row">
            <span class="col-6 text-center">  {{ $t("internal.second_settlement_initial_review.text__3")}}</span>
            <span class="col-6 text-center"> {{$t("internal.second_settlement_initial_review.text__5")}}</span>
          </div>
          <div v-for="(item) in list" class="row">
            <span class="col-6 text-center">   {{moment(item.applicationTime).format('YYYY/MM/DD')}}</span>
            <span class="col-6 text-center">  {{addCommas(item.changeTotalAmount)}}</span>
           
          </div>
        </div>
      </div>
        
        <q-card-actions>
          <div class="row justify-center q-gutter-x-md full-width mt20x mb10x">
            <!--取消-->
            <q-btn 
            class="panda-btn-primary-dense bg-primary mr5x"
            style="width: 68px; height: 30px"
            :label="$t('internal.cancel')" color="primary" v-close-popup />
            <!--确定-->
            <q-btn 
            class="panda-btn-primary-dense bg-primary mr5x"
            style="width: 68px; height: 30px"
            :label="$t('internal.sure')"  color="primary" @click="submit_handle"/>
          </div>
        </q-card-actions>
      </div>
      </div>
  </div>
</template>


<script>
import { i18n } from "src/boot/i18n";
import moment from "moment";
import { handleCopy } from "src/util/module/common.js";
import { api_finance } from "src/api/index.js";
import { mapGetters, mapActions } from "vuex";
export default {
  name: "reason_for_rejection_dialog",

  props: {
    detailObj: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      // 获取审核值
      audit_value:{},
      list:[],
      list_one:[],
      list_two:[],
    };
  },
  created() {
    this.list=this.detailObj.list
    this.get_audit_value()
  },
  mounted() {
  },
  methods: {
    ...mapActions(['set_compensation_record_count']),
    moment,
    // 添加千位符
    addCommas(num) {
      return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    // 获取审核值
    get_audit_value(){
      api_finance.
      settlement_inquiry.get_getSystemParamConfig()
        .then(res => {
          let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          if (code == "0000000") {
         let data =  this.$lodash.get(res, "data.data")
         if(data){
          this.audit_value=data.systemParams
        //提交二初次次审核
        this.second_review_logic()
         }
          } else {
            this.$message.error(msg, 5);
          }
        });
    },
    /*
    * 二次审核逻辑
    */
    second_review_logic(){
       if(this.audit_value.secSettSecondAuditAmt!=''&&this.audit_value.secSettSecondAuditFlag=='1'){
        // 设置了二次审核并开启
        this.list.forEach(item => {
          if(Math.abs(parseFloat(item.changeTotalAmount))<=parseFloat(this.audit_value.secSettSecondAuditAmt)){
            this.list_one.push(item)
          }else{
            this.list_two.push(item)
          }
        });
      }else{
        //初次和二次都没有设置，直接走赔付
        this.list_one=this.list
      }

    },
    /*
    * 提交
    */
    submit_handle(){
      if(this.list.length==0){
        this.$message.error(i18n.t("internal.finance.secondarysettlement.label30"), 5);
        return
      }
      if(this.detailObj.type==1){
        //初次审核界面
        if(this.list_one.length!=0){
          let params_1={}
        //提交赔付
        params_1.pageSource=this.detailObj.type
        params_1.settleTimes=[]
        this.list_one.forEach(item => {
      let settleTime=moment(item.settleTime).format("YYYYMMDD") 
      params_1.settleTimes.push(settleTime)
        });
        this.submit_data(params_1)
      }
      if(this.list_two.length!=0){
        let params_2={}
        //提交二次审核
        params_2.pageSource=this.detailObj.type
        params_2.settleTimes=[]
        this.list_two.forEach(item => {
      let settleTime=moment(item.settleTime).format("YYYYMMDD") 
      params_2.settleTimes.push(settleTime)
        });
        this.submit_data(params_2)

      
      }
      }else{
        let params={}
        //审核界面-提交通过
        params.pageSource=this.detailObj.type
        params.settleTimes=[]
        this.list.forEach(item => {
      let settleTime=moment(item.settleTime).format("YYYYMMDD") 
      params.settleTimes.push(settleTime)
        });
        this.submit_data(params)
      }
    },
    
    /*
    * 最终提交
    */
    submit_data(params){
      
      params.settleTimes = this.$lodash.sortedUniq(params.settleTimes);
      api_finance.
      settlement_inquiry.submitSettleAudit(params)
        .then(res => {
          let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          if (code == "0000000") {
            this.$message.success( msg);
            this.set_compensation_record_count('refresh')
            this.$emit('close_dialog',true)
          } else {
            this.$message.error(msg, 5);
          }
        });
    },
  },
};
</script>
<style>
</style>