/*
 * @Description: 账户中心-异常用户名单(对外) 查询异常 配置
 */
import { i18n } from "src/boot/i18n";
let table_title = i18n.t('external.merchant.bettinguser.config')
export const dialog_find_abnormal_config = ({src_internal})=> [
    {//序号
      title:i18n.t("internal.user_abnormal_list.label14"),
      width: 80,
      dataIndex: "_index",
      key: "_index",
      fixed: "left",
      align: "center"
    },
  {//用户ID
    title: i18n.t("internal.user_abnormal_list.label7"),
    width: 80,
    dataIndex: "userId",
    key: "userId",
    align: "center",
    scopedSlots: { customRender: "userId" },
  },
  {//用户名
    title: i18n.t("internal.user_abnormal_list.label8"),
    dataIndex: "userName",
    key: "userName",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "userName" },
  },
  src_internal ?
  {// 商户
    title: i18n.t("internal.user_abnormal_list.label3"),
    dataIndex: "merchantCode",
    key: "merchantCode",
    width: 130,
    align: "center",
    scopedSlots: { customRender: "merchantCode" },
  }:"",
  {// 操作时间
    title: i18n.t("internal.user_abnormal_list.label9"),
    width: 90,
    dataIndex: "operateTime",
    key: "operateTime",
    align: "center",
    scopedSlots: { customRender: "operateTime" },
  },
  {// 异常类型
    title: i18n.t("internal.user_abnormal_list.label4"),
    dataIndex: "riskType",
    key: "riskType",
    width: 100,
    align: "center",
  },
  !src_internal ? {//体育赔率分组
   title: i18n.t('external.merchant.bettinguser.config.23'), 
   dataIndex: "marketLevel",
   key: "marketLevel",
   width: 180,
   align: "center",
   scopedSlots: { customRender: "marketLevel" },
 }:"",
 !src_internal ?{// 电竞赔率分组
  title: i18n.t('external.merchant.bettinguser.config.24'), 
   dataIndex: "esMarketLevel",
   key: "esMarketLevel",
   width: 180,
   align: "center",
   scopedSlots: { customRender: "esMarketLevel" },
 }:"",
    !src_internal ?{// 投注延时
     title: i18n.t("internal.user_abnormal_list.label730_25"),
     dataIndex: "specialBettingLimitDelayTime",
     key: "specialBettingLimitDelayTime",
     width: 100,
     align: "center",
     scopedSlots: { customRender: "specialBettingLimitDelayTime" },
    }:"",
  {// 备注
      title: i18n.t("internal.user_abnormal_list.label730_9"),
      dataIndex: "remark",
      key: "remark",
      width: 100,
      align: "center",
      customRender: (text, row, index) => {
        return text || '-'
        },
   },
];

