/*
 * <AUTHOR> Rank
 * @Date           : 2021-05-18 10:24:12
 * @Description    : 运营位接口
 * @FilePath       : /merchant-manage/src/api/internal/module/operate/module/operation_site.js
 */
import axios from "src/api/common/axioswarpper.js";
let prefix_2 = process.env.API_PREFIX_4;

export const bannerSetSave = (
    //【后台参数】保存banner
    data,
    url = "/manage/banner/saveBanner"
  ) => axios.post(`${prefix_2}${url}`, data);

  export const bannerUploadImg = (
    //【后台参数】保存banner
    data,
    url = "/manage/banner/uploadImg"
  ) => axios.post(`${prefix_2}${url}`, data,{type:3})

  export const getBanners = (
    //【后台参数】保存banner
    data,
    url = "/manage/banner/getBanners"
  ) => axios.post(`${prefix_2}${url}`, data);

  export const deleteBanner = (
   // 【后台参数】删除banner
    data,
    url = "/manage/banner/deleteBanner"
  ) => axios.post(`${prefix_2}${url}`, data);
  export const updateBanner = (
    //修改banner
    data,
    url = "/manage/banner/updateBanner"
  ) => axios.post(`${prefix_2}${url}`, data);

