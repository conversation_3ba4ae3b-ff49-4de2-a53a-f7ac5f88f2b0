/*
 * @FilePath: /src/components/common/bet_slip/config/zr_config.js
 * @Description: 注单查询-真人-表格配置
 */
import { i18n } from "src/boot/i18n";

export const zr_tablecolumns_config = (src_internal, src_external, userInfo) => [
  {
    title: i18n.t("internal.table_title.label1"), // 序号
    width: 60,
    dataIndex: "_index",
    key: "_index",
    align: "center",
  },
  {
    title: i18n.t("internal.table_title.yh45"), // 会员ID
    width: 180,
    dataIndex: "uid",
    key: "uid",
    align: "left",
    scopedSlots: { customRender: "uid" },
  },
  // {
  //   title: i18n.t("internal.table_title.yh46"), // 会员昵称
  //   width: 180,
  //   dataIndex: "nickName",
  //   key: "nickName",
  //   align: "left",
  //   scopedSlots: { customRender: "nickName" },
  // },
  {
    title: i18n.t("internal.content_manage.t18"), // 会员账号
    width: 240,
    dataIndex: "userName",
    key: "userName",
    align: "left",
    scopedSlots: { customRender: "userName" },
  },
  src_internal || (src_external && [1, 10].includes(userInfo.agentLevel))
    ? {
        title: i18n.t("internal.table_title.label72"), // 商户编号
        width: 100,
        dataIndex: "merchantCode",
        key: "merchantCode",
        align: "left",
        scopedSlots: { customRender: "merchantCode" },
      }
    : {},
  {
    title: i18n.t("internal.table_title.yh47"), // 游戏名称
    width: 120,
    dataIndex: "gameTypeName",
    key: "gameTypeName",
    align: "left",
    scopedSlots: { customRender: "gameTypeName" },
  },
  {
    title: i18n.t("internal.table_title.label54"), // 注单编号
    width: 180,
    dataIndex: "id",
    key: "id",
    align: "left",
    scopedSlots: { customRender: "id" },
  },
  {
    title: i18n.t("internal.table_title.label23"), // 币种
    dataIndex: "currency",
    key: "currency",
    width: 90,
    align: "center",
    scopedSlots: { customRender: "currency" },
  },
  {
    title: i18n.t("internal.table_title.label8"), // 投注总金额 / 投注额
    dataIndex: "betAmount",
    key: "betAmount",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "betAmount" },
  },
  {
    title: i18n.t("internal.table_title.label05"), // 有效投注额
    dataIndex: "validBetAmount",
    key: "validBetAmount",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "validBetAmount" },
  },
  {
    title: i18n.t("internal.table_title.yh36"), // 输赢额
    dataIndex: "netAmount",
    key: "netAmount",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "netAmount" },
  },
  // {
  //   title: i18n.t("internal.table_title.yh37"), // 下注前余额
  //   dataIndex: "beforeAmount",
  //   key: "beforeAmount",
  //   width: 100,
  //   align: "center",
  //   scopedSlots: { customRender: "beforeAmount" },
  // },
  // {
  //   title: i18n.t("internal.table_title.yh29"), //  中奖金额 / 返奖金额
  //   dataIndex: "payAmount",
  //   key: "payAmount",
  //   width: 100,
  //   align: "center",
  //   scopedSlots: { customRender: "payAmount" },
  // },
  {
    title: i18n.t("internal.table_title.yh44"), //  厅名称
    dataIndex: "platformName",
    key: "platformName",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "platformName" },
  },
  // {
  //   title: i18n.t("internal.user_betting_amount.label12"), //  玩法，下注点
  //   dataIndex: "betPointId",
  //   key: "betPointId",
  //   width: 100,
  //   align: "center",
  //   scopedSlots: { customRender: "betPointId" },
  // },
  {
    title: i18n.t("internal.table_title.yh18"), //  玩法名称
    dataIndex: "betPointName",
    key: "betPointName",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "betPointName" },
  },
  {
    title: i18n.t("internal.table_title.yh39"), //  台桌号
    dataIndex: "tableCode",
    key: "tableCode",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "tableCode" },
  },
  {
    title: i18n.t("internal.table_title.yh40"), //  局号
    dataIndex: "roundNo",
    key: "roundNo",
    width: 140,
    align: "center",
    scopedSlots: { customRender: "roundNo" },
  },
  // {
  //   title: i18n.t("internal.table_title.yh48"), //  游戏模式 0=常规 1=好路 3=多台
  //   dataIndex: "gameMode",
  //   key: "gameMode",
  //   width: 100,
  //   align: "center",
  //   scopedSlots: { customRender: "gameMode" },
  // },

  {
    title: i18n.t("internal.table_title.yh52"), //  结果
    dataIndex: "judgeResult",
    key: "judgeResult",
    width: 150,
    align: "center",
    scopedSlots: { customRender: "judgeResult" },
  },
  {
    title: i18n.t("internal.basic.s3"), //  投注时间
    dataIndex: "createTime",
    key: "createTime",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "createTime" },
  },
  {
    title:i18n.t("internal.basic.s4"), //  结算时间
    dataIndex: "netAt",
    key: "netAt",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "netAt" },
  },
  {
    title: i18n.t("internal.table_title.label46"), //  最后更新时间
    dataIndex: "updatedAt",
    key: "updatedAt",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "updatedAt" },
  },

  // {
  //   title: i18n.t("internal.table_title.yh23"), // 终端
  //   dataIndex: "deviceType",
  //   key: "deviceType",
  //   width: 100,
  //   align: "center",
  //   scopedSlots: { customRender: "deviceType" },
  // },
  {
    title: i18n.t("internal.state"), // 状态
    dataIndex: "betStatus",
    key: "betStatus",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "betStatus" },
  },
  // {
  //   title: i18n.t("internal.match_live.t10"), // 操作
  //   key: "operate",
  //   width: 150,
  //   align: "center",
  //   dataIndex: "operate",
  //   scopedSlots: {
  //     customRender: "operate",
  //   },
  // },
];

// 真人- 设备类型   1=网页，2=手机网页，3=Ios，4=Android，5=其他设备
export const zr_device_type = [
  {
    value: 1,
    label: i18n.t("internal.table_title.yh41"), // 网页
  },
  {
    value: 2,
    label: i18n.t("internal.table_title.yh42"), // 手机网页
  },
  {
    value: 3,
    label: "IOS", // IOS
  },
  {
    value: 4,
    label: "Android", // Android
  },
  {
    value: 5,
    label: i18n.t("internal.table_title.yh43"), // 其他设备
  },
];

// 真人- 设备类型映射
export const zr_device_type_map = zr_device_type.reduce(
  (o, v) => ((o[v.value] = v.label), o),
  {}
);

// 真人-注单状态
export const zr_betStatus_list = [
  {
    value: "",
    label: i18n.t("internal.table_title.label104"), // 全部
  },
  {
    value: 0,
    label: i18n.t("internal.table_title.yh38"), // 未结算
  },
  {
    value: 1,
    label: i18n.t("internal.table_title.yh35"), // 已结算
  },
];
// 真人-注单状态映射
export const zr_betStatus_map = zr_betStatus_list.reduce((prev, cur) => {
  prev[cur.value] = cur.label;
  return prev;
}, {});

// 真人-币种
export const zr_currency_list = [
  { label: i18n.t("internal.common.label8"), value: "" },
  { key: 1, value: "CNY", label: "人民币" },
  { key: 2, value: "KRW", label: "韩元" },
  { key: 3, value: "MYR", label: "马来西亚币" },
  { key: 4, value: "USD", label: "美元" },
  { key: 5, value: "JPY", label: "日元" },
  { key: 6, value: "THB", label: "泰铢" },
  { key: 7, value: "BTC", label: "比特币" },
  { key: 8, value: "VND", label: "越南盾" },
  { key: 9, value: "EUR", label: "欧元" },
  { key: 10, value: "HKD", label: "港币" },
  { key: 11, value: "AUD", label: "澳元" },
  { key: 12, value: "TWD", label: "台币" },
  { key: 13, value: "IDR", label: "印尼盾" },
  { key: 14, value: "INR", label: "印度卢比" },
  { key: 15, value: "BND", label: "文莱币" },
  { key: 16, value: "USDT", label: "泰达币" },
  { key: 17, value: "VNDONETH", label: "越南盾一千" },
  { key: 18, value: "IDRONETH", label: "印尼盾一千" },
  { key: 19, value: "ZMW", label: "赞比亚克瓦查" },
  { key: 20, value: "KES", label: "肯尼亚先令" },
  { key: 21, value: "BRL", label: "巴西雷亚尔" },
];

// 真人-币种映射
export const zr_currency_map = zr_currency_list.reduce(
  (o, v) => ((o[v.value] = v.label), o),
  {}
);
