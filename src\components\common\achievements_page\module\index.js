/*
 * @Description: 战绩
 * @FilePath: 
 */

import { i18n } from 'src/boot/i18n';
export const  matchLevelList=i18n.t('internal.filters.matchLevelList')
export const  syndicate_results={
  //合买未成功
  2:i18n.t("internal.data.achievements.text1"),
  //合买成功
  3:i18n.t("internal.data.achievements.text2")
}
//展开条件
export const  expand_conditions={
  1: i18n.t("internal.syndicate.text14") ,
  2: i18n.t("internal.syndicate.text15") ,
  3: i18n.t("internal.syndicate.text16")
  
}
  export const  matchStatus=i18n.t('internal.filters.matchStatus')
  export const  purchaseStatus=i18n.t('internal.filters.purchaseStatus')

  