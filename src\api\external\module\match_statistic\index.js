/*
 * @FilePath: /src/api/external/module/match_statistic/index.js
 * @Description: 即时赛事统计报表
 */

import axios from "src/api/common/axioswarpper.js";

let prefix2 = process.env.API_PREFIX_17; // yewu17

// 即时赛事统计报表 (主列表-对外)
export const post_admin_match_queryMatchReportDateList = (
  params,
  url = "/admin/reportMatch/reportDateList"
) => axios.post(`${prefix2}${url}`, params);

// 即时赛事统计报表---下拉联赛
export const query_pull_down_tournament = (
  params,
  url = "/admin/reportMatch/tournamentDataList"
) => axios.post(`${prefix2}${url}`, params);

// 即时赛事统计报表---单个赛事多币种
export const post_admin_match_queryMatchStatisticById = (
  params,
  url = "/admin/reportMatch/currencyDataList"
) => axios.post(`${prefix2}${url}`, params);

// 即时赛事统计报表---玩法明细信息
export const post_admin_match_queryPlayStatisticList = (
  params,
  url = "/admin/reportMatch/marketBetDetailList"
) => axios.post(`${prefix2}${url}`, params);

// 即时赛事统计报表---多币种汇总查询
export const post_admin_currency_summary_query = (
  params,
  url = "/admin/reportMatch/currencySum"
) => axios.post(`${prefix2}${url}`, params);

// 即时赛事统计报表---导出报表---导出赛事统计
export const post_export_report_excel = (params, url = "/admin/reportMatch/matchBetReportDataExport") => {
  return axios.post(`${prefix2}${url}`, params, {
    responseType: "blob",
  });
}

// 即时赛事统计报表---导出报表---导出玩法统计
export const post_export_markets_report_excel = (params, url = "/admin/reportMatch/marketBetReportDataExport") => {
  return axios.post(`${prefix2}${url}`, params, {
    responseType: "blob",
  });
}

// 赛事盈亏信息
export const selectSettleDataList = (
  params,
  url = "/admin/match/replay/selectSettleDataList"
) => axios.post(`${prefix2}${url}`, params);

// 投注阶段信息
export const selectMatchTypeDataList = (
  params,
  url = "/admin/match/replay/selectMatchTypeDataList"
) => axios.post(`${prefix2}${url}`, params);

// 玩法类别信息
export const selectCategoryDataList = (
  params,
  url = "/admin/match/replay/selectCategoryDataList"
) => axios.post(`${prefix2}${url}`, params);

// 玩法列表信息
export const selectPlayDataList = (
  params,
  url = "/admin/match/replay/selectPlayDataList"
) => axios.post(`${prefix2}${url}`, params);

// 玩法列表信息
export const selectMarketDataList = (
  params,
  url = "/admin/match/replay/selectMarketDataList"
) => axios.post(`${prefix2}${url}`, params);

// 赔率图表接口入参
export const getMatchReviewReportList = (
  params,
  url = "/admin/match/replay/getMatchReviewReportList"
) => axios.post(`${prefix2}${url}`, params);

// 基准分统计数据接口
export const getMatchReviewScoreData = (
  params,
  url = "/admin/match/replay/getMatchReviewScoreData"
) => axios.post(`${prefix2}${url}`, params);

// 新赛事复盘-货量数据
export const selectLineDataList = (
  params,
  url = '/admin/match/replay/selectLineDataList'
)=> axios.post(`${prefix2}${url}`,params)

// 新赛事复盘-比分/关盘 markline
export const getMatchReviewScorePeriod = (
  params,
  url = '/admin/match/replay/getMatchReviewScorePeriod'
)=> axios.post(`${prefix2}${url}`,params)

// 新赛事复盘-事件轴
export const getMatchReviewEventList = (
  params,
  url = '/admin/match/replay/getMatchReviewEventList'
)=> axios.post(`${prefix2}${url}`,params)