<!--
 * @Desc: 运营管理/活动管理/活动列表 - 新增 && 编辑 && 详情 定时播放烟花活动弹窗
 * @Date: 2024-10-26 15:49:45
 * @Author:Nice
 * @FilePath: src/pages/internal/operations/activity_manage/component/dialog_create_activity.vue
 -->
 <template>
    <div
      style="width: 820px; height: auto; max-width: 820px;"
      class="text-panda-text-7 "
    >
      <q-card class="bg-white text-black">
        <q-card-section class="no-padding">
          <div
            class="
              row
              line-height-40px
              fs14
              bg-panda-dialog
              text-panda-text-7
              pr10x
            "
          >
            <div v-if="!is_view" class="pl20x fw_600">
              <!-- {{ is_edit? '编辑': '新增' }}-定时播放烟花活动 -->
               {{ is_edit? $t("internal.activity_manage.config2.t23") : $t("internal.activity_manage.config2.t24") }}-{{ $t("internal.activity_manage.config2.t3") }} 
            </div>
            <!-- 详情-定时播放烟花活动 -->
            <div v-else class="pl20x fw_600">
              {{ $t("internal.activity_manage.config2.t26") }}
            </div>
            <q-space></q-space>
            <q-btn
              flat
              dense
              icon="close"
              v-close-popup
              class="text-panda-dialog-close"
            />
          </div>
        </q-card-section>
        <q-separator></q-separator>
        <!-- 配置信息 -->
        <a-divider style="color: rgb(13, 18, 248);" class="pl20x pr20x pt10x pb10x"> {{ $t("internal.activity_manage.config2.t25") }}</a-divider>
        <q-card-section class="fs14 mt10x">
          <a-form
            id="components-form-bullentin-edit"
            class="edit pl20x"
            :form="form"
            @submit="handle_submit"
          >
            <!-- 活动标题 -->
            <div class="row">
              <a-form-item>
                <div class="row mr10x">
                    <div class="text-panda-text-7 fw_600">
                      {{ $t("internal.activity_manage.t2") }} 
                    <span class="panda-text-red">*</span>
                    </div>
                    <div class="ml10x">
                      <a-input
                        autocomplete="off"
                        :style="{ width: '260px' }"
                        :placeholder="$t('internal.common.qsr')"
                        size="large"
                        v-decorator="[
                            'activityTitle',
                            {
                            rules: [
                                {
                                required: true,
                                message: '请输入活动标题',
                                },
                                // {
                                // pattern: /^[_0-9A-Za-z]{1,20}$/,
                                // message: $t('internal.message.label145'),
                                // },
                            ],
                            },
                        ]"
                        :disabled="is_view"
                      />
        
                    </div>
                </div>
              </a-form-item>
            <!-- 活动类型 -->
            <!--{{ form.getFieldValue('activityType') }} -->
              <a-form-item class="ml20x">
                <div class="row ">
                    <div class="text-panda-text-7 fw_600">
                      {{ $t("internal.activity_manage.t3") }}
                    <span class="panda-text-red">*</span>
                    </div>
                    <div class="row ml10x">
                    <a-select
                      autocomplete
                      size="large"
                      v-decorator="[
                      'activityType',
                      {
                        rules: [
                          {
                            required: true,
                            message: '请选择活动类型',
                          },
                        ],
                      },
                      ]"
                      :disabled="is_view"
                    >
                        <a-select-option
                            :value="item.value"
                            v-for="(item, index) in activity_type_arr"
                            :key="index + '_type'"
                        >{{ item.label }}</a-select-option
                        >
                    </a-select>
                    </div>
                </div>
              </a-form-item>
            </div>
        <!-- 展示设备 -->
        <div class="row">
          <div class="col">
            <a-form-item >
              <div class="text-panda-text-7 row">
       
                <div class="text-panda-text-7 fw_600 mr20x">
                  {{ $t("internal.activity_manage.config.t3") }} 
                    <span class="panda-text-red">*</span>
                </div>
                <!-- 全选 -->
                <a-checkbox
                  :checked="isAllSelected"  
                  :disabled="is_view"
                  @change="onCheckAllChange"
                >
                {{ $t("internal.activity_manage.config2.t27") }} 
                </a-checkbox>
                <!-- {{ form.getFieldValue('deviceTypeList') }} -->
                <span class="ml40x" style="line-height: 41px; height: 41px">
                    <a-checkbox-group
                        v-decorator="[
                          'deviceTypeList',
                          {
                            rules: [
                              {
                              required: true,
                              message: '请选择展示设备',
                              }
                            ]
                          },
                        ]"
                        name="checkboxgroup"
                        :options="show_shebei_list"
                        :disabled="is_view"

                    />
                </span>
              </div>
            </a-form-item>
          </div>
        </div>
        <!-- 活动时间 -->
        <div class="row">
          <div class="col">
            <a-form-item >
              <div class="text-panda-text-7  row">
                <div class="text-panda-text-7 fw_600">
                  {{ $t("internal.activity_manage.t5") }}
                    <span class="panda-text-red">*</span>
                </div>
                <span class="ml20x">
                  <a-range-picker
                    v-decorator="['range-time-picker', rangeConfig]"
                    show-time
                    format="YYYY-MM-DD HH:mm:ss"
                    :disabled="is_view"

                  />
                </span>
              </div>
            </a-form-item>
          </div>
        </div>
        <!-- 播放方式 -->
         <!-- {{ form.getFieldValue('radio-group') }} -->
        <div class="row">
          <div class="col">
            <a-form-item >
              <div class="text-panda-text-7 mt10x row">
                <div class="text-panda-text-7 fw_600 mr20x">  
                {{ $t("internal.activity_manage.config2.t28") }} 

                    <span class="panda-text-red">*</span>
                </div>
                <span class="" style="line-height: 41px; height: 41px">
                  <a-radio-group 
                    name="radioGroup" 
                    v-decorator="['playType',
                      {
                        rules: [
                          {
                          required: true,
                          message: '请选择播放方式',
                          },
                      ],
                      }
                    ]" 
                    :disabled="is_view"
                  >
                  <!-- 仅一次   -->
                    <a-radio :value="0">
                      {{ $t("internal.activity_manage.config2.t14") }} 
                    </a-radio>
                    <!-- 连续播放三次 -->
                    <a-radio class="ml30x mr30x" :value="1">
                      {{ $t("internal.activity_manage.config2.t15") }} 
                    </a-radio>
                    <!-- 循环播放 -->
                    <a-radio :value="2">
                      {{ $t("internal.activity_manage.config2.t16") }} 

                    </a-radio>
                  </a-radio-group>
                </span>
              </div>
            </a-form-item>
          </div>
        </div>
        <!-- 烟花款式 -->
        <!-- 烟花款式{{ form.getFieldValue('fireworksType') }} -->
        <div class="row">
          <div class="col">
            <a-form-item >
              <div class="text-panda-text-7  row">
                <div class="text-panda-text-7 fw_600">
                  {{ $t("internal.activity_manage.config.t6") }} 
                    <span class="panda-text-red">*</span>
                </div>
                <span class="ml20x">
                  <a-select
                    autocomplete
                    size="large"
                    v-decorator="[
                      'fireworksType',
                      {
                        rules: [
                          {
                            required: true,
                            message: '请选择烟花款式',
                          },
                        ],
                      },
                    ]"
                    :disabled="is_view"
                  >
                    <a-select-option
                        :value="item.value"
                        v-for="(item, index) in yanhua_style_arr"
                        :key="index + '_type'"
                    >{{ item.label }}
                    </a-select-option>
                    </a-select>
                </span>
              </div>
            </a-form-item>
          </div>
        </div>
        <!-- 活动状态 {{ form.getFieldValue('activityStatus') }} -->
        <div class="row">
          <div class="col">
            <a-form-item >
              <div class="text-panda-text-7  row">
                <div class="text-panda-text-7 fw_600">
                
                  {{ $t("internal.activity_manage.config.t7") }} 
                    <span class="panda-text-red">*</span>
                </div>
                <span class="ml20x">
                  <a-switch 
                    :checked-children="$t('internal.template.label177')" 
                    :un-checked-children="$t('internal.template.label171')"
                    v-decorator="['activityStatus', { valuePropName: 'checked' }]" 
                    :disabled="is_view"
                  />
                </span>
              </div>
            </a-form-item>
          </div>
        </div>
        <!-- 上传logo {{ form.getFieldValue('activityStatus') }} -->
        <div class="row">
          <div class="col">
            <a-form-item >
              <div class="text-panda-text-7  row">
                <div class="text-panda-text-7 fw_600">
            
                  {{ $t("internal.activity_manage.config2.t29") }} 

                </div>
                <span class="ml20x">
                  <!-- logo图 -->
                  <uploadList :is_view="is_view" style="width:500px" @onImgListUpdate="onImgListUpdate" :defaultFileList="defaultFileList">
                  </uploadList>
                  <!-- 72*72, 支持.jpg .jpeg .png,图片不能超过 10 KB -->
                  <p style="margin-top: -26px;"> 
                    {{ $t('internal.activity_manage.config2.t31') }}
                  </p>
                </span>
              </div>
            </a-form-item>
          </div>
        </div>
        <!-- 文案说明 {{ form.getFieldValue('activityStatus') }} -->
        <div class="row">
          <div class="col">
            <a-form-item >
              <div class="text-panda-text-7  row">
                <div class="text-panda-text-7 fw_600">
                  {{ $t("internal.activity_manage.config.t9") }} 

                </div>
                <!-- {{ titlelist }} -->
                <div class="ml20x">
                  <div
                    class="row"
                    v-for="(item, index) in lan_list_name"
                    :key="item + index"
                    :class="lan_list_name.length > 1 && index > 0 ? ' mt30x' : ''"
                  >
                    <div class="left pl20x">
                      <div>
                        <div class="append-handle-btn-input row">
                          <span class="text-panda-text-7 mr10x" :class="[is_zs == 'zs' ? 'w-70' : 'w-140']"> {{ item.msg  }}</span>
                          <q-input
                            v-model.trim="titlelist[item.languageName]"
                            style="width: 300px"
                            outlined
                            dense
                            color="panda-text-light"
                            class=" text-panda-text-dark panda-input-dense input-height-36px "
                            :placeholder=" $t('internal.activity_manage.t9', [item.msg]) "
                            lazy-rules
                            :disable="is_view"
                          >
                  
                          </q-input>
                        </div>
                      </div>
                    </div>
                  </div>
               
                </div>
              </div>
            </a-form-item>
          </div>
        </div>

        <a-divider />
        <a-form-item class="text-center">
          <!-- 确认 取消按钮 -->
          <q-btn
            class="panda-btn-primary-dense bg-primary mr20x"
            style="width: 120px; height: 32px"
            @click="handle_submit"
            :disable="is_view"
            :loading="loading"
            :label="$t('internal.activity_manage.config2.t30')"
          />
          <q-btn
            class="panda-btn-white border-1px"
            style="width: 100px; height: 32px"
            v-close-popup
            :label="$t('internal.cancel')"
          />
        </a-form-item>
          </a-form>
        </q-card-section>
      </q-card>
    </div>
  </template>
  <script>
  import { mapGetters } from "vuex";
import { compute_image_src, IMAGE_ORIGIN } from "src/pages/internal/web/config/other_domain.js";
import uploadList from 'src/pages/internal/operations/activity_manage/component/upload_activity_list2.vue';
import {api_merchant, api_operate } from "src/api/internal/index.js";
import {  internal_activity_management } from "src/api/index.js";
import commonmixin from "src/mixins/internal/common/commontoolmixin.js";
import otherconstantmixin from "src/mixins/internal/constant/otherconstantmixin.js";
import { i18n } from "src/boot/i18n";

import moment from "moment";
  export default {
    mixins: [commonmixin, otherconstantmixin],
    components: {
      uploadList
    },
    props: {
      detailObj: {
        type: Object,
        default() {
          return {};
        },
      },
      // 奖券类型
      select_ticket_list: {
        type: Array,
        default() {
          return [];
        },
      },
      dialog_data: {
        type: Object,
        default() {
          return null
        }
      },
      is_view: {
        type: Boolean,
        default() {
          return false;
        }
      }
    },
    data() {
      return {
        defaultFileList: [],   // logo封面图
        is_edit: false, // 是否是编辑
        loading: false, //  保存按钮loading
        task_type: 1, // 任务ID
        new_form: {},
        cur_ticket_type: "", // 奖券类型
        selectedDevices: [], // 存储选中的设备
        allLanguageReqList: [], // 所有语言
        params: {
          logo: '',   // logo封面图
        },
        titlelist: {}, //文案说明 -语言
        deviceType_arr: { //设备类型映射
          0: i18n.t("internal.activity_manage.config2.t9"),  //"PC亚洲版,移动亚洲版,PC欧洲版,H5欧洲版"
          1: i18n.t("internal.activity_manage.config2.t10"),  //PC亚洲版
          2: i18n.t("internal.activity_manage.config2.t11"), //"移动亚洲版"
          3: i18n.t("internal.activity_manage.config2.t12"), //"PC欧洲版",
          4: i18n.t("internal.activity_manage.config2.t13"), //"H5欧洲版",
        },
        show_shebei_list :[
            {// PC亚洲版
                key: 'pc_yz',
                value: 1,
                // label: 'PC亚洲版', 
                label: i18n.t("internal.activity_manage.config2.t10"), 
            },
            {//移动端-亚洲版
                key: 'yd_yz',
                value: 2,
                // label: '移动端亚洲版', 
                label: i18n.t("internal.activity_manage.config2.t11"),

            },
            {//PC欧洲版
                key: 'pc_oz',
                value: 3,
                // label: 'PC欧洲版', 
                label: i18n.t("internal.activity_manage.config2.t12"), 

            },
            {//H5欧洲版
                key: 'h5_oz',
                value: 4,
                // label: 'H5欧洲版', 
                label: i18n.t("internal.activity_manage.config2.t13"), 

            },
        ],
        rangeConfig: {
          rules: [{ type: 'array', required: true, message: '请选择活动时间' }],
        },
        activity_type_arr: [
          {
            key: 1,
            value: "",
            // label: "请选择",
            label: i18n.t("internal.activity_manage.config2.t6"),

          },
          {
            key: 2,
            value: 1,
            // label: "定时播放烟花活动",
            label: i18n.t("internal.activity_manage.config2.t3"),

          },

        ],
        yanhua_style_arr: [
          {
            key: 1,
            value: "",
            // label: "请选择",
            label: i18n.t("internal.activity_manage.config2.t6"),

          },
          {
            key: 2,
            value: 1,
            // label: "礼花1",
            label: i18n.t("internal.activity_manage.config2.t4"),

          
          },
          {
            key: 3,
            value: 2,
            // label: "礼花2",
            label: i18n.t("internal.activity_manage.config2.t5"),

          },
        ],
        lan_list_name:[
          {id: 1, languageName: 'zh', msg_zs: '中简',msg_en:"Simplified Chinese", msg_ko:"단순화 된 중국어"},
          {id: 3, languageName: 'tw', msg_zs: '中繁',msg_en:"traditional Chinese", msg_ko:"전통적인 중국어"},
          {id: 2, languageName: 'en', msg_zs: '英文',msg_en:"English", msg_ko:"영어"},
          {id: 4, languageName: 'vi', msg_zs: '越南语',msg_en:"Vietnamese", msg_ko:"베트남어"},
          {id: 5, languageName: 'th', msg_zs: '泰语',msg_en:"Thai language ", msg_ko:"태국"},
          {id: 6, languageName: 'ms', msg_zs: '马来语',msg_en:"TMalay ", msg_ko:"말레이 사람"},
          {id: 7, languageName: 'ad', msg_zs: '印尼语',msg_en:"Indonesian", msg_ko:"인도네시아 인"},
          {id: 8, languageName: 'mya', msg_zs: '缅甸语',msg_en:"Burmese", msg_ko:"버마 사람"}, // mya
          {id: 9, languageName: 'pt', msg_zs: '葡萄牙语',msg_en:"Portuguese", msg_ko:"포르투갈 인"},//pt
          {id: 11, languageName: 'ko', msg_zs: '韩语',msg_en:"Korean", msg_ko:"한국인"},//ko
          {id: 12, languageName: 'es', msg_zs: '西班牙语',msg_en:"es", msg_ko:"스페인의"},//es
          // {id: 10, languageName: 'ja', msg_zs: '日语',msg_en:"Japanese ", msg_ko:"일본어"}, // ja
          {id: 13, languageName: 'ara', msg_zs: '阿拉伯语',msg_en:"ara", msg_ko:"아라비아 말"},//arabic
          {id: 14, languageName: 'ru', msg_zs: '俄语',msg_en:"ru", msg_ko:"러시아인"},//ru
          {id: 15, languageName: 'hin', msg_zs: '印地语',msg_en:"hindi", msg_ko:"힌디 어"},//hin
        ],
      };
    },
    created() {

      this.initconfig();


      console.log('dialog_data----', this.dialog_data);
      if ( this.dialog_data != null ) {
        this.is_edit = true
        // this.initconfig();
        console.log('这是编辑弹窗 或者 详情 弹窗--');
        this.on_get_article_detail()
      

      }
    },
    computed: {
        ...mapGetters(["get_user_info"]),
        // 任务ID允许输入阈值
        isNormalTick() {
            // console.log(this.cur_ticket_type);
            return this.cur_ticket_type == 1;
        },
        isAllSelected() {
            return this.selectedDevices.length === this.show_shebei_list.length;
        },
    },
    beforeCreate() {
      this.form = this.$form.createForm(this, { name: "update" });
    },
    mounted() {
      this.task_type = this.$q.localStorage.getItem("task_type")
        ? this.$q.localStorage.getItem("task_type")
        : 1;
    },
    watch: {
      "new_form.taskId": function (val) {
        this.checktaskId();
      },
      "new_form.reBonus": function (val) {
        if (val) {
          this.new_form.reBonus = parseInt(val);
          this.form.setFieldsValue({
            reBonus: parseInt(val),
          });
        }
      },
    },
    methods: {
      moment,
    //   全选
    toggleSelectAll() {
      if (this.isAllSelected) {
        this.selectedDevices = []; // 取消全选
      } else {
        this.selectedDevices = this.show_shebei_list.map(item => item.value); // 全选
      }
      this.form.setFieldsValue({
        deviceTypeList: this.selectedDevices, // 更新表单字段
      });
    },
    onCheckAllChange(e) {
      const checked = e.target.checked;
      if (checked) {
        this.selectedDevices = this.show_shebei_list.map(item => item.value); // 全选
      } else {
        this.selectedDevices = []; // 取消全选
      }
      this.form.setFieldsValue({
        deviceTypeList: this.selectedDevices, // 更新表单字段
      });
    },
    // 展示设备切换
    on_shebei_change(shebei_list) {
        // this.$nextTick(() => {
        //     this.form.setFieldsValue({
        //         shebei_list: shebei_list,
        //     });
        // })
    },
      /**
       * @description:通过任务id获取补发奖券数
       * @param {type}
       * @return {type}
       */
      on_ticket_change(userName) {
        // this.cur_ticket_type = userName;
      },
      /**
       * reBonus：补发奖券数   taskId：任务ID
       */
      checktaskId() {
        if (this.new_form.taskId) {
          api_operate
            .getBonusByTaskId({ taskId: this.new_form.taskId })
            .then((res) => {
              let { code, data, msg } = res.data;
              if (code == "0000000") {
                this.new_form.reBonus = parseInt(data.ticketNum);
                this.form.setFieldsValue({
                  reBonus: parseInt(data.ticketNum),
                });
                // this.$message.success(msg);
              } else {
                this.$message.error(msg);
  
                this.new_form.reBonus = "";
                this.form.setFieldsValue({
                  reBonus: "",
                });
              }
            });
        }
      },
      // 补发奖券数-校验
      validator_reBonus(rule, value, callback) {
        let home = Number(value);
        if ((value != "" && value != null) || value == 0) {
          if (home <= 0) {
            return callback(new Error(this.$t("internal.common.label_78"))); //不能小于0
          }
          if (!Number.isSafeInteger(home)) {
            return callback(new Error(this.$t("internal.common.a1"))); //只能为数字
          }
          return callback();
        } else {
          return callback(new Error(this.$t("internal.operations_site.s46"))); //不能为空
        }
      },
      /**
       * @description 保存按钮
       * @return {undefined} undefined
       */
      handle_submit(e) {
        // this.loading = true;
        e.preventDefault();
        this.form.validateFields((err, values) => {
          if (!err) {
            console.log('values参数',values);
            // 时间转换处理
            const [startTime, endTime] = values['range-time-picker'];
            let params = {
              ...values,
            };
            params.beginTime =  moment(startTime).valueOf(), // 开始时间 
            params.endTime= moment(endTime).valueOf(), // 截止时间 
            params.activityStatus = params.activityStatus?1:0 // 活动状态开关
            // console.log('this.titlelist',this.titlelist);
            let languageReqList = Object.entries(this.titlelist).map(([languageType, copyDesc]) => {
              const existingEntry = this.allLanguageReqList.find(item => item.languageType === languageType);

              return { 
                languageType,
                copyDesc,
                ...(this.is_edit && existingEntry && existingEntry.id ? { id: existingEntry.id } : {})
                // ...(this.is_edit ? { id: existingEntry ? existingEntry.id : "" } : {}) // 仅在编辑时添加 id
              }
            });
            // console.log('languageReqList',languageReqList);
            params.languageReqList = languageReqList;
            //如果设备类型都选中，则传0 全部
            if( params.deviceTypeList.length == 4) {
              params.deviceTypeList = [0]
            }
            params.logo = this.params.logo;
            delete params['range-time-picker'];
            // return
            params = this.delete_empty_property_with_exclude(params);
            if(this.is_edit){
              console.log('这是编辑----',this.dialog_data);

            }else{
              console.log('这是新增----');
            }
            // 如果是编辑则传id,如果是编辑则单个语言类型也要传id
          
            if(this.is_edit) {
              params.id = this.dialog_data.id;
            }
            console.log('params',params);
            const create_or_edit_activity_management = this.is_edit?
                    internal_activity_management.post_edit_editActivity :  internal_activity_management.post_add_createActivity
            create_or_edit_activity_management(params).then((res) => {
              this.loading = false;
              let { code, msg } = res.data;
              if (code == "0000") {
                this.$message.success(msg);
                this.$emit("on_close", { operate_success: true });
              } else {
                this.$message.error(msg);
              }
            });
          } else {
            this.loading = false;
          }
        });
      },
          // 编辑进来 初始化数据
    async on_get_article_detail() {
      console.log('编辑进来');
      const { id } = this.dialog_data;
      const res = await internal_activity_management.post_detail_queryActivityDetail({ id });
      let { code, msg } = res.data;
      if (code == "0000") {
        const data = this.$lodash.get(res, "data.data") || [];
        const {  logo } = data;
        this.params = { logo };
        // logo封面图
        let fileList = [];
        logo && logo.split(";").forEach((item, index) => {
          fileList.push({ uid: index, url: compute_image_src(item || ""), status: 'done', isDefault: true, defaultUrl: item, name: item });
        });
        this.defaultFileList = fileList;

        this.$nextTick(() => {
          const { activityStatus, beginTime,  endTime, deviceType, deviceTypeList, languageReqList } = data;
          //处理设备类型 如果deviceType 返回类型是 '0' 则是全部
          let updatedDeviceType = deviceType;
          if (updatedDeviceType == 0) {
            updatedDeviceType = "1,2,3,4";
          }
          // 处理语言去重
          const seenLanguages = new Set();
          const uniqueLanguageReqList = languageReqList.filter(item => {
            if (!seenLanguages.has(item.languageType)) {
              seenLanguages.add(item.languageType);
              return true; // 保留该项
            }
            return false; // 过滤掉重复项
          });
          // 存储一份语言列表
          this.allLanguageReqList = uniqueLanguageReqList;
          const langTitleList = uniqueLanguageReqList?.reduce((acc, item) => {
              acc[item.languageType] = item.copyDesc;
              return acc;
          }, {});
          this.titlelist = langTitleList
          // console.log('this.titlelist',this.titlelist);
          // console.log('this.allLanguageReqList',this.allLanguageReqList);

          

          this.form.setFieldsValue({
            ...data,
            'range-time-picker': [beginTime ? moment(new Date(beginTime)) : null, endTime ? moment(new Date(endTime)) : null],
            // topStartTime: topStartTime ? moment(new Date(topStartTime)) : null,
            // topEndTime: topEndTime ? moment(new Date(topEndTime)) : null,
            deviceTypeList:  updatedDeviceType.split(',').map(Number), // 更新表单字段
            activityStatus:activityStatus? true: false
            
          });
        });
      } else {
        this.$message.error(msg);
      }
    },
      // 封面图
      onImgListUpdate(thumbnailsData) { 
          this.params.logo = thumbnailsData.join(";");
      },
        /**
     * @description:查询c端设定
     */
     async initconfig() {
      this.lan_list_name = this.lan_list_name.map(function (x) {
        return {
          languageName: x.languageName,
          msg: this.is_zs == "zs" ? x.msg_zs : this.is_zs == "en" ? x.msg_en : x.msg_ko,
        };
      }, this);
 
      // 测试数据
      let langTitle = {"zs":"5454545","en":"56","tw":"444","vi":"555","th":"666","ad":"777","mya":"缅甸语","es":"11","ara":"22","ru":"33","hin":"44"}
      // 如果是查看详情弹窗 或 编辑弹窗 则赋值
      // if ( this.dialog_data != null ) {
      //   this.titlelist = langTitle
      // }
      return
      const get_cplate_config = this.src_internal
        ? api_merchant.get_cplate_config
        : api_account.get_cplate_config;
      let res = await get_cplate_config({
        merchantCode: this.detailObj.merchantCode,
      });
      let { code, data } = res.data;
      if (code == "0000000") {
        this.formData = Object.assign(data, { oddsOptionType: data.oddsOptionType ?? 2, defaultOddsOptions: data.defaultOddsOptions ?? 2 });
        this.titlelist =
          (this.formData.title && JSON.parse(this.formData.title)) || {};
      }
    },
    },
  };
  </script>
  <style lang="scss" scoped>
  ::v-deep .ant-form-vertical .ant-form-item {
    padding-bottom: 0;
  }
  ::v-deep .ant-input-lg {
    font-size: 14px;
  }
  ::v-deep .ant-select-lg {
    font-size: 14px;
  }
  ::v-deep .ant-input-number-lg {
    width: 170px;
    font-size: 14px;
  }
  ::v-deep .ant-form-explain {
    font-size: 12px;
  }
  ::v-deep .ant-calendar-picker-input.ant-input {
    height: 39px;
  }
  ::v-deep .ant-select-selection {
    width: 260px;
  }
  ::v-deep .ant-divider .ant-divider-horizontal .ant-divider-with-text-center::before{
      color:  rgb(13, 18, 248);
  }
  </style>
  