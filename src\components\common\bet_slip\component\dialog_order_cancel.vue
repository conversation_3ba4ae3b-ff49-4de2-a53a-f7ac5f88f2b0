
<!--
 * @FilePath: /src/components/common/bet_slip/index.vue
 * @Description:【共用】注单查询(对内外)-取消注单弹框
-->
<template>
    <div >
      <!-- 一键取消第一次的弹窗 -->
      <q-dialog class="text-center"  v-model="show_cancel_confirm"  persistent>
        <q-card style="width: 520px">
          <q-card-section>
            <div class="text-center full-width text-h6 text-weight-bolder">
             <span v-if="show_confirm_type==1">{{ $t("external.label.label64") }}</span> 
             <span v-if="show_confirm_type==2">{{ $t("external.label.label65") }}</span> 
             <span v-if="show_confirm_type==3">{{ $t("external.label.label66") }}</span> 
             <span v-if="show_confirm_type==4">{{ $t("external.label.label67") }}</span> 
            </div>
          </q-card-section>
          <q-card-section>
            <!--请输入原因-->
            <div v-if="cancel_confirm_text_alert" class="q-mb-sm text-center text-red">
              {{ cancel_confirm_text_alert_text }}
            </div>
            <q-input v-model="cancel_confirm_text" outlined :placeholder="$t('external.label.label42')" type="textarea" />
          </q-card-section>
          <q-card-actions>
            <div class="row justify-center q-gutter-x-md full-width">
              <!--确定-->
              <q-btn :label="$t('external.label.label44')" class="q-mr-md" color="primary" @click="handle_begin_cancel_process_2()" />
              <!--返回-->
              <q-btn :label="$t('external.label.label43')" color="primary" @click="$emit('close_dialog')" />
            </div>
          </q-card-actions>
        </q-card>
      </q-dialog>
      <!-- 一键取消第 二次的弹窗 -->
      <q-dialog class="text-center"  v-model="show_cancel_confirm_2" persistent>
        <q-card style="width: 520px">
          <q-card-section>
            <div class="mb10x">
              {{ warn_title}}
            </div>
            <div class="text-red mb10x" v-if="show_confirm_type==2">   {{ $t("external.label.label62") }}</div>
          <!-- <div class="mb10x"> {{ $t("external.label.label68") }}</div>
         <div class="mb10x" style="word-break:break-all;">{{betno_confirm_text}}</div>  -->

          </q-card-section>
          <q-card-actions>
            <div class="row justify-center q-gutter-x-md full-width">
              <!--确定-->
              <q-btn :label="$t('external.label.label44')" class="q-mr-md" color="primary" @click="handle_begin_cancel_process_3()" />
              <!--返回-->
              <q-btn v-if="[1,2].includes(this.show_confirm_type)" :label="$t('external.label.label43')" color="primary" v-close-popup />
              <q-btn v-if="[3,4].includes(this.show_confirm_type)" :label="$t('external.label.label43')" color="primary" @click="$emit('close_dialog')" />
            </div>
          </q-card-actions>
        </q-card>
      </q-dialog>
    </div>
  </template>
  <script>
  import moment from "moment";
  import { i18n } from "src/boot/i18n";
  
  export default {
  props: {
    //1:取消投注项，2：取消注单，3：一键冻结，4:一键解冻
    show_confirm_type:{
        type:Number,
    },
    cancel_arr:{
      type:Array
    },
  },
    data() {
      return {
        cancel_num:0,//数量
        show_cancel_confirm:true,
        show_cancel_confirm_2: false, // 取消注单弹窗
        cancel_confirm_text: "", //取消注单留言信息
        cancel_confirm_text_alert: false, //取消注单留言信息  警告语句
        cancel_confirm_text_alert_text: "", //取消注单留言信息  警告语句
        betno_confirm_text: "", //取消注单留言信息

      };
   
    },
    computed: {
      warn_title(){
        if(this.show_confirm_type==1){
          return i18n.t("external.label.label63",[this.cancel_num]) 
        }else if(this.show_confirm_type==2){
          return i18n.t("external.label.label63",[this.cancel_num]) 
        }else if(this.show_confirm_type==3){
          return i18n.t("external.label.label69",[this.cancel_num]) 
        } else if(this.show_confirm_type==4){
          return i18n.t("external.label.label70",[this.cancel_num]) 
        }
      },
    },
    created() {
      this.handle_begin_freeze()
      this.handle_begin_cancel()

    },
    watch: {
    },
    methods:{
      /*
      * 解冻，解冻
      */
      handle_begin_freeze(){
        if([3,4].includes(this.show_confirm_type)){

        let betNos = [];
        this.cancel_arr.map((x) => {
          betNos.push(x.betNo);
      });
        this.cancel_num=betNos.length
        this.betno_confirm_text=betNos.join(',')

        this.show_cancel_confirm=false
        this.show_cancel_confirm_2 = true;
      }
      },
    /*
    * 注单取消
    */
    bet_can_cancel(item){
      //1:已结算，5：投资失败,3:已取消
       if(![1,5,3].includes(item.betStatus)){
        return true
      }
    },
      /*
      * 取消投注项，取消注单
      */
      handle_begin_cancel(){
        if([1,2].includes(this.show_confirm_type)){

        let betNos = [];
        this.cancel_arr.map((x) => {
          betNos.push(x.betNo);
          if(this.show_confirm_type==2){
           if( x.detail&& x.detail.length!=0){
            x.detail.orderDetailList.forEach(element => {
              if(this.bet_can_cancel(element)){
                betNos.push(element.betNo);
              }
            });
           }
          }
      });
      betNos=this.$lodash.uniq(betNos)
        this.cancel_num=betNos.length
        this.betno_confirm_text=betNos.join(',')
      }
      },
        
    // //   第二个弹窗
    handle_begin_cancel_process_2() {
      if (!this.cancel_confirm_text) {
        //1:取消投注项，2：取消注单 必须输入取消原因
        this.cancel_confirm_text_alert_text = "--请输入取消原因--";
        this.cancel_confirm_text_alert = true;
      } else {
        this.show_cancel_confirm_2 = true;
      }
    },
    
    // 一键取消第二个弹窗的 确定按钮
    handle_begin_cancel_process_3() {
        this.$emit("handle_begin_cancel_process_3",this.cancel_confirm_text,this.betno_confirm_text)
    },
    },
}
  </script>
  