/*
 * @FilePath: /src/components/common/bet_slip/config/cp_config.js
 * @Description: 注单查询-彩票-表格配置
 */
import { i18n } from "src/boot/i18n";
export const cp_tablecolumns_config = (src_internal, src_external, userInfo) => [
  {
    title: i18n.t("internal.table_title.label1"), // 序号
    width: 60,
    dataIndex: "_index",
    key: "_index",
    align: "center",
  },
  {
    title: i18n.t("internal.table_title.yh45"), // 会员ID
    width: 180,
    dataIndex: "uid",
    key: "uid",
    align: "left",
    scopedSlots: { customRender: "uid" },
  },
  {
    title: i18n.t("internal.content_manage.t18"), // 会员账号
    width: 200,
    dataIndex: "memberAccount",
    key: "memberAccount",
    align: "left",
    scopedSlots: { customRender: "memberAccount" },
  },
  src_internal || (src_external && [1, 10].includes(userInfo.agentLevel))
    ? {
        title: i18n.t("internal.table_title.label72"), // 商户编号
        width: 100,
        dataIndex: "merchantCode",
        key: "merchantCode",
        align: "left",
        scopedSlots: { customRender: "merchantCode" },
      }
    : {},
  {
    title: i18n.t("internal.table_title.label54"), // 注单编号
    width: 180,
    dataIndex: "orderId",
    key: "orderId",
    align: "left",
    scopedSlots: { customRender: "orderId" },
  },
  {
    title: i18n.t("internal.table_title.yh16"), // 彩种名称
    dataIndex: "ticketName",
    key: "ticketName",
    width: 120,
    align: "center",
    scopedSlots: { customRender: "ticketName" },
  },
  {
    title: i18n.t("internal.table_title.yh17"), // 期号
    dataIndex: "ticketPlanNo",
    key: "ticketPlanNo",
    width: 150,
    align: "center",
    scopedSlots: { customRender: "ticketPlanNo" },
  },
  {
    title: i18n.t("internal.table_title.yh18"), // 玩法名称
    dataIndex: "playName",
    key: "playName",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "playName" },
  },
  {
    title: i18n.t("internal.table_title.yh19"), // 投注内容
    dataIndex: "betContent",
    key: "betContent",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "betContent" },
  },
  {
    title: i18n.t("internal.table_title.yh20"), // 更新时间
    dataIndex: "updateAt",
    key: "updateAt",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "updateAt" },
  },
  {
    title: i18n.t("internal.table_title.yh21"), // 投注时间
    dataIndex: "betTime",
    key: "betTime",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "betTime" },
  },
  {
    title: i18n.t("internal.table_title.yh22"), // 是否为追号单
    dataIndex: "chaseOrder",
    key: "chaseOrder",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "chaseOrder" },
  },
  {
    title: i18n.t("internal.table_title.yh111"), // 追号单编号
    dataIndex: "chaseId",
    key: "chaseId",
    width: 180,
    align: "center",
    scopedSlots: { customRender: "chaseId" },
  },
  {
    title: i18n.t("internal.table_title.yh23"), // 终端
    dataIndex: "device",
    key: "device",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "device" },
  },
  {
    title: i18n.t("internal.table_title.yh24"), // 开奖号码
    dataIndex: "ticketResult",
    key: "ticketResult",
    width: 170,
    align: "center",
    scopedSlots: { customRender: "ticketResult" },
  },
  {
    title: i18n.t("internal.table_title.yh25"), // 注数
    dataIndex: "betNums",
    key: "betNums",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "betNums" },
  },
  {
    title: i18n.t("internal.table_title.yh26"), // 倍数
    dataIndex: "betMultiple",
    key: "betMultiple",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "betMultiple" },
  },
  {
    title: i18n.t("internal.table_title.yh27"), // 货币名称
    dataIndex: "currencyType",
    key: "currencyType",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "currencyType" },
  },
  {
    title: i18n.t("internal.match_live.t82"), // 投注金额
    dataIndex: "betMoney",
    key: "betMoney",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "betMoney" },
  },
  {
    title: i18n.t("internal.table_title.yh28"), // 投注返点
    dataIndex: "betRebate",
    key: "betRebate",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "betRebate" },
  },
  {
    title: i18n.t("internal.table_title.yh29"), // 中奖金额
    dataIndex: "winAmount",
    key: "winAmount",
    width: 100,
    align: "center",
    scopedSlots: { customRender: "winAmount" },
  },
  // {
  //   title: i18n.t("internal.table_title.yh30"), // 中投比
  //   dataIndex: "middleBetRatio",
  //   key: "middleBetRatio",
  //   width: 100,
  //   align: "center",
  //   scopedSlots: { customRender: "middleBetRatio" },
  // },
  {
    title: i18n.t("internal.state"), // 状态
    dataIndex: "betStatus",
    key: "betStatus",
    width: 60,
    align: "center",
    scopedSlots: { customRender: "betStatus" },
  },
  // {
  //   title: i18n.t("internal.match_live.t10"), // 操作
  //   key: "operate",
  //   width: 150,
  //   align: "center",
  //   dataIndex: "operate",
  //   scopedSlots: {
  //     customRender: "operate",
  //   },
  // },
];

// 彩票-多币种参数对照表
export const currencyType_list = [
  { label: i18n.t("internal.common.label8"), value: "" },
  {
    value: 1,
    label: "人民币",
    key: "CNY",
  },
  {
    value: 2,
    label: "美元",
    key: "USD",
  },
  {
    value: 3,
    label: "新加坡元",
    key: "SGD",
  },
  {
    value: 4,
    label: "马来西亚林吉特",
    key: "MYR",
  },
  {
    value: 5,
    label: "新台币",
    key: "TWD",
  },
  {
    value: 6,
    label: "泰铢",
    key: "THB",
  },
  {
    value: 7,
    label: "文莱林吉特",
    key: "BND",
  },
  {
    value: 8,
    label: "越南盾",
    key: "VND",
  },
  {
    value: 9,
    label: "日元",
    key: "JPY",
  },
  {
    value: 10,
    label: "韩元",
    key: "KRW",
  },
  {
    value: 11,
    label: "缅甸元",
    key: "MMK",
  },
  {
    value: 12,
    label: "巴西雷亚尔",
    key: "BRL",
  },
  {
    value: 13,
    label: "哥伦比亚比索",
    key: "COP",
  },
  {
    value: 14,
    label: "泰达币",
    key: "USDT",
  },
  {
    value: 15,
    label: "印尼盾",
    key: "IDR",
  },
  {
    value: 16,
    label: "VNDK",
    key: "VNDK",
  },
];

// 彩票-货币名称映射
export const currencyType_map = currencyType_list.reduce((prev, cur) => {
  prev[cur.value] = cur.label;
  return prev;
}, {});

// 彩票-货币名称映射 {1:CNY}
export const currencyType_cny_map = currencyType_list.reduce((prev, cur) => {
  prev[cur.value] = cur.key;
  return prev;
}, {});

// 彩票-注单状态
export const betStatus_list = [
  // {
  //   value: "",
  //   label: i18n.t("internal.table_title.label104"), // 全部
  // },
  {
    value: 1,
    label: i18n.t("internal.table_title.yh31"), // 待开奖
  },
  {
    value: 2,
    label: i18n.t("internal.table_title.yh32"), // 未中奖
  },
  {
    value: 3,
    label: i18n.t("internal.table_title.yh33"), // 已中奖
  },
  {
    value: 4,
    label: i18n.t("internal.table_title.yh34"), // 挂起
  },
  {
    value: 5,
    label: i18n.t("internal.table_title.yh35"), // 已结算
  },
];

// 彩票-注单状态映射
export const betStatus_map = betStatus_list.reduce((prev, cur) => {
  prev[cur.value] = cur.label;
  return prev;
}, {});

// 彩票-撤单类型
export const cancelType_map = {
  1: i18n.t("internal.table_title.yh56"), // 个人撤单
  2: i18n.t("internal.table_title.yh57"), // 系统撤单
  3: i18n.t("internal.table_title.yh58"), // 中奖停追撤单
  4: i18n.t("internal.table_title.yh59"), // 不中停追撤单
};
