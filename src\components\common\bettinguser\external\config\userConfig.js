/*
 * @Desc: 
 * @Date: 2020-01-27 15:42:23
 * @Author:Nice
 * @FilePath: /src/components/common/bettinguser/external/config/userConfig.js
 */
import { i18n } from 'src/boot/i18n';
const position = 'left'
let table_title =  i18n.t('external.merchant.bettinguser.userConfig')
export const tablecolumns_config = [
  {
    title:table_title[0],
    width: 80,
    dataIndex: "_index",
    key: "_index",
    fixed: "left",
    align: "center"
  },
  {//
    title:table_title[1],
    width: 140,
    dataIndex: "ip",
    key: "ip",
    fixed: "left",
    align: position
  },
  {
    title:table_title[2],
    dataIndex: "ipAddress",
    key: "1",
    width: 150,
    align: position
  },
  {
    title:table_title[3],
    dataIndex: "betNumber",
    key: "2",
    align: position
  },
  {
    title:table_title[4],
    dataIndex: "loginNumber",
    key: "3",
    width: 100,
    align: position
  },
  {
    title:table_title[5],
    dataIndex: "endTime",
    key: "4",
    width: 150,
    align: "center"
  },
];

