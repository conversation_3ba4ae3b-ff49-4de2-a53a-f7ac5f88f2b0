/*
 * @FilePath: /src/components/common/dialog_lan/mixin/lan_name.js
 * @Description: 默认语言配置
 * //语言类型(语言顺序排序) 1. 中文简体-zh  2. 简称   3. 中文繁体-zh   4. 英语-en   5. 越南语-vi   6. 泰语-th    7.马来语-ms   8. 印尼语-ad   9. 葡萄牙语-pt   10. 韩语-ko   11. 西班牙语-es   12. 缅甸语-mya   13.日语
 */
export default{
  inject:['is_zs'],
    data() {
        return {
            lan_list_name:[
                {id: 1, languageName: 'zh', msg_zs: '中文简体',msg_en:"Simplified Chinese", msg_ko:"단순화 된 중국어"},
                {id: 2, languageName: 'en', msg_zs: '英文',msg_en:"English", msg_ko:"영어"},
                {id: 3, languageName: 'tw', msg_zs: '中文繁体',msg_en:"traditional Chinese", msg_ko:"전통적인 중국어"},
                {id: 4, languageName: 'vi', msg_zs: '越南语',msg_en:"Vietnamese", msg_ko:"베트남어"},
                {id: 5, languageName: 'th', msg_zs: '泰语',msg_en:"Thai language ", msg_ko:"태국"},
                {id: 6, languageName: 'ms', msg_zs: '马来语',msg_en:"TMalay ", msg_ko:"말레이 사람"},
                {id: 7, languageName: 'ad', msg_zs: '印尼语',msg_en:"Indonesian", msg_ko:"인도네시아 인"},
                {id: 8, languageName: 'mya', msg_zs: '缅甸语',msg_en:"Burmese", msg_ko:"버마 사람"}, // mya
                {id: 9, languageName: 'pt', msg_zs: '葡萄牙语',msg_en:"Portuguese", msg_ko:"포르투갈 인"},//pt
                {id: 10, languageName: 'ja', msg_zs: '日语',msg_en:"Japanese ", msg_ko:"일본어"}, // ja
                {id: 11, languageName: 'ko', msg_zs: '韩语',msg_en:"Korean", msg_ko:"한국인"},//ko
                {id: 12, languageName: 'es', msg_zs: '西班牙语',msg_en:"es", msg_ko:"스페인의"},//es
                {id: 13, languageName: 'ara', msg_zs: '阿拉伯语',msg_en:"ara", msg_ko:"아라비아 말"},//arabic
                {id: 14, languageName: 'ru', msg_zs: '俄语',msg_en:"ru", msg_ko:"러시아인"},//ru
                {id: 15, languageName: 'hin', msg_zs: '印地语',msg_en:"hindi", msg_ko:"힌디 어"},//hin

              ],
              lan_list: [
                {id: 1, languageName: 'zh', msg_zs: '中文简体',msg_en:"Simplified Chinese", msg_ko:"단순화 된 중국어"},//--zh
                {id: 2, languageName: 'en', msg_zs: '英文',msg_en:"English", msg_ko:"영어"},//en
                {id: 3, languageName: 'tw', msg_zs: '中文繁体',msg_en:"traditional Chinese", msg_ko:"전통적인 중국어"},
                {id: 4, languageName: 'vi', msg_zs: '越南语',msg_en:"Vietnamese", msg_ko:"베트남어"}, //vi
                {id: 5, languageName: 'th', msg_zs: '泰语',msg_en:"Thai language ", msg_ko:"태국"},//th
                {id: 6, languageName: 'ms', msg_zs: '马来语',msg_en:"TMalay ", msg_ko:"말레이 사람"}, //ms
                {id: 7, languageName: 'ad', msg_zs: '印尼语',msg_en:"Indonesian", msg_ko:"인도네시아 인"},//ad
                {id: 8, languageName: 'mya', msg_zs: '缅甸语',msg_en:"Burmese", msg_ko:"버마 사람"},  //mya
                {id: 9, languageName: 'pt', msg_zs: '葡萄牙语',msg_en:"Portuguese", msg_ko:"포르투갈 인"},//pt
                {id: 10, languageName: 'ja', msg_zs: '日语',msg_en:"Japanese ", msg_ko:"일본어"}, //ja
                {id: 11, languageName: 'ko', msg_zs: '韩语',msg_en:"Korean", msg_ko:"한국인"},//ko
                {id: 12, languageName: 'es', msg_zs: '西班牙语',msg_en:"es", msg_ko:"스페인의"},//es
                {id: 13, languageName: 'ara', msg_zs: '阿拉伯语',msg_en:"ara", msg_ko:"아라비아 말"},//arabic
                {id: 14, languageName: 'ru', msg_zs: '俄语',msg_en:"ru", msg_ko:"러시아인"},//ru
                {id: 15, languageName: 'hin', msg_zs: '印地语',msg_en:"hindi", msg_ko:"힌디 어"},//hin

              ],
    }   
    },
}

// 韩语：ko 
// 葡萄牙语：pt
// 西班牙语：es----------新增
// 缅甸语：mya
// ZS("zs", "中文简体"),
// EN("en", "英文    "),
// ZH("zh", "中文繁体"),
// TW("tw", "中文繁体"),
// JP("jp", "日语  "),
// JC("jc", "简称"),
// PT("pt", "葡萄牙语"),
// RU("ru", "俄罗斯语"),
// IT("it", "意大利语"),
// DE("de", "德语    "),
// FR("fr", "法语    "),
// KO("ko", "韩语    "),
// TH("th", "泰语    "),
// VI("vi", "越南语  "),
// ES("es", "西班牙语"),
// MS("ms", "马来语  "),
// AD("ad", "印尼语  "),
// OTHER("other", "其他语种");
// 这个是目前后端在用的code值对应关系，你可以参考下