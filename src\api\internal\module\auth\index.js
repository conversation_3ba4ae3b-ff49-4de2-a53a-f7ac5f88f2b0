/*
 * @Desc: 
 * @Date:
 * @Author:Nice
 * @FilePath       : /TOB对内商户/src/api/internal/module/auth/index.js
 */
import axios from "src/api/common/axioswarpper.js";

const BASE_9 = process.env. API_PREFIX_4;//yewu9



// // 菜单所对应的权限
export const post_manage_mp_getPermissionByMenuIds = (
    params,
    url = "/manage/mp/getPermissionByMenuIds"
) => axios.post(`${BASE_9}${url}`, params);

// 商户的对应权限和菜单
export const post_manage_mp_getAll = (
    params,
    url = "/manage/mp/getAll"
    ) => axios.post(`${BASE_9}${url}`, params);

// 商户添加菜单和权限
export const post_manage_mp_addRoleMenu = (
    params,
    url = "/manage/mp/addRoleMenu"
    ) => axios.post(`${BASE_9}${url}`, params, { type: 6 });

