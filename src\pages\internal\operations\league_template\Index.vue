<!--
 * @Author: 
 * @Date: 2024-10-23 10:43:51
 * @Description: 运营管理/活动管理
 * @FilePath: src/pages/internal/operations/activity_manage/Index.vue
-->
<template>
  <div class="full-width full-height">
    <!--运营管理/活动管理-->
    <div class="pl10x pt10x pb10x fs14" id="top1">
      <q-breadcrumbs separator="/" active-color="whiddte">
        <q-breadcrumbs-el
          :label="$t('internal.menujs')[36]"
          class="panda-text-2"
        />
        <q-breadcrumbs-el
          :label="$t('internal.menujs')['league_template']"
          class="panda-text-1 fs14"
        />
      </q-breadcrumbs>
    </div>

        <MainInfo></MainInfo>

  </div>
</template>

<script>
import MainInfo from "src/pages/internal/operations/league_template/component/main_info.vue";//活动账变记录
export default {
  components: {
    MainInfo,
  },
};
</script>

<style lang="scss" scoped>

 
</style>
