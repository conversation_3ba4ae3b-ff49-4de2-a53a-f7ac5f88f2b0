/*
 * @FilePath: /src/components/common/matchbonus/config/common.js
 * @Description: 投注用户管理-战绩
 */
import { i18n } from 'src/boot/i18n';
let  title_config = i18n.t('internal.data.matchbonus.config')
let  title_config_1 = i18n.t('internal.data.achievements.config')
const POSITION = 'center';
/**
 * src_internal： 对内 / 对内
 * is_zs：中文 / 英文
 * is_statistic： 即时赛事统计报表 / 赛事投注统计
 */
export default ({ src_internal,}) =>
  [
    {
      //序号
      title: title_config[0],
      width: 80,
      dataIndex: "_index",
      key: "_index",
      align: "center",
    },
    {
      //赛种
      title: title_config[2],
      width: 130,
      dataIndex: "sportName",
      key: "sportName",
      align: "left",
      scopedSlots: {
        customRender: "sportName",
      },
    },
    {
      //赛事ID
      title:title_config_1.text9,
      width: 130,
      dataIndex: "matchId",
      key: "matchId",
      align: "left",
      scopedSlots: {
        customRender: "matchId",
      },
    },
    {
      //赛事对阵
      title: title_config[3],
      width: 200,
      dataIndex: "matchInfo",
      key: "matchInfo",
      align: "left",
      scopedSlots: {
        customRender:"matchInfo",
      },
    },
    {
      //联赛名
      title: title_config[17],
      width: 200,
      dataIndex: "tournamentName",
      key: "tournamentName",
      align: "left",
      scopedSlots: {
        customRender: "tournamentName",
      },
    },
    {
      //赛事等级
      title: title_config[4],
      dataIndex: "tournamentLevel",
      key: "tournamentLevel",
      width: 120,
      align: 'left',
      scopedSlots: {
        customRender:"tournamentLevel",
      },
    },
    {
      //投注玩法+投注项
      title: title_config_1.text1,
      dataIndex: "playName",
      key: "playName",
      width: 180,
      align: "left",
      scopedSlots: { customRender: "playName" },
    },
    {
      //赛事赛果
      title: title_config_1.text2,
      dataIndex: "outCome",
      key: "outCome",
      width: 140,
      align: POSITION,
      scopedSlots: { customRender: "outCome" },
    },
    {
      //合买结果
      title: title_config_1.text3,
      dataIndex: "purchaseFrontendStatus",
      key: "purchaseFrontendStatus",
      width: 140,
      align: POSITION,
      scopedSlots: { customRender: "purchaseFrontendStatus" },
    },
    {
      // 注单赔率
      title: title_config_1.text4,
      dataIndex: "oddsValue",
      key:  "oddsValue",
      width: 140,
      align: POSITION,
      scopedSlots: { customRender: "oddsValue" },
    },
    {
      //合买人数
      title: title_config_1.text6,
      dataIndex: "purchaseCount",
      key: "purchaseCount",
      width: 120,
      align: POSITION,
      scopedSlots: {
        customRender: "purchaseCount",
      },
    },
    {
      //合买喜好
      title: title_config_1.text7,
      dataIndex: "buying_preferences",
      key: "buying_preferences",
      width: 190,
      align: POSITION,
      scopedSlots: {
        customRender: "buying_preferences",
      },
    },
    {
      //状态
      title: title_config_1.text8,
      dataIndex: "purchaseStatus",
      key: "purchaseStatus",
      width: 180,
      align: "center",
      scopedSlots: {
        customRender:"purchaseStatus",
      },
    },
  ].filter((x) => x);


