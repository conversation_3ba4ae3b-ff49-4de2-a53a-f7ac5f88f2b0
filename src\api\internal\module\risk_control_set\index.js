/*  
 * @path: src/api/internal/module/risk_control_set/index.js
 * @Descripttion: 风控查询-危险联赛池管理&&危险球队池管理
 * @Author: 
 * @Date: 2024-07-14 09:48:04
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_4; //yewu9



//危险投注
export const get_sportList = (params, url = "/manage/sport/list") =>
  axios.post(`${prefix}${url}`, params); //获取球类种类


// 危险联赛池管理
export const post_danger_League_list = (//危险联赛列表
  params,
  url = "/manage/riskTournament/getTyRiskTournamentList"
) => axios.post(`${prefix}${url}`, params);

export const post_add_Risk_Tournament = (//新增危险联赛
  params,
  url = "/manage/riskTournament/addRiskTournament"
) => axios.post(`${prefix}${url}`, params);

export const del_Risk_Tournament_List = (//删除危险联赛列表
  params,
  url = "/manage/riskTournament/delRiskTournamentList"
) => axios.post(`${prefix}${url}`, params);

export const query_disRisk_TournamentList = (//查询非危险联赛列表
  params,
  url = "/manage/riskTournament/queryDisRiskTournamentList"
) => axios.post(`${prefix}${url}`, params);

export const update_Risk_LevelById = (//危险等级更改
  params,
  url = "/manage/riskTournament/updateRiskLevelById"
) => axios.post(`${prefix}${url}`, params);

export const update_Status_ById = (//危险联赛有效状态更改
  params,
  url = "/manage/riskTournament/updateStatusById"
) => axios.post(`${prefix}${url}`, params);

export const query_Region_Listd = (//地区下拉查询
  params,
  url = "/manage/riskCommon/queryRegionList"
) => axios.post(`${prefix}${url}`, params);

// 危险球队池管理
export const post_danger_team_list = (
  params,
  url = "/manage/riskManagement/getRiskTeamList"
) => axios.post(`${prefix}${url}`, params);


export const getMerchantRiskList = (//获取商户信息
  params,
  url = "/manage/merchant/getMerchantRiskList"
) => axios.get(`${yewu21}${url}`, { params })

export const updateMerchantRiskStatus = (// 修改 1756特殊脚本商户
  params,
  url = "/manage/merchant/updateMerchantRiskStatus"
) => axios.post(`${yewu21}${url}`, params);

export const update_merchant_riskStatus = (// 新接口 tagid 267 修改 1756特殊脚本商户
  params,
  url = "/manage/merchant/updateDxMerchantRiskStatus"
) => axios.post(`${yewu21}${url}`, params);


// 危险联赛列表
export const post_getTyRiskTournamentList = (
  params,
  url = "/riskManagement/getTyRiskTournamentList"
) => axios.post(`${prefix}${url}`, params);
//危险球队列表
export const post_get_matchList = (
  params,
  url = "/manage/riskManagement/getRiskTeamList"
) => axios.post(`${prefix}${url}`, params)

// 球队列表
export const get_Team_List = (
  params,
  url = "/manage/riskManagement/getTeamList"
) => axios.post(`${prefix}${url}`, params)

//新增危险球队
export const post_add_RiskTeam = (
  params,
  url = "/manage/riskManagement/addRiskTeam"
) => axios.post(`${prefix}${url}`, params)

// 删除危险球队
export const delete_RiskTeam = (
  params,
  url = "/manage/riskManagement/deleteRiskTeam"
) => axios.post(`${prefix}${url}`, params)

// 危险球队信息危险等级修改
export const update_Risk_TeamLeve = (
  params,
  url = "/manage/riskManagement/updateRiskTeamLevel"
) => axios.post(`${prefix}${url}`, params)

// 危险球队信息状态修改
export const update_RiskTeam_Status = (
  params,
  url = "/manage/riskManagement/updateRiskTeamStatus"
) => axios.post(`${prefix}${url}`, params)
