/*
 * @FilePath: d:/projects/new-merchant-management/src/api/internal/module/operate/module/article_management.js
 * @Description:文章管理接口
 */

let prefix_1 = process.env.API_PREFIX_4;
import axios from "src/api/common/axioswarpper.js";

// 文章分页列表
export const get_article_page_list = (params) => {
  let prefix = prefix_1;
  let url = `/manage/article/list`;
  return axios.post(`${prefix}${url}`, params);
}

// 新增文章
export const add_article = (params) => {
  let prefix = prefix_1;
  let url = `/manage/article/add`;
  return axios.post(`${prefix}${url}`, params);
}

// 编辑文章
export const edit_article = (params) => {
  let prefix = prefix_1;
  let url = `/manage/article/update`;
  return axios.post(`${prefix}${url}`, params);
}

// 上线文章
export const show_article = (params) => {
  let prefix = prefix_1;
  let url = `/manage/article/show`;
  return axios.get(`${prefix}${url}`, {
    params: {
      ...params
    }
  });
}

// 下架文章
export const hide_article = (params) => {
  let prefix = prefix_1;
  let url = `/manage/article/hide`;
  return axios.get(`${prefix}${url}`, {
    params: {
      ...params
    }
  });
}

// 比赛类型下拉框
export const get_sprots_list = () => {
  let prefix = prefix_1;
  let url = `/manage/article/sprots`;
  return axios.get(`${prefix}${url}`);
}

// 文章联赛下拉选, 查询列表
export const get_article_leagues = (params) => {
  let prefix = prefix_1;
  let url = `/manage/article/leagues`;
  return axios.get(`${prefix}${url}`, {
    params: {
      ...params
    }
  });
}

// 文章赛事查询
export const article_search_matchs = (params) => {
  let prefix = prefix_1;
  let url = `/manage/article/searchMatchs`;
  return axios.post(`${prefix}${url}`, params);
}

// 文章详情查询
export const get_article_detail = (params) => {
  let prefix = prefix_1;
  let url = `/manage/article/detail`;
  return axios.get(`${prefix}${url}`, {
    params: {
      ...params
    }
  });
}
