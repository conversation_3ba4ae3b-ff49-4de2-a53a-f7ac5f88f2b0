<!--
 * @Description: 财务中心-结算二次审核-设置审核金额
-->
<template>
  <div style="min-width: 500px">
    <q-card class="bg-white text-black">
      <q-card-section class="no-padding">
        <div
          class="row line-height-40px fs14 bg-panda-dialog text-panda-text-7 pr10x"
        >
          <!-- 标题 设置审核金额 -->
          <div class="pl20x fw_600">
            {{ $t("internal.second_settlement_initial_review.text__30") }}
          </div>
          <q-space></q-space>
          <q-btn
            class="mr5x text-panda-dialog-close"
            icon="close"
            v-close-popup
          />
        </div>
      </q-card-section>
      <q-separator></q-separator>
      <div
        class="col-12 bg-panda-bg-6 radius-4 pl20x pr20x"
        style="max-height: 800px; overflow: auto"
      >
        <div>
          <div class="mt20x  row" >
           <span class="pr10x col-4 text-center"> {{ $t("internal.second_settlement_initial_review.text__31") }}</span>
            <a-input-number
              v-model="audit_value.secSettInitAuditAmt"
           
              class="col-4 text-center"
              :step="0.01"
              :min="0"
              :precision="2"
              autocomplete="off"
              allowClear
            ></a-input-number>

           <span class="text-grey pl10x col-4 text-center"> {{ $t("internal.second_settlement_initial_review.text__33") }}</span>
          </div>
          <div>
            <div class="mt20x  row" >
             <span class="pr10x col-4 text-center"> 
              <a-checkbox v-model="audit_value.secSettSecondAuditFlag"   >
              </a-checkbox>
              {{ $t("internal.second_settlement_initial_review.text__32") }}</span>
              <a-input-number
                v-model="audit_value.secSettSecondAuditAmt"
             
                class="col-4 text-center"
                :step="0.01"
                :min="0"
                :precision="2"
                autocomplete="off"
                allowClear
              ></a-input-number>
  
             <span class="text-grey pl10x col-4 text-center"> {{ $t("internal.second_settlement_initial_review.text__33") }}</span>
            </div>
            </div>

            <div class="row justify-center q-gutter-x-md full-width mt20x mb10x" >
           
            <!-- 取消  -->
            <a-button v-close-popup >
              {{ $t("internal.cancel") }}
            </a-button>
             <!-- 确认-->
             <a-button type="primary" @click="submit_data()">
              {{ $t("internal.sure") }}
            </a-button>
          </div>
        </div>
      </div>
    </q-card>
  </div>
</template>
<script>
import moment from "moment";
import { i18n } from "src/boot/i18n";
import { api_finance } from "src/api/index.js";

export default {
  name: "dialog_set_the_audit_amount",

  data() {
    return {
      // 获取审核值
      audit_value: {
        secSettInitAuditAmt:0,
        secSettSecondAuditAmt:0,
        secSettSecondAuditFlag:null

      },
    };
  },
  beforeCreate() {},
  created() {
    this.get_audit_value()},
  mounted() {},
  watch: {},
  methods: {
    // 获取审核值
    get_audit_value(){
      api_finance.
      settlement_inquiry.get_getSystemParamConfig()
        .then(res => {
          let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          if (code == "0000000") {
         let data =  this.$lodash.get(res, "data.data")
         if(data){
          this.audit_value={
        secSettInitAuditAmt:data.systemParams.secSettInitAuditAmt,
        secSettSecondAuditAmt:data.systemParams.secSettSecondAuditAmt,
        secSettSecondAuditFlag:data.systemParams.secSettSecondAuditFlag=='1'?true:false
          }
         }
          } else {
            this.$message.error(msg, 5);
          }
        });
    },
  
  /*
  * 最终提交
  */
  submit_data(){
  let secSettInitAuditAmt=""
  if(this.audit_value.secSettInitAuditAmt){
    secSettInitAuditAmt=this.audit_value.secSettInitAuditAmt
  }
  let secSettSecondAuditAmt=""
  if(this.audit_value.secSettSecondAuditAmt){
    secSettSecondAuditAmt=this.audit_value.secSettSecondAuditAmt
  }
  if(secSettInitAuditAmt!=''&&secSettSecondAuditAmt!=''){
    if(secSettSecondAuditAmt<=secSettInitAuditAmt){
      // 二次审核金额不能小于初次审核金额！！
    this.$message.error(i18n.t("internal.finance.secondarysettlement.label29"), 5);
    return
  }
  }
    let params={
      systemParams:{
      secSettInitAuditAmt,
      secSettSecondAuditAmt,
      secSettSecondAuditFlag:this.audit_value.secSettSecondAuditFlag?'1':'0'
      }
    }
    api_finance.
    settlement_inquiry.setSystemParamConfig(params)
      .then(res => {
        let code = this.$lodash.get(res, "data.code");
        let msg = this.$lodash.get(res, "data.msg");
        if (code == "0000000") {
          this.$message.success( msg);
          this.$emit('close_dialog')
        } else {
          this.$message.error(msg, 5);
        }
      });
  },

  },};
</script>

<style lang="scss" scoped></style>
