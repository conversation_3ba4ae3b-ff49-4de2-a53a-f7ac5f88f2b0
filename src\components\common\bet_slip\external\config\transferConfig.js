/*
 * @Desc: 
 * @Date: 2020-01-27 15:42:23
 * @Author:Nice
 * @FilePath       : /对外商户后台/src/components/common/bet_slip/external/config/transferConfig.js
 */

import { i18n } from 'src/boot/i18n';
const POSITION = 'right'
let table_title = i18n.t('external.data.betslip.transferConfig')
export const transfer_config = [
  {//账变时间
    title:  table_title[0],
    width: 150,
    dataIndex: "createTime",
    key: "createTime",
    align: "center",
    scopedSlots: { customRender: 'createTime' },
  },
  {//账变来源
    title: table_title[1],
    width: 160,
    dataIndex: "remark",
    key: "remark",
    align: 'center',
    scopedSlots: { customRender: 'remark' },
  },
  {//账变金额
    title: table_title[2],
    width: 100,
    dataIndex: "changeAmount",
    key: "changeAmount",
    align: POSITION,
    scopedSlots: { customRender: 'changeAmount' },
  },
  {//账变前余额
    title: table_title[3],
    dataIndex: "beforeTransfer",
    key: "beforeTransfer",
    width: 130,
    align: POSITION,
    scopedSlots:{customRender:"beforeTransfer"},
  },
  {//账变后余额
    title: table_title[4],
    width: 130,
    dataIndex: "afterTransfer",
    key: "afterTransfer",
    align: POSITION,
    scopedSlots: { customRender: 'afterTransfer' },
  },
    {//相关注单单号
      title:  table_title[5],
      dataIndex: "orderNo",
      key: "orderNo",
      width: 180,
      align: 'center',
      scopedSlots:{customRender:"orderNo"},
    }
];

