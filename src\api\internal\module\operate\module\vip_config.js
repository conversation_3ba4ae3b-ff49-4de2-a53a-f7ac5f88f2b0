/*
 * @FilePath:   /src/api/internal/module/operate/module/vip_config.js
 * @Description: vip 配置，运营——vip 用户总览
 * @Author: tony
 */


import axios from "src/api/common/axioswarpper.js";


let prefix_1 = process.env.API_PREFIX_4;

// 新建vip post
export const vip_config_save = (params) => {
    let prefix = prefix_1;
    let url = `/vip/config/save`;
    return axios.post(`${prefix}${url}`, params);
}
// 更新vip post
export const vip_config_update = (params) => {
  let prefix = prefix_1;
  let url = `/vip/config/update`;
  return axios.post(`${prefix}${url}`, params);
}

// vip系统开关 post
export const vip_config_getVipSwitch = (params) => {
    let prefix = prefix_1;
    let url = `/vip/config/getVipSwitch`;
    return axios.post(`${prefix}${url}`, params);
  }

//   vip系统开关修改 post
export const vip_config_updateVipSwitch = (params) => {
let prefix = prefix_1;
let url = `/vip/config/updateVipSwitch`;
return axios.post(`${prefix}${url}`, params);
}  

// //   
// 对内get  获取商户信息
export const vip_config_getMerchantList = () => {
    let prefix = prefix_1;
    let url = `/vip/config/getMerchantList`;
    return axios.get(`${prefix}${url}`);
  }

  export const vip_config_list = (params) => {
    let prefix = prefix_1;
    let url = `/vip/config/list`;
    return axios.post(`${prefix}${url}`, params);
  }  

  // /vip/config/list

// 	对外get
// export const vip_config_getMerchantList_out = () => {
//     let prefix = prefix_1;
//     let url = `yewu17/vip/config/getMerchantList`;
//     return axios.get(`${prefix}${url}`);
//   }
// 	yewu17/vip/config/getMerchantList

// 成长值设置：post
export const vip_config_growthvalue_set = (params) => {
  let prefix = prefix_1;
  let url = `/vip/config/growthvalue/set`;
  return axios.post(`${prefix}${url}`, params);
}  


// 获取等级设置
// yewu9/vip/config/getVipLevel?vipId=21608  获取vipLevel (get请求)
export const vip_config_getVipLevel = (params) => {
  let prefix = prefix_1;
  let url = `/vip/config/getVipLevel`;
  return axios.get(`${prefix}${url}`,{ params: { ...params } });
}

// vip/config/growthvalue/get
export const vip_config_growthvalue_get = (params) => {
  let prefix = prefix_1;
  let url = `/vip/config/growthvalue/get`;
  return axios.post(`${prefix}${url}`,params);
}

// 系统级别成长值设置，定级设置
export const vip_config_sys_growthvalue_set = (params) => {
  let prefix = prefix_1;
  let url = `/vip/config/sys/growthvalue/set`;
  return axios.post(`${prefix}${url}`, params);
}  


//初始化VIP信息 get  merchantCode=(非必传)
export const vip_config_initialVipInfo = (params) => {
  let prefix = prefix_1;
  let url = `/vip/config/initialVipInfo`;
  return axios.get(`${prefix}${url}`,{ params: { ...params } });
}

//商户VIP成长值等级统计 post
// /vip/config/listVipInfoStats
export const vip_config_listVipInfoStats = (params) => {
  let prefix = prefix_1;
  let url = `/vip/config/listVipInfoStats`;
  return axios.post(`${prefix}${url}`, params);
}  

// /vip/config/listVipInfoChangeHistory	
export const vip_config_listVipInfoChangeHistory = (params) => {
  let prefix = prefix_1;
  let url = `/vip/config/listVipInfoChangeHistory`;
  return axios.post(`${prefix}${url}`, params);
}  