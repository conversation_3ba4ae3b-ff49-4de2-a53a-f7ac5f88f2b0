<!--
 * @FilePath: /src/pages/internal/merchant/system_level_switch/dialog_chatroom.vue
 * @Description:  商户中心-系统级别开关(聊天室开关设置弹窗)
-->
<template>
    <div
      style="width: 500px; height: auto; max-width: 500px; overflow: hidden"
      class="text-panda-text-7"
    >
      <q-card class="bg-white text-black">
        <q-card-section class="no-padding" v-if="src_internal">
          <div class=" row line-height-40px fs14 bg-panda-dialog text-panda-text-7 pr10x " >
            <!-- 聊天室 -->
            <div class="pl20x fw_600">
                {{$t("internal.system_level_switch.label6")}}
            </div>
            <q-space></q-space>
            <q-btn
              class="mr5x text-panda-dialog-close"
              icon="close"
              v-close-popup
            />
          </div>
        </q-card-section>
        <q-separator v-if="src_internal"></q-separator>
        <q-card-section
          class="position-relative"
          :class="src_internal ? 'no-padding' : 'plr-0x'"
        >
          <div class="row ml20x mb10x" :class="src_internal ? 'mt20x' : ''">
            <div class="full-height full-width">
              <div class=" line-height-30px items-center text-panda-text-dark bg-panda-bg-6 ml10x mr10x border-radius-4px shadow-3 " >
                <div class="ml80x">
                  <!-- 聊天室  开关-->
                  <span >{{$t("internal.system_level_switch.label6")}} </span>
                  <div>
                    <a-radio-group
                      name="radioGroup"
                      v-model="chatRoomSwitch"
                    >
                      <!-- 0关    1开 -->
                      <a-radio :value="1" >{{ $t("internal.video_control_management.label3") }} </a-radio>
                      <a-radio :value="0"> {{ $t("internal.video_control_management.label4") }} </a-radio>
                    </a-radio-group>
                    <div class="ml45x">
                      <!-- 拉取时间频率 -->
                      <span class="mr10x" >{{$t("internal.system_level_switch.label15")}}:</span >
                      <!-- <a-input-number
                        id="inputNumber"
                        v-if="videoSettings == 0"
                        :disabled="disabled"
                        v-model="viewingTime"
                      /> -->
                      <a-input-number
                        id="inputNumber"
                        :min="5"
                        :max="120"
                        v-model="pullMsgRate"
                      />
                      <!-- 秒/次 -->
                     <span class="ml10x">{{$t("internal.system_level_switch.label16")}}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <q-card-section class="fs14">
            <div class="row flex justify-center">
              <!-- <q-space></q-space> -->
              <div class="row mb10x mr5x">
               <!-- 保存 -->
                <q-btn
                  class="panda-btn-primary-dense bg-primary mr20x"
                  style="width: 100px; height: 32px"
                  @click="handle_submit"
                  :loading="loading"
                  :label="$t('internal.save')"
                />
                <!-- 取消 -->
                <q-btn
                  v-if="src_internal"
                  class="panda-btn-white border-1px"
                  style="width: 80px; height: 32px"
                  v-close-popup
                  :label="$t('internal.cancel')"
                />
                <q-btn
                  v-else
                  class="panda-btn-white border-1px"
                  style="width: 80px; height: 32px"
                  @click="$emit('handle_close_lan')"
                  :label="$t('internal.cancel')"
                />
              </div>
            </div>
          </q-card-section>
        </q-card-section>
      </q-card>
    </div>
  </template>
  <script>
  import { api_merchant } from "src/api/index.js";
  import lan_name from "src/components/common/dialog_lan/mixin/lan_name.js";
  const FOR_WHICH = process.env.FOR_WHICH;
  export default {
    mixins: [lan_name],
    data() {
      return {
        //能够选的值
        all_lan_list: [],
        //默认选中的语言
        lan_selection: "",
        enable_lan_selection: [],
        loading: false,
        use_lan_list: [], // 可用的语言
        enable_lan_list: [], // 禁用的语言
        lan_show: [],
        disabled: true,
        closedWithoutOperation: null, // 长时间未操作暂停视频(0关,1开)
        videoSettings: null, // 视频设置(0默认,1自定义)
        viewingTime: null, //视频观看时长设置(5~120分钟)
        noBackgroundPlay: null, //不可背景播放(0关,1开)
        customViewTime: null, //存放自定义设置的时间
        videoControlSwitch: null, // 视频流量管控
        chatRoomSwitch: null, // 聊天室
        allDataList: [],
        pullMsgRate: null
      };
    },
    props: {
      detailObj: {
        type: Object,
        default() {
          return {};
        },
      },
    },
    mounted() {
      this.init();
    },
    methods: {
      //获取商户当前视频控制的状态
    init(){
       let params = {}
       api_merchant.get_Merchant_querySystemSwitch(params).then((res)=>{
          let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          let data = this.$lodash.get(res, "data.data");
          this.allDataList = data
          console.log(res,'--datares')
          if(code == '0000000'){
            this.videoControlSwitch = this.$lodash.find(data,{configKey:'videoControlSwitch'}).configValue
            this.chatRoomSwitch = this.$lodash.find(data,{configKey:'chatRoomSwitch'}).configValue
            this.pullMsgRate = this.$lodash.find(data,{configKey:'chatRoomSwitch'}).pullMsgRate
            this.chatRoomSwitch = Number(this.chatRoomSwitch)
          }else{
             console.error(msg)
          }  
        }).catch(err=>{
             console.error(err)
        })
    },
      // 商户操作提交参数开关状态
      handle_submit() {
        this.loading = true;
        let params = {
            id: 4,
            configKey: "chatRoomSwitch",
            configValue: this.chatRoomSwitch,
            pullMsgRate: this.pullMsgRate
        };
  
        api_merchant
          .get_Merchant_updateSystemSwitch(params)
          .then((res) => {
            let code = this.$lodash.get(res, "data.code");
            let msg = this.$lodash.get(res, "data.msg");
            if (code == "0000000") {
              this.loading = false;
              this.$message.success(msg);
              if (this.src_internal) {
                this.$emit("handleCloseLanDefault");
              }
            } else {
              this.loading = false;
              this.$message.error(msg);
              console.error(msg);
            }
          })
          .catch((err) => {
            this.loading = false;
            this.$message.error(err);
          });
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  </style>
  