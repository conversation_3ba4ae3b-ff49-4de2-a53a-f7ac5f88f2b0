/*
 * @Desc: 
 * @Date: 2020-01-27 15:42:23
 * @Author:Nice
 * @FilePath: /src/components/common/bettinguser/internal/config/despconfig.js
 */
import { i18n } from 'src/boot/i18n';
const position = 'center'
let table_title =  i18n.t('internal.merchant.bettinguser.despconfig_1')
export const despconfig_1 = [
    {//单关
      title:table_title[0],
      dataIndex: "sportId",
      key: "sportId",
     
      width: 150,
      align: position
    },
  {//单注赔付限额
    title:table_title[1],
    dataIndex: "singleNoteClaimLimit",
    key: "singleNoteClaimLimit",
  
    width: 150,
    align: position
  },
  {//单日赔付限额
    title:table_title[2],
    dataIndex: "singleGameClaimLimit",
    key: "singleGameClaimLimit",
   
    width: 160,
    align: position
  },
];
let table_title_2 = i18n.t('internal.merchant.bettinguser.despconfig_2')
export const despconfig_2 = [
    {//单关
      title: table_title_2[0],
      dataIndex: "sportId",
      key: "sportId",
     
      width: 150,
      align: position
    },
  {//单注赔付限额
    title: table_title_2[1],
    dataIndex: "singleNoteClaimLimit",
    key: "singleNoteClaimLimit",
   
    width: 150,
    align: position
  },
  {//单日赔付限额
    title: table_title_2[2],
    dataIndex: "singleGameClaimLimit",
    key: "singleGameClaimLimit",
   
    width: 160,
    align: position
  },
];

