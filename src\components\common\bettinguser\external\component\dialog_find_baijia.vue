<!--
 * @FilePath: /src/components/common/bettinguser/external/component/dialog_find_abnormal.vue
 * @Description: 账户中心-投注用户管理(百家赔分布 弹窗) 
-->
<template>

<div
      style=" max-width:710px; overflow: hidden"
      class="text-panda-text-7 sidebox "
  >
      <q-card class="bg-white text-black">
          <q-card-section class="no-padding">
              <div class=" row line-height-40px fs14 bg-panda-dialog text-panda-text-7 pr10x " >     
                  <!-- 百家赔分布 -->
                  <div  class="pl20x fw_600 col q-pr-md"> {{ $t("external.account.user_risk_control.label39") }}  
                    
                    
                    <a-tooltip placement="bottomLeft">
                      <template slot="title">
                        <!-- <span>白家赔分布</span> -->
                        <span>{{ $t("external.account.user_risk_control.label52") }}</span>
                      </template>
                      <a-icon type="question-circle"  />
                    </a-tooltip></div>
                  <div class="col-1 justify-end">
                      <q-space></q-space>
                      <q-btn class="mr5x text-panda-dialog-close" icon="close" v-close-popup />
                  </div>
              </div>
          </q-card-section>
          <q-separator></q-separator>
          <div class="ml20x mt10x mb10x">{{ $t("external.account.user_risk_control.label40") }}</div>
          
          <q-separator></q-separator>
          <div class="">
        <div class="pr" style=" min-height: 600px;"  >

            <div class=" ml10x" >
              <div style="display: flex;" class="mt10x">
                 <div class="ml10x mr20x">{{ $t("external.account.user_risk_control.label41") }} {{detailObj.userName}} </div>
                <div class="mr20x">{{ $t("external.account.user_risk_control.label42") }}  {{detailObj.userId}}</div>
            </div>
                <div
                id="top2"
                style="min-width: 1600px; overflow-x: hidden"
                class=" row
                    pb10x
                    pt10x
                    border-radius-4px
                "
                >
             

                <!-- 用户ID/用户名 -->
                <div class="append-handle-btn-input ml10x" style="line-height: 30px;">
                  {{ $t("external.account.user_risk_control.label51",[init_day]) }} 
                </div>
              
    
                <!-- 日期选择 -->
                <!-- 
                说明：
                disabledDate：不可选择的日期
                :allowClear="false"    不显示清除日期 x
                -->
                <div class="ml20x w-200">
                    <a-range-picker
                    :allowClear="false"
                    @change="on_change"
                    :value="[moment(searchForm.startDate,'YYYY-MM-DD'), moment(searchForm.endDate, 'YYYY-MM-DD')]"
                    format="YYYY-MM-DD"
                    />
                </div>
    
                
                <!-- 查询 -->
                <div class="append-handle-btn-input pl10x height-30px line-height-30px" >
                    <a-button
                    type="primary"
                    @click="handle_serarch"
                    >
                    {{ $t('internal.user_abnormal_list.label5') }}
                    </a-button>
                </div>
    
    

                <q-space />
                </div>
                <div class="pr">
                  <div class="ml10x mb10x" > {{ $t("external.account.user_risk_control.label43") }} {{marketOddsSum}}</div>
                <!--  表格数据 
                columns 表格头部配置
                dataSource 内容数据
                showHeader  没数据不显示表头
                -->
                <a-table
                    ref="TableInfo"
                    class="pl10x pr10x expanded"
                    :columns="columns"
                    :dataSource="tabledata"
                    :pagination="false"
                    :loading="tabledata_loading"
                    size="middle"
                    :rowKey="(record) => record.id"

                >
                 <!-- 区分分布 -->
                <span slot="marketOddsType" slot-scope="text, record">
                  {{marketOddsType_label[text]}}
                </span>
                <!-- 占比 -->
               <span slot="ratio" slot-scope="text, record">
               {{toFixedTwo(record.ratio*100)}}%
               </span>
                </a-table>
                <!-- <div v-if="marketOddsIcon==1" class="text-red mt10x"><a-icon  type="exclamation-circle" class="text-red" />此用户高于市场赔率的注单比例>=50%</div> -->
    
                </div>
            </div>
    
        </div>

              
          </div>
        
      </q-card>
  </div>




  </template>
  <script>
  import { i18n } from "src/boot/i18n";
  import { mapGetters } from "vuex";
  import { dialog_find_baijia_config as internal_tablecolumns_config } from "src/components/common/bettinguser/external/config/dialog_find_baijia_config.js"//账户中心-异常用户名单(对外) & 数据中心—异常用户名单(对内)----列表配置共用

  import { api_merchant  ,api_account} from "src/api/index.js";
  import { handleCopy } from "src/util/module/common.js";
  import financesorter from "src/mixins/internal/module/financesorter.js";
  import commonmixin from "src/mixins/internal/common/commontoolmixin.js";
  import dataCenterMixin from "src/components/common/bet_slip/internal/mixin/datacentertablemixin.js";
  import financesorter_external from "src/mixins/external/module/financesorter.js"; // pagination 分页
  import commonmixin_external from "src/mixins/external/common/commontoolmixin.js"; // 格式化请求参数delete_empty_property_with_exclude
  import moment from "moment";
  
  const FOR_WHICH = process.env.FOR_WHICH;
  const src_internal = FOR_WHICH == "internal";
  let mixins_custom = [
    commonmixin,
    financesorter,
    dataCenterMixin
  ];
  if (FOR_WHICH == "external") {
    mixins_custom = [
      commonmixin_external,
      financesorter_external,
    ];
  }
  export default {
    name: "checkToolsUser",
    mixins: [...mixins_custom],
    components: {
    },
    props:['detailObj'],
    data () {
      return {
        //高于市场赔率>=50
        marketOddsIcon:'',
        //总数
        marketOddsSum:0,
        marketOddsType_label:{
          1: i18n.t("external.account.user_risk_control.label47"),
          2: i18n.t("external.account.user_risk_control.label48"),
          3: i18n.t("external.account.user_risk_control.label49"),
          4: i18n.t("external.account.user_risk_control.label50")
        } ,
        tabledata_loading: false, //表格loading
        tabledata: [],   // 表格数据
        columns: [],   // 表格头部配置
        totalColumns: [],  // (总计配置)
        showDialogObj: {},
        init_day:35,
        searchForm: {
          startDate: moment(new Date().setDate(new Date().getDate()-1-35 )).format("YYYY-MM-DD"),      // 日开始时间
          endDate: moment(new Date().setDate(new Date().getDate()-1)).format("YYYY-MM-DD"),  // 日结束时间
          userId: "", // 用户ID
        },
        value: [],// 日期默认值
      };
    },
    computed: {
      ...mapGetters(["get_user_info"]),
    },
    created () {
      this.searchForm.userId = this.detailObj.userId
      // this.searchForm.userId = 	'510912487687500002'
      // 商户表头判断
      let params = { src_internal:src_internal };
      this.columns = internal_tablecolumns_config(params).filter((x) =>x)
      this.initTableData()
    },
    methods: {
      moment,
       toFixedTwo(num) {
        return parseFloat(num.toFixed(2));
      },
      handleCopy,
      // 组装数据
      compute_init_tabledata_params () {
        let isClick = this.$route.query.isClick
        let { userId, startDate, endDate } = this.searchForm;
      
        let params = {
          userId,
          startDate,
          endDate,
          removeFlag:1,
        };      
        return params;
      },
      // 请求数据
      initTableData () {
        let params = this.compute_init_tabledata_params();
        params = this.delete_empty_property_with_exclude(params)   // 此方法 值针对一层 或者 2层 结构
        let api_fn_name ="post_manage_baijia_display"
        if(!this.src_internal){
          api_fn_name="post_baijia_display";
        }
        this.tabledata_loading = true;
        api_merchant[api_fn_name](params).then(res => {
          this.tabledata_loading = false;
          let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          if (code == "200") {
            let arr = this.$lodash.get(res, "data.data") || [];
             this.tabledata = this.rebuild_tabledata_to_needed(arr);
            this.pagination.total = this.$lodash.get(res, "data.data.total") * 1 || 0;
          } else {
            this.$message.error(msg, 5)
            this.tabledata_loading = false;
          }
        });
      },
      /**
       * 向返回的表格数据中添加——index下标
       */
      rebuild_tabledata_to_needed(arr) {
        this.marketOddsSum=0
         arr.map((item, index) => {
          item._index =
            (this.pagination.current - 1) * this.pagination.pageSize + index + 1;
            this.marketOddsSum+=item.orderCount
        })
        return arr
          },
      // 查询
      handle_serarch(){      
        this.initTableData()
      },
  
      // 更新日期
      on_change (date, dateStrings) {
        if (dateStrings) {
          Object.assign(this.searchForm, {
            startDate: dateStrings[0],
            endDate: dateStrings[1]
          });
        this.init_day= this.DateDiff(dateStrings[0],dateStrings[1])
        }
      },
     /**  
* 计算两个日期之间的天数  
* @param {string} Date_end 结束日期  
* @param {string} Date_start 开始日期  
* @return {number} iDays间隔的天数  
*/ 
 DateDiff(Date_end, Date_start){ 
    let aDate, oDate1, oDate2, iDays;
    Date_end = Date_end.split(" "); //将时间以空格划分为两个数组  第一个数组是 2019-05-20 第二个数组是 00：00：00
    aDate = Date_end[0].split("-"); //获取第一个数组的值
    oDate1 = new Date(aDate[0] , aDate[1] ,aDate[2]); //将前半个数组以-拆分，每一个是一个数值
    Date_start = Date_start.split(" ");
    aDate = Date_start[0].split("-");
    oDate2 = new Date(aDate[0] , aDate[1] , aDate[2]);
    iDays = parseInt(Math.abs(oDate1 - oDate2) / 1000 / 60 / 60 / 24); //把相差的毫秒数转换为天数
    return iDays;
  }
  
  
    }
  };
  </script>
  