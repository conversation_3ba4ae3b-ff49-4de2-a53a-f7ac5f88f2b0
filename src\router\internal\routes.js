/*
 * @Date           : 2021-06-17 23:07:53
 * @FilePath: /src/router/internal/routes.js
 * @description    :
 */
const routes = [
  // {
  //   path: "/",
  //   component: () => import("src/pages/internal/login2/login.vue")
  // },
  {
    path: "/",
    redirect: {
      name: "login"
    },
    component: () => import( /* webpackChunkName: "login" */ "src/pages/login/internal/index.vue"),
    children: [{
        path: "login",
        name: "login",
        component: () => import( /* webpackChunkName: "login" */ "src/pages/login/internal/module/login.vue")
      },

      {
        path: "newpassword",
        name: "newpassword",
        component: () => import( /* webpackChunkName: "login" */ "src/pages/login/internal/module/newpassword.vue")
      },
      {
        path: "password",
        name: "password",
        component: () => import( /* webpackChunkName: "login" */ "src/pages/login/internal/module/password.vue")
      },
      {
        path: "no_auth",
        name: "no_auth",
        component: () => import( /* webpackChunkName: "login" */ "src/pages/internal/permission/index.vue")
      },
      { // 注单查询-百家赔
        path: "bet_slip_order_no",
        name: "bet_slip_order_no",
        component: () => import( /* webpackChunkName: "data_center" */ "src/components/common/bet_slip/index.vue")
      },
    ]
  },
  {
    path: "/main",
    name: "main_container",
    component: () => import("src/layouts/internal/MyLayout.vue"),
    children: [{ //  首页
        path: "home",
        name: "home",
        component: () => import( /* webpackChunkName: "home" */ "src/pages/internal/home/<USER>")
      },
      { //商户中心
        path: "merchant_centre",
        name: "merchant_centre",
        component: () => import( /* webpackChunkName: "merchant_centre" */ "src/pages/internal/merchant/index.vue"),
        children: [{ //商户中心/商户管理
            path: "merchantc",
            name: "merchantc",
            component: () => import( /* webpackChunkName: "merchant_centre" */ "src/pages/internal/merchant/merchantc/index.vue"),
          },

          // {
          //   path: "merchantc_edit",
          //   name: "merchantc_edit",
          //   component: () => import(/* webpackChunkName: "merchant_centre" */ "src/pages/internal/merchant/merchantc/edit.vue"),
          //   meta: {
          //     father_menu: ['merchantc']
          //   }
          // },
          // {
          //   path: "merchantc_update",
          //   name: "merchantc_update",
          //   component: () => import(/* webpackChunkName: "merchant_centre" */ "src/pages/internal/merchant/merchantc/update.vue"),
          //   meta: {
          //     father_menu: ['merchantc']
          //   }
          // },
          // {
          //   path: "merchantc_channel_edit",
          //   name: "merchantc_channel_edit",
          //   component: () => import(/* webpackChunkName: "merchant_centre" */ "src/pages/internal/merchant/merchantc/channelEdit.vue"),
          //   meta: {
          //     father_menu: ['merchantc']
          //   }
          // },
          // {
          //   path: "merchantc_channel_update",
          //   name: "merchantc_channel_update",
          //   component: () => import(/* webpackChunkName: "merchant_centre" */ "src/pages/internal/merchant/merchantc/channelUpdate.vue"),
          //   meta: {
          //     father_menu: ['merchantc']
          //   }
          // },
          // {
          //   path: "merchantc_second_edit",
          //   name: "merchantc_second_edit",
          //   component: () => import(/* webpackChunkName: "merchant_centre" */ "src/pages/internal/merchant/merchantc/secondEdit.vue"),
          //   meta: {
          //     father_menu: ['merchantc']
          //   }
          // },
          // {
          //   path: "merchantc_second_update",
          //   name: "merchantc_second_update",
          //   component: () => import(/* webpackChunkName: "merchant_centre" */ "src/pages/internal/merchant/merchantc/secondUpdate.vue"),
          //   meta: {
          //     father_menu: ['merchantc']
          //   }
          // },
          { //商户中心/商户组域名查询
            path: "merchant_group_domain_query",
            name: "merchant_group_domain_query",
            component: () => import( /* webpackChunkName: "merchant_centre" */ "src/pages/internal/merchant/merchant_group_domain_query/index.vue")
          },
          { //商户中心/证书管理
            path: "key_management",
            name: "key_management",
            component: () => import( /* webpackChunkName: "merchant_centre" */ "src/components/common/mykey/index.vue")
          },
          { //商户中心/投注用户管理
            path: "betting_user",
            name: "betting_user",
            component: () => import( /* webpackChunkName: "merchant_centre" */ "src/components/common/bettinguser/index.vue")
          },
          {//账户中心/投注用户管理/投注vip用户详情
            path: "user_vip_detail",
            name: "user_vip_detail",
            component: () => import(/* webpackChunkName: "account" */ "src/components/common/bettinguser/user_vip_detail.vue"),
            meta: {
              father_menu: ['betting']
            }
          },             
          { //商户中心/投注用户管理/投注用户详情
            path: "betting_user_detail",
            name: "betting_user_detail",

            component: () =>
              import( /* webpackChunkName: "merchant_centre" */ "src/components/common/bettinguser/user_detail.vue"),
            meta: {
              father_menu: ['betting_user']
            }
          },
          { //商户中心/投注用户白名单
            path: "white_betting_users",
            name: "white_betting_users",
            component: () => import( /* webpackChunkName: "merchant_centre" */ "src/components/common/bettinguser/index.vue")
          },
          { //商户中心/IP白名单管理
            path: "ip_white_list_management_page",
            name: "ip_white_list_management_page",
            component: () => import( /* webpackChunkName: "merchant_centre" */ "src/pages/internal/merchant/ip_white_list_management/index.vue"),
          },
          { //商户中心/代理商管理
            path: "agent_management",
            name: "agent_management",
            component: () => import( /* webpackChunkName: "merchant_centre" */ "src/pages/internal/merchant/agent_management/index.vue"),
          },
          { //商户中心/失效商户管理
            path: "invalid_management",
            name: "invalid_management",
            component: () => import( /* webpackChunkName: "merchant_centre" */ "src/pages/internal/merchant/invalid_management/index.vue"),
          },
          // { //商户中心/视频控制管理
          //   path: "video_control_management",
          //   name: "video_control_management",
          //   component: () => import( /* webpackChunkName: "merchant_centre" */ "src/pages/internal/merchant/video_control_management/index.vue")
          // },
          { //商户中心/系统级别开关
            path: "system_level_switch",
            name: "system_level_switch",
            component: () => import( /* webpackChunkName: "merchant_centre" */ "src/pages/internal/merchant/system_level_switch/index.vue")
          },
         

        ]
      },
      { //财务中心
        path: "finance",
        name: "finance",
        component: () => import( /* webpackChunkName: "finance" */ "src/pages/internal/finance/index.vue"),
        children: [{ //财务中心/清算管理
            path: "liquidation",
            name: "liquidation",
            component: () => import( /* webpackChunkName: "finance" */ "src/pages/internal/finance/liquidation/index.vue"),
          },
          { //财务中心/对账单
            path: "statements",
            name: "statements",
            component: () => import( /* webpackChunkName: "finance" */ "src/components/common/statements/index.vue"),
          },
          {//财务中心/对账工具
            path: "check_tools",
            name: "check_tools",
            component: () => import(/* webpackChunkName: "check_tools" */ "src/pages/internal/merchant/check_tools/index.vue"),
          },
          {//VIP注单下载
            path: "VIP_bet_download",
            name: "VIP_bet_download",
           component: () => import(/* webpackChunkName: "finance" */ "src/pages/finance/vipbet_download/index.vue"),
          },
            {//二次结算查询
              path: "secondary_settlement",
              name: "secondary_settlement",
             component: () => import(/* webpackChunkName: "finance" */ "src/components/common/secondary_settlement/index.vue"),
            },
            {//二次结算初次审核
              path: "second_settlement_initial_review",
              name: "second_settlement_initial_review",
             component: () => import(/* webpackChunkName: "finance" */ "src/pages/internal/second_settlement_initial_review/index.vue"),
            },
            {//二次结算初次审核--商户详情
              path: "second_settlement_initial_review_detail",
              name: "second_settlement_initial_review_detail",
             component: () => import(/* webpackChunkName: "finance" */ "src/pages/internal/second_settlement_initial_review_detail/index.vue"),
            },,
            {//二次结算初次审核--注单详情
              path: "second_settlement_initial_review_order_details",
              name: "second_settlement_initial_review_order_details",
             component: () => import(/* webpackChunkName: "finance" */ "src/pages/internal/second_settlement_initial_review_order_details/index.vue"),
            },
            {//二次结算二次审核
              path: "second_settlement_and_second_review",
              name: "second_settlement_and_second_review",
             component: () => import(/* webpackChunkName: "finance" */ "src/pages/internal/second_settlement_initial_review/index.vue"),
            },
            {//二次结算二次审核--商户详情
              path: "second_settlement_and_second_review_detail",
              name: "second_settlement_and_second_review_detail",
             component: () => import(/* webpackChunkName: "finance" */ "src/pages/internal/second_settlement_initial_review_detail/index.vue"),
            },
            {//二次结算二次审核--注单详情
              path: "second_settlement_and_second_review_order_details",
              name: "second_settlement_and_second_review_order_details",
             component: () => import(/* webpackChunkName: "finance" */ "src/pages/internal/second_settlement_initial_review_order_details/index.vue"),
            },
            {//赔付注单
              path: "payout_bets",
              name: "payout_bets",
              component: () => import(/* webpackChunkName: "finance" */ "src/pages/internal/payout_bets/index.vue"),
            },
            {//赔付注单--商户详情
              path: "payout_bets_detail",
              name: "payout_bets_detail",
              component: () => import(/* webpackChunkName: "finance" */ "src/pages/internal/payout_bets/index.vue"),
            },
            {//赔付注单--注单详情
              path: "payout_bets_order_details",
              name: "payout_bets_order_details",
             component: () => import(/* webpackChunkName: "finance" */ "src/pages/internal/second_settlement_initial_review_order_details/index.vue"),
            },
        ]
      },

      { //数据中心
        path: "data_center",
        name: "data_center",
        component: () => import( /* webpackChunkName: "data_center" */ "src/pages/internal/data/index.vue"),
        children: [{ //赛事投注统计
            path: "match_bonus",
            name: "match_bonus",
            component: () => import( /* webpackChunkName: "data_center" */ "src/components/common/matchbonus/index.vue")
          },
          { // 即时赛事统计报表
            path: "match_statistic_statement",
            name: "match_statistic_statement",
            component: () => import( /* webpackChunkName: "data_center" */ "src/components/common/matchbonus/index.vue")
          },
          { //注单查询
            path: "bet_slip",
            name: "bet_slip",
            component: () => import( /* webpackChunkName: "data_center" */ "src/components/common/bet_slip/index.vue")
          },
          { // 注单查询-合买进度注单查询
            path: "check_the_progress_of_joint_purchase",
            name: "check_the_progress_of_joint_purchase",
            component: () => import( /* webpackChunkName: "data_center" */ "src/components/common/bet_slip/index.vue")
          },
          { // 注单查询-百家赔
            path: "bet_slip_order_no_page",
            name: "bet_slip_order_no_page",
            component: () => import( /* webpackChunkName: "data_center" */ "src/components/common/bet_slip/index.vue")
          },
          // { //注单查询(ES)
          //   path: "bet_slip_es",
          //   name: "bet_slip_es",
          //   component: () => import( /* webpackChunkName: "data_center" */ "src/components/common/bet_slip/index.vue")
          // },
          { //注单查询(ES实时流)
            path: "bet_slip_es_timely",
            name: "bet_slip_es_timely",
            component: () => import( /* webpackChunkName: "data_center" */ "src/components/common/bet_slip/index.vue")
          },
          { //交易&账变记录查询
            path: "record_query",
            name: "record_query",
            component: () => import( /* webpackChunkName: "data_center" */ "src/pages/internal/data/recordquery/index.vue")
          },
          { //商户注单统计
            path: "merchant_note",
            name: "merchant_note",
            component: () => import( /* webpackChunkName: "data_center" */ "src/components/common/statements/index.vue")
          },
          { //用户投注统计
            path: "users",
            name: "users",
            component: () => import( /* webpackChunkName: "data_center" */ "src/components/common/users/index.vue"),
            meta: {
              keepAlive: true // 需要被缓存
            }
          },
          { //数据中心/投注用户管理/投注用户详情
            path: "users_user_detail",
            name: "users_user_detail",
            component: () => import( /* webpackChunkName: "data_center" */ "src/pages/internal/data/userdetail/index.vue"),
            meta: {
              father_menu: ['users'],
            }
          },
          { //用户投注额度查询
            path: "user_betting_amount",
            name: "user_betting_amount",
            component: () => import( /* webpackChunkName: "user_betting_amount" */ "src/pages/internal/data/user_betting_amount/index.vue")
          },
          { //异常用户名单
            path: "user_abnormal_list",
            name: "user_abnormal_list",
            component: () => import( /* webpackChunkName: "user_abnormal_list" */ "src/pages/internal/data/user_abnormal_list/index.vue")
          },
          { //对账商户投注统计
            path: "reconciliation_merchant_betting_statement",
            name: "reconciliation_merchant_betting_statement",
            component: () => import( /* webpackChunkName: "user_abnormal_list" */ "src/components/common/statements/index.vue")
          },
          { //前端域名查询
            path: "front_end_domain_query",
            name: "front_end_domain_query",
            component: () => import( /* webpackChunkName: "user_abnormal_list" */ "src/pages/internal/data/front_end_domain_query/index.vue")
          },
          // { //API域名查询
          //   path: "API_domain_query",
          //   name: "API_domain_query",
          //   component: () => import( /* webpackChunkName: "user_abnormal_list" */ "src/pages/internal/data/API_domain_query/index.vue")
          // },
        ]
      },
      { //消息中心
        path: "message_center",
        name: "message_center",
        component: () => import( /* webpackChunkName: "message_center" */ "src/pages/internal/message/index.vue"),
        children: [{ //消息中心/公告栏
            path: "bulletin",
            name: "bulletin",
            component: () => import( /* webpackChunkName: "message_center" */ "src/components/common/bulletin/index.vue")
          },
          { //消息中心/公告栏/发布公告
            path: "bulletin_edit",
            name: "bulletin_edit",
            component: () => import( /* webpackChunkName: "message_center" */ "src/components/common/bulletin/internal/editNew.vue"),
            meta: {
              father_menu: ['bulletin']
            }
          },
          { //消息中心/公告栏/编辑公告
            path: "bulletin_update",
            name: "bulletin_update",
            component: () => import( /* webpackChunkName: "message_center" */ "src/components/common/bulletin/internal/editNew.vue"),
            meta: {
              father_menu: ['bulletin']
            }
          },
          { //消息中心/我的消息
            path: "mymessage",
            name: "mymessage",
            component: () => import( /* webpackChunkName: "message_center" */ "src/pages/message_center/mymessage/index.vue")
          },
        ]
      },
      // 测试页面
      // {
      //   path: "test",
      //   name: "test",
      //   component: () => import("src/pages/internal/test/ant/index.vue")

      // },
      { //设置中心
        path: "setting",
        name: "setting",
        component: () => import( /* webpackChunkName: "setting" */ "src/pages/internal/data/index.vue"),
        children: [{ //设置中心/商户等级设置
            path: "merchantlevel",
            name: "merchantlevel",
            component: () => import( /* webpackChunkName: "setting" */ "src/pages/internal/set/merchantlevel/index.vue")
          },
            {//设置中心/商户等级设置
              path: "merchantlevel2",
              name: "merchantlevel2",
              component: () => import(/* webpackChunkName: "setting" */ "src/pages/internal/set/merchantlevel_2/index.vue")
            },
          {//设置中心/平台费率设置
            path: "platformrate",
            name: "platformrate",
            component: () => import( /* webpackChunkName: "setting" */ "src/pages/internal/set/platformrate/index.vue")
          },
          { //设置中心/操作日志查询
            path: "logquery",
            name: "logquery",
            component: () => import( /* webpackChunkName: "setting" */ "src/pages/internal/set/logquery/index.vue")
          },
          { //设置中心/商户前端域名管理
            path: "domain_manager",
            name: "domain_manager",
            component: () => import( /* webpackChunkName: "setting" */ "src/pages/internal/set/domain_manager/index.vue")
          },
          { //设置中心/商户app域名管理
            path: "app_manager",
            name: "app_manager",
            component: () => import( /* webpackChunkName: "setting" */ "src/pages/internal/set/domain_manager/index.vue")
          },
          { //设置中心/脱敏设置
            path: "desensitization",
            name: "desensitization",
            component: () => import( /* webpackChunkName: "setting" */ "src/pages/internal/set/desensitization/index.vue")
          },
          // { //设置中心/全局接口配置
          //   path: "global_interface",
          //   name: "global_interface",
          //   component: () => import( /* webpackChunkName: "setting" */ "src/pages/internal/set/global_interface/index.vue")
          // },
          { //设置中心/公司代码设置
            path: "company_code_setting",
            name: "company_code_setting",
            component: () => import( /* webpackChunkName: "setting" */ "src/pages/internal/set/company_code_setting/index.vue")
          },
          { //设置中心/域名池管理
            path: "domain_pool_manage",
            name: "domain_pool_manage",
            component: () => import( /* webpackChunkName: "setting" */ "src/pages/internal/set/domain-pool-manage/index.vue")
          },

          { //设置中心/域名切换日志
            path: "domain_switch_log",
            name: "domain_switch_log",
            component: () => import( /* webpackChunkName: "setting" */ "src/pages/internal/set/domain-switch-log/index.vue")
          },
          { //域名设置/域名设置
            path: "domain_setting",
            name: "domain_setting",
            component: () => import( /* webpackChunkName: "setting" */ "src/pages/internal/set/domain-setting/index2.vue")
          },
          { //设置中心/异常IP域名池
            path: "abnormal_ip_domain_pool",
            name: "abnormal_ip_domain_pool",
            component: () => import( /* webpackChunkName: "setting" */ "src/pages/internal/set/abnormal-ip-domain-pool/index.vue")
          },
          { //设置中心/运营管理-运营活动设置
            path: "operational_activity_settings",
            name: "operational_activity_settings",
            meta: {
              annotation: "运营管理-运营活动设置",
            },
            component: () => import( /*webpackChunkName:'Task_settings'*/ "src/pages/internal/operations/operational_activity_settings/component/switch_page.vue")
          },
          { //设置中心/Api商户分组
            path: "operational_port_config",
            name: "operational_port_config",
            meta: {
              annotation: "设置中心-Api商户分组",
            },
            component: () => import( /*webpackChunkName:'domain_port_config'*/ "src/pages/internal/set/domain_port_config")
          },
          { //设置中心/数据同步工具
            path: "data_synchronization_tool",
            name: "data_synchronization_tool",
            component: () => import( /* webpackChunkName: "setting" */ "src/pages/internal/set/data_synchronization_tool/data_synchronization_tool.vue")
          },
          { //设置中心/域名查询
            path: "domain_query",
            name: "domain_query",
            component: () => import( /* webpackChunkName: "setting" */ "src/pages/internal/set/domain-query/index.vue")
          },
        ]
      },

      { //任务中心
        path: "my_task",
        name: "my_task",
        component: () => import( /* webpackChunkName: "setting" */ "src/pages/internal/my_task/Index.vue"),
        children: [{ //任务中心/我的导出任务
          path: "download_list",
          name: "download_list",
          component: () => import( /* webpackChunkName: "setting" */ "src/components/common/download_list/index.vue")
        }]
      },
      { //运营管理
        path: "operations_management",
        name: "operations_management",
        meta: {
          annotation: "运营管理"
        },

        component: () =>
          import(
            /*webpackChunkName:'operations'*/
            "src/pages/internal/operations/Index.vue"
          ),
        children: [
          // {
          //   path: "key_word", //热词
          //   name: "key_word",
          //   meta: {
          //     annotation: "运营管理-热词",
          //     need_refresh: 0,
          //     developer: 'nice'
          //   },

          //   component: () =>
          //     import(
          //       /*webpackChunkName:'event_sequencing'*/ "src/pages/internal/operations/key_word/Index.vue"
          //     )
          // },
          // {
          //   path: "league_menu", //运营管理-联赛菜单
          //   name: "league_menu",
          //   meta: {
          //     annotation: "运营管理-联赛菜单",
          //     need_refresh: 0,
          //     developer: 'nice'
          //   },

          //   component: () =>
          //     import(
          //       /*webpackChunkName:'league_menu'*/ "src/pages/internal/operations/league_menu/Index.vue"
          //     )
          // },
          // {
          //   path: "menu_settings", //运营管理-搜索设置
          //   name: "menu_settings",
          //   meta: {
          //     annotation: "运营管理-搜索设置",
          //     need_refresh: 0,
          //     developer: 'nice'
          //   },

          //   component: () =>
          //     import(
          //       /*webpackChunkName:'menu_settings'*/ "src/pages/internal/operations/menu_settings/Index.vue"
          //     )
          // },
          { //运营管理-运营位
            path: "operation_site",
            name: "operation_site",
            meta: {
              annotation: "运营管理-运营位",
              need_refresh: 0,
              developer: 'rank'
            },
            component: () =>
              import(
                /*webpackChunkName:'menu_settings'*/
                "src/pages/internal/operations/operation_site/Index.vue"
              )
          },
          { //运营管理-用户年报管理
            path: "user_annual_report",
            name: "user_annual_report",
            meta: {
              annotation: "运营管理-用户年报管理",
            },
            component: () =>
              import(
                /*webpackChunkName:'Task_settings'*/
                "src/pages/internal/operations/user_annual_report/Index.vue"
              )
          },
          {
            path: "operation_site_update",
            name: "operation_site_update",
            component: () => import( /* webpackChunkName: "message_center" */ "src/pages/internal/operations/operation_site/component/dialogEdit.vue"),
            meta: {
              father_menu: ['operation_site']
            }
          },
          { //运营管理-任务设置
            path: "task_settings",
            name: "task_settings",
            meta: {
              annotation: "运营管理-任务设置",
            },
            component: () =>
              import(
                /*webpackChunkName:'Task_settings'*/
                "src/pages/internal/operations/task_settings/Index.vue"
              )
          },
          { //运营管理-活动投注统计
            path: "activity_betting_statistics",
            name: "activity_betting_statistics",
            meta: {
              annotation: "运营管理-活动投注统计",
            },
            component: () =>
              import(
                /*webpackChunkName:'Task_settings'*/
                "src/pages/internal/operations/activity_betting_statistics/switch_page.vue"
              )
          },
          { //运营管理-VIP用户总览
            path: "vip_user_overview",
            name: "vip_user_overview",
            meta: {
              annotation: "运营管理-VIP用户总览",
            },
            component: () =>
              // src\components\common\vip_user_overview
              import(
                "src/components/common/vip_user_overview/index.vue"
              )
          },
          { //运营管理-VIP商户设置
            path: "vip_merchant_settings",
            name: "vip_merchant_settings",
            meta: {
              annotation: "运营管理-VIP商户设置",
            },
            component: () =>
              // "src/pages/internal/operations/vip_merchant_settings/index.vue"
              import(
                "src/components/common/vip_merchant_settings/index.vue"
              )
          },              
          { //运营管理-盲盒领取记录
            path: "blind_box_collection_record",
            name: "blind_box_collection_record",
            meta: {
              annotation: "运营管理-盲盒领取记录",
            },
            component: () =>
              import(
                /*webpackChunkName:'Task_settings'*/
                "src/pages/internal/operations/blind_box_collection_record/switch_page.vue"
              )
          },
          { //运营管理-盲盒奖品设置
            path: "blind_box_setting_background",
            name: "blind_box_setting_background",
            meta: {
              annotation: "运营管理-盲盒奖品设置",
            },
            component: () =>
              import(
                /*webpackChunkName:'Task_settings'*/
                "src/pages/internal/operations/blind_box_setting_background/switch_page.vue"
              )
          },
          { //运营管理-奖券派发记录
            path: "lottery_distribution_record",
            name: "lottery_distribution_record",
            meta: {
              annotation: "运营管理-奖券派发记录",
            },

            component: () =>
              import(
                /*webpackChunkName:'Task_settings'*/
                "src/pages/internal/operations/lottery_distribution_record/switch_page.vue"
              )
          },
          { //运营管理-资源配置
            path: "resource_configuration",
            name: "resource_configuration",
            meta: {
              annotation: "运营管理-资源配置",
            },

            component: () =>
              import(
                /*webpackChunkName:'Task_settings'*/
                "src/pages/internal/operations/resource_configuration_v2/Index.vue"
              )
          },
          { //运营管理-广告位管理
            path: "advertising_space",
            name: "advertising_space",
            meta: {
              annotation: "运营管理-广告位管理",
            },
            component: () =>
              import(
                /*webpackChunkName:'Task_settings'*/
                "src/pages/internal/operations/advertising_space/switch_page.vue"
              )
          },
          { //运营管理-游戏记录
            path: "game_record",
            name: "game_record",
            meta: {
              annotation: "运营管理-游戏记录",
            },

            component: () =>
              import(
                /*webpackChunkName:'Task_settings'*/
                "src/pages/internal/operations/game_record/switch_page.vue"
              )
          },
          { //运营管理-游戏配置
            path: "game_configuration",
            name: "game_configuration",
            meta: {
              annotation: "运营管理-游戏配置",
            },

            component: () =>
              import(
                /*webpackChunkName:'Task_settings'*/
                "src/pages/internal/operations/game_configuration/Index.vue"
              )
          },
          { //运营管理-奖券管理
            path: "ticket_management",
            name: "ticket_management",
            meta: {
              annotation: "运营管理-奖券管理",
            },

            component: () =>
              import(
                /*webpackChunkName:'Task_settings'*/
                "src/pages/internal/operations/ticket_management/Index.vue"
              )
          },
          { //运营管理-道具管理
            path: "props_management",
            name: "props_management",
            meta: {
              annotation: "运营管理-道具管理",
            },

            component: () =>
              import(
                /*webpackChunkName:'Task_settings'*/
                "src/pages/internal/operations/props_management/Index.vue"
              )
          },
          { //运营管理-活动账变记录
            path: "activity_account_change_record",
            name: "activity_account_change_record",
            meta: {
              annotation: "运营管理-活动账变记录",
            },

            component: () =>
              import(
                /*webpackChunkName:'Task_settings'*/
                "src/pages/internal/operations/activity_account_change_record/Index.vue"
              )
          },
          { //运营管理-活动管理
            path: "activity_manage",
            name: "activity_manage",
            meta: {
              annotation: "运营管理-活动管理",
            },

            component: () =>
              import(
                /*webpackChunkName:'Task_settings'*/
                "src/pages/internal/operations/activity_manage/Index.vue"
              )
          },
          { //运营管理-联赛模板
            path: "league_template",
            name: "league_template",
            meta: {
              annotation: "运营管理-活动管理",
            },

            component: () =>
              import(
                /*webpackChunkName:'Task_settings'*/
                "src/pages/internal/operations/league_template/Index.vue"
              )
          },
          // { //运营管理-游戏记录
          //   path: "game_record",
          //   name: "game_record",
          //   meta: {
          //     annotation: "运营管理-游戏记录",
          //   },

          //   component: () =>
          //     import(
          //       /*webpackChunkName:'Task_settings'*/
          //       "src/pages/internal/operations/game_record/switch_page.vue"
          //     )
          // },
          // { //运营管理-游戏配置
          //   path: "game_configuration",
          //   name: "game_configuration",
          //   meta: {
          //     annotation: "运营管理-游戏配置",
          //   },

          //   component: () =>
          //     import(
          //       /*webpackChunkName:'Task_settings'*/
          //       "src/pages/internal/operations/game_configuration/Index.vue"
          //     )
          // },
          // { //运营管理-奖券管理
          //   path: "ticket_management",
          //   name: "ticket_management",
          //   meta: {
          //     annotation: "运营管理-奖券管理",
          //   },

          //   component: () =>
          //     import(
          //       /*webpackChunkName:'Task_settings'*/
          //       "src/pages/internal/operations/ticket_management/Index.vue"
          //     )
          // },
          // { //运营管理-道具管理
          //   path: "props_management",
          //   name: "props_management",
          //   meta: {
          //     annotation: "运营管理-道具管理",
          //   },

          //   component: () =>
          //     import(
          //       /*webpackChunkName:'Task_settings'*/
          //       "src/pages/internal/operations/props_management/Index.vue"
          //     )
          // },
          // { //运营管理-活动账变记录
          //   path: "activity_account_change_record",
          //   name: "activity_account_change_record",
          //   meta: {
          //     annotation: "运营管理-活动账变记录",
          //   },

          //   component: () =>
          //     import(
          //       /*webpackChunkName:'Task_settings'*/
          //       "src/pages/internal/operations/activity_account_change_record/Index.vue"
          //     )
          // },
        ]
      },
      { //赛事文章
        path: "match_article",
        name: "match_article",
        meta: {
          annotation: "赛事文章"
        },
        component: () => import( /*webpackChunkName:'match_article'*/ "src/pages/internal/match_article/Index.vue"),
        children: [
          { //采集管理
            path: "acquisition_manager",
            name: "acquisition_manager",
            meta: {
              annotation: "采集管理",
            },
            component: () => import( /*webpackChunkName:'match_article'*/ "src/pages/internal/match_article/acquisition_manager/index.vue")
          },
          { //文章数据
            path: "article_data",
            name: "article_data",
            meta: {
              annotation: "文章数据",
            },
            component: () => import( /*webpackChunkName:'match_article'*/ "src/pages/internal/match_article/article_data/index.vue")
          },
          { //文章管理
            path: "article_manager",
            name: "article_manager",
            meta: {
              annotation: "文章管理",
            },
            component: () => import( /*webpackChunkName:'match_article'*/ "src/pages/internal/match_article/article_manager/index.vue")
          },
          { //标签管理
            path: "tag_management",
            name: "tag_management",
            meta: {
              annotation: "标签管理",
            },
            component: () => import( /*webpackChunkName:'match_article'*/ "src/pages/internal/match_article/tag_management/Index.vue")
          },
          { //作者管理
            path: "author_management",
            name: "author_management",
            meta: {
              annotation: "作者管理",
            },
            component: () => import( /*webpackChunkName:'match_article'*/ "src/pages/internal/match_article/author_management/index.vue")
          },
        ]
      },
      {
        path: "chat_match_live",
        name: "chat_match_live",
        meta: {
          annotation: "赛事直播",
        },
        component: () => import(/*webpackChunkName:'match_live'*/ "src/pages/internal/match_live/Index.vue"),
        children: [
          {
            //主播管理
            path: "anchor_manage",
            name: "anchor_manage",
            meta: {
              annotation: "主播管理",
            },
            component: () => import( /*webpackChunkName:'anchor_manage'*/ "src/pages/internal/match_live/anchor_manage/index.vue"),
          },
          {
            //直播管理
            path: "live_manage",
            name: "live_manage",
            meta: {
              annotation: "直播管理",
            },
            component: () => import(/*webpackChunkName:'live_manage'*/ "src/pages/internal/match_live/live_manage/index.vue"),
          },
          {
            //内容管理
            path: "content_manage",
            name: "content_manage",
            meta: {
              annotation: "内容管理",
            },
            component: () => import(/*webpackChunkName:'content_manage'*/ "src/pages/internal/match_live/content_manage/index.vue"),
          },
        ],
      },
      {//风控查询
        path: "risk_control_query",
        name: "risk_control_query",
        meta: {
          annotation: "风控查询",
        },
        component: () => import(/*webpackChunkName:'risk_control_query'*/ "src/components/common/risk_control_query/Index.vue"),
        children: [
          {
            //危险联赛池管理
            path: "dangerous_league_pool",
            name: "dangerous_league_pool",
            meta: {
              annotation: "危险联赛池管理",
            },
            component: () => import( /*webpackChunkName:'dangerous_league_pool'*/ "src/components/common/risk_control_query/dangerous_league_pool/index.vue"),
          },
          {
            //危险球队池管理
            path: "dangerous_team_pool",
            name: "dangerous_team_pool",
            meta: {
              annotation: "危险球队池管理",
            },
            component: () => import( /*webpackChunkName:'dangerous_team_pool'*/ "src/components/common/risk_control_query/dangerous_team_pool/index.vue"),
          },
          {
            //出货单管理
            path: "ship_order_management",
            name: "ship_order_management",
            meta: {
              annotation: "出货单管理",
            },
            component: () => import( /*webpackChunkName:'ship_order_management'*/ "src/components/common/risk_control_query/ship_order_management/index.vue"),
          },
        ],
      },
    ]
  }
];

// Always leave this as last one
if (process.env.mOde !== "ssr") {
  routes.push({
    path: "*",
    component: () => import( /* webpackChunkName: "login" */ "src/pages/internal/error/error404.vue")
  });
}

export default routes;
