"use strict";function _interopDefault(e){return e&&"object"==typeof e&&"default"in e?e.default:e}var constants=require('./constants'),utils=require('./utils'),utilsLite=require("utils-lite");require("echarts/lib/chart/pie");var Core=_interopDefault(require('./core')),_extends=Object.assign||function(e){for(var i=1;i<arguments.length;i++){var t=arguments[i];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},pieRadius=100,ringRadius=[80,100],roseRingRadius=[20,100],pieOffsetY=200;function getPieSeries(e){var i=e.innerRows,t=e.dataType,n=e.percentShow,r=e.dimension,o=e.metrics,a=e.radius,l=e.offsetY,s=e.selectedMode,u=e.hoverAnimation,d=e.digit,c=e.roseType,m=e.label,p=e.level,f=e.limitShowNum,h=e.isRing,v=e.labelLine,g=e.itemStyle,w=[],y={},b=[];p?(p.forEach(function(e,i){e.forEach(function(e){utils.setArrayValue(y,e,i)})}),i.forEach(function(e){var i=y[e[r]];i&&i.length&&i.forEach(function(i){utils.setArrayValue(b,i,e)})})):b.push(i);var R={type:"pie",selectedMode:s,hoverAnimation:u,roseType:c,center:["50%",l]},S=b.length;if(b.forEach(function(e,i){var l=_extends({data:[]},R),s=a/S;if(i){var u=s+a/(2*S)*(2*i-1),c=u+a/(2*S);l.radius=[u,c]}else l.radius=h?a:s;S>1&&0===i&&(l.label={normal:{position:"inner"}}),m&&(l.label=m),v&&(l.labelLine=v),g&&(l.itemStyle=g),n&&(l.label={normal:{show:!0,position:S>1&&0===i?"inner":"outside",formatter:function(e){var i=[];return i.push(e.name+":"),i.push(utils.getFormated(e.value,t,d)),i.push("("+e.percent+"%)"),i.join(" ")}}}),l.data=e.map(function(e){return{name:e[r],value:e[o]}}),w.push(l)}),f&&f<w[0].data.length){var L=w[0].data,N=0;L.slice(f,L.length).forEach(function(e){N+=e.value}),w[0].data=L.slice(0,f),w[0].data.push({name:"其他",value:N})}return w}function getPieLegend(e){var i=e.innerRows,t=e.dimension,n=e.legendLimit,r=e.legendName,o=e.level,a=e.limitShowNum,l=[],s=[];if(o)o.forEach(function(e){e.forEach(function(e){s.push(e)})}),l=s;else if(a&&a<i.length){for(var u=0;u<a;u++)l.push(i[u][t]);l.push("其他")}else l=i.map(function(e){return e[t]});return!!l.length&&{data:l,show:l.length<n,formatter:function(e){return null!=r[e]?r[e]:e}}}function getPieTooltip(e){var i=e.dataType,t=e.innerRows,n=e.limitShowNum,r=e.digit,o=e.metrics,a=e.dimension,l=0,s=t.map(function(e){return l+=e[o],{name:e[a],value:e[o]}}).slice(n,t.length);return{formatter:function(e){var t=[];return t.push(constants.itemPoint(e.color)),n&&"其他"===e.name?(t.push("其他:"),s.forEach(function(e){var n=e.name,o=e.value,a=utils.getFormated(o/l,"percent");t.push("<br>"+n+":"),t.push(utils.getFormated(o,i,r)),t.push("("+a+")")})):(t.push(e.name+":"),t.push(utils.getFormated(e.value,i,r)),t.push("("+e.percent+"%)")),t.join(" ")}}}var pie$1=function(e,i,t,n,r){var o=utilsLite.cloneDeep(i),a=t.dataType,l=void 0===a?"normal":a,s=t.percentShow,u=t.dimension,d=void 0===u?e[0]:u,c=t.metrics,m=void 0===c?e[1]:c,p=t.roseType,f=void 0!==p&&p,h=t.radius,v=void 0===h?r?f?roseRingRadius:ringRadius:pieRadius:h,g=t.offsetY,w=void 0===g?pieOffsetY:g,y=t.legendLimit,b=void 0===y?30:y,R=t.selectedMode,S=void 0!==R&&R,L=t.hoverAnimation,N=void 0===L||L,T=t.digit,E=void 0===T?2:T,P=t.legendName,x=void 0===P?{}:P,A=t.label,j=void 0!==A&&A,q=t.level,O=void 0!==q&&q,V=t.limitShowNum,Y=void 0===V?0:V,_=t.labelLine,F=t.itemStyle,M=n.tooltipVisible,D=n.legendVisible;return Y&&o.sort(function(e,i){return i[m]-e[m]}),{series:getPieSeries({innerRows:o,dataType:l,percentShow:s,dimension:d,metrics:m,radius:v,offsetY:w,selectedMode:S,hoverAnimation:N,digit:E,roseType:f,label:j,level:O,legendName:x,limitShowNum:Y,isRing:r,labelLine:_,itemStyle:F}),legend:D&&getPieLegend({innerRows:o,dimension:d,legendLimit:b,legendName:x,level:O,limitShowNum:Y}),tooltip:M&&getPieTooltip({dataType:l,innerRows:o,limitShowNum:Y,digit:E,metrics:m,dimension:d})}},index=_extends({},Core,{name:"VePie",data:function(){return this.chartHandler=pie$1,{}}});module.exports=index;
