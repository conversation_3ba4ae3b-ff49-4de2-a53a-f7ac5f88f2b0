import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_10;

//搜索设置列表
export const post_v1_searchSettings_getSettings = (params, url = "/v1/searchSettings/getSettings") =>
  axios.post(`${prefix}${url}`, params);

//搜索设置添加
export const post_v1_searchSettings_addSetting = (params, url = "/v1/searchSettings/addSetting") =>
  axios.post(`${prefix}${url}`, params);

//搜索设置更新
export const post_v1_searchSettings_updateSetting = (params, url = "/v1/searchSettings/updateSetting") =>
  axios.post(`${prefix}${url}`, params);

//搜索设置删除
export const post_v1_searchSettings_deleteSetting = (params, url = "/v1/searchSettings/deleteSetting") =>
  axios.post(`${prefix}${url}`, params);

//搜索设置--获取赛事信息--热推词
export const post_v1_searchSettings_getMatchInfoByMid = (params, url = "/v1/searchSettings/getMatchInfoByMid") =>
  axios.post(`${prefix}${url}`, params);