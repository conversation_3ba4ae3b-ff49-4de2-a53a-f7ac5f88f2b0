.bet_slip{
::v-deep .ant-spin-nested-loading > div > .ant-spin {
  max-height: 660px;
  min-height: 660px;
}
::v-deep .ant-empty-normal {
  margin: 287px 0;
}
::v-deep .match-team-child .text-panda-text-light ::v-deep .show_toggle {
  position: absolute;
  right: 0;
}
::v-deep
  .ant-table-middle
  > .ant-table-content
  > .ant-table-scroll
  > .ant-table-header
  > table
  > .ant-table-thead
  > tr
  > th {
  background: #f4f5f8;
}
.center {
  width: 30px;
  border-left: 0;
  pointer-events: none;
  background-color: #fff;
}
.max {
  width: 100px;
  text-align: center;
  border-left: 0;
}
.min {
  width: 100px;
  text-align: center;
  border-right: 0;
  border-radius: 4px 0 0 4px;
}
::v-deep .leftdetail div {
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ellipsis {
  overflow: hidden;
  max-width: 235px;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.flipy {
  -moz-transform: scaleY(-1);
  -webkit-transform: scaleY(-1);
  -o-transform: scaleY(-1);
  transform: scaleY(-1);
  /* IE */
  filter: FlipV;
}
::v-deep .icon-tog {
  font-size: 16px;
}
.remarklist {
  width: 180px;
}
.item-content {
  // width: 250px;
}
.item-open {
  height: 60px;
  padding: 0 8px;
}
.tdpadding {
  padding: 0 8px;
}
::v-deep .ant-input-number {
  width: 110px;
}
.ellipsis {
  max-width: 230px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
::v-deep .ant-table-thead > tr > th .anticon-filter,
.ant-table-thead > tr > th .ant-table-filter-icon {
  right: 35px;
}
.hide {
  display: none;
}
.show {
  display: block;
}
::v-deep .ant-table-filter-dropdown {
  z-index: 100005;
}
::v-deep .ant-dropdown {
  z-index: 100006;
}
.text-over2 {
  display: inline-block;
  max-width: 110px;
  vertical-align: middle;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
::v-deep .ant-input-number {
  width: 90px;
}
::v-deep
  .ant-table-middle
  > .ant-table-content
  > .ant-table-scroll
  > .ant-table-header
  > table
  > .ant-table-thead
  > tr
  > th {
  background: #f4f5f8;
}
/*--------提前结算---------*/
::v-deep .ant-table-tbody .ant-table-row td.ant-table-row-expand-icon-cell {
  border: none !important;
  margin: 0;
  width: 0;
}
::v-deep .ant-table-tbody .ant-table-row td.ant-table-row-expand-icon-cell div {
  display: none;
}
::v-deep .ant-table-tbody .ant-table-expanded-row > td {
  padding: 0 !important;
  border: none !important;
}
::v-deep .ant-table-fixed .ant-table-thead th.ant-table-expand-icon-th {
  width: 0;
  padding: 0 !important;
  margin: 0;
}
.expandedRowRender-detail ::v-deep .ant-table-thead {
  display: none;
}
.currency-detail {
  cursor: pointer;
  color: #1990ff;
  padding: 0 4px;
  border: 1px solid #1990FF;
}
// 下注时区
.betting-time {
  display: flex;
  span{
    margin-right: 3px;
  }
  .bet-select{
    width:80px
  }
}
.betting-time-es {
  position: absolute;
  left: 310px;
  top:95px;
}
.betting-time-es-in {
  position: absolute;
  left: 500px;
  top:95px;
}

// 分页隐藏跳转
.bet-pagination-page-size {
  // 除了当前页都隐藏
  ::v-deep .ant-pagination-item:not(.ant-pagination-item-active) {
    display: none;
  }
  // 隐藏向前5页
  ::v-deep .ant-pagination-jump-next {
    display: none;
  }
  // 隐藏向后5页
  ::v-deep .ant-pagination-jump-prev {
    display: none;
  }
  // 隐藏跳转页
  ::v-deep .ant-pagination-options .ant-pagination-options-quick-jumper {
    display: none;
  }
}
::v-deep .ant-tabs-nav .ant-tabs-tab {
  font-size: 14px;
  font-weight: 600;
  color: #3c4551;
}
.center_content{
  justify-content: center;
  display: flex;
  align-items: center;
}
  .bet-normal-progress{
    width: 100px;
  }
  
  .progress-yellow{
    ::v-deep .ant-progress-bg{
      background: #fb8c0c;
    }
  }
  .text-expired{
    color: white;
    background: #d2d2d2;
  }
  .circle-text-red{
    color:#f5222d;

  }
  .circle-text-green{
    color:#55c420;
  }
  .circle-text-yellow{
    color:#fb8c0c;
  }
  .bet-normal-text-grey{
    color: #8a92a5;
  }
  
.yellow-circle{
  background: #fb8c0c;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  color: white;
  padding: 2px;
}
  ::v-deep tr[data-row-key^="tr_is_syndicate_child_"]{
    background:#fff7eb;
    display: none;
  }
  .hm-icon-square{
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    color: #515a6e;
    cursor: pointer;
  }
}