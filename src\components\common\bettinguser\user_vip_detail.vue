<template>
    <div class="full-width full-height">
        <div class="pl10x pt10x pb10x fs14" id="top1">
            <q-breadcrumbs separator="/" active-color="whiddte">
                <q-breadcrumbs-el :label="$t('internal.merchant.bettinguser.user_detail.bar')[0]"
                    class="panda-text-2" />
                <!-- :label="$t('internal.menujs')[34]" -->
                <q-breadcrumbs-el :label="$t('internal.merchant.bettinguser.user_detail.bar')[1]"
                    class="panda-text-2" />
                <q-breadcrumbs-el :label="$t('internal.vip_user_details.vip_details')"
                    class="panda-text-1 fs14" />
            </q-breadcrumbs>
        </div>
        <div class="bg-white 
            text-panda-text-dark
            bg-panda-bg-6
            pb10x
            border-radius-4
            ml10x
            mr10x
            ">
            <div class="row justify-start line-height-40px fs14 pl20x pr20x">
                <a-button type="primary" style="height: 30px; line-height: 16px" class="mt12x" @click="handle_back">{{
                    $t("internal.back") }}
                </a-button>
            </div>
            <div class="row line-height-40px fs14 pl20x pr20x">
                <div class="col-6">

                    <div class="row">
                        <span class="panda-text-2">
                            <!-- 用户名： -->
                            {{ $t("internal.merchant.bettinguser.user_detail.userName") }}:
                        </span>
                        <span class="panda-text-1">{{ user_vip_datetail.userName }}</span>
                    </div>
                    <div class="row">
                        <span class="panda-text-2">
                            <!-- 用户ID： -->
                            {{ $t("internal.merchant.bettinguser.user_detail.userId") }}:
                        </span>
                        <span class="panda-text-1">{{ user_vip_datetail.userId }}</span>
                    </div>
                    <div class="row">
                        <span class="panda-text-2">
                            <!-- 所属商户: -->
                            {{ $t("internal.merchant.bettinguser.user_detail.merchantName") }}:
                        </span>
                        <span class="panda-text-1">{{ user_vip_datetail.merchantName }}</span>
                    </div>
                    <div class="row">
                        <span class="panda-text-2">
                            <!-- 商户编号： -->
                            {{$t('internal.table_title.label72')}}:
                        </span>
                        <span class="panda-text-1">{{ user_vip_datetail.merchantCode }}</span>
                    </div>
                </div>
                <div class="col-6">
                    <div class="row justify-end pr20x ">
                        <span class="panda-text-2 fs30">
                            VIP{{ user_vip_datetail.userVipLevel }} - <span v-show="user_vip_datetail.vipType ==1">{{$t('internal.vip_user_overview.system')}}</span><span v-show="user_vip_datetail.vipType ==2">{{$t('internal.vip_user_overview.customize')}}</span>
                        </span>
                    </div>
                    <div class="row justify-end pr20x ">
                        <span class="panda-text-2 fs30">{{ user_vip_datetail.userVipValue ? user_vip_datetail.userVipValue : '-'
                            }} / {{ user_vip_datetail.nextRankVipValue ? user_vip_datetail.nextRankVipValue : '-'
                            }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!--表格区域-->
        <div clss="      
            row     
            text-panda-text-dark
            bg-panda-bg-6
            pb10x
            border-radius-4
            ml10x
            mr10x">
            <div class="row line-height-40px fs14 pl20x pr20x  mt10x mr10x ml10x bg-panda-bg-6">
                <!-- 日期选择 -->
                <div class="ml10x w-200">
                    <a-range-picker :allowClear="true"  @change="on_month_change" :disabledDate="disabledDate"  />
                    <!-- <a-range-picker
                        :placeholder="[$t('internal.data.user.index.placeholder.dateType')[0], $t('internal.data.user.index.placeholder.dateType')[1]]"
                        format="YYYY-MM-DD" :open="isopen" @openChange="handleOpenChange" @panelChange="handlePanelChange"
                        @change="on_month_change" /> -->

                </div>
                <div class="ml10x w-200">
                    <!-- //账变记录  -->
                    <!-- :userDetail="userDetail" -->
                    <a-select class="ml10x w-200" :allowClear="true" autocomplete v-model="searchForm.changeType" :placeholder="$t('internal.vip_user_details.account_change_type')">
                        <a-select-option :value="item.key" v-for="(item, index) in account_change_type" :key="item.key">
                            {{ item.label_name }}
                            <q-tooltip anchor="bottom middle" self="top middle">
                                {{ item.label_name }}
                            </q-tooltip>
                        </a-select-option>
                    </a-select>
                </div>
                <div class="ml10x w-200" v-if="searchForm.changeType ==0 || searchForm.changeType ==1" >
                    <!-- //账变记录  -->
                    <!-- :userDetail="userDetail" -->
                    <a-select class="ml10x w-200" v-if="searchForm.changeType == 0" :allowClear="true"  autocomplete v-model="searchForm.eventType" :placeholder="$t('internal.vip_user_details.event_type')">
                        <a-select-option :value="item.key" v-for="(item, index) in user_life_cycle_data"
                            :key="item.key">
                            {{ item.label_name }}
                            <q-tooltip anchor="bottom middle" self="top middle">
                                {{ item.label_name }}
                            </q-tooltip>
                        </a-select-option>
                    </a-select>
                    <a-select class="ml10x w-200" v-if="searchForm.changeType == 1" :allowClear="true"  autocomplete v-model="searchForm.eventType" :placeholder="$t('internal.vip_user_details.event_type')">
                        <a-select-option :value="item.key" v-for="(item, index) in user_behavior_events_data"
                            :key="item.key">
                            {{ item.label_name }}
                            <q-tooltip anchor="bottom middle" self="top middle">
                                {{ item.label_name }}
                            </q-tooltip>
                        </a-select-option>
                    </a-select>                    
                </div>
                <div class="ml20x ">
                    <a-button @click="getTableData" type="primary">{{ $t('internal.search') }}</a-button>
                </div>
                <div class="ml10x">
                    <!-- :disabled="tabledata.length == 0" -->
                    <a-button type="primary"  @click="handle_export_excel_for_vip_info()">{{ $t('internal.export_text')
                        }}</a-button>
                </div>
            </div>
            <div class="mr10x ml10x bg-panda-bg-6 pr20x ">
                <a-table :columns="columns" :dataSource="tabledata" :pagination="pagination"
                    :loading="tabledata_loading" @change="handleTableChange" size="middle"
                    :key="'id' + tabledata.length" rowKey="id" :scroll="{ x: 1500 }">

                    <span slot="createDt" slot-scope="text, record">
                        <span>{{ ge_format_date(record.createDt, "-") }}</span>
                    </span>
                    <span slot="changeType" slot-scope="text, record">
                        {{ get_change_type_value(text) }}
                    </span>
                    <span slot="eventType" slot-scope="text, record">
                        <span v-if="record.changeType ==2 || record.changeType ==3">-</span>
                        <span v-if="record.changeType ==1">{{ get_user_behavior_events_data_value(text) }}</span>
                        <span v-if="record.changeType ==0">{{ get_user_life_cycle_data_value(text) }}</span>
                        
                    </span>
                    <!-- 帐变成长值 -->
                    <span slot="growthPoint" slot-scope="text, record">
                        <span v-if="record.changeType ==2 || record.changeType ==3">-</span>
                        <span v-else>{{ text | filterNumberFormat}}</span>
                        
                    </span>
                    <!-- 帐变前-->
                    <span slot="lastPoint" slot-scope="text, record">
                        <span v-if="record.changeType ==2"> VIP{{ record.vipLevel - 1  }}</span>
                        <span v-else-if="record.changeType ==3">VIP{{ record.vipLevel + 1  }}</span>
                        <span v-else> {{ text | filterNumberFormat}}</span>
                    </span>
                    <!-- 帐变后 -->
                    <span slot="point" slot-scope="text, record">
                        <span v-if="record.changeType ==2">VIP{{ record.vipLevel  }}</span>
                        <span v-else-if="record.changeType ==3">VIP{{ record.vipLevel  }}</span>
                        <span v-else>{{ text | filterNumberFormat}}</span>
                        
                    </span>                                        
                    <!-- 操作 -->
                    <span slot="option" slot-scope="text, record">
                            <span>{{$t('internal.vip_user_overview.system')}}</span>
                        
                    </span>     
                </a-table>
            </div>

        </div>
    <!-- 报表下载弹窗 -->
    <q-dialog
      v-model="exportExcelShow"
      persistent
      transition-show="scale"
      transition-hide="scale"
    >
      <dialog-excel :export_param="export_param"></dialog-excel>
    </q-dialog>
    </div>
</template>

<script>
import { i18n } from 'src/boot/i18n';
import { user_behavior_events, user_life_cycle, conditional_variables,columns_vip_detail } from "src/components/common/vip_user_overview/config/config.js";;
import { api_operate, api_merchant,api_data } from "src/api/index.js";
import moment from "moment";
import export_file_mixin from "src/mixins/module/export_file.mixin.js"
import dialogExcel from "src/components/common/dialog/dialogExcel.vue";
import commonmixin from "src/mixins/external/common/commontoolmixin.js";
export default {
    mixins: [commonmixin],
    components: {
        dialogExcel,
    },
    data() {
        return {
            //导出报表参数
            export_param: {},
            exportExcelShow: false,            
            isopen: false,
            tabledata: [
                // {
                //     "uid": "uid",
                //     "createDt": Date.now(),
                //     // Date.now()
                //     "changeType": "2",
                //     "eventType": "1",

                // },
            ], // 表格数据
            pagination: {
                pageSize: 10, // 每页显示条数
                showSizeChanger: true, // 是否可以改变每页显示条数
                pageSizeOptions: ['10', '20', '50', '100'], // 可选的每页显示条数
                showQuickJumper: true, // 是否可以快速跳转到指定页
                // showTotal: (total) => `共 ${total} 条数据`, // 显示总条数
                showTotal: (total) =>  i18n.t("external.account.dialogSelectLevel.label7")+` ${total} `+i18n.t("external.account.dialogSelectLevel.label8") ,// 显示总条数
                current: 1, // 当前页数
                total: 0, // 总条数
            },
            searchForm: {
                startDate: '',
                endDate: '',
                changeType: undefined,
                eventType: undefined,
            },
            columns: columns_vip_detail, // 表格配置
            tabledata_loading: false, // 表格loading            
            user_life_cycle_data: user_life_cycle,
            user_behavior_events_data:user_behavior_events,
            
            user_vip_datetail: {},
            account_change_type: [

                {
                    // 定级
                    key: '0',
                    label_name: i18n.t('internal.vip_user_details.leveling'),
                },
                {
                    // 日常成长值
                    key: '1',
                    label_name: i18n.t('internal.vip_user_details.daily_growth_value'),
                },
                {
                    // 升级
                    key: '2',
                    label_name: i18n.t('internal.vip_user_details.upgrade'),
                },
                {
                    // 降级
                    key: '3',
                    label_name: i18n.t('internal.vip_user_details.downgrade'),
                },
                {
                    // 配置变更
                    // Configuration changes
                    key: '4',
                    label_name: i18n.t('internal.vip_user_details.configuration_changes')
                },
            ],
            map_account_change_type: {
            },
            // 定级时间类型事件
            map_user_life_cycle_data: {
            },
            // 日常成长事件类型事件
            map_user_behavior_events_data: {

            }            

        }
    },
    mounted() {
        this.init_map_type_data();
        this.getTableData();
        this.init_session_record();

    },
    methods: {
        handle_back() {
            this.$router.go(-1);
        },
        /**
         * @description 初始化用户基本信息
         * @return {undefined} undefined
         */
        async init_session_record() {
            let record = this.$q.sessionStorage.getItem("record");
            this.userDetail = record;
            let get_order_user_getUserDetail = this.src_internal
                ? api_merchant.get_order_user_getUserDetail
                : api_data.get_admin_userReport_getUserInfo;
            let res = await get_order_user_getUserDetail({
                uid: record.userId,
                currencyCode: this.src_internal
                    ? record["record"]
                    : record["currencyCode"],
            });
            // console.warn(res)
            if (res.data.code == "0000000") {
                // this.userDetail = { ...record, ...res.data.data };
                this.user_vip_datetail = res.data.data;

            }
        },

        // 获取列表数据
        getTableData() {
            // vip_config_listVipInfoStats
            let params = {
                // uidList: this.$route.query.userId,
                uid: this.$route.query.userId,
                startDate: this.searchForm.startDate,
                endDate: this.searchForm.endDate,
                changeType: this.searchForm.changeType,
                eventType: this.searchForm.eventType,
                page: this.pagination.current,
                pageSize: this.pagination.pageSize,
            }
            api_operate.vip_config_listVipInfoChangeHistory(params).then(res => {
                let { code, msg } = res.data;
                if (code == "200") {
                    this.tabledata = res.data.data;
                    this.pagination.total = res.data.totalCount;
                    this.pagination.current = res.data.currentPage;
                    this.$message.success(msg);
                } else {
                    this.$message.error(msg);
                }
            });
        },
        handleTableChange(pagination) {
            this.pagination.current = pagination.current;
            this.pagination.pageSize = pagination.pageSize;
            this.getTableData();
        },
        handlePanelChange() { },
        handleOpenChange() { },
        on_month_change(date, dateStrings) {
            if (dateStrings) {
                Object.assign(this.searchForm, {
                    startDate: dateStrings[0],
                    endDate: dateStrings[1]
                });
            }
        },
        changeTypeToString(value) {

        },
        init_map_type_data() {
            this.map_account_change_type = this.change_arr_To_map(this.account_change_type);
            this.map_user_life_cycle_data = this.change_arr_To_map(this.user_life_cycle_data);
            this.map_user_behavior_events_data = this.change_arr_To_map(this.user_behavior_events_data);
        },
        change_arr_To_map(arr) {
            // 将数组转成对象
            const obj = {};
            for (let i = 0; i < arr.length; i++) {
                const item = arr[i];

                obj[item.key+''] = item.label_name;
            }
            return obj;
        },
        get_change_type_value(value) {
            return this.map_account_change_type[value] || '未知类型';
        },
        get_user_life_cycle_data_value(value) {
            return this.map_user_life_cycle_data[value] || '未知类型';
        },
        get_user_behavior_events_data_value(value) {
            return this.map_user_behavior_events_data[value] || '未知类型';
        },
        ge_format_date(date) {
            return moment(date).format('YYYY-MM-DD HH:mm:ss');
        },
        compute_init_tabledata_params() {
            return {
                uidList: this.$route.query.userId,
                uid: this.$route.query.uid,
                startDate: this.searchForm.startDate,
                endDate: this.searchForm.endDate,
                changeType: this.searchForm.changeType,
                eventType: this.searchForm.eventType,
                // page: this.pagination.current,
                // pageSize: this.pagination.pageSize,
            }
        },
        disabledDate(date) {
            const currentDate = moment();
            const oneYearAgo = moment().subtract(1, 'year');
            return date.isBefore(oneYearAgo) || date.isAfter(currentDate);
        },
        // 导出
        handle_export_excel_for_vip_info() {
            // this.pagination.total > 0
            if (this.pagination.total > 0) {
                let params = this.compute_init_tabledata_params();
                let { userId, appId } = this.user_vip_datetail;
                let res = "";
                let url = "/vip/config/exportListVipInfo";
                // let url = e.key == 1 ? "/order/user/exportTicketList" : "/order/user/exportTicketAccountHistoryList";
                //账变导出
                params = {
                    ...params,
                    url,
                    // "user-id": userId,
                    // "app-id": appId,
                };
                params = this.delete_empty_property_with_exclude(params);
                Object.assign(params, { url: url });
                this.export_param = params;
                this.exportExcelShow = true;
            } else {
                // this.handle_error();
            }
        }
        // 
    }
}
</script>