/*  
 * @path: src/api/internal/module/activity_manage/index.js
 * @Descripttion: 运营管理-活动管理 烟花
 * @Author: 
 * @Date: 2024-11-16 09:48:04
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_4; //yewu9

// 列表查询接口
export const post_querySelectActivity_list = (
  params,
  url = "/manage/fireworksActivity/selectActivity"
) => axios.post(`${prefix}${url}`, params);

// 列表新增接口
export const post_add_createActivity = (
  params,
  url = "/manage/fireworksActivity/createActivity"
) => axios.post(`${prefix}${url}`, params);

// 列表详情接口
export const post_detail_queryActivityDetail = (
  params,
  url = "/manage/fireworksActivity/queryActivityDetail"
) => axios.post(`${prefix}${url}`, params);

// 列表编辑接口
export const post_edit_editActivity = (
  params,
  url = "/manage/fireworksActivity/editActivity"
) => axios.post(`${prefix}${url}`, params);

// 列表活动状态开关修改接口
export const post_updateActivityStatus = (
  params,
  url = "/manage/fireworksActivity/updateActivityStatus"
) => axios.post(`${prefix}${url}`, params);

// 列表删除接口
export const post_delete_delActivity = (
  params,
  url = "/manage/fireworksActivity/delActivity"
) => axios.post(`${prefix}${url}`, params);

// 商户后台开关 烟花开关 查询接口
export const post_getMerchantSwitch = (
  params,
  url = "/manage/switchConfig/getMerchantSwitch"
) => axios.post(`${prefix}${url}`, params);

// 商户后台开关 烟花开关 修改接口
export const post_setMerchantSwitch = (
  params,
  url = "/manage/switchConfig/setMerchantSwitch"
) => axios.post(`${prefix}${url}`, params);