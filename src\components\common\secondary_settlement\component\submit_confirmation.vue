<!--
 * @FilePath:
 * @Description: 二次结算查询-提交赔付和提交审批弹框
-->
<template>
  <div style="width: 400px; height: auto; overflow: hidden;  min-width:400px; " class="text-panda-text-7 bg-white">
    <div class="bg-white text-black">
      <q-card-section class="no-padding">
        <div class="row line-height-40px fs14 text-panda-text-7 ">
        
          <q-space />
          <q-btn class="mr5x text-panda-dialog-close" icon="close" v-close-popup />
        </div>
      </q-card-section>
      <!-- <q-separator></q-separator> -->
      <div>
        <div style="display: flex; justify-content: center;" class=" text-weight-bolder">
          <div style="display: flex; align-items: center;">
            <a-icon
            type="exclamation-circle"
            style="color: orange; cursor: pointer; width: 30px; font-size: 20px"
          />
          <span v-if="unable_to_submit">
            {{ $t("internal.finance.secondarysettlement.label25") }}</span>
          <span v-else-if="audit_type==1">
            {{ $t("internal.finance.secondarysettlement.label23") }}</span>
          <span v-else-if="audit_type==2">
            {{ $t("internal.finance.secondarysettlement.label24") }}</span>
          </div>
        </div>
        <q-card-actions>   
          <div class="row justify-center q-gutter-x-md full-width mt20x mb10x" v-if="unable_to_submit">
            <q-btn 
            class="panda-btn-primary-dense bg-primary mr5x"
            style="width: 68px; height: 30px"
            :label="$t('internal.sure')" color="primary" v-close-popup />
          </div>
          <div class="row justify-center q-gutter-x-md full-width mt20x mb10x" v-else>
            <!--取消-->
            <q-btn 
            class="panda-btn-primary-dense bg-primary mr5x"
            style="width: 68px; height: 30px"
            :label="$t('internal.cancel')" color="primary" v-close-popup />
            <!--确定-->
            <q-btn 
            class="panda-btn-primary-dense bg-primary mr5x"
            style="width: 68px; height: 30px"
            :label="$t('internal.sure')"  color="primary" @click="submit_handle"/>
          </div>
        </q-card-actions>
      </div>
      </div>
  </div>
</template>


<script>
import moment from "moment";
import { handleCopy } from "src/util/module/common.js";
import { api_finance } from "src/api/index.js";
import { mapGetters, mapActions } from "vuex";
export default {
  name: "reason_for_rejection_dialog",

  props: {
    detailObj: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      audit_type:0,
      // 获取审核值
      audit_value:{},
      //不能提交
      unable_to_submit:false,
      order_list:[]
    };
  },
  created() {
    this.get_audit_value()
  },
  mounted() {
  },
  methods: {
    ...mapActions(['set_compensation_record_count']),
    moment,
    // 获取审核值
    get_audit_value(){
      api_finance.
      settlement_inquiry.get_getSystemParamConfig()
        .then(res => {
          let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          if (code == "0000000") {
         let data =  this.$lodash.get(res, "data.data")
         if(data){
          this.audit_value=data.systemParams
     
        //提交-初次审核
        this.initial_review_logic()
         }
          } else {
            this.$message.error(msg, 5);
          }
        });
    },
    /*
    * 初次审核逻辑
    */
    initial_review_logic(){

      if(this.audit_value.secSettInitAuditAmt!=''){
        // 设置了初次审核
        if(parseFloat(this.detailObj.secSettleCompTotal)<=parseFloat(this.audit_value.secSettInitAuditAmt)){
          //赔付
          this.audit_type=1
        }else{
          //审核
          this.audit_type=2
        }
      }else if(this.audit_value.secSettSecondAuditAmt!=''&&this.audit_value.secSettSecondAuditFlag=='1'){
        // 设置了二次审核并开启
        // 设置了初次审核
        if(parseFloat(this.detailObj.secSettleCompTotal)<=parseFloat(this.audit_value.secSettSecondAuditAmt)){
          //赔付
          this.audit_type=1
        }else{
          //审核
          this.audit_type=2
        }
      }else{
        //初次和二次都没有设置，直接走赔付
      this.audit_type=1
      }
    },
    /*
    * 查询是否存在待定数据
    */
    query_pending(){
    let params=this.detailObj.list_by_query
    params.compensateType=0
      api_finance.post_secondary_settlemen_pageList(params)
        .then(res => {
          let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          if (code == "0000000") {
         let arr =  this.$lodash.get(res, "data.data")
         
         if(arr?.pageInfo?.records){
          //含有待定，不能提交
      this.unable_to_submit=true
            }
         else{
          //提交审核
          this.submit_data()
         }
          } else {
            this.$message.error(msg, 5);
          }
        });
    },
    /*
    * 最终提交
    */
    submit_data(){
      let {
        startTime,
      }=this.detailObj.list_by_query
      let jssjtime=moment(startTime).format("YYYYMMDD") 
      let params={
        settleTimes:[jssjtime],
        pageSource:0,
      }
      // if( this.audit_type==2){
      //   //进入初次待处理
      //   params.pageSource=1
      // }else{
      //   //审核通过
      //   params.pageSource=3
      // }
      api_finance.
      settlement_inquiry.submitSettleAudit(params)
        .then(res => {
          let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          if (code == "0000000") {
            this.$message.success( msg);
            this.set_compensation_record_count('refresh')
            this.$emit('hidden_submit_confirmation')
          } else {
            this.$message.error(msg, 5);
          }
        });
    },
    submit_handle(){
      this.query_pending()
    },
  },
};
</script>
<style>
</style>