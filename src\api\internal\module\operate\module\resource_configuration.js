/*
 * @FilePath: d:/projects/new-merchant-management/src/api/internal/module/operate/module/resource_configuration.js
 * @Description:
 */

import axios from "src/api/common/axioswarpper.js";
let prefix_1 = process.env.API_PREFIX_4;
//资源配置-查询
export const selectOne_detail = (
  params,
  url = "/manage/festival/selectList"
) => axios.get(`${prefix_1}${url}`, {
  params: {
    ...params
  }
});
//资源配置保存
export const festival_save = (
  params,
  url = "/manage/festival/save"
) => axios.post(`${prefix_1}${url}`, params);


//banner启用接口
export const festival_enable = (
  params,
  url = "/manage/festival/enable"
) => axios.post(`${prefix_1}${url}`, params, {
  type: 1
});

//资源配置-平台下拉框接口
export const get_platform = (
  url = "/manage/aggregateFestival/getPlatform"
) => axios.get(`${prefix_1}${url}`);



//资源列表接口
export const get_resource_list = (
  params,
  url = "/manage/aggregateFestival/list"
) => axios.post(`${prefix_1}${url}`, params, {
  type: 1
});

// 删除资源
export const del_resource = (
  params,
  url = "/manage/aggregateFestival/del"
) => axios.post(`${prefix_1}${url}`, params, {
  type: 1
});

//资源配置-节庆资源开关列表
export const get_stateList = (
  url = "/manage/aggregateFestival/stateList"
) => axios.get(`${prefix_1}${url}`);

//资源配置-节庆资源开关
export const enableBatch = (
  params,
  url = "/manage/aggregateFestival/enableBatch"
) => axios.post(`${prefix_1}${url}`, params, {
  type: 1
});

//资源配置-新增资源
export const addResource = (
  params,
  url = "/manage/aggregateFestival/add"
) => axios.post(`${prefix_1}${url}`, params);

//资源配置-更新资源
export const updateResource = (
  params,
  url = "/manage/aggregateFestival/update"
) => axios.post(`${prefix_1}${url}`, params);
