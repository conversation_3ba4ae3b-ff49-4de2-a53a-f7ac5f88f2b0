
<!--
 * @FilePath:
 * @Description: 财务中心-赔付注单
 -->
 <template>
  <div class="full-width full-height" >
    <div class="pl10x pt10x pb10x" id="top1">
      <q-breadcrumbs
        separator="/"
        active-color="whiddte"
        class="panda-text-2"
      >
        <q-breadcrumbs-el
        :label="$t('internal.finance.liquidation.index.bar')[0]"
        />
          <q-breadcrumbs-el
          v-if="month_type&&month_type!=0"
          :label="set_cards[month_type]?.title1"
          class="fw_600 panda-text-1"
        />
        <q-breadcrumbs-el
        v-else
          :label="$t('internal.menujs.payout_bets')"
          class="fw_600 panda-text-1"
        />
      
      </q-breadcrumbs>
    </div>
    <div
      class="bg-panda-bg-6 shadow-3 border-radius-4px mt0x mr10x mb10x ml10x overflow-hidden"
    >
    <div 
    v-if="page_type==1"
    class="row q-col-gutter-md q-pa-md">
      <!-- 卡片 -->
      <div class="col-md-3 col-sm-6 col-xs-12" v-for="(card, index) in set_cards" :key="index">
        <q-card class="bg-white">
          <q-card-section>
            <div  :class="index!=0?'cursor-pointer':''" @click="go_merchant_detail(index)">
            <div class="text-subtitle2 text-center ">
              {{ card.title1 }}
             <span v-if="index!=0">
              <img
              src="~src/assets/internal/img/amount_1.png"
              width="18"
              class="img-mr5x"
            />
             </span>
            </div>
            <div class="text-subtitle2 text-center font-weight-bold">{{ card.total_money }}</div>
          </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
      <!-- 搜索条件   数据中心/前端域名查询    -->
      <div
        id="top2"
        style="min-width: 1600px; overflow-x: hidden"
        class="
          row
          line-height-30px
          items-center
          text-panda-text-7
          bg-panda-bg-6
          pb10x
          pt10x
          border-radius-4px
        "
      >
        <!-- 请输入查询域名,支持模糊搜索-->
      <div class="no-left append-handle-btn-input ml20x position-relative" >
      
        <div  class="append-handle-btn-input ml10x row position-relative w-200">
          <a-input
            :placeholder="$t('internal.finance.secondarysettlement.label5')"
            autocomplete="off"
             v-model="searchForm.merchantName"
            allowClear
          >
           </a-input>
        </div>
      </div>
      <!-- 请输入查询域名,支持模糊搜索-->
    <div class="no-left append-handle-btn-input ml20x position-relative" >
    
      <div  class="append-handle-btn-input ml10x row position-relative w-200">
        <a-input
          :placeholder="$t('internal.finance.secondarysettlement.label26')"
          autocomplete="off"
           v-model="searchForm.merchantCode"
          allowClear
        >
         </a-input>
      </div>
    </div>
      
          <!-- 请选择二次结算原因-->
          <div class="append-handle-btn-input position-relative ml10">
            <a-select 
            autocomplete
              style="width: 200px"
              v-model="searchForm.remark"
            >
            <a-select-option
            :value="''"
             >{{$t('internal.finance.secondary_settlement_list_title') }}</a-select-option
           > 
               <a-select-option
               :value="item"
                 v-for="(item, index) in secondary_settlement_list"
                :key="index"
                >{{ item }}</a-select-option
              > 
            </a-select>
          </div>
          <!-- 日期选择 -->
           
          <!-- v-if="page_type==1"  -->
          <div
          class="append-handle-btn-input ml10x w-270">
            {{$t("internal.second_settlement_initial_review.text__34")}}
            <a-range-picker 
            format="YYYY-MM-DD"
            :show-time="true"
            showToday 
            :allowClear="false"
            :disabled="page_type!=1"
             :value="[
             moment(searchForm.startTime, 'YYYY-MM-DD'),
             moment(searchForm.endTime, 'YYYY-MM-DD'),
              ]"
              @change="on_change"
             />
          </div>
          <!--搜索-->
          <div class="append-handle-btn-input ml10x height-30px line-height-30px">
            <a-button type="primary" style="height: 30px; line-height: 30px" @click="initTableData">{{
              $t("internal.search")
            }}</a-button>
          </div>
        <q-space />
        <!--返回-->
        <div 
        v-if="page_type==2"
        class="append-handle-btn-input ml10x height-30px line-height-30px mr10x">
          <a-button type="primary" style="height: 30px; line-height: 30px" @click="handle_back">{{
            $t("internal.back")
          }}</a-button>
        </div>
      </div>
      <!-- 表格区域 -->
      <a-table
        bordered
        ref="TableInfo"
        class="pl10x pr10x expanded"
        :columns="columns"
        :dataSource="tabledata"
        :scroll="{ x: 1600, y: scrollHeight -(page_type==1?150:0) }"
        :pagination="false"
        :loading="tabledata_loading"
        size="middle"
        rowKey="_index"
      >   
       
        <div slot="remark" slot-scope="text, record" class="tdpadding">
          <!-- 二次结算原因 -->
          <div v-if="record.remark">
            {{ record.remark}}
          </div>
        </div>
      <div slot="applicationTime" slot-scope="text, record" class="tdpadding">
        {{moment(text).format('YYYY/MM/DD')}}
        </div>
      <!-- 赔偿金额 -->
      <div slot="changeTotalAmount" slot-scope="text, record" class="tdpadding">
        {{addCommas(text)}}
        </div>
          <!-- 操作 -->
          <div slot="action" slot-scope="text, record" class="tdpadding"> 
            <span  class="pl10x text-blue cursor-pointer" @click="go_detail(record)">
              {{ $t("internal.second_settlement_initial_review.text__15") }}
            </span>
           </div>
           <template slot="footer">
             <a-pagination
             v-if="tabledata.length>0"
             :total="pagination.total"
             :current="pagination.current"
             show-size-changer 
             show-quick-jumper
             :page-size-options="pagination.pageSizeOptions"
             :page-size="pagination.pageSize"
             :show-total="total => $t('internal.showTotal_text',[pagination.total])"
             @change="onChange"
             @showSizeChange="onShowSizeChange"
           />
         <div
             class="fs16 position-absolute line-height-24px"
             style="bottom: 10px; left: 25px;"
             v-if="tabledata.length > 0 "
           >
           <div>
            <span class="row ml10x" v-if="tabledata.length>0">
            
          <!-- 赔付总金额 -->
          <span class="pr10x">
            <span class="title-grey">{{ $t("internal.second_settlement_initial_review.text__12") }}:</span>
             <span class="fw_600 text-red">
              {{addCommas(order_totals.totalPayoutAmt)}}
        </span>
      </span>
           </span>
           
         </div>
       </div> 
            </template>
      </a-table>
    </div>
    
  </div>
</template>
<script>
import { i18n } from "src/boot/i18n";
import { mapGetters, mapActions } from "vuex";
import { api_finance } from "src/api/index.js";
import mixins from "src/mixins/internal/index.js";
import { tablecolumns_config } from "./config/config.js";
import moment from "moment";
import { getFirstDayOfMonth, getLastDayOfMonth ,getFirstDayOfLastMonth,getLastDayOfLastMonth} from "src/pages/internal/second_settlement_initial_review/this_month_or_last_month.js";

import { resetRecordCounts} from "src/pages/internal/payout_bets/module/resetRecordCounts.js";

export default {
  mixins: [...mixins],
  data() {
    return {
      set_cards: [
        { title1:i18n.t("internal.second_settlement_initial_review.text__35"), total_money: "" },
        { title1:i18n.t("internal.second_settlement_initial_review.text__36"), total_money: "" },
        { title1:i18n.t("internal.second_settlement_initial_review.text__37"), total_money: "" },
        { title1:i18n.t("internal.second_settlement_initial_review.text__38"), total_money: "" },
      ],
      //请选择二次结算原因
      secondary_settlement_list:i18n.t("internal.finance.secondary_settlement_list"),
      columns:tablecolumns_config,
      order_totals:{},//总额数据 
      //搜索条件
      searchForm: {
        startTime: "", //开始日期
        endTime: "", // 结束日期 
        remark: "", //二次结算原因/变动原因
        merchantName: "", // 商户名称
        // 商户编码
        merchantCode:"",
      },
      tabledata: [], // 表格数据
      tabledata_loading: false, //表格还没有加载过来的时候转圈圈
 
      show_rejection_confirm:false,
    };
  },
  watch:{
    month_type(){
      this.init_search_date()
      this.initTableData()
    },
    
    get_compensation_record_count(n){
      if(n!="refresh"&&n!==0){
        this.update_compensation_record_count()
      }
    }
  },
  computed: {
    ...mapGetters(['get_compensation_record_count']),
    page_type(){
      if(this.$route.name == "payout_bets"){
        //赔付注单
        return 1
      }else if(this.$route.name == "payout_bets_detail"){
        //商户详情
        return 2
      }
    },
    month_type(){
      return this.$route.query.month_type||0
    },
  },
  created() {
    this.init_search_date()
    this.tabledata_loading = true;
    this.initTableData();
    this.update_compensation_record_count()
  },
  methods: {
    ...mapActions(['set_compensation_record_count']),
    moment,
    resetRecordCounts,
    getFirstDayOfMonth, getLastDayOfMonth ,getFirstDayOfLastMonth,getLastDayOfLastMonth,
    update_compensation_record_count(){
      if(this.get_compensation_record_count!=0&&this.get_compensation_record_count!='refresh'){
      this.resetRecordCounts()
      this.set_compensation_record_count('no_refresh')
      }
    },
    init_search_date(){
      if(this.month_type==0){
      //今日
      this.searchForm.startTime=`${moment(new Date().setDate(new Date().getDate() )).format("YYYY-MM-DD")}`
      this.searchForm.endTime=`${moment(new Date().setDate(new Date().getDate() )).format("YYYY-MM-DD")}`
    }else if(this.month_type==1){
      //近7日
      this.searchForm.startTime=`${moment(new Date().setDate(new Date().getDate()-7 )).format("YYYY-MM-DD")}`
      this.searchForm.endTime=`${moment(new Date().setDate(new Date().getDate() )).format("YYYY-MM-DD")}`
    }else if(this.month_type==2){
      //本月
      this.searchForm.startTime=this.getFirstDayOfMonth(false)
      this.searchForm.endTime=this.getLastDayOfMonth(false) 
    }else if(this.month_type==3){
      //上月
      this.searchForm.startTime=this.getFirstDayOfLastMonth(false)
      this.searchForm.endTime=this.getLastDayOfLastMonth(false)
    }
    },
    // 添加千位符
    addCommas(num) {
      return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    // 日期选择
    on_change(dates, dateStrings) {
      if (!(dateStrings[0] && dateStrings[1])) {
        return false;
      }

      Object.assign(this.searchForm, {
        startTime: dateStrings[0],
        endTime: dateStrings[1],
      });
    },
    go_detail(record){
      this.$router.push({name: 'payout_bets_order_details', query: {month_type:this.month_type ,batchNo:record.batchNo}})
    },
    go_merchant_detail(index){
      if(index!=0){
        this.$router.push({name: 'payout_bets_detail', query: {month_type:index ,
          _t: Date.now() // 添加时间戳强制刷新
          }})
      }
    },
    handle_back() {
      this.$router.go(-1);
    },
    // 请求列表数据
    initTableData() {
      this.tabledata_loading = true;
      let params = this.compute_init_tabledata_params();
      params = this.delete_empty_property_with_exclude(params);
      api_finance.settlement_inquiry.getSummaryMerchantList(params).then(res => {
          let code = this.$lodash.get(res, "data.code");
          let msg = this.$lodash.get(res, "data.msg");
          this.tabledata_loading = false;
          if ( code && code == "0000000" ) {
            let arr = this.$lodash.get(res, "data.data") || [];
            let info_data=[]
            if(arr?.pageInfo?.records){
              info_data=arr?.pageInfo?.records
              this.order_totals.totalPayoutAmt=arr?.totalPayoutAmt
            }
            this.tabledata = this.rebuild_tabledata_to_needed(info_data);
            // 今日赔付金额
            this.set_cards[0].total_money=arr?.todayPayoutAmt
          // 近七日赔付金额
          this.set_cards[1].total_money=arr?.last7DaysPayoutAmt
          // 本月赔付金额
          this.set_cards[2].total_money=arr?.currentMonthPayoutAmt
          // 上月赔付金额
          this.set_cards[3].total_money=arr?.lastMonthPayoutAmt

            this.pagination.total =
            this.tabledata.length || 0;
          } else {
            this.$message.error(msg, 5);
          }
      });
    },
    
    rebuild_tabledata_to_needed(arr) {
      if(!arr || !arr.length) {
        return []
      }
      // 就散这一页数据 能取消的所有 注单的总数
      arr.map((item, index) => {

       item._index =
       (this.pagination.current - 1) * this.pagination.pageSize + index + 1;
      });
      return arr;

    },
    // 计算请求参数
    compute_init_tabledata_params() {
      let { pageSize, sort, orderBy } = this.pagination;
      let {
        remark, //二次结算原因/变动原因
        merchantName,
        merchantCode,
        startTime, //开始日期
        endTime, // 结束日期
      } = this.searchForm;
      let params= {
        remark, //二次结算原因/变动原因
        merchantName,
        merchantCode,
        "timeType": 4, //1.结算时间
        "approvalStatusList":[3],
        pageNum: this.pagination.current, //分页，查询第几页数据。
        pageSize, //分页，每页查询多少条，默认20条。可不传
      }
        //结算时间
        params.startTime=new Date(startTime+` 00:00:00`).getTime()
         params.endTime=new Date(endTime+` 23:59:59`).getTime()
      return params;
    },
    
  }
};
</script>
<style lang="scss" scoped>
</style>
