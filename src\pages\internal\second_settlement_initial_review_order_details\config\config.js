/*
 * @Author:nico
 * @FilePath: /src/components/common/secondary_settlement/config/config.js
 * @Description: 财务中心 / 二次结算查询 配置文件
 */

import { i18n } from "src/boot/i18n";
const POSITION = "left";
let table_title = i18n.t("internal.finance.secondarysettlement.config");
export const columns_by_timeType=(page_type)=>{

  const tablecolumns_config = [
    page_type==3?{}:{
      //全选
      width: 130,
      dataIndex: "all_select",
      key: "all_select",
      fixed: "left",
      align: "center",
      scopedSlots: {
        customRender: "all_select",
      },
      slots: {
        title: "all_select_header",
      },
    },
    {
      //序号
      title: table_title[0],
      width: 60,
      dataIndex: "_index",
      key: "_index",
      fixed: "left",
      align: "center",
    },
    {
      //用户ID
      title: table_title[1],
      width: 200,
      dataIndex: "uid",
      key: "uid",
      fixed: "left",
      align: POSITION,
      scopedSlots: { customRender: "uid" },
    },
    {
      //用户名
      title: table_title[2],
      dataIndex: "username",
      key: "username",
      width: 200,
      fixed:'left',
      align: POSITION,
      scopedSlots: { customRender: "username" },
    },
    {
      //注单号
      title: table_title[5],
      dataIndex: "orderNo",
      key: "orderNo",
      width: 200,
      align: POSITION,
      scopedSlots: { customRender: "orderNo" },
    },
    {
      //负余额
      title: table_title[6],
      dataIndex: "negativeAmount",
      key: "negativeAmount",
      width: 150,
      align: POSITION,
    },
    {
      //当前余额
      title: table_title[7],
      dataIndex: "amount",
      key: "amount",
      width: 150,
      align: POSITION,
    },
    {
      //赛事ID
      title: table_title[8],
      dataIndex: "matchId",
      key: "matchId",
      width: 150,
      align: POSITION,
      scopedSlots: { customRender: "matchId" },
    },
    {
      //对阵名
      title: table_title[9],
      dataIndex: "matchInfo",
      key: "matchInfo",
      width:200,
      align: POSITION,
    },
    {
      //联赛名称
      title: table_title[10],
      dataIndex: "matchName",
      key: "matchName",
      width: 200,
      align: POSITION,
    },
    {
      //玩法名称
      title: table_title[11],
      dataIndex: "playName",
      key: "playName",
      width: 150,
      align: POSITION,
    },
    {
      //投注项名称
      title: table_title[12],
      dataIndex: "playOptionName",
      key: "playOptionName",
      width: 150,
      align: POSITION,
    },
    {
      //开赛时间
      title: table_title[14],
      dataIndex: "beginTime",
      key: "beginTime",
      width: 200,
      align: POSITION,
    },
   {
      //二次结算原因
      title: page_type==3?table_title[21]: i18n.t("internal.second_settlement_initial_review.text__39"),
      dataIndex: "settleType",
      key: "settleType",
      width: 200,
      fixed: "right",
      align: POSITION,
      scopedSlots: { customRender: "settleType" },
    },
    page_type==3?{}:{
      //状态
      title: table_title[23],
      dataIndex: "approvalStatus",
      key: "approvalStatus",
      width: 200,
      fixed:'right',
      align: POSITION,
      scopedSlots: { customRender: "approvalStatus" },
    },
  {
      title: i18n.t("internal.second_settlement_initial_review.text__11"), // 操作
      key: "action",
      dataIndex: "action",
      fixed:'right',
      width: 170,
      align: POSITION,
      scopedSlots: {
        customRender: "action",
      },
    },
  ];
  return tablecolumns_config
}
