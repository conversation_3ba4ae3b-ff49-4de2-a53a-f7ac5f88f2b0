
/*
 * @Author: 
 * @Date: 2021-07-02 23:39:34
 * @Description:每日任务
 * @FilePath: /src/api/internal/module/operate/module/operational_activity_settings.js
 */
import axios from "src/api/common/axioswarpper.js";
let prefix_1 = process.env.API_PREFIX_4;
let prefix_2 = process.env.API_PREFIX_18;

//搜索每日任务列表
export const activityEntrance_query = (params, url = "/manage/activityEntrance/queryList") =>
  axios.post(`${prefix_1}${url}`, params);

  
//活动配置查询
export const activityEntrance_detail = (
  params,
  url = "/manage/activityEntrance/detail"
) => axios.get(`${prefix_1}${url}`, { params: { ...params } });

//活动配置保存
export const activityConfigUpdate = (params, url = "/manage/activityEntrance/activityConfigUpdate") =>
  axios.post(`${prefix_1}${url}`, params);

  //活动入口配置保存
  export const activityEntrance_save = (params, url = "/manage/activityEntrance/save") =>
    axios.post(`${prefix_1}${url}`, params);

  //活动维护设置保存
export const activityMaintain_save = (
  params,
  url = "/manage/activityEntrance/activityMaintain"
) =>  axios.post(`${prefix_1}${url}`, params);

  
  
//活动维护
export const querygetActivityMaintainList = (
  params,
  url = "/manage/activityEntrance/getActivityMaintain"
) => axios.get(`${prefix_1}${url}`, { params: { ...params } });

//活动入口设置查询
export const queryActivityEntranceList = (
  params,
  url = "/manage/activityEntrance/queryActivityEntranceList"
) => axios.get(`${prefix_1}${url}`, { params: { ...params } });
  
//一键开启设置查询
export const getActivityMerchantCode = (
  params,
  url = "/manage/activityEntrance/getActivityMerchantCode"
) => axios.get(`${prefix_1}${url}`, { params: { ...params } });


  //一键开启/关闭入口 
  export const onceOperationMerchantEntrance = (params, 
    url = "/manage/activityEntrance/onceOperationMerchantEntrance") =>
    axios.post(`${prefix_1}${url}`, params);

      //一键delete
      export const onceOperationMerchantDelete = (params, 
        url = "/manage/activityEntrance/onceOperationMerchantDelete") =>
        axios.post(`${prefix_1}${url}`, params);
  
//活动开关更新
export const activityEntrance_update = (
  params,
  url = "/manage/activityEntrance/update"
) => axios.get(`${prefix_1}${url}`, { params: { ...params } });
  
//活动时间查询
export const queryActivityTime = (
  params,
  url = "/manage/activityEntrance/queryActivityTime"
) => axios.get(`${prefix_1}${url}`, { params: { ...params } });
  
//活动时间设置
export const activityTimeUpdate = (params, url = "/manage/activityEntrance/activityTimeUpdate") =>
axios.post(`${prefix_1}${url}`, params);

export const getEventDateConfig = (params, url = "/manage/dj/activityEntrance/getEventDateConfig") =>
axios.get(`${prefix_2}${url}`, { params: { ...params } });

export const getMerchantActivityList = (params, url = "/manage/dj/activityEntrance/getMerchantActivityList") =>
axios.post(`${prefix_2}${url}`, params,{type:1});

export const dj_getMerchantTree = (params, url = "/manage/dj/activityEntrance/getMerchantTree") =>
axios.post(`${prefix_2}${url}`, params);

export const getActivityList = (params, url = "/manage/dj/activityEntrance/getActivityList") =>
axios.get(`${prefix_2}${url}`, { params: { ...params } });

export const dj_addAcMer = (params, url = "/manage/dj/activityEntrance/addAcMer") =>
axios.post(`${prefix_2}${url}`, params);

//活动入口设置查询
export const dj_queryActivityEntranceList = (
  params,
  url = "/manage/dj/activityEntrance/getEntranceBanner"
) => axios.get(`${prefix_2}${url}`, { params: { ...params } });

//活动维护
export const dj_getMaintainConfig = (
  params,
  url = "/manage/dj/activityEntrance/getMaintainConfig"
) => axios.get(`${prefix_2}${url}`, { params: { ...params } });


//活动配置保存
export const dj_updateMaintainConfig = (params, url = "/manage/dj/activityEntrance/updateMaintainConfig") =>
  axios.post(`${prefix_2}${url}`, params);

  
//活动配置查询
export const dj_activityEntrance_detail = (
  params,
  url = "/manage/dj/activityEntrance/getAcMerById"
) => axios.get(`${prefix_2}${url}`, { params: { ...params } });

//活动开关更新
export const dj_activityEntrance_update = (
  params,
  url = "/manage/dj/activityEntrance/updateStatus"
) => axios.post(`${prefix_2}${url}`, params,{type:1});


//活动时间设置查询
export const dj_getEventDateConfig = (
  params,
  url = "/manage/dj/activityEntrance/getEventDateConfig"
) => axios.get(`${prefix_2}${url}`, { params: { ...params } });

//删除商户入口配置
export const dj_activityEntrance_delMerchant = (
  params,
  url = "/manage/dj/activityEntrance/delMerchant"
) => axios.post(`${prefix_2}${url}`, params,{type:1});

//活动入口配置保存
export const dj_activityEntrance_save = (
  params,
  url = "/manage/dj/activityEntrance/updateEntranceBanner"
) => axios.post(`${prefix_2}${url}`, params,{type:1});


//商户活动配置获取配置接口
export const dj_getAcMerById = (
  params,
  url = "/manage/dj/activityEntrance/getAcMerById"
) => axios.get(`${prefix_2}${url}`, { params: { ...params } });


//商户活动配置保存配置接口
export const dj_updateAcMerById = (
  params,
  url = "/manage/dj/activityEntrance/updateAcMerById"
) => axios.post(`${prefix_2}${url}`, params,{type:1});

//活动时间设置修改
export const dj_updateEventDateConfig = (
  params,
  url = "/manage/dj/activityEntrance/updateEventDateConfig"
) => axios.post(`${prefix_2}${url}`, params);