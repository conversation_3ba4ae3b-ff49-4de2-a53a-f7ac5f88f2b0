/*
 * @FilePath: d:/projects/new-merchant-management/src/api/internal/module/operate/index.js
 * @Description:
 */
import * as search from "src/api/internal/module/operate/module/search.js"
import * as league_menu from "src/api/internal/module/operate/module/league_menu.js"
import * as menu_settings from "src/api/internal/module/operate/module/menu_settings.js"
import * as task_settings from "src/api/internal/module/operate/module/task_settings.js"
import * as operation_site from "src/api/internal/module/operate/module/operation_site.js"
import * as operational_activity_settings from "src/api/internal/module/operate/module/operational_activity_settings.js"
import * as blind_box from "src/api/internal/module/operate/module/blind_box.js"
import * as lottery_distribution_record from "src/api/internal/module/operate/module/lottery_distribution_record.js"
import * as activity_betting_statistics from "src/api/internal/module/operate/module/activity_betting_statistics.js"
import * as resource_configuration from "src/api/internal/module/operate/module/resource_configuration.js"
import * as ticket_management from "src/api/internal/module/operate/module/ticket_management.js"
import * as props_management from "src/api/internal/module/operate/module/props_management.js"
import * as user_annual_report from "src/api/internal/module/operate/module/user_annual_report.js"
import * as slot_machine from "src/api/internal/module/operate/module/slot_machine.js"
import * as game_record from "src/api/internal/module/operate/module/game_record.js"
import * as activity_account_change from "src/api/internal/module/operate/module/activity_account_change.js"
import * as tag_management from "src/api/internal/module/operate/module/tag_management.js"
import * as author_management from "src/api/internal/module/operate/module/author_management.js"
import * as article_management from "src/api/internal/module/operate/module/article_management.js"
import * as vip_config from "src/api/internal/module/operate/module/vip_config.js"
export default {
  ...search,
  ...league_menu,
  ...menu_settings,
  ...task_settings,
  ...operation_site,
  ...operational_activity_settings,
  ...blind_box,
  ...lottery_distribution_record,
  ...activity_betting_statistics,
  ...resource_configuration,
  ...ticket_management,
  ...props_management,
  ...user_annual_report,
  ...slot_machine,
  ...game_record,
  ...activity_account_change,
  ...tag_management,
  ...author_management,
  ...article_management,
  ...vip_config,
}
