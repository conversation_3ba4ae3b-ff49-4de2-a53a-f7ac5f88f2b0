/*
 * @Desc:
 * @Date: 2019-12-28 20:59:34
 * @Author:Nice
 * @FilePath: /src/api/external/module/account/index.js
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_17;

//生成管理员密码
export const get_admin_merchant_getPassword = (
  params,
  url = "/admin/merchant/getPassword"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//设置管理员
export const get_admin_merchant_createAdmin = (
  params,
  url = "/admin/merchant/createAdmin"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//修改管理员密码
export const get_admin_merchant_updateAdminPassword = (
  params,
  url = "/admin/merchant/updateAdminPassword"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//查询二级商户列表  
export const get_admin_merchant_merchantList = (
  params,
  url = "/admin/merchant/merchantList"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//查询商户详情       基本资料管理
export const get_admin_merchant_getDetail = (
  params,
  url = "/admin/merchant/getDetail"
) => axios.get(`${prefix}${url}`, { params: { ...params } });


// http://lan-yapi.sportxxxr1pub.com/project/98/interface/api/6587
//  获取查询条件设置   (账户中心/商户信息管理/查看二级商户基本资料) 注单查询条件设置 页面   
export const post_admin_config_getQueryConditionSetting = (
  params,
  url = "/admin/config/getQueryConditionSetting"
) => axios.post(`${prefix}${url}`, params);



//http://lan-yapi.sportxxxr1pub.com/project/98/interface/api/6594
//  修改查询条件设置   (账户中心/商户信息管理/查看二级商户基本资料) 注单查询条件设置 页面   
export const post_admin_config_editQueryConditionSetting = (
  params,
  url = "/admin/config/editQueryConditionSetting"
) => axios.post(`${prefix}${url}`, params);


//修改商户  基本资料管理
export const post_admin_merchant_update = (
  params,
  url = "/admin/merchant/update"
) => axios.post(`${prefix}${url}`, params);


//二级商户管理 新增二级商户
export const post_admin_merchant_create = (
  params,
  url = "/admin/merchant/create"
) => axios.post(`${prefix}${url}`, params);

//二级商户管理 修改客户端状态
export const get_admin_merchant_updateMerchantStatus = (
  params,
  url = "/admin/merchant/updateMerchantStatus"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//二级商户管理 修改商户后台状态
export const get_admin_merchant_updateMerchantBackendStatus = (
  params,
  url = "/admin//merchant/updateMerchantBackendStatus"
) => axios.get(`${prefix}${url}`, { params: { ...params } });



// 我的证书
// 获取key信息
export const get_admin_merchant_getKey = (
  params,
  url = "/admin/merchant/getKey"
) => axios.get(`${prefix}${url}`, { params: { ...params } });


// 生成key
export const get_admin_merchant_generateKey = (
  params,
  url = "/admin/merchant/generateKey"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

// 更新key
export const get_admin_merchant_updateKey = (
  params,
  url = "/admin/merchant/updateKey"
) => axios.get(`${prefix}${url}`, { params: { ...params } });


// 子证书信息列表
export const get_admin_merchant_queryKeyList = (
  params,
  url = "/admin/merchant/queryKeyList"
) => axios.get(`${prefix}${url}`, { params: { ...params } });


// 用户投注管理
export const post_admin_userRepor_queryAllUserStatisticList = (
  params,
  url = "/admin/userReport/queryAllUserStatisticList"
) => axios.post(`${prefix}${url}`, params);


//根据用户Id查看限额信息
export const post_order_user_queryUserBetLimitDetail = (
  params,
  url = "/admin/userReport/queryUserBetLimitDetail"
  ) => axios.get(`${prefix}${url}`, { params: { ...params } });

  //根据用户Id查看赔率
  export const post_order_user_getUserMarketLevels = (
    params,
    url = "/admin/userReport/getUserMarketLevels"
    ) => axios.get(`${prefix}${url}`, { params: { ...params } });

//根据用户Id查看用户延时赛种
export const get_order_user_getUserTradeRestrict = (
  params,
  url = "/admin/userReport/getUserTradeRestrict"
  ) => axios.get(`${prefix}${url}`, { params: { ...params } });


// 用户投注管理-投注详情- 账变明显
export const post_admin_userReport_queryUserTransferList = (
  params,
  url = "/admin/userReport/queryUserTransferList"
) => axios.post(`${prefix}${url}`, params);

// 用户投注管理-投注详情- 账变明显ES
export const post_admin_userReport_queryUserTransferListES = (
  params,
  url = "/admin/userReport/queryUserTransferListES"
) => axios.post(`${prefix}${url}`, params);

//用户投注用户管理-投注详情-账变记录ES实时流
export const post_admin_userReport_queryUserTransferListES_timely = (
  params,
  url = "/admin/userReport/queryUserTransferListStream"
) => axios.post(`${prefix}${url}`, params);

// 数据中心-交易记录查询- 交易记录
export const post_admin_account_findRecord = (
  params,
  url = "/admin/account/findRecord"
) => axios.post(`${prefix}${url}`, params);

// 数据中心-交易记录查询- 账变记录
export const post_admin_account_findAccountHistory = (
  params,
  url = "/admin/account/findAccountHistory"
) => axios.post(`${prefix}${url}`, params);

// 数据中心-交易记录查询- 上下分记录
export const post_admin_account_findAccountTransHistory = (
  params,
  url = "/admin/account/findAccountTransHistory"
) => axios.post(`${prefix}${url}`, params);


// 数据中心-交易记录查询- 账变记录-加扣款 弹窗 (提交)
export const post_admin_account_addChangeRecordHistory = (
  params,
  url = "/admin/account/addChangeRecordHistory"
) => axios.post(`${prefix}${url}`, params);



// 数据中心-交易记录查询- 异常交易记录
export const post_admin_account_findReTryRecord = (
  params,
  url = "/admin/account/findReTryRecord"
) => axios.post(`${prefix}${url}`, params);

//数据中心-交易记录查询-重新发起 单个/多个
export const post_admin_account_retryTransfer = (
  params,
  url = "/admin/account/retryTransfer"
) => axios.post(`${prefix}${url}`, params);

//日志查询
export const post_admin_log_findLog = (
  params,
  url = "/admin/log/findLog"
) => axios.post(`${prefix}${url}`, params);

//日志查询-日志页面下拉查询接口
export const get_admin_log_getLogPages = (
  params,
  url = "/admin/log/getLogPages"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//日志查询-日志页面操作类型查询接口
export const get_admin_log_getLogTypes = (
  params,
  url = "/admin/log/getLogTypes"
) => axios.get(`${prefix}${url}`, { params: { ...params } });


//查询c端设定
export const get_cplate_config = (
  params,
  url = "/admin/config/getConfig"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//保存c端设定
export const post_cplate_updateConfig = (
  params,
  url = "/admin/config/updateConfig"
) => axios.post(`${prefix}${url}`, params);
//c端设定-更新logo地址
export const post_cplate_uploadImgUrl = (
  params,
  url = "/admin/config/uploadImgUrl"
) => axios.post(`${prefix}${url}`, params);


// 账户中心-c端综合设置 查询时区接口
export const post_switchConfig_getShiquTime = (
  params,
  url = "/admin/switchConfig/getMerchantSwitch"
  ) => axios.post(`${prefix}${url}`, params);

// 账户中心-c端综合设置 修改时区接口
export const post_add_merchant_shiquTime_update_switchConfig = (
params,
url = "/admin/switchConfig/setMerchantSwitch"
) => axios.post(`${prefix}${url}`, params);




    //   "1": "无",
      //   "2": "特殊百分比限额",  设置 百分比限额

// /admin/userReport/updateRcsLimit

//
export const post_userReport_updateRcsLimit = (
  params,
  url = "/admin/userReport/updateRcsLimit"
) => axios.post(`${prefix}${url}`, params);



// 获取联赛投注统计数据
export const post_getQueryTournamentUrl = (
  params,
  url = "/admin/match/getQueryTournamentUrl"
) => axios.post(`${prefix}${url}`, params);

//渠道修改管理员密码
export const get_updateAdminUserName = (
  params,
  url = "/admin/merchant/updateAdminUserName"
) => axios.post(`${prefix}${url}`, params);

// 真人-交易&账变记录查询
export const post_zr_getTransferList = (params, url = "/admin/zr/getTransferList") =>
  axios.post(`${prefix}${url}`, params);

// 彩票-交易&账变记录查询
export const post_cp_getTransferList = (params, url = "/admin/cp/getTransferList") =>
  axios.post(`${prefix}${url}`, params);


  // 对内 商户中心 商户管理 新建直营商户  商户分库  初始域名分组  域名方案
export const init_domain_group = (
  params,
  url = "/admin/merchant/initDomainGroup"
) =>  axios.get(`${prefix}${url}`, { params: { ...params } });

// 账户中心- 获取二级商户额度状态
export const post_switchConfig_getErjiDetail = (
  params,
  url = "/admin/switchConfig/getMerchantSwitch"
) => axios.post(`${prefix}${url}`, params);

// 账户中心- 二级商户额度修改
export const post_add_merchant_erji_update_switchConfig = (
  params,
  url = "/admin/switchConfig/setMerchantSwitch"
) => axios.post(`${prefix}${url}`, params);

// /yewu17/admin/merchant/initDomainGroup?merchantCode=

// 商户中心-c端综合设置 查询客户端显示赔率类型状态
export const post_switchConfig_getOddsDetail = (
  params,
  url = "/admin/switchConfig/getMerchantSwitch"
) => axios.post(`${prefix}${url}`, params);

// 商户中心-c端综合设置 修改客户端显示赔率类型状态
export const post_add_merchant_odds_update_switchConfig = (
  params,
  url = "/admin/switchConfig/setMerchantSwitch"
) => axios.post(`${prefix}${url}`, params);


//获取特殊管控-

import * as rcs_user_delay_setting from "src/api/external/module/account/module/rcs_user_delay_setting.js"
export {rcs_user_delay_setting}