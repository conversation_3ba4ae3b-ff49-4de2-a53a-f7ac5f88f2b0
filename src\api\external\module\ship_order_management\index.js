/*  
 * @path: src/api/internal/module/risk_control_set/index.js
 * @Descripttion: 风控查询-出货单管理
 * @Author: 
 * @Date: 2024-08-10 09:48:04
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_17;

// 查询赛事玩法选项
export const get_findOptionsByPlayId = (params, url = "/admin/playList/findOptionsByPlayId") =>
    axios.get(`${prefix}${url}`, { params: { ...params }});

// 新增出货单
export const post_createNewShipOrder = (
    params,
    url = "/admin/rcsShipment/create"
  ) => axios.post(`${prefix}${url}`, params);

// 编辑出货单
export const post_editShipOrder = (
    params,
    url = "/admin/rcsShipment/updateInfo"
  ) => axios.post(`${prefix}${url}`, params);

// 删除出货单
export const post_deleteShipOrder = (
    params,
    url = "/admin/rcsShipment/batchDelete"
  ) => axios.post(`${prefix}${url}`, params);

// 查询出货单列表
export const post_queryShipOrderList = (
    params,
    url = "/admin/rcsShipment/pageList"
  ) => axios.post(`${prefix}${url}`, params);

  // 根据赛事获取赛种
export const get_getSportIdByMatchManageId = (
  params,
  url = "/admin/getSportIdByMatchManageId"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

// 所有玩法列表查询
export const get_queryPlayList = (
  params,
  url = "/admin/BettingOrder/v1/betOrder/queryHotPlayName"
) => axios.get(`${prefix}${url}`, { params: { ...params } });