/*
 * @Desc: 
 * @Date: 2020-02-25 16:10:38
 * @Author:Nice
 * @FilePath: /src/api/internal/module/base/index.js
 */
import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_4;
//设置中心/域名设置---商户后台查询接口
export const domain_query = (
  params,
  url = '/manage/merchant/queryMerchantDomain'
) => axios.get(`${prefix}${url}`, { params: { ...params } })

//商户费率设置-新增//
export const post_manage_merchantRate_add = (
  params,
  url = "/manage/merchantRate/add"
) => axios.post(`${prefix}${url}`, params);

//商户费率设置-修改//
export const post_manage_merchantRate_update = (
  params,
  url = "/manage/merchantRate/update"
) => axios.post(`${prefix}${url}`, params);

//商户费率设置--查询列表//
export const post_set_merchantrate_query = (
  params,
  url = "/manage/merchantRate/queryList"
) => axios.post(`${prefix}${url}`, params);

//商户等级查询--下拉列表无分页//
export const get_manage_merchantLevel_queryLevelList = (
  params,
  url = "/manage/merchantLevel/queryLevelList"
) => axios.get(`${prefix}${url}`, { params: { ...params } });


//商户等级查询--下拉列表无分页//
export const get_manage_merchantLevelcurrencyRate = (
  params,
  url = "/manage/merchantLevel/currencyRate"
) => axios.post(`${prefix}${url}`, params);

//查询费率列表无分页(port:10712)//
export const get_manage_merchantRate_queryRateList = (
  params,
  url = "/manage/merchantRate/queryRateList"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//商户等级设置--查询费率对象//
export const get_manage_merchantLevel_getMerchantLevel = (
  params,
  url = "/manage/merchantLevel/getMerchantLevel"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//商户等级设置--查询//
export const post_manage_merchantLevel_queryList = (
  params,
  url = "/manage/merchantLevel/queryList"
) => axios.post(`${prefix}${url}`, params);

//商户等级设置--修改//
export const post_manage_merchantLevel_update = (
  params,
  url = "/manage/merchantLevel/update"
) => axios.post(`${prefix}${url}`, params);

//商户等级设置--新增//
export const post_manage_merchantLevel_add = (
  params,
  url = "/manage/merchantLevel/add"
) => axios.post(`${prefix}${url}`, params);

// 商务列表  3-12
export const get_manage_merchant_admin_list = (
  params,
  url = "/manage/merchant/admin/list"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//商户等级设置--详情查询
export const post_manage_merchantLevel_byid = (params, url = "/manage/merchantLevel") =>
  axios.post(`${prefix}${url}/${params.id}`);


//日志查询
export const post_manage_log_findLog = (
  params,
  url = "/manage/log/findLog"
) => axios.post(`${prefix}${url}`, params);

//日志查询-日志页面下拉查询接口
export const get_manage_log_getLogPages = (
  params,
  url = "/manage/log/getLogPages"
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//日志查询-日志页面操作类型查询接口
export const get_manage_log_getLogTypes = (
  params,
  url = "/manage/log/getLogTypes"
) => axios.get(`${prefix}${url}`, { params: { ...params } });


//获取商户域名list
export const post_merchant_domain_list = (
  params,
  url = "/manage/merchant/domain/merchantDomainList"
) => axios.post(`${prefix}${url}${get_data(params)}`, params);

//获取商户APP域名list
export const post_merchant_app_domain_list = (
  params,
  url = "/manage/merchant/domain/merchantAppDomainList"
) => axios.post(`${prefix}${url}${get_data(params)}`, params);


//修改商户域名
export const post_update_domain_list = (
  params,
  url = "/manage/merchant/domain/updateDomainList"
) => axios.post(`${prefix}${url}${get_data(params, 1)}`, params);

//修改商户APP域名
export const post_update_app_domain_list = (
  params,
  url = "/manage/merchant/domain/updateAppDomainList"
) => axios.post(`${prefix}${url}${get_data(params, 1)}`, params);


//更新商户域名  post请求修改为get 单个刷新缓存
export const post_clean_merchant = (
  params,
  url = `/manage/merchant/domain/cleanMerchant`
) => axios.get(`${prefix}${url}`, { params: { ...params } });

//更新商户域名  post请求修改为get 批量刷新缓存
export const post_clean_all_merchant = (
  params,
  url = `/manage/merchant/domain/cleanAllMerchant`
) => axios.get(`${prefix}${url}`, { params: { ...params } });

const  get_data= (data, type = 2)=>{

  let str='?'
  for(let i in data){
    if(data[i]!==''){
        str+=`${i}=${data[i]}&`
    }
  
  }
  str=str.slice(0,str.length-1)
  return str

}


//全局接口配置
export const post_oss_upload = (
  params,
  url = "/manage/merchantDomain/ossUpload"
) => axios.post(`${prefix}${url}`, params);

//全局接口获取
export const init_oss_upload = (
  url
) => axios.get(`${url}`);



//1540需求【商户等级设置2】--查询//
export const get_manage_config_queryCodeConfigList = (
  params,
  url = "/manage/config/queryCodeConfigList"
) => axios.get(`${prefix}${url}`, { params: { ...params } });




//1540需求【商户等级设置2】--编辑//点编辑是这个接口，，一条一条改
export const post_manage_config_updateCodeConfig = (
  params,tokenCode,
  url = `/manage/config/updateCodeConfig?tokenCode=${tokenCode}`
) => axios.post(`${prefix}${url}`, params);



