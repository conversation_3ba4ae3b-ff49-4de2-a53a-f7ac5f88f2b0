/*
 * @Date           : 2020-10-03 19:58:50
 * @FilePath: d:/projects/new-merchant-management/src/boot/ivew.js
 * @description    :
 */
import Vue from "vue";
import { SessionStorage } from "quasar";
import {
    Checkbox,
    Tree,
    ColorPicker,
    Table,
    Input,
    Button,
    Select,
    Option,
    Modal,
    Timeline,
    TimelineItem,
  } from "view-design";
  import lang from "view-design/dist/locale/en-US";
  import 'view-design/dist/styles/iview.css';

  if (SessionStorage.getItem("i18n") == "en") {
    locale(lang);
  }
  //  从新 定义  iview 组件名称 ，方便 代码内区分
  const iviewModule = {
    iCheckbox: Checkbox,
    Tree,
    ColorPicker,
    iTable:Table,
    iInput: Input,
    iButton:Button,
    iModal:Modal,
    iSelect: Select,
    iOption:Option,
    iTimeline:Timeline,
    iTimelineItem:TimelineItem,
  };
  Object.keys(iviewModule).forEach(key => {
    Vue.component(key, iviewModule[key]);
  });
