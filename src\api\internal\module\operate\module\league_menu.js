import axios from "src/api/common/axioswarpper.js";

let prefix = process.env.API_PREFIX_10;

//获取热门联赛球种
export const post_v1_tournamentHot_getTournamentSport = (params, url = "/v1/tournamentHot/getTournamentSport") =>
  axios.post(`${prefix}${url}`, params, {type: 1});

//获取热门联赛赛事信息
export const post_v1_tournamentHot_getTournamentMatchs = (params, url = "/v1/tournamentHot/getTournamentMatchs") =>
  axios.post(`${prefix}${url}`, params);

//更新热门联赛
export const post_v1_tournamentHot_updateHotTournament = (params, url = "/v1/tournamentHot/updateHotTournament") =>
  axios.post(`${prefix}${url}`, params);

//获取已设置热门联赛
export const post_v1_tournamentHot_getHotTournament = (params, url = "/v1/tournamentHot/getHotTournament") =>
  axios.post(`${prefix}${url}`, params, {type: 1});

//添加热门联赛
export const post_v1_tournamentHot_addHotTournament = (params, url = "/v1/tournamentHot/addHotTournament") =>
  axios.post(`${prefix}${url}`, params);
